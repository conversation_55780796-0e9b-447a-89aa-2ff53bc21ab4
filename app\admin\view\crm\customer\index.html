<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-tab" lay-filter="nav-tabs-index">
            <ul class="layui-tab-title">
                <li class="layui-this" data-value="1" data-field="scope">{:fy("It's mine")}</li>
                <li  data-value="2" data-field="scope">{:fy('Subordinate')}</li>
                <li  data-value="3" data-field="scope">{:fy('All')}</li>

                <li  data-value="20" data-field="scope">{:fy('Share with others')}</li>
                <li  data-value="21" data-field="scope">{:fy('Shared with me')}</li>

                <li  data-value="10" data-field="scope">{:fy('To be followed')}</li>
                <li  data-value="11" data-field="scope">{:fy('Followed up today')}</li>
                <li  data-value="12" data-field="scope">{:fy('Never followed up')}</li>
            </ul>
        </div>
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="{:auth('crm.customer/add')}"
               data-auth-addOrder="{:auth('crm.customer/add')}"
               data-auth-edit="{:auth('crm.customer/edit')}"
               data-auth-delete="{:auth('crm.customer/delete')}"
               data-auth-import="{:auth('crm.customer/import')}"
               data-auth-export="{:auth('crm.customer/export')}"
               data-auth-fields="{:auth('system.fields/index')}"
               data-auth-to_move_gh="{:auth('crm.customer/to_move_gh')}"
               data-auth-alter_pr_user="{:auth('crm.customer/alter_pr_user')}"
               data-auth-dialogue="{:auth('client/dialogue')}"
               data-auth-share="{:auth('crm.customer/share')}"
               data-auth-del_share="{:auth('crm.customer/del_share')}"
               data-auth-reduplicate="{:auth('crm.customer/reduplicate')}"
               data-auth-hangup="{:auth('crm.customer/hangup')}"
               data-auth-dial="{:auth('crm.customer/dial')}"
               lay-filter="currentTable">
        </table>
    </div>
</div>