<?php


namespace app\plugins\wechat\admin\controller;

use app\common\controller\PluginsController;
use app\wechat\model\WechatAccount;
use app\wechat\model\WechatMenu;
use think\App;
use think\facade\View;

class Menu extends PluginsController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new \app\plugins\wechat\common\model\WechatMenu();
    }

/*    public function index()
    {
        if ($this->request->isAjax()) {
            if($this->request->param('selectFields')){
                $this->selectList();
            }
            list($this->page, $this->pageSize,$sort,$where) = $this->buildParames();
            $count = $this->model
                ->where($where)
                ->count();
            $list = $this->model
                ->where($where)
                ->order($sort)
                ->page($this->page,$this->pageSize)
                ->select();
            $result = ['code' => 0, 'msg' => lang('operation success'), 'data' => $list, 'count' => $count];
            return json($result);
        }
        return $this->fetch();
    }*/

    /**
     * @NodeAnnotation ('Add')
     * @return \think\response\View|void
     */
    public function add()
    {
        if($this->request->isAjax()){
            $post = $this->request->post();
            $rule = ['menu_data'=>'require'];
            $this->validate($post, $rule);
            try {
                $post['status'] = 0;
                $save = $this->model->save($post);
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }
            $save ? $this->success(fy('Save successfully')) : $this->error(fy('Save failed'));
        }
        $account=\think\facade\Db::name('weixin')->where('name','=','account')->value('value');
        if(empty($account)){
            $this->error(fy("Please configure the official account first"),myurl('plugins/wechat/admin/account/index'));
        }
        try {
            $menu = new \WeChat\Menu(\app\plugins\wechat\library\Config::load());
            $list=$menu->get();
            print_r($list);
        }catch(\Exception $e){
            $this->error(fy("Please configure the official account Appid and AppSecret correctly"));
        }



        $view=[];
        $view['account']=json_decode($account,true);
        $view['formData']=[];

        if(isset($list['menu']['button'])){
            $view['formData']['menu_data']=$list['menu']['button'];
            $view['formData']['menu_name']=fy("Default menu");
        }
        return view('menu/add',$view);
    }
    /**
     * 跟新菜单
     */
    public function edit($id)
    {
        $row = $this->model->find($id);
        if(empty($row)) $this->error(fy('The data does not exist'));

        if ($this->request->isPost()) {
            $post = $this->request->post();
            $rule = ['menu_data'=>'require'];
            $this->validate($post, $rule);
            try {
                $save = $row->save($post);
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }
            $save ? $this->success(fy('Save successfully')) : $this->error(fy('Save failed'));
        }

        $account=\think\facade\Db::name('weixin')->where('name','=','account')->value('value');
        if(empty($account)){
            $this->error('请先进行公众号配置',myurl('plugins/wechat/admin/account/index'));
        }
        $row->menu_data = json_decode($row->menu_data,true);

        $view = ['formData'=>$row,'account'=>json_decode($account,true)];
        return view('menu/add',$view);
    }
    /**
     * @NodeAnnotation(title="modify")
     */
    public function modify(){
        $this->checkPostRequest();
        $post = $this->request->post();
        $rule = [
            'id|ID'    => 'require',
            'field|字段' => 'require',
            'value|值'  => 'require',
        ];
        $this->validater($post, $rule);

        $row = $this->model->find($post['id']);
        if (!$row) {
            $this->error(fy('The data does not exist'));
        }
        if (!in_array($post['field'], $this->allowModifyFields)) {
            $this->error(fy('This field is not allowed to be modified').':' . $post['field']);
        }

        $res=false;
            try{
                \think\facade\Db::startTrans();
                $res=$row->save([
                    $post['field'] => $post['value'],
                ]);
                if($res && $post['value']==1 && $post['field']=='status'){

                    $this->model->where('id','<>',$post['id'])->update(['status'=>0]);
//
                    $menu = new \WeChat\Menu(\app\plugins\wechat\library\Config::load());
//                    $menu->delete();
                    $menu_data =$this->menuData(json_decode($row->menu_data,true));
                    $menu->create(['button' => $menu_data]);

                }
                \think\facade\Db::commit();
            }catch(\Exception $e){
                \think\facade\Db::rollback();
                $this->error($e->getMessage());
            }
        if($res){
            $this->success(fy('Save successfully'));
        }else{
            $this->success(fy('Save failed'));
        }


    }

    protected function menuData($menu_data){
        $data = [];
        if(empty($menu_data)){
            throw new \Exception('菜单数据为空！');
        }
//        var_dump($menu_data);
        foreach($menu_data as $k=>&$v){
            if(empty($v['type'])){
                continue;
            }
            $data[$k]['type'] = isset($v['type'])?$v['type']:'';
            $data[$k]['name'] = $v['name'];
            switch($v['type']){
                case 'click':
                    $data[$k]['key'] = $v['key'];
                    if(!$v['key']) {throw new \Exception('第'.$k.'个菜单key不能为空'); };
                    break;
                case 'view':
                    $data[$k]['url'] = $v['url'];
                    if(!$v['url']) {throw new \Exception('第'.$k.'个菜单url不能为空') ;};
                    break;
                case 'miniprogram':
                    $data[$k]['appid'] = $v['appid'];
                    $data[$k]['pagepath'] = $v['pagepath'];
                    if(!$v['appid'] || !$v['pagepath']) {throw new \Exception('第'.$k.'个菜单appid或pagepath不能为空') ;};
                    break;
                case 'scancode_waitmsg':
                    $data[$k]['key'] = 'rselfmenu_0_0';
                    break;
                case 'scancode_push':
                    $data[$k]['key'] = 'rselfmenu_0_1';
                    break;
                case 'location_select':
                    $data[$k]['key'] = 'rselfmenu_2_0';
                    break;
                case 'pic_sysphoto':
                    $data[$k]['key'] = 'rselfmenu_1_0';
                    break;
                case 'pic_photo_or_album':
                    $data[$k]['key'] = 'rselfmenu_1_1';
                    break;
                case 'pic_weixin':
                    $data[$k]['key'] = 'rselfmenu_1_2';
                    break;
            }
            if(isset($v['sub_button']) && $v['sub_button']){
                foreach($v['sub_button'] as $key=>$val){
                    $data[$k]['sub_button'][$key]['type'] =  isset($val['type'])?$val['type']:'';
                    $data[$k]['sub_button'][$key]['name'] =  $val['name'];
                    switch($val['type']){
                        case 'click':
                            $data[$k]['sub_button'][$key]['key'] = $val['key'];
                            if(!$val['key']) {throw new \Exception('第'.$k.'个菜单第'.$key.'个子菜单key不能为空'); };
                            break;
                        case 'view':
                            $data[$k]['sub_button'][$key]['url'] = $val['url'];
                            if(!$val['url']) {throw new \Exception('第'.$k.'个菜单第'.$key.'个子菜单url不能为空'); };
                            break;
                        case 'miniprogram':
                            $data[$k]['sub_button'][$key]['appid'] = $val['appid'];
                            $data[$k]['sub_button'][$key]['pagepath'] = $val['pagepath'];
                            if(!$val['appid'] || !$val['pagepath']) {throw new \Exception('第'.$k.'个菜单第'.$key.'个子菜单appid或pagepath不能为空'); };
                            break;
                        case 'scancode_waitmsg':
                            $data[$k]['sub_button'][$key]['key'] = 'rselfmenu_0_0';
                            break;
                        case 'scancode_push':
                            $data[$k]['sub_button'][$key]['key'] = 'rselfmenu_0_1';
                            break;
                        case 'location_select':
                            $data[$k]['sub_button'][$key]['key'] = 'rselfmenu_2_0';
                            break;
                        case 'pic_sysphoto':
                            $data[$k]['sub_button'][$key]['key'] = 'rselfmenu_1_0';
                            break;
                        case 'pic_photo_or_album':
                            $data[$k]['sub_button'][$key]['key'] = 'rselfmenu_1_1';
                            break;
                        case 'pic_weixin':
                            $data[$k]['sub_button'][$key]['key'] = 'rselfmenu_1_2';
                            break;
                    }
                }
            }
        }
        return $data;
    }



}
