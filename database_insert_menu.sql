-- 插入通话统计菜单到权限规则表

-- 1. 获取现有的"数据分析"菜单ID（ID: 384）
SET @analysis_id = 384;

-- 2. 插入"通话统计"菜单
INSERT INTO `ymwl_auth_rule` (`href`, `title`, `type`, `status`, `authopen`, `icon`, `condition`, `pid`, `sort`, `addtime`, `menustatus`, `target`) VALUES
('analysis.callstatistics/index', '通话统计', 1, 1, 1, 'fa fa-phone', '', @analysis_id, 60, UNIX_TIMESTAMP(), 1, '_self');

-- 获取通话统计菜单的ID
SET @call_stats_id = (SELECT id FROM `ymwl_auth_rule` WHERE `href` = 'analysis.callstatistics/index' LIMIT 1);

-- 3. 插入通话统计的子菜单权限
INSERT INTO `ymwl_auth_rule` (`href`, `title`, `type`, `status`, `authopen`, `icon`, `condition`, `pid`, `sort`, `addtime`, `menustatus`, `target`) VALUES
('analysis.callstatistics/chart', '图表', 1, 1, 1, '', '', @call_stats_id, 0, UNIX_TIMESTAMP(), 0, '_self'),
('analysis.callstatistics/summary', '汇总', 1, 1, 1, '', '', @call_stats_id, 1, UNIX_TIMESTAMP(), 0, '_self'),
('analysis.callstatistics/export', '导出', 1, 1, 1, '', '', @call_stats_id, 2, UNIX_TIMESTAMP(), 0, '_self'),
('analysis.callstatistics/delete', '删除', 1, 1, 1, '', '', @call_stats_id, 3, UNIX_TIMESTAMP(), 0, '_self');

-- 4. 为超级管理员组添加权限（group_id = 1）
-- 获取新增的权限ID
SET @new_rule_ids = (
    SELECT GROUP_CONCAT(id)
    FROM `ymwl_auth_rule`
    WHERE `href` IN ('analysis.callstatistics/index', 'analysis.callstatistics/chart', 'analysis.callstatistics/summary', 'analysis.callstatistics/export', 'analysis.callstatistics/delete')
);

-- 更新超级管理员组的权限规则
UPDATE `ymwl_auth_group`
SET `rules` = CONCAT(IFNULL(`rules`, ''), ',', @new_rule_ids)
WHERE `id` = 1;
