-- 插入数据统计菜单到权限规则表

-- 1. 首先查找"数据分析"父菜单的ID（假设已存在）
-- 如果不存在，需要先创建数据分析菜单
INSERT IGNORE INTO `ymwl_auth_rule` (`href`, `title`, `type`, `status`, `authopen`, `icon`, `condition`, `pid`, `sort`, `addtime`, `menustatus`, `target`) VALUES
('analysis', '数据分析', 1, 1, 1, 'fa fa-bar-chart', '', 0, 8, UNIX_TIMESTAMP(), 1, '_self');

-- 获取数据分析菜单的ID
SET @analysis_id = (SELECT id FROM `ymwl_auth_rule` WHERE `href` = 'analysis' AND `pid` = 0 LIMIT 1);

-- 2. 插入"通话统计"菜单
INSERT INTO `ymwl_auth_rule` (`href`, `title`, `type`, `status`, `authopen`, `icon`, `condition`, `pid`, `sort`, `addtime`, `menustatus`, `target`) VALUES
('analysis/callstatistics', '通话统计', 1, 1, 1, 'fa fa-phone', '', @analysis_id, 1, UNIX_TIMESTAMP(), 1, '_self');

-- 获取通话统计菜单的ID
SET @call_stats_id = (SELECT id FROM `ymwl_auth_rule` WHERE `href` = 'analysis/callstatistics' LIMIT 1);

-- 3. 插入通话统计的子菜单权限
INSERT INTO `ymwl_auth_rule` (`href`, `title`, `type`, `status`, `authopen`, `icon`, `condition`, `pid`, `sort`, `addtime`, `menustatus`, `target`) VALUES
('analysis/callstatistics/index', '查看', 1, 1, 1, '', '', @call_stats_id, 0, UNIX_TIMESTAMP(), 0, '_self'),
('analysis/callstatistics/export', '导出', 1, 1, 1, '', '', @call_stats_id, 1, UNIX_TIMESTAMP(), 0, '_self'),
('analysis/callstatistics/delete', '删除', 1, 1, 1, '', '', @call_stats_id, 2, UNIX_TIMESTAMP(), 0, '_self');

-- 4. 为超级管理员组添加权限（group_id = 1）
-- 获取新增的权限ID
SET @new_rule_ids = (
    SELECT GROUP_CONCAT(id) 
    FROM `ymwl_auth_rule` 
    WHERE `href` IN ('analysis', 'analysis/callstatistics', 'analysis/callstatistics/index', 'analysis/callstatistics/export', 'analysis/callstatistics/delete')
);

-- 更新超级管理员组的权限规则
UPDATE `ymwl_auth_group` 
SET `rules` = CONCAT(IFNULL(`rules`, ''), ',', @new_rule_ids)
WHERE `id` = 1;
