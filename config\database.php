<?php
//此源码禁止用于含木马、病毒、色情、赌博、诈骗等违法用途。对违法违规用途的用户，拒绝提供后续技术支持，并协助有关行政机关等进行追索和查处。

use think\facade\Env;
return [
    // 默认使用的数据库连接配置
    'default'         => Env::get('database.driver', 'mysql'),

    // 自定义时间查询规则
    'time_query_rule' => [],

    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => true,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 数据库连接配置信息
    'connections'     => [
        'mysql' => [
    // 数据库类型
            'type'              => Env::get('database.type', 'mysql'),
    // 服务器地址
            'hostname'          => Env::get('database.hostname', '127.0.0.1'),
    // 数据库名
            'database'          => Env::get('database.database', '127'),
    // 用户名
            'username'          => Env::get('database.username', '127'),
    // 密码
            'password'          => Env::get('database.password', 'RWkCrGifLKDEfSbF'),
    // 端口
            'hostport'          => Env::get('database.hostport', '3306'),
    // 数据库连接参数
    'params'          => [],
    // 数据库编码默认采用utf8
            'charset'           => Env::get('database.charset', 'utf8mb4'),
    // 数据库表前缀
            'prefix'            => Env::get('database.prefix', 'ymwl_'),
    // 数据库调试模式

    // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
    'deploy'          => 0,
    // 数据库读写是否分离 主从式有效
    'rw_separate'     => false,
    // 读写分离后 主服务器数量
    'master_num'      => 1,
    // 指定从服务器序号
    'slave_no'        => '',
    // 是否严格检查字段是否存在
    'fields_strict'   => true,
    // 自动写入时间戳字段
    // 是否需要断线重连
    'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'       => env('APP_DEBUG', false),
            // 开启字段缓存
            'fields_cache'      => true,
            // 字段缓存路径
            'schema_cache_path' => app()->getRuntimePath() . 'schema' . DIRECTORY_SEPARATOR,
        ],

        // 更多的数据库配置信息
    ],
];
