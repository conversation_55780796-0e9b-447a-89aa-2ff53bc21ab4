<?php /*a:2:{s:54:"C:\wwwroot\127.0.0.1\app\admin\view\attachs\index.html";i:1672228088;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container" id="app">
  <table class="layui-table" id="table-pro">
    <thead>
    <tr>
      <th><?php echo fy('Attachment'); ?> <?php echo fy('Name'); ?></th>
      <th><?php echo fy('Upload time'); ?></th>
      <th><?php echo fy('file type'); ?></th>
    </tr>
    </thead>
    <tbody>
    <?php foreach($list as $v){ ?>
    <tr>
      <td><a href="<?php echo attrUrl($v['url']); ?>" target="_blank"><?php echo htmlentities($v['original_name']); ?></a></td>
      <td><?php echo date('Y-m-d H:i',$v['create_time']); ?></td>
      <td><?php echo htmlentities($v['file_ext']); ?></td>
    </tr>
    <?php } ?>
    </tbody>
  </table>
</div>
</body>
</html>