<?php /*a:3:{s:59:"/www/wwwroot/ceshi71.cn/app/admin/view/general/profile.html";i:1672234310;s:55:"/www/wwwroot/ceshi71.cn/app/admin/view/common/head.html";i:1671106604;s:55:"/www/wwwroot/ceshi71.cn/app/admin/view/common/foot.html";i:1672069288;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/public/static/plugs/layui-v2.7.6/css/layui.css" media="all">

    <link rel="stylesheet" href="/public/static/plugs/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/global.css" media="all">
    <script src="/public/static/common/js/jquery.min.js"></script>

    <script src="/public/static/admin/js/common.js"></script>
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
</head>
<body >
<div class="admin-main layui-anim layui-anim-upbit">
  <fieldset class="layui-elem-field layui-field-title">
    <legend><?php echo fy('Profile modification'); ?></legend>
  </fieldset>
  <form class="layui-form layui-form-pane" lay-filter="form">
    <div class="layui-form-item">
      <label class="layui-form-label"><?php echo lang('Username'); ?></label>
      <div class="layui-input-block">
        <input type="text" name="username" lay-verify="required" class="layui-input" readonly>
      </div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label"><?php echo lang('Password'); ?></label>
      <div class="layui-input-block">
        <input type="password" name="pwd" placeholder="<?php echo lang('Please enter'); ?><?php echo lang('Password'); ?>" <?php if(ACTION == 'adminadd'): ?>lay-verify="required"<?php endif; ?> class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label"><?php echo fy('Avatar'); ?></label>
      <input type="hidden" name="avatar" id="avatar">
      <input type="hidden" name="admin_id" id="admin_id">
      <div class="layui-input-block">
        <div class="layui-upload">
          <button type="button" class="layui-btn layui-btn-primary" id="adBtn"><i class="icon icon-upload3"></i><?php echo fy('Click Upload'); ?></button>
          <div class="layui-upload-list">
            <img class="layui-upload-img" id="adPic">
            <p id="demoText"></p>
          </div>
        </div>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label"><?php echo lang('email'); ?></label>
      <div class="layui-input-block">
        <input type="text" name="email" lay-verify="email" placeholder="<?php echo lang('Please enter'); ?>用户邮箱" class="layui-input"></div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label"><?php echo lang('tel'); ?></label>
      <div class="layui-input-block">
        <input type="text" name="tel" lay-verify="phone" value="" placeholder="<?php echo lang('Please enter'); ?>手机号" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <div class="layui-input-block">
        <button type="button" class="layui-btn" lay-submit="" lay-filter="submit"><?php echo lang('submit'); ?></button>
      </div>
    </div>
  </form>
</div>

<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/public/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script>
  layui.use(['form', 'layer','upload'], function () {
    var form = layui.form, layer = layui.layer,$= layui.jquery,upload = layui.upload;
    var info = <?php echo $info; ?>;
    form.val("form", info);
    if(info){
      $('#adPic').attr('src',"<?php echo attrUrl($info_raw['avatar']); ?>");
    }
    form.render();
    form.on('submit(submit)', function (data) {
      loading =layer.load(1, {shade: [0.1,'#fff']});
      $.post("", data.field, function (res) {
        layer.close(loading);
        if (res.code > 0) {
          layer.msg(res.msg, {time: 2800, icon: 1}, function () {
            reloadPage();
          });
        } else {
          layer.msg(res.msg, {time: 2800, icon: 2});
        }
      });
    });
    //普通图片上传
    var uploadInst = upload.render({
      elem: '#adBtn'
      ,url: '<?php echo url("ajax/upload"); ?>'
      ,before: function(obj){
        //预读本地文件示例，不支持ie8
        obj.preview(function(index, file, result){
          $('#adPic').attr('src', result); //图片链接（base64）
        });
      },
      done: function(res){
        if(res.code>0){
          $('#avatar').val(res.data.url);
        }else{

          return layer.msg('<?php echo fy("Upload failed"); ?>');
        }
      }
      ,error: function(){
        //演示失败状态，并实现重传
        var demoText = $('#demoText');
        demoText.html('<span style="color: #FF5722;"><?php echo fy("Upload failed"); ?></span> <a class="layui-btn layui-btn-mini demo-reload"><?php echo fy("Retry"); ?></a>');
        demoText.find('.demo-reload').on('click', function(){
          uploadInst.upload();
        });
      }
    });
  });
</script>