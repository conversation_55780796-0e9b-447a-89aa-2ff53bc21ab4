<?php
namespace app\api\controller;

use think\facade\Db;
use think\facade\Validate;

/**
 * 通话数据统计控制器
 */
class CallStatistics extends Common
{
    /**
     * 上传通话数据
     * @return void
     */
    public function upload()
    {
        // 获取参数
        $callCount = $this->request->post('call_count/d', 0); // 通话次数
        $callDuration = $this->request->post('call_duration/d', 0); // 通话时长（秒）
        $callConnected = $this->request->post('call_connected/d', 0); // 接通次数
        $callDate = $this->request->post('call_date', date('Y-m-d')); // 统计日期
        
        // 参数验证
        $validate = Validate::rule([
            'call_count' => 'require|number|egt:0',
            'call_duration' => 'require|number|egt:0',
            'call_connected' => 'require|number|egt:0|elt:call_count',
            'call_date' => 'require|date',
        ])->message([
            'call_count.require' => '通话次数不能为空',
            'call_count.number' => '通话次数必须为数字',
            'call_count.egt' => '通话次数必须大于等于0',
            'call_duration.require' => '通话时长不能为空',
            'call_duration.number' => '通话时长必须为数字',
            'call_duration.egt' => '通话时长必须大于等于0',
            'call_connected.require' => '接通次数不能为空',
            'call_connected.number' => '接通次数必须为数字',
            'call_connected.egt' => '接通次数必须大于等于0',
            'call_connected.elt' => '接通次数不能大于通话次数',
            'call_date.require' => '统计日期不能为空',
            'call_date.date' => '统计日期格式不正确',
        ]);
        
        if (!$validate->check([
            'call_count' => $callCount,
            'call_duration' => $callDuration,
            'call_connected' => $callConnected,
            'call_date' => $callDate,
        ])) {
            $this->error($validate->getError());
        }
        
        // 检查日期是否合法（不能是未来日期）
        if (strtotime($callDate) > time()) {
            $this->error('统计日期不能是未来日期');
        }
        
        // 查询是否已存在该日期的记录
        $exists = Db::name('ymwl_call_statistics')
            ->where('admin_id', $this->adminInfo['admin_id'])
            ->where('call_date', $callDate)
            ->find();
        
        if ($exists) {
            // 更新记录
            $result = Db::name('ymwl_call_statistics')
                ->where('id', $exists['id'])
                ->update([
                    'call_count' => $callCount,
                    'call_duration' => $callDuration,
                    'call_connected' => $callConnected,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            
            if ($result) {
                $this->success('更新成功');
            } else {
                $this->error('更新失败');
            }
        } else {
            // 新增记录
            $result = Db::name('ymwl_call_statistics')->insert([
                'admin_id' => $this->adminInfo['admin_id'],
                'call_count' => $callCount,
                'call_duration' => $callDuration,
                'call_connected' => $callConnected,
                'call_date' => $callDate,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            
            if ($result) {
                $this->success('添加成功');
            } else {
                $this->error('添加失败');
            }
        }
    }
    
    /**
     * 获取通话数据统计列表
     * @return void
     */
    public function index()
    {
        // 获取参数
        $startDate = $this->request->param('start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = $this->request->param('end_date', date('Y-m-d'));
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 15);
        
        // 参数验证
        $validate = Validate::rule([
            'start_date' => 'require|date',
            'end_date' => 'require|date',
        ])->message([
            'start_date.require' => '开始日期不能为空',
            'start_date.date' => '开始日期格式不正确',
            'end_date.require' => '结束日期不能为空',
            'end_date.date' => '结束日期格式不正确',
        ]);
        
        if (!$validate->check([
            'start_date' => $startDate,
            'end_date' => $endDate,
        ])) {
            $this->error($validate->getError());
        }
        
        // 构建查询条件
        $where = [
            ['admin_id', '=', $this->adminInfo['admin_id']],
            ['call_date', '>=', $startDate],
            ['call_date', '<=', $endDate],
        ];
        
        // 查询数据
        $count = Db::name('ymwl_call_statistics')->where($where)->count();
        $list = Db::name('ymwl_call_statistics')
            ->where($where)
            ->page($page, $limit)
            ->order('call_date', 'desc')
            ->select()
            ->toArray();
        
        // 计算接通率
        foreach ($list as &$item) {
            $item['connect_rate'] = $item['call_count'] > 0 ? round($item['call_connected'] / $item['call_count'] * 100, 2) : 0;
            $item['avg_duration'] = $item['call_connected'] > 0 ? round($item['call_duration'] / $item['call_connected'], 2) : 0;
        }
        
        // 计算汇总数据
        $summary = [
            'total_call_count' => 0,
            'total_call_duration' => 0,
            'total_call_connected' => 0,
            'avg_connect_rate' => 0,
            'avg_duration' => 0,
        ];
        
        $allData = Db::name('ymwl_call_statistics')
            ->where($where)
            ->select()
            ->toArray();
        
        foreach ($allData as $item) {
            $summary['total_call_count'] += $item['call_count'];
            $summary['total_call_duration'] += $item['call_duration'];
            $summary['total_call_connected'] += $item['call_connected'];
        }
        
        $summary['avg_connect_rate'] = $summary['total_call_count'] > 0 ? 
            round($summary['total_call_connected'] / $summary['total_call_count'] * 100, 2) : 0;
        $summary['avg_duration'] = $summary['total_call_connected'] > 0 ? 
            round($summary['total_call_duration'] / $summary['total_call_connected'], 2) : 0;
        
        $data = [
            'count' => $count,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
            'summary' => $summary,
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 获取通话数据统计图表数据
     * @return void
     */
    public function chart()
    {
        // 获取参数
        $startDate = $this->request->param('start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = $this->request->param('end_date', date('Y-m-d'));
        
        // 参数验证
        $validate = Validate::rule([
            'start_date' => 'require|date',
            'end_date' => 'require|date',
        ])->message([
            'start_date.require' => '开始日期不能为空',
            'start_date.date' => '开始日期格式不正确',
            'end_date.require' => '结束日期不能为空',
            'end_date.date' => '结束日期格式不正确',
        ]);
        
        if (!$validate->check([
            'start_date' => $startDate,
            'end_date' => $endDate,
        ])) {
            $this->error($validate->getError());
        }
        
        // 构建查询条件
        $where = [
            ['admin_id', '=', $this->adminInfo['admin_id']],
            ['call_date', '>=', $startDate],
            ['call_date', '<=', $endDate],
        ];
        
        // 查询数据
        $list = Db::name('ymwl_call_statistics')
            ->where($where)
            ->order('call_date', 'asc')
            ->select()
            ->toArray();
        
        // 准备图表数据
        $dates = [];
        $callCounts = [];
        $callConnecteds = [];
        $connectRates = [];
        $avgDurations = [];
        
        foreach ($list as $item) {
            $dates[] = $item['call_date'];
            $callCounts[] = $item['call_count'];
            $callConnecteds[] = $item['call_connected'];
            $connectRates[] = $item['call_count'] > 0 ? round($item['call_connected'] / $item['call_count'] * 100, 2) : 0;
            $avgDurations[] = $item['call_connected'] > 0 ? round($item['call_duration'] / $item['call_connected'], 2) : 0;
        }
        
        $data = [
            'dates' => $dates,
            'call_counts' => $callCounts,
            'call_connecteds' => $callConnecteds,
            'connect_rates' => $connectRates,
            'avg_durations' => $avgDurations,
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 删除通话数据统计记录
     * @return void
     */
    public function delete()
    {
        $id = $this->request->param('id/d');
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        // 查询记录是否存在
        $record = Db::name('ymwl_call_statistics')
            ->where('id', $id)
            ->where('admin_id', $this->adminInfo['admin_id'])
            ->find();
        
        if (empty($record)) {
            $this->error('记录不存在或无权限删除');
        }
        
        // 删除记录
        $result = Db::name('ymwl_call_statistics')->delete($id);
        
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 获取新增通话数据统计
     * @return void
     */
    public function growth()
    {
        // 获取参数
        $startDate = $this->request->param('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $this->request->param('end_date', date('Y-m-d'));
        $compareType = $this->request->param('compare_type', 'day'); // 比较类型：day-日环比，week-周环比，month-月环比
        
        // 参数验证
        $validate = Validate::rule([
            'start_date' => 'require|date',
            'end_date' => 'require|date',
            'compare_type' => 'require|in:day,week,month',
        ])->message([
            'start_date.require' => '开始日期不能为空',
            'start_date.date' => '开始日期格式不正确',
            'end_date.require' => '结束日期不能为空',
            'end_date.date' => '结束日期格式不正确',
            'compare_type.require' => '比较类型不能为空',
            'compare_type.in' => '比较类型只能是day,week,month',
        ]);
        
        if (!$validate->check([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'compare_type' => $compareType,
        ])) {
            $this->error($validate->getError());
        }
        
        // 构建查询条件
        $where = [
            ['admin_id', '=', $this->adminInfo['admin_id']],
            ['call_date', '>=', $startDate],
            ['call_date', '<=', $endDate],
        ];
        
        // 查询当前周期数据
        $currentData = Db::name('ymwl_call_statistics')
            ->where($where)
            ->order('call_date', 'asc')
            ->select()
            ->toArray();
        
        // 计算比较周期的日期范围
        $compareStartDate = '';
        $compareEndDate = '';
        $dateDiff = strtotime($endDate) - strtotime($startDate);
        $dayDiff = floor($dateDiff / (60 * 60 * 24)) + 1; // 当前周期天数
        
        switch ($compareType) {
            case 'day':
                // 日环比：前一天同时段
                $compareStartDate = date('Y-m-d', strtotime($startDate) - 86400);
                $compareEndDate = date('Y-m-d', strtotime($endDate) - 86400);
                break;
            case 'week':
                // 周环比：前一周同时段
                $compareStartDate = date('Y-m-d', strtotime($startDate) - 7 * 86400);
                $compareEndDate = date('Y-m-d', strtotime($endDate) - 7 * 86400);
                break;
            case 'month':
                // 月环比：前一月同时段
                $compareStartDate = date('Y-m-d', strtotime($startDate . ' -1 month'));
                $compareEndDate = date('Y-m-d', strtotime($endDate . ' -1 month'));
                break;
        }
        
        // 查询比较周期数据
        $compareWhere = [
            ['admin_id', '=', $this->adminInfo['admin_id']],
            ['call_date', '>=', $compareStartDate],
            ['call_date', '<=', $compareEndDate],
        ];
        
        $compareData = Db::name('call_statistics')
            ->where($compareWhere)
            ->order('call_date', 'asc')
            ->select()
            ->toArray();
        
        // 计算当前周期汇总数据
        $currentSummary = [
            'total_call_count' => 0,
            'total_call_duration' => 0,
            'total_call_connected' => 0,
            'avg_connect_rate' => 0,
            'avg_duration' => 0,
        ];
        
        foreach ($currentData as $item) {
            $currentSummary['total_call_count'] += $item['call_count'];
            $currentSummary['total_call_duration'] += $item['call_duration'];
            $currentSummary['total_call_connected'] += $item['call_connected'];
        }
        
        $currentSummary['avg_connect_rate'] = $currentSummary['total_call_count'] > 0 ? 
            round($currentSummary['total_call_connected'] / $currentSummary['total_call_count'] * 100, 2) : 0;
        $currentSummary['avg_duration'] = $currentSummary['total_call_connected'] > 0 ? 
            round($currentSummary['total_call_duration'] / $currentSummary['total_call_connected'], 2) : 0;
        
        // 计算比较周期汇总数据
        $compareSummary = [
            'total_call_count' => 0,
            'total_call_duration' => 0,
            'total_call_connected' => 0,
            'avg_connect_rate' => 0,
            'avg_duration' => 0,
        ];
        
        foreach ($compareData as $item) {
            $compareSummary['total_call_count'] += $item['call_count'];
            $compareSummary['total_call_duration'] += $item['call_duration'];
            $compareSummary['total_call_connected'] += $item['call_connected'];
        }
        
        $compareSummary['avg_connect_rate'] = $compareSummary['total_call_count'] > 0 ? 
            round($compareSummary['total_call_connected'] / $compareSummary['total_call_count'] * 100, 2) : 0;
        $compareSummary['avg_duration'] = $compareSummary['total_call_connected'] > 0 ? 
            round($compareSummary['total_call_duration'] / $compareSummary['total_call_connected'], 2) : 0;
        
        // 计算增长率
        $growth = [
            'call_count_growth' => $compareSummary['total_call_count'] > 0 ? 
                round(($currentSummary['total_call_count'] - $compareSummary['total_call_count']) / $compareSummary['total_call_count'] * 100, 2) : 0,
            'call_duration_growth' => $compareSummary['total_call_duration'] > 0 ? 
                round(($currentSummary['total_call_duration'] - $compareSummary['total_call_duration']) / $compareSummary['total_call_duration'] * 100, 2) : 0,
            'call_connected_growth' => $compareSummary['total_call_connected'] > 0 ? 
                round(($currentSummary['total_call_connected'] - $compareSummary['total_call_connected']) / $compareSummary['total_call_connected'] * 100, 2) : 0,
            'connect_rate_growth' => $compareSummary['avg_connect_rate'] > 0 ? 
                round(($currentSummary['avg_connect_rate'] - $compareSummary['avg_connect_rate']) / $compareSummary['avg_connect_rate'] * 100, 2) : 0,
            'avg_duration_growth' => $compareSummary['avg_duration'] > 0 ? 
                round(($currentSummary['avg_duration'] - $compareSummary['avg_duration']) / $compareSummary['avg_duration'] * 100, 2) : 0,
        ];
        
        // 准备返回数据
        $data = [
            'current_period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'summary' => $currentSummary,
            ],
            'compare_period' => [
                'start_date' => $compareStartDate,
                'end_date' => $compareEndDate,
                'summary' => $compareSummary,
            ],
            'growth' => $growth,
            'compare_type' => $compareType,
        ];
        
        $this->success('获取成功', $data);
    }
}