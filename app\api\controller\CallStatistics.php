<?php

namespace app\api\controller;

use app\api\model\CallStatistics as CallStatisticsModel;

/**
 * 通话统计控制器
 */
class CallStatistics extends Common
{
    protected $model;
    
    public function initialize()
    {
        parent::initialize();
        $this->model = new CallStatisticsModel();
    }
    
    /**
     * 上传通话统计数据
     * POST /api/call/statistics
     */
    public function upload()
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $params = $this->request->post();
        $this->validateRequired($params, ['stat_date', 'call_count', 'avg_duration', 'total_duration', 'connect_rate']);
        
        // 验证数据格式
        $statDate = $params['stat_date'];
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $statDate)) {
            $this->jsonError('统计日期格式错误，应为YYYY-MM-DD');
        }
        
        $callCount = (int)$params['call_count'];
        $avgDuration = (int)$params['avg_duration'];
        $totalDuration = (int)$params['total_duration'];
        $connectRate = (float)$params['connect_rate'];
        
        // 数据验证
        if ($callCount < 0) {
            $this->jsonError('通话量不能为负数');
        }
        
        if ($avgDuration < 0) {
            $this->jsonError('平均时长不能为负数');
        }
        
        if ($totalDuration < 0) {
            $this->jsonError('总通话时长不能为负数');
        }
        
        if ($connectRate < 0 || $connectRate > 100) {
            $this->jsonError('接通率应在0-100之间');
        }
        
        // 检查日期不能是未来日期
        if (strtotime($statDate) > strtotime(date('Y-m-d'))) {
            $this->jsonError('不能上传未来日期的统计数据');
        }
        
        // 准备数据
        $data = [
            'admin_id' => $this->admin['admin_id'],
            'username' => $this->admin['username'],
            'stat_date' => $statDate,
            'call_count' => $callCount,
            'avg_duration' => $avgDuration,
            'total_duration' => $totalDuration,
            'connect_rate' => $connectRate
        ];
        
        try {
            $result = $this->model->saveOrUpdateStats($data);
            
            if ($result) {
                $this->logApiAccess('upload_call_statistics', $data);
                $this->jsonSuccess('统计数据上传成功');
            } else {
                $this->jsonError('统计数据上传失败');
            }
        } catch (\Exception $e) {
            $this->jsonError('上传失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取通话统计数据
     * GET /api/call/statistics
     */
    public function index()
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        $days = (int)$this->request->param('days', 7); // 默认查询最近7天
        
        $adminId = $this->admin['admin_id'];
        
        if ($startDate && $endDate) {
            // 按日期范围查询
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
                $this->jsonError('日期格式错误，应为YYYY-MM-DD');
            }
            
            if (strtotime($startDate) > strtotime($endDate)) {
                $this->jsonError('开始日期不能大于结束日期');
            }
            
            $list = $this->model->getStatsByDateRange($adminId, $startDate, $endDate);
            $summary = $this->model->getStatsSummary($adminId, $startDate, $endDate);
        } else {
            // 查询最近N天
            $days = min(30, max(1, $days)); // 限制在1-30天之间
            $list = $this->model->getRecentStats($adminId, $days);
            
            $endDate = date('Y-m-d');
            $startDate = date('Y-m-d', strtotime("-{$days} days"));
            $summary = $this->model->getStatsSummary($adminId, $startDate, $endDate);
        }
        
        $data = [
            'list' => $list,
            'summary' => $summary,
            'query_params' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'days' => $days
            ]
        ];
        
        $this->jsonSuccess('获取成功', $data);
    }
    
    /**
     * 获取统计汇总
     * GET /api/call/statistics/summary
     */
    public function summary()
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        $period = $this->request->param('period', 'week'); // week, month, year
        $adminId = $this->admin['admin_id'];
        
        switch ($period) {
            case 'week':
                $startDate = date('Y-m-d', strtotime('-7 days'));
                $endDate = date('Y-m-d');
                break;
            case 'month':
                $startDate = date('Y-m-d', strtotime('-30 days'));
                $endDate = date('Y-m-d');
                break;
            case 'year':
                $startDate = date('Y-m-d', strtotime('-365 days'));
                $endDate = date('Y-m-d');
                break;
            default:
                $this->jsonError('无效的统计周期');
        }
        
        $summary = $this->model->getStatsSummary($adminId, $startDate, $endDate);
        $summary['period'] = $period;
        $summary['date_range'] = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        $this->jsonSuccess('获取成功', $summary);
    }
    
    /**
     * 删除统计数据
     * DELETE /api/call/statistics/{date}
     */
    public function delete($date)
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            $this->jsonError('日期格式错误，应为YYYY-MM-DD');
        }
        
        $adminId = $this->admin['admin_id'];
        
        $record = $this->model
            ->where('admin_id', $adminId)
            ->where('stat_date', $date)
            ->find();
            
        if (!$record) {
            $this->jsonError('统计记录不存在');
        }
        
        try {
            $result = $record->delete();
            
            if ($result) {
                $this->logApiAccess('delete_call_statistics', ['date' => $date]);
                $this->jsonSuccess('删除成功');
            } else {
                $this->jsonError('删除失败');
            }
        } catch (\Exception $e) {
            $this->jsonError('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 批量上传统计数据
     * POST /api/call/statistics/batch
     */
    public function batchUpload()
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $params = $this->request->post();
        $this->validateRequired($params, ['data']);
        
        $dataList = $params['data'];
        if (!is_array($dataList) || empty($dataList)) {
            $this->jsonError('数据格式错误');
        }
        
        if (count($dataList) > 31) {
            $this->jsonError('单次最多上传31条记录');
        }
        
        $successCount = 0;
        $failedList = [];
        
        foreach ($dataList as $index => $item) {
            try {
                // 验证必需字段
                if (!isset($item['stat_date']) || !isset($item['call_count']) || 
                    !isset($item['avg_duration']) || !isset($item['total_duration']) || 
                    !isset($item['connect_rate'])) {
                    $failedList[] = ['index' => $index, 'error' => '缺少必需字段'];
                    continue;
                }
                
                $data = [
                    'admin_id' => $this->admin['admin_id'],
                    'username' => $this->admin['username'],
                    'stat_date' => $item['stat_date'],
                    'call_count' => (int)$item['call_count'],
                    'avg_duration' => (int)$item['avg_duration'],
                    'total_duration' => (int)$item['total_duration'],
                    'connect_rate' => (float)$item['connect_rate']
                ];
                
                $result = $this->model->saveOrUpdateStats($data);
                if ($result) {
                    $successCount++;
                } else {
                    $failedList[] = ['index' => $index, 'error' => '保存失败'];
                }
            } catch (\Exception $e) {
                $failedList[] = ['index' => $index, 'error' => $e->getMessage()];
            }
        }
        
        $data = [
            'total' => count($dataList),
            'success_count' => $successCount,
            'failed_count' => count($failedList),
            'failed_list' => $failedList
        ];
        
        if ($successCount > 0) {
            $this->logApiAccess('batch_upload_call_statistics', $data);
        }
        
        $this->jsonSuccess('批量上传完成', $data);
    }
}
