<?php
namespace app\admin\controller\crm;

use app\common\controller\AdminController;
use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use think\facade\Db;
use think\facade\View;


class Seas  extends AdminController{
    //领取客户
    public function robClient(){
        $ids = $this->request->param('id');
//      开启事务,防止2人同时抢造成混乱
        if(empty($ids)){
            $this->error('请选择客户');
        }

        $res=$this->getGrabCount();
        if(!$res['code']){
            $this->error($res['msg']);

        }

        if((isset($res['count']) && $res['count']<1) || (isset($res['count']) && is_array($ids) && $res['count']<count($ids))){

            $this->error(fy("You can choose to claim up to %s customers",[$res['count']]));

        }
        $allowCustomersNum=(new \app\admin\model\CrmCustomer())->allowCustomersNum($this->admin);
        if($allowCustomersNum['max_customers_num']>0){
//                大于零说明要验证
            $yx_c=$allowCustomersNum['yx_c'];
            if($yx_c<=0){

                $this->error(fy("The maximum number of customers you can currently have is full and cannot be claimed"));

            }
            if(is_array($ids) && $yx_c<count($ids)){
                $this->error(fy("You can currently choose to claim up to %s customers",[$yx_c]));
            }
        }


        Db::startTrans();
        try {
            //然后查询的时候加锁,
            //抢客户之前，先去判断是否可抢
            $ids = Db::name('crm_customer')->where(['id' => $ids])->where(['status'=> 2])->lock(true)->column('id');
            if ($ids){
                $data['to_kh_time'] = time();
                $data['status'] = 1;//0-线索，1客户，2公海
                $data['pr_user'] = $this->admin['username'];
                $data['head_admin_id'] = $this->admin['admin_id'];
//                返回的是影响的条数
                $result = Db::name('crm_customer')->where(['id'=>$ids])->update($data);
                if ($result){
                    if(is_array($ids)){
                        $ids=implode(',',$ids);
                    }
                    Db::name('crm_grab')->insert(['admin_id'=>$this->admin['admin_id'],'customer_ids'=>$ids,'createtime'=>time(),'nums'=>$result]);
                    Db::commit();

                }else{
                    throw new \Exception(fy("Failed to claim"),0);
                }
            }else{
                throw new \Exception(fy("Sorry, the client has been snatched away"), 0);

            }

        }catch (\Throwable $t){
            Db::rollback();
            $this->error($t->getMessage());
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage());

        }
        $this->success(fy("Claim successful"));


    }

    public function export()
    {
        @ini_set("memory_limit", '-1');
        @ini_set('max_execution_time', '0');

        list($page, $limit, $where, $sort) = $this->buildTableParames();

        $where[]=['status','=',2];

        // ...（保留原有的 $where 条件设置逻辑，这部分代码未改动）

        $str_fields = '`id`';
        $fields = Db::query('SELECT `field`, `name`, `xsname`, `width`, `rule`, `formtype`, `option` FROM `' . getDataBaseConfig('prefix') . 'system_field` WHERE (`export`=1 OR `show`=1) AND `table`="crm_customer" ORDER BY `sort` ASC, id ASC');

        // 初始化 Spout writer
        $writer = WriterEntityFactory::createXLSXWriter();

        $tempPath=$this->app->getRootPath().'public'.DIRECTORY_SEPARATOR.'temp'.DIRECTORY_SEPARATOR;
        if(!is_dir($tempPath)){
            mkdir($tempPath,755,true);
        }
        deleteFilesOlderThanDays($tempPath, 1);
        $tempFile =  date('YmdHis') .'uid'.$this->admin['admin_id'].'_公海.xlsx';
        $writer->openToFile($tempPath.$tempFile);


        $str_fields='`id`';

        foreach ($fields as $k => $v) {
            $name=$v['xsname']?$v['xsname']:$v['name'];
            $fields[$k]['xsname']=$name;

            if($v['field']!='id'){
                $str_fields=$str_fields.',`'.$v['field'].'`';
            }
        }
        // 添加表头
        $headerRow = WriterEntityFactory::createRowFromArray(array_column($fields, 'xsname'));
        $writer->addRow($headerRow);

        // 查询数据并写入文件
        $cursor = $this->model->field(trim($str_fields, ','))->where($where)->order($sort)->cursor();


        foreach ($cursor as $item) {
            $rowData = [];
            foreach ($fields as $field) {
                $value = $item[$field['field']];
                $value = real_field_val($field, $value);
                $rowData[] = $value;
            }
            $dataRow = WriterEntityFactory::createRowFromArray($rowData);
            $writer->addRow($dataRow);
        }

        // 关闭 writer 并准备下载
        $writer->close();
        $this->redirect(__MY_PUBLIC__.'/temp/'.$tempFile);

    }





}
