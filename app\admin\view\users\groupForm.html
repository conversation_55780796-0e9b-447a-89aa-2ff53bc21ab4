{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit" ng-app="hd" ng-controller="ctrl">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{$title}</legend>
    </fieldset>
    <form class="layui-form layui-form-pane">
        <div class="layui-form-item">
            <label class="layui-form-label">会员组名称</label>
            <div class="layui-input-block">
                <input type="text" name="level_name" ng-model="field.level_name" lay-verify="required" placeholder="输入会员组名称" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">积分条件</label>
                <div class="layui-input-inline" style="width: 100px;">
                    <input type="text" name="bomlimit" ng-model="field.bomlimit" placeholder="输入下限积分" class="layui-input">
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline" style="width: 100px;">
                    <input type="text" name="toplimit" ng-model="field.toplimit" placeholder="输入上限积分" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('order')}</label>
            <div class="layui-input-block">
                <input type="text" name="sort" ng-model="field.sort" placeholder="从小到大{:fy('Sort')}" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">
                从小到大{:fy('Sort')}
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">{:lang('submit')}</button>
                <a href="{:url('userGroup')}" class="layui-btn layui-btn-primary">{:lang('back')}</a>
            </div>
        </div>
    </form>
</div>
{include file="common/foot"/}
<script src="__MY_PUBLIC__/static/common/js/angular.min.js"></script>
<script>
    var m = angular.module('hd',[]);
    m.controller('ctrl',['$scope',function($scope) {
        $scope.field = '{$info|raw}'!='null'?{$info|raw}:{level_id:'',level_name:'',bomlimit:'',toplimit:'',sort:50};
        layui.use(['form', 'layer'], function () {
            var form = layui.form, $ = layui.jquery;
            form.on('submit(submit)', function (data) {
                // 提交到方法 默认为本身
                var loading = layer.load(1, {shade: [0.1, '#fff']});
                data.field.level_id = $scope.field.level_id;
                $.post("", data.field, function (res) {
                    layer.close(loading);
                    if (res.code > 0) {
                        layer.msg(res.msg, {time: 2800, icon: 1}, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {time: 2800, icon: 2});
                    }
                });
            })
        });
    }]);
</script>