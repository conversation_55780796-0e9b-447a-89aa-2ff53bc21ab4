<?php

namespace app\admin\controller\crm;

use app\common\controller\AdminController;

use think\App;
use think\facade\Db;
use think\exception\ValidateException;
use think\exception;
use think\facade\Request;
use think\facade\View;
use Box\Spout\Reader\Common\Creator\ReaderEntityFactory;
use Box\Spout\Common\Type;
use Box\Spout\Writer\WriterFactory;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\Style\Color;
use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Common\Entity\Row;



/**
 * @ControllerAnnotation(title="crm_customer")
 */
class Customer extends AdminController
{
    public $sort_by = 'id';
    public $sort_order = 'DESC';

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowModifyFields = [
        'status',
        'sort',
        'remark',
        'is_delete',
        'is_auth',
        'title',
        'kh_rank',
    ];

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->model = new \app\admin\model\CrmCustomer();

    }

    /**
     * @NodeAnotation(title="列表")
     */
    public function index()
    {

        $fields=cache('crm_customer_fields');
        if(!$fields){
            $prefix=getDataBaseConfig('prefix');
            $fields=Db::query("SELECT  `field`, `jscol`,`show` FROM `{$prefix}system_field` WHERE `table`='crm_customer' AND `show`=1 AND `jscol` is not null order BY `sort` ASC,id ASC");
            $field_str=$jscol_str='';
            foreach ($fields as $key=>$value){
                $field_str.=$value['field'].',';
                if($value['show']==1){
                    $jscol_str.=$value['jscol'].',';
                }
            }
            $fields=['field_str'=>trim($field_str,','),'jscol_str'=>trim($jscol_str,',')];
            cache('crm_customer_fields',$fields);
        }


        if ($this->request->isAjax()) {
            if (input('selectFields')) {
                return $this->selectList();
            }
            list($page, $limit, $where,$sort) = $this->buildTableParames();
            $scope=$this->request->get('scope', 1,'intval');

            if($scope==2){
//                    展示其他的  不包括自己
                $adminName=(new \app\admin\model\Admin())->getViewAdminName($this->admin);
                if(empty($adminName)){
                    return json([
                        'code'  => 0,
                        'msg'   => '',
                        'count' => 0,
                        'data'  => [],
                    ]);
                }
                if($adminName!=='ALL'){
                    $where[] = ['pr_user', 'in',$adminName];
                }elseif($adminName=='ALL'){
//                    展示其他的  不包括自己需要做排除
                    $where[] = ['pr_user', '<>',$this->admin['username']];
                }

            }elseif($scope==3){
//                    展示全部 包括自己
                $adminName=(new \app\admin\model\Admin())->getViewAdminName($this->admin,true);
                if(empty($adminName)){
                    return json([
                        'code'  => 0,
                        'msg'   => '',
                        'count' => 0,
                        'data'  => [],
                    ]);
                }
                if($adminName!=='ALL'){
                    $where[] = ['pr_user', 'in',$adminName];
                }
            }elseif($scope==10){
// 待跟进
                $where[] = ['next_time', '>', 0];
                $where[] = ['next_time', '<', strtotime('tomorrow')];
// 待跟进限制展示自己的
                $where[] = ['pr_user', '=', $this->admin['username']];

            }elseif($scope==11){
// 今天已跟进
                $where[] = ['last_up_time', '>=', strtotime('today')];
                $where[] = ['last_up_time', '<', strtotime('tomorrow')];
// 待跟进限制展示自己的
                $where[] = ['pr_user', '=', $this->admin['username']];

            }elseif($scope==12){
// 从未跟进
                $where[] = ['last_up_time', '=', 0];
// 限制展示自己的
                $where[] = ['pr_user', '=', $this->admin['username']];

            }elseif($scope==20){

                //            展示自己分享给他人的
                $where[] = ['pr_user', '=', $this->admin['username']];
                $where[] = ['share_admin_ids', '<>', ''];

            }elseif($scope==21){
                //                    展示分享给我的
                $where[]=['','exp',\think\facade\Db::raw("FIND_IN_SET('{$this->admin['admin_id']}',share_admin_ids)")];
            }else{
//                   限制展示自己的
                $where[] = ['pr_user', '=', $this->admin['username']];
            }
            $where[]=['status','=',1];
            if(!empty($this->system['chjkhdlzhsh'])){
                $where[]=['issuccess','=',0];
            }
            $count = $this->model
                ->where($where)
                ->count();
            $list=[];
            if($count){
                $field_str=empty($fields['field_str'])?'*':$fields['field_str'];
                if (empty($fields['field_str'])){
                    $field_str='*';
                }else{
                    if(!in_array('id',explode(',',$fields['field_str']))){
                        $field_str='id,'.$field_str;
                    }
                }

                $list = $this->model->field($field_str)
                    ->where($where)
                    ->page($page, $limit)
                    ->order($sort)
                    ->select()->toArray();


                if ($this->admin['isphone'] == 0 && $list) {
                    foreach ($list as $key => $value) {
                        $value['phone'] = mb_substr($value['phone'], 0, 3).'****'. mb_substr($value['phone'], 7, 11);
                        $list[$key] = $value;
                    }
                }
            }

            $data = [
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $list,
            ];
            return json($data);
        }


        $jscol_str= $fields['jscol_str'];




        $jscol_str=str_replace(['"已成交"','"未成交"'],['"'.fy('已成交').'"','"'.fy('未成交').'"'],$jscol_str);
//
        $this->app->view->engine()->layout(false);
        $jscol_str=$this->display($jscol_str);

        $this->app->view->engine()->layout($this->layout);
        $jscol_str=str_replace(['":"{"','"}"'],['":{"','"}'],$jscol_str);
        $this->assignconfig('cols_fields',json_decode('['.$jscol_str.']',true));
//        $this->assignconfig('parameter',['scope'=>$this->request->get('scope',1,'intval')]);
        return $this->fetch();
    }
    /**
     * @NodeAnotation(title="公海")
     */
    public function seas()
    {
        if ($this->request->isAjax()) {
            $this->sort_by = 'to_gh_time';
            $this->sort_order = 'ASC';
            if (input('selectFields')) {
                return $this->selectList();
            }
            list($page, $limit, $where,$sort) = $this->buildTableParames();
            $where[]=['status','=',2];
            $count = $this->model
                ->where($where)
                ->count();
            $list = $this->model
                ->where($where)
                ->page($page, $limit)
                ->order($sort)
                ->select();
            if ($this->admin['group_id'] != 1) {
                foreach ($list as $key => $value) {
                    $value['phone'] = mb_substr($value['phone'], 0, 3).'****'. mb_substr($value['phone'], 7, 11);
                    $list[$key] = $value;
                }
            }
            $data = [
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $list,
            ];
            return json($data);
        }


        $this->model->autoRecycle($this->system);
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `name`,`jscol` FROM `'.$prefix.'system_field` WHERE (`show`=1 AND `table`="crm_customer" AND `jscol` is not null) OR `field`="to_gh_time" OR `field`="pr_user_bef" order BY `sort` ASC,id ASC');
        $fields_str='';
        foreach ($fields as $v){

            $fields_str.=$v['jscol'].',';
        }
        $fields_str=str_replace(['"已成交"','"未成交"'],['"'.fy('已成交').'"','"'.fy('未成交').'"'],$fields_str);
        $this->app->view->engine()->layout(false);
        $fields_str=$this->display(trim($fields_str,','));
        $this->app->view->engine()->layout($this->layout);
        $fields_str=str_replace(['":"{"','"}"'],['":{"','"}'],$fields_str);
        $this->assignconfig('cols_fields',json_decode('['.$fields_str.']',true));
        $this->assignconfig('parameter',['scope'=>$this->request->get('scope',1,'intval')]);
        View::assign('grabCountMsg',$this->getGrabCount()['msg']);
        return $this->fetch();
    }

    public function reduplicate(){
        if ($this->request->isAjax()) {
            if (input('selectFields')) {
                return $this->selectList();
            }
            list($page, $limit, $where) = $this->buildTableParames();
            $list = $this->model->field('`id`,`name`,`phone`,`contact`,`at_user`,`pr_user`,`last_up_time`,`issuccess`,`create_time`,`update_time`')
                ->where($where)
                ->limit(10) //查重复最多显示10条
                ->order($this->sort)
                ->select();


            foreach ($list as $key => $value) {
                $value['phone'] = mb_substr($value['phone'], 0, 3).'****'. mb_substr($value['phone'], 7, 11);
                $list[$key] = $value;
            }
            $data = [
                'code'  => 0,
                'msg'   => '',
                'count' => count($list),
                'data'  => $list,
            ];
            return json($data);
        }
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query("SELECT `name`,`jscol` FROM `{$prefix}system_field` WHERE `field` IN ('name','phone','contact','at_user','pr_user','last_up_time','issuccess','create_time','update_time') order BY `sort` ASC,id ASC");
        $fields_str='';
        foreach ($fields as $v){
            $fields_str.=$v['jscol'].',';
        }

        $fields_str=str_replace(['"已成交"','"未成交"'],['"'.fy('已成交').'"','"'.fy('未成交').'"'],$fields_str);
//
        $this->app->view->engine()->layout(false);
        $fields_str=$this->display(trim($fields_str,','));

        $this->app->view->engine()->layout($this->layout);
//        $fields_str=str_replace(['":"{:',')}"'],['":{:',')}'],$fields_str);
        $fields_str=str_replace(['":"{"','"}"'],['":{"','"}'],$fields_str);

//        var_dump(json_decode('['.$fields_str.']',true));
        $this->assignconfig('cols_fields',json_decode('['.$fields_str.']',true));
        return $this->fetch();
    }
    /**
     * @NodeAnotation(title="成交客户")
     */
    public function issuccess()
    {
        if ($this->request->isAjax()) {
            $this->sort_by = 'success_time';
            $this->sort_order = 'DESC';
            if (input('selectFields')) {
                return $this->selectList();
            }
            list($page, $limit, $where,$sort) = $this->buildTableParames();
            $where[]=['issuccess','=',1];
            if($this->admin['group_id']>1){
                $where[] = ['pr_user', '=', $this->admin['username']];
            }

            $count = $this->model
                ->where($where)
                ->count();
            $list = $this->model
                ->where($where)
                ->page($page, $limit)
                ->order($sort)
                ->select();
            if ($this->admin['isphone'] == 0) {
                foreach ($list as $key => $value) {
                    $value['phone'] = mb_substr($value['phone'], 0, 3).'****'. mb_substr($value['phone'], 7, 11);
                    $list[$key] = $value;
                }
            }
            $data = [
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $list,
            ];
            return json($data);
        }
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `name`,`jscol` FROM `'.$prefix.'system_field` WHERE `show`=1 AND `table`="crm_customer" AND `jscol` is not null order BY `sort` ASC,id ASC');
        $fields_str='';
        foreach ($fields as $v){

            $fields_str.=$v['jscol'].',';
        }
        $fields_str=str_replace(['"已成交"','"未成交"'],['"'.fy('已成交').'"','"'.fy('未成交').'"'],$fields_str);


        $this->app->view->engine()->layout(false);
        $fields_str=$this->display(trim($fields_str,','));
        $this->app->view->engine()->layout($this->layout);
        $fields_str=str_replace(['":"{"','"}"'],['":{"','"}'],$fields_str);
        $this->assignconfig('cols_fields',json_decode('['.$fields_str.']',true));
        $this->assignconfig('parameter',['scope'=>$this->request->get('scope',1,'intval')]);
        return $this->fetch();
    }


    /**
     * @NodeAnotation(title="添加")
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $allowCustomersNum=$this->model->allowCustomersNum($this->admin);
            if($allowCustomersNum['max_customers_num']>0){
//                对于零说明要验证
                if($allowCustomersNum['yx_c']<=0){
                    $this->error(fy("The maximum number of customers you have is full and you can't add client information"));
                }
            }
            $post = $this->request->post();
            $post=$this->param_to_str($post);
            $this->verifyFields($post);
            try {
                $post=post_convert('crm_customer',$post);
                $post['at_user']=$this->admin['username'];
                $post['pr_user']=$this->admin['username'];
                $post['head_admin_id']=$this->admin['admin_id'];
                $post['create_time']= $post['update_time']=$post['to_kh_time']=time();
                $save = $this->model->save($post);
            } catch (\Exception $e) {
                $msg=$e->getMessage();
                if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                    $msg = "包含【{$matches[1]}】的记录已存在";
                }elseif (preg_match("/Data too long for column '(.+)' at row/is", $msg, $matches)) {
                    $msg = "字段【{$matches[1]}】长度不足";
                }
                $this->error(fy('Save failed').':'.$msg);
            }
            $save ? $this->success(fy('Save successfully')) : $this->error(fy('Save failed'));
        }
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `name`,`addinput` FROM `'.$prefix.'system_field` WHERE `edit`=1 AND `table`="crm_customer" AND `addinput` is not null order BY `sort` ASC,id ASC');
        $fields_str='';
        foreach ($fields as $v){
            $fields_str.=trim($v['addinput']);
        }

        $fields_str=str_replace(['>已成交<','>未成交<'],['>'.fy('已成交').'<','>'.fy('未成交').'<'],$fields_str);
        $this->app->view->engine()->layout(false);
        $fields_str=$this->display($fields_str,['row'=>[]]);
        $this->app->view->engine()->layout($this->layout);
        $this->assign('fields_str', $fields_str);
        return $this->fetch();
    }

    /**
     * @NodeAnotation(title="编辑")
     */
    public function edit($id)
    {
        $row = $this->model->find($id);
        empty($row) && $this->error(fy('The data does not exist'));
        $this->modifyPermissionsByName($row['pr_user']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post=$this->param_to_str($post);
            $this->verifyFields($post);
            $prefix=getDataBaseConfig('prefix');
            $fields =Db::query('SELECT `field`,`formtype`,`xsname`,`name` FROM `'.$prefix.'system_field` WHERE `edit`=1 AND `table`="crm_customer" AND `editinput` is not null ');

//            针对多选无值赋空
            $fields_name=[];
            foreach ($fields as $v){
                $name=!empty($v['xsname'])?$v['xsname']:$v['name'];
                if($v['formtype']=='checkbox' || $v['formtype']=='lcheckbox'){
                    if(!isset($post[$v['field']])){
                        $post[$v['field']]='';
                    }
                }
                $fields_name[$v['field']]=$name;


            }
            Db::startTrans();
            try {
                $post=post_convert('crm_customer',$post);
                $post['update_time']=time();
                if(strpos($post['phone'],'****')!==false){
                    unset($post['phone']);
                }
                if(!empty($this->system['customer_record_fields'])){
//                    不为空则记录变动的值
                    $record_fields=explode(',',$this->system['customer_record_fields']);
                    $fields=Db::name('system_field')->where('`edit`=1 AND `table`="crm_customer" AND `editinput` is not null')->select();
                    $record=[];
                    $record['customer_id']=$row['id'];
//                    转字符串
                    $record['customer_name']=$row['name'];
                    $record['create_admin_id']=$this->admin['admin_id'];
                    $record['create_username']=$this->admin['username'];
                    $record['create_time']=time();
                    $record['ip']=getRealIp();
                    $sql='';

                    foreach ($record_fields as $v){
                        if(isset($post[$v]) && $post[$v]!=$row[$v]){
                            $record['on']=$fields_name[$v];
                            $record['on_field']=$v;
                            $record['before_value']=$row[$v];
                            $record['after_value']=$post[$v];
                            $sql.=Db::name('customer_changes_record')->fetchSql(true)->insert($record).';';
                        }
                    }
                    if($sql){
                        \think\facade\Db::connect('mysql')->getPdo()->exec($sql);
                    }
                }
                $save = $row->save($post);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $msg=$e->getMessage();
                if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                    $msg = fy('A record containing %s already exists',[$matches[1]]);

                }elseif (preg_match("/Data too long for column '(.+)' at row/is", $msg, $matches)) {
                    $msg = fy('Fields %s Insufficient length',[$matches[1]]);
                }
                $this->error(fy('Save failed').':'.$msg);
            }
            $save ? $this->success(fy('Save successfully')) : $this->error(fy('Save failed'));
        }
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `editinput` FROM `'.$prefix.'system_field` WHERE `edit`=1 AND `table`="crm_customer" AND `editinput` is not null order BY `sort` ASC,id ASC');
        $fields_str='';
        foreach ($fields as $v){
            $fields_str.=trim($v['editinput']);
        }
        $fields_str=str_replace(['>已成交<','>未成交<'],['>'.fy('已成交').'<','>'.fy('未成交').'<'],$fields_str);
        $this->app->view->engine()->layout(false);
        if ($this->admin['isphone'] == 0) {
            $row['phone'] = mb_substr($row['phone'], 0, 3).'****'. mb_substr($row['phone'], 7, 11);
        }
        $fields_str=$this->display($fields_str,['row'=>$row]);
        $this->app->view->engine()->layout($this->layout);
        $this->assign('fields_str', $fields_str);
        $this->assign('id',$id);
        return $this->fetch();
    }

    public function delete($id)
    {

        $this->checkPostRequest();
        $pr_users = $this->model->whereIn('id', $id)->column('DISTINCT pr_user');
        $this->modifyPermissionsByName($pr_users);

        $row = $this->model->whereIn('id', $id)->select();

        $row->isEmpty() && $this->error(fy('The data does not exist'));
        try {
            $save = $row->delete();
        } catch (\Exception $e) {
            $this->error(fy('Delete failed'));
        }
        $save ? $this->success(fy('Delete succeeded')) : $this->error(fy('Delete failed'));
    }

    protected function verifyFields($post){
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `name`,`xsname`,`rule`,`msg`,`field` FROM `'.$prefix.'system_field` WHERE rule <> "" AND `edit`=1 AND `table`="crm_customer" AND `editinput` is not null order BY `sort` ASC,id ASC');
        $rule=[];
        foreach ($fields as $v){
            $msg=!empty(trim($v['xsname']))?'|'.fy($v['xsname']):'|'.fy($v['name']);
            $ruleKey=$v['field'].$msg;
            $rule[$ruleKey]=str_replace('unique','unique:crm_customer',str_replace(',','|',trim($v['rule'],',')));
        }
        if($rule){
            $this->validater($post, $rule);
        }
    }

    //客户转移，变更负责人
    public function alter_pr_user(){
        //1，获取提交的线索ID 【1,2,3,4,】
//        获取客户ID
        $ids = $this->request->param('id');
        $cus_lst=$this->model->field('id,name,pr_user')->where('id','in',$ids)->select();
        if($cus_lst->isEmpty()){
            $this->error(fy("Customer data does not exist"));
        }
//        获取 $cus_lst负责人字段作为数组
        $pr_user_arr=array_column($cus_lst->toArray(),'pr_user');
        $this->modifyPermissionsByName($pr_user_arr);


        if ($this->request->isAjax()){
            $username = $this->request->param('username','','trim');
            $type = $this->request->param('type',1,'intval');
            if(empty($username)){
                $this->error(fy("The person in charge must choose"));
            }
            $idsArr = explode(",",$ids);

            $count = 0;

            $username_arr= explode(',',$username);//转成数组
            $admin_count=count($username_arr);//管理员数量
            $i=0;//
            foreach ($idsArr as  $value){
                if ($type==2){
                    //随机分配客户
                    $username=$username_arr[mt_rand(0, $admin_count - 1)];
                }else{
                    //平均分配
                    $username=$username_arr[$i];
                    $i==($admin_count-1)?$i=0:$i++;
                }

                $max_customers_num=Db::name('admin')->alias('a')->cache('max_customers_num_'.$username)
                    ->join(config('database.connections.mysql.prefix').'auth_group ag','a.group_id = ag.id','left')
                    ->where(['a.username'=>$username])
                    ->value('ag.max_customers_num');
                if( $max_customers_num > 0){
                    //            验证业务员的最大容量

                    $cz_c=Db::name('crm_customer')->whereRaw('pr_user=:username AND status=1 AND `issuccess` =0',['username'=>$username])->count();
                    if($cz_c>=$max_customers_num){
                        $this->error($username."最大拥有的客户数量已满,限制" . $max_customers_num . "条,成功转移{$count}条!");
                    }
                }

                $data['pr_user_bef'] = Db::name('crm_customer')->where(['id'=>$value])->value('pr_user');
                $data['head_admin_id'] = Db::name('admin')->cache('admin_username_'.$username,1200)->where(['username'=>$username])->value('admin_id');
                $data['pr_user'] = $username;
                $data['to_kh_time'] = time();
                $insertAll = Db::name('crm_customer')->where(['id'=>$value])->update($data);
                if ($insertAll){
                    $count ++;
                }
            }
            if ($count > 0){
                $this->success(fy("Transfer %s customers successfully",[$count]));
            }else{
                $this->error(fy("Failed"));
            }
        }

        $this->assign('cus_lst',$cus_lst);
        View::assign('ids',$ids);

        //查询所有管理员（去除admin）
        $adminResult = Db::name('admin')->where('group_id','<>', 1)->field('admin_id,username')->select();
        View::assign('adminResult',$adminResult);

        return $this->fetch();
    }

    /**
     *批量导入客户
     */
    public function import()
    {

        if ($this->request->isPost()) {
            @ini_set("memory_limit",'-1');
            @ini_set('max_execution_time', '0');
            $params = $this->request->post();

            if (!$params['filepath']) {
                $this->error(fy("Parameter error"));
            }
            $filePath = $this->app->getRootPath().'public'.$params['filepath'];

            if (!is_file($filePath)) {
                $this->error(fy("The uploaded file was not found"));
            }

            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xlsx'])) {
                $this->error(fy("Please select EXCEL format to import"));
            }
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);
                $reader = ReaderEntityFactory::createCSVReader();
            } else {
                $reader = ReaderEntityFactory::createXLSXReader();

            }



            //加载文件
            $insert = 0;
            try {
                $reader->setShouldFormatDates(true); //false default value // true will return formatted dates

                $reader->open($filePath);
                /*if (!$PHPExcel = $reader->load($filePath)) {
                    throw new  exception(fy("Failed to get file data"));
                }*/

                $prefix=getDataBaseConfig('prefix');
                $fields=Db::query('SELECT `name`,`xsname`,`rule`,`msg`,`field` FROM `'.$prefix.'system_field` WHERE (rule <> "" AND `edit`=1 AND `table`="crm_customer" AND `editinput` is not null)  order BY `sort` ASC,id ASC');
                $rule=$arr_fields=[];
                foreach ($fields as $v){
                    $name=$v['xsname']?$v['xsname']:$v['name'];
                    $msg=!empty(trim($v['msg']))?'|'.$v['msg']:'|'.$name;
                    $ruleKey=$v['field'].$msg;
                    $rule[$ruleKey]=str_replace('unique','unique:crm_customer',str_replace(',','|',$v['rule']));
                }
//                获取字段对应类型关系
                $fields=Db::query('SELECT `field`,`formtype`,`option`,`lang` FROM `'.$prefix.'system_field` WHERE `export`=1 AND `table`="crm_customer" order BY `sort` ASC,id ASC');
                foreach ($fields as $v){
                    $arr_fields[$v['field']]=$v;
                }

                $allowCustomersNum=$this->model->allowCustomersNum($this->admin);
                if($allowCustomersNum['max_customers_num']>0){
//                大于零说明要验证
                    $yx_c=$allowCustomersNum['yx_c'];
                    if($yx_c<=0){
                        throw new  exception(fy("The maximum number of customers you can currently have is full and cannot be imported"));
                    }
                }

                $fields = [];$insert=0;$sql='';
//                获取第一列
                foreach ($reader->getSheetIterator() as $sheet) {
                    foreach ($sheet->getRowIterator() as $index => $row) {
                        $data=[];
                        if(isset($yx_c)){
                            if($yx_c<=0){
                                if($sql){
                                    \think\facade\Db::connect('mysql')->getPdo()->exec($sql);
                                    $sql='';
                                }
                                throw new  exception(fy("The maximum number of customers you can currently have is full and cannot be imported"));
                            }
                        }
                        foreach ($row->getCells() as $column => $cell)  {
                            $val=$cell->getValue();
//                        *客户名称(name)  获取字段
                            $val =trim($val );
                            if($index==1) {
                                preg_match('/\(([a-zA-Z][a-zA-Z0-9_]*)\)$/',$val,$match);
                                if(!empty($match[1])){
                                    $fields[$column] = $match[1];
                                }else{
                                    throw new  exception(fy("Please use the correct import template"));
                                }

                            }else{
                                if(empty($val))continue;
                                 // 数据表字段容量
                                // 计算备注文本的实际长度（以 UTF-8 编码为例）
                                if (mb_strlen($val, 'utf-8') > $arr_fields[$fields[$column]]['lang']) {
                                    // 如果实际长度超过了允许的最大长度，则提示错误信息
                                    throw new  exception(fy("The length of the field %s exceeds the maximum length of %s",[$fields[$column],$arr_fields[$fields[$column]]['lang']]));
                                }
                                $data[$fields[$column]]=$val;
                            }
                        }
                        if(empty(array_filter($data))){
//                        如果为空则直接跳过
                            continue;
                        }
//                    0-线索，1-客户，2-公海，3-删除
                        if($params['pr_user']){
                            $data['pr_user']=$params['pr_user'];
                            $data['head_admin_id'] = Db::name('admin')->cache('admin_username_'.$params['pr_user'],3600)->where(['username'=>$params['pr_user']])->value('admin_id');
                            $data['status']=1;
                            $data['to_kh_time']=time();
                        }else{
                            $data['pr_user']='';
                            $data['head_admin_id']=0;
                            $data['status']=2;
                            $data['to_gh_time']=time();
                        }
                        $data['at_user']=$this->admin['username'];
                        $data['update_time']=$data['create_time']=time();

                        try {
                            if($rule){
                                parent::validate($data, $rule);
                            }
                            foreach ($data as $k=>$v){
                                if(isset($arr_fields[$k]['formtype'])){
                                    switch($arr_fields[$k]['formtype']){
                                        case 'datetime':
                                        case 'date':
                                            if(!is_numeric($v)){
                                                $data[$k]=strtotime($v);
                                            }
                                            break;
                                        case 'select':
                                        case 'radio':
                                            $selectList=[];
                                            $option=explode(',',$arr_fields[$k]['option']);
                                            if($option){
                                                foreach ($option as $v1){
                                                    $vv=explode(':',$v1);
                                                    if($vv){
                                                        $selectList[trim($vv[1])]=trim($vv[0]);
                                                    }
                                                }
                                                if(isset($selectList[$v])){
                                                    $data[$k]= $selectList[$v];
                                                }
                                            }
                                    }
                                }
                            }
                            $temp = Db::name('crm_customer')->fetchSql(true)->save($data).';';
                            if ($temp) {
                                $sql=$sql.$temp;
                                $insert++;
                                if(isset($yx_c)){
                                    $yx_c--;
                                }
                                if($insert%600==0){
                                    flush();
                                    ob_flush();
//                                            每600条执行一遍
                                    \think\facade\Db::connect('mysql')->getPdo()->exec($sql);
                                    $sql='';
                                }

                            }
                        } catch (\Exception $e) {
                            if ($params['skip'] != 1) {
                                throw new  exception($e->getMessage().'，待插入的数据：'.var_export($data,true));
                            }else{
                                \think\facade\Log::write($e->getMessage(),'error');
                            }
                        }

                    }
                }
                $reader->close();

                if($sql){
                    \think\facade\Db::connect('mysql')->getPdo()->exec($sql);

                }

            } catch (\Exception $e) {

                $this->error($e->getMessage());
            } catch (\Throwable $e) {
                $this->error($e->getMessage());
            }
            if (!$insert) {
                $this->error(fy("No data was imported"));
            }
            $this->success(fy('%s imported successfully',[$insert]));
        }
        return $this->fetch();
    }


    /**
     * 获取导入模板
     * @throws PDOException
     * @throws \think\db\exception\BindParamException
     */
    public function getImportTpl()
    {
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `field`,`name`,`xsname`,`width`,`rule` FROM `'.$prefix.'system_field` WHERE `export`=1 AND `table`="crm_customer" order BY `sort` ASC,id ASC');
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $i = 0;
        foreach ($fields as $k => $v) {
            $name=$v['xsname']?$v['xsname']:$v['name'];
            if ($i >= 26) {
                $cell = chr(65 + $i / 26 - 1) . chr(65 + $i % 26);
            } else {
                $cell = chr(65 + $i);
            }
            $spreadsheet->getActiveSheet()->getStyle($cell . '1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('cdf79e');
            $required='';
            if(strpos($v['rule'],'require')!==false)$required='*';
            $sheet->getColumnDimension($cell)->setWidth($v['width'],'px');
            $sheet->setCellValue($cell . '1', $required.$name."({$v['field']})");
            $i++;
        }

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=customertpl.xlsx");
        header('Cache-Control: max-age=0');
        header('Cache-Control: cache, must-revalidate');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        try {
            $writer->save('php://output');
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
        exit();
    }

    /**
     * @NodeAnotation(title="导出")
     */
    public function exportbak()
    {
        @ini_set("memory_limit",'-1');
        @ini_set('max_execution_time', '0');

        list($page, $limit, $where,$sort) = $this->buildTableParames();
        $scope=$this->request->get('scope', 1,'intval');

        if($scope==2){
//                    展示其他的  不包括自己
            $adminName=(new \app\admin\model\Admin())->getViewAdminName($this->admin);
            if(empty($adminName)){
                return json([
                    'code'  => 0,
                    'msg'   => '',
                    'count' => 0,
                    'data'  => [],
                ]);
            }
            if($adminName!=='ALL'){
                $where[] = ['pr_user', 'in',$adminName];
            }elseif($adminName=='ALL'){
//                    展示其他的  不包括自己需要做排除
                $where[] = ['pr_user', '<>',$this->admin['username']];
            }

        }elseif($scope==3){
//                    展示全部 包括自己
            $adminName=(new \app\admin\model\Admin())->getViewAdminName($this->admin,true);
            if(empty($adminName)){
                $where[] = ['id', 'in',-1];
            }elseif($adminName!=='ALL'){
                $where[] = ['pr_user', 'in',$adminName];
            }
        }elseif($scope==10){
// 待跟进
            $where[] = ['next_time', '>', 0];
            $where[] = ['next_time', '<', strtotime('tomorrow')];
// 待跟进限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];

        }elseif($scope==11){
// 今天已跟进
            $where[] = ['last_up_time', '>=', strtotime('today')];
            $where[] = ['last_up_time', '<', strtotime('tomorrow')];
// 待跟进限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];

        }elseif($scope==12){
// 从未跟进
            $where[] = ['last_up_time', '=', 0];
// 限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];

        }elseif($scope==20){

            //            展示自己分享给他人的
            $where[] = ['pr_user', '=', $this->admin['username']];
            $where[] = ['share_admin_ids', '<>', ''];

        }elseif($scope==21){
            //                    展示分享给我的
            $where[]=['','exp',\think\facade\Db::raw("FIND_IN_SET('{$this->admin['admin_id']}',share_admin_ids)")];
        }else{
//                   限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];
        }
        $where[]=['status','=',1];
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::query('SELECT `field`,`name`,`xsname`,`width`,`rule`,`formtype`,`option` FROM `'.$prefix.'system_field` WHERE (`export`=1 OR `show`=1) AND `table`="crm_customer" order BY `sort` ASC,id ASC');
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $worksheet = $spreadsheet->getActiveSheet();
        $i = 0;
        $str_fields='`id`';
        $arr_fields=[];

        foreach ($fields as $k => $v) {
            $name=$v['xsname']?$v['xsname']:$v['name'];
            if ($i >= 26) {
                $cell = chr(65 + $i / 26 - 1) . chr(65 + $i % 26);
            } else {
                $cell = chr(65 + $i);
            }
            $worksheet->getStyle($cell . '1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('cdf79e');
            $worksheet->getColumnDimension($cell)->setWidth($v['width'],'px');
            if($v['field']!='id'){
                $str_fields=$str_fields.',`'.$v['field'].'`';
            }

            $arr_fields[$cell]=$v;
            $worksheet->setCellValue($cell . '1', $name);
            $i++;
        }
        $str_fields=trim($str_fields,',');
        $line=1;
        $cursor=$this->model->field($str_fields)
            ->where($where)
            ->order($sort)->cursor();
        $styleArray = array(
            'font' => array(
                'bold'  => false,
                'color' => array('rgb' => '000000'),
                'size'  => 12,
                'name'  => 'Microsoft Yahei'
            ));
//        循环输出
        foreach ($cursor as $key => $item) {
            $line++;
            foreach ($arr_fields as $cell => $field) {
                $value=$item[$field['field']];
                $value=real_field_val($field,$value);

                $worksheet->setCellValue($cell . $line, $value.' ');
                $worksheet->getStyle($cell . $line)->getNumberFormat()->setFormatCode('@');
                $worksheet->getCell($cell . $line)->getStyle()->applyFromArray($styleArray);

            }
        }
        ob_end_clean();
        $title = date("YmdHis");
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $title . '.xlsx"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: cache, must-revalidate');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        try {
            $writer->save('php://output');
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }

        exit();
    }





    public function export()
    {
        @ini_set("memory_limit", '-1');
        @ini_set('max_execution_time', '0');

        list($page, $limit, $where, $sort) = $this->buildTableParames();
        $scope = $this->request->get('scope', 1, 'intval');

        if($scope==2){
//                    展示其他的  不包括自己
            $adminName=(new \app\admin\model\Admin())->getViewAdminName($this->admin);
            if(empty($adminName)){
                return json([
                    'code'  => 0,
                    'msg'   => '',
                    'count' => 0,
                    'data'  => [],
                ]);
            }
            if($adminName!=='ALL'){
                $where[] = ['pr_user', 'in',$adminName];
            }elseif($adminName=='ALL'){
//                    展示其他的  不包括自己需要做排除
                $where[] = ['pr_user', '<>',$this->admin['username']];
            }

        }elseif($scope==3){
//                    展示全部 包括自己
            $adminName=(new \app\admin\model\Admin())->getViewAdminName($this->admin,true);
            if(empty($adminName)){
                $where[] = ['id', 'in',-1];
            }elseif($adminName!=='ALL'){
                $where[] = ['pr_user', 'in',$adminName];
            }
        }elseif($scope==10){
// 待跟进
            $where[] = ['next_time', '>', 0];
            $where[] = ['next_time', '<', strtotime('tomorrow')];
// 待跟进限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];

        }elseif($scope==11){
// 今天已跟进
            $where[] = ['last_up_time', '>=', strtotime('today')];
            $where[] = ['last_up_time', '<', strtotime('tomorrow')];
// 待跟进限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];

        }elseif($scope==12){
// 从未跟进
            $where[] = ['last_up_time', '=', 0];
// 限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];

        }elseif($scope==20){

            //            展示自己分享给他人的
            $where[] = ['pr_user', '=', $this->admin['username']];
            $where[] = ['share_admin_ids', '<>', ''];

        }elseif($scope==21){
            //                    展示分享给我的
            $where[]=['','exp',\think\facade\Db::raw("FIND_IN_SET('{$this->admin['admin_id']}',share_admin_ids)")];
        }else{
//                   限制展示自己的
            $where[] = ['pr_user', '=', $this->admin['username']];
        }
        $where[]=['status','=',1];

        // ...（保留原有的 $where 条件设置逻辑，这部分代码未改动）

        $str_fields = '`id`';
        $fields = Db::query('SELECT `field`, `name`, `xsname`, `width`, `rule`, `formtype`, `option` FROM `' . getDataBaseConfig('prefix') . 'system_field` WHERE (`export`=1 OR `show`=1) AND `table`="crm_customer" ORDER BY `sort` ASC, id ASC');

        // 初始化 Spout writer
        $writer = WriterEntityFactory::createXLSXWriter();

        $tempPath=$this->app->getRootPath().'public'.DIRECTORY_SEPARATOR.'temp'.DIRECTORY_SEPARATOR;
        if(!is_dir($tempPath)){
            mkdir($tempPath,755,true);
        }
        deleteFilesOlderThanDays($tempPath, 1);
        $tempFile =  date('YmdHis') .'uid'.$this->admin['admin_id'].'_export.xlsx';
        $writer->openToFile($tempPath.$tempFile);


        $str_fields='`id`';

        foreach ($fields as $k => $v) {
            $name=$v['xsname']?$v['xsname']:$v['name'];
            $fields[$k]['xsname']=$name;

            if($v['field']!='id'){
                $str_fields=$str_fields.',`'.$v['field'].'`';
            }
        }
        // 添加表头
        $headerRow = WriterEntityFactory::createRowFromArray(array_column($fields, 'xsname'));
        $writer->addRow($headerRow);

        // 查询数据并写入文件
        $cursor = $this->model->field(trim($str_fields, ','))->where($where)->order($sort)->cursor();


        foreach ($cursor as $item) {
            $rowData = [];
            foreach ($fields as $field) {
                $value = $item[$field['field']];
                $value = real_field_val($field, $value);
                $rowData[] = $value;
            }
            $dataRow = WriterEntityFactory::createRowFromArray($rowData);
            $writer->addRow($dataRow);
        }

        // 关闭 writer 并准备下载
        $writer->close();
        $this->redirect(__MY_PUBLIC__.'/temp/'.$tempFile);

    }


    //移入公海
    public function to_move_gh(){
        //1，获取提交的线索ID 【1,2,3,4,】
        $ids = Request::param('id');

        if ($this->request->isAjax()){
            $count = 0;
            foreach ($ids as $value){
                $data['pr_user_bef'] = Db::name('crm_customer')->where(['id'=>$value])->value('pr_user');
                if(empty($data['pr_user_bef'])){
                    $data['pr_user_bef']='';
                }
                $data['pr_user'] = '';
                $data['head_admin_id'] = 0;

                $data['to_gh_time'] = time();
                $data['status'] = 2;//0-线索，1客户，2公海
                $result = Db::name('crm_customer')->where(['id'=>$value])->update($data);
                if ($result){
                    $count ++;
                }
            }
            if ($count > 0){
                $this->success($count.'个客户移入公海成功！');
            }else{
                $this->error(fy('Failed'));
            }
        }
    }
//客户共享
    public function share(){
//        总针对一个id
        $ids = $this->request->param('id');

        //查询所有管理员（去除admin）
        $adminResult = Db::name('admin')->where('admin_id','<>',$this->admin['admin_id'])->field('admin_id,username')->select();
        View::assign('adminResult',$adminResult);

        if ($this->request->isAjax()){
            $share_admin_ids = $this->request->post('share_admin_ids','','trim');
            if(empty($share_admin_ids)){
                $this->error(fy("No colleagues selected to share"));
            }
            $this->model->where('id','in',$ids)->update(['share_admin_ids'=>$share_admin_ids]);
            $this->success(fy("Successfully shared"));
        }
//        只针对一个客户进行分享
        $cus_lst=$this->model->field('id,name,share_admin_ids')->where('id','in',$ids)->find();
        if(empty($cus_lst)){
            $this->error(fy("Customer data does not exist"));
        }
        View::assign('cus_lst',$cus_lst);
        View::assign('ids',$ids);

        return $this->fetch();
    }


//取消共享
    public function del_share($id)
    {
        $this->checkPostRequest();
        $row = $this->model->field('id,share_admin_ids')->whereIn('id', $id)->select();
        $row->isEmpty() && $this->error(fy('The data does not exist'));
        try {
            $save = Db::name('crm_customer')->whereIn('id', $id)->update(['share_admin_ids'=>'']);
        } catch (\Exception $e) {
            $this->error(fy("Unsharing failed"));
        }
        $save ? $this->success(fy("Cancel sharing successfully")) : $this->error(fy("Unsharing failed"));
    }

    public function success_bgshow(){
        $this->sort_by = 'success_time';
        $this->sort_order = 'DESC';
//        $this->system['customer_big_fields']
        $prefix=getDataBaseConfig('prefix');
        $fields=Db::name('system_field')->field('field,name,xsname,width,rule,formtype,option')->where('field','in',$this->system['customer_big_fields'])->where('table','=','crm_customer')->order('sort asc,id asc')->column('field,name,xsname,width,rule,formtype,option','field');
//
        list($page, $limit, $where,$sort) = $this->buildTableParames();
        $kwd=$this->request->param('kwd','','trim');
        if($kwd){
            $where[]=['name','like','%'.$kwd.'%'];
        }
//        $where[]=['status','=',1];
        $where[]=['issuccess','=',1];
        $field_str=$this->system['customer_big_fields'];
        if(!in_array('id',explode(',',$field_str))){
            $field_str='id,'.$field_str;
        }


        $cursor=$this->model->field($field_str)
            ->where($where)
            ->order($sort)->limit(1000)->cursor();
        $this->assign('list', $cursor);
        $this->assign('customer_big_fields', $this->system['customer_big_fields']);
        $this->assign('fields', $fields);
        $this->assign('isphone',$this->admin['isphone']);
        $this->assign('system',$this->system);
        $this->assign('kwd',$kwd);
        $this->app->view->engine()->layout(false);
        return $this->fetch();

    }

    /**
     * 挂断操作
     */
    public function hangup()
    {
        if ($this->request->isAjax()) {
            // 这里可以添加挂断的具体逻辑
            $this->success('挂断操作执行成功');
        }
        $this->error('非法请求');
    }

    /**
     * 拨号操作
     */
    public function dial()
    {
        if ($this->request->isAjax()) {
            // 这里可以添加拨号的具体逻辑
            $this->success('拨号操作执行成功');
        }
        $this->error('非法请求');
    }

}
