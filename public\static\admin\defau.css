@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */
 
/* common start */
body { _margin:0; _height:100%; /*IE6 BUG*/ }
.aui_outer { text-align:left; }
table.aui_border, table.aui_dialog { border:0; margin:0; border-collapse:collapse; width:auto; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_c, .aui_e, .aui_sw, .aui_s, .aui_se, .aui_header, .aui_tdIcon, .aui_footer { padding:0; }
.aui_header, .aui_buttons button { font: 12px/1.11 'Microsoft Yahei', Tahoma, Arial, Helvetica, STHeiti; _font-family:Tahoma,Arial,Helvetica,STHeiti; -o-font-family: Tahoma, Arial; }
.aui_title { overflow:hidden; text-overflow: ellipsis; }
.aui_state_noTitle .aui_title { display:none; }
.aui_close { display:block; position:absolute; text-decoration:none; outline:none; _cursor:pointer; }
.aui_close:hover { text-decoration:none; }

.aui_content { *zoom:1; *display:inline; text-align:left; border:none 0; }
.aui_content.aui_state_full { display:block; width:100%; margin:0; padding:0!important; height:100%; }
.aui_loading { /* width:96px; */ height:320px; text-align:left; text-indent:-999em; overflow:hidden; background:url(images/wxMenu/loading.gif) no-repeat center center; }
.aui_icon { vertical-align: middle; }
.aui_icon div { width:48px; height:48px; margin:10px 0 10px 10px; background-position: center center; background-repeat:no-repeat; }
.aui_buttons { padding:6px; text-align:center; white-space:nowrap; }
.aui_buttons button { margin-left:15px; padding: 6px 8px; cursor: pointer; display: inline-block; text-align: center;font-size:14px;font-weight:bold; line-height: 1; letter-spacing:2px; font-family: Tahoma, Arial/9!important; width:auto; overflow:visible; *width:1; color: #666; border: 1px solid #dddcdc; border-radius: 2px; background: #e6e6e6;min-width:80px;}
.aui_buttons button::-moz-focus-inner{ border:0; padding:0; margin:0; }
.aui_buttons button:hover { color:#000;background:#f2f2f2;}
button.aui_state_highlight { color: #FFF; border: 1px solid #d94e36; padding:6px 20px;background: #de533c;font-weight:bold;font-size:14px;text-align:center;border-radius:2px;-webkit-border-radius:2px;-moz-border-radius:2px;-o-border-radius:2px;  }
button.aui_state_highlight:hover { color:#FFF; border:1px solid #d94e36;background:#e65c45; }
/* common end */

.aui_inner { background:#FFF; }
.aui_outer, .aui_inner { /* border:1px solid rgba(0, 0, 0, .5); border:1px solid #333\9; */ }
.aui_border { box-shadow: inset 0 0 1px rgba(255, 255, 255, .9); }
.aui_nw, .aui_ne, .aui_sw, .aui_se { width:10px; height:10px; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_e, .aui_sw, .aui_s, .aui_se { background:rgba(0, 0, 0, .4); background:#000\9!important; filter:alpha(opacity=40); }
.aui_state_lock .aui_nw, .aui_state_lock .aui_n, .aui_state_lock .aui_ne, .aui_state_lock .aui_w, .aui_state_lock .aui_e, .aui_state_lock .aui_sw, .aui_state_lock .aui_s, .aui_state_lock .aui_se { background:rgba(0, 0, 0, .3); background:#000\9!important; filter:alpha(opacity=30); }
.aui_state_focus .aui_dialog { box-shadow: 0 0 3px rgba(0, 0, 0, 0.4); }
.aui_state_focus .aui_outer { box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1); }

.aui_state_drag .aui_outer, .aui_outer:active { box-shadow:none; }
.aui_titleBar { position:relative; height:100%; }
.aui_title { height:35px; line-height:35px; padding:0 28px 0 10px; font-weight:bold; color:#222;font-size:14px; font-family: Tahoma, Arial/9!important; background-color:#fff; }
.aui_state_drag .aui_title { background: linear-gradient(top, #333, #555); background: -moz-linear-gradient(top, #333, #555); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#333), to(#555)); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#333', endColorstr='#555'); box-shadow:none; }
.aui_state_drag .aui_titleBar { box-shadow:none; }
.aui_close { padding:0; top:6px; right:4px; width:21px; height:21px; line-height:21px; font-size:20px; color:#000; text-align:center; font-family: Helvetica, STHeiti; _font-family: Tahoma, '\u9ed1\u4f53', 'Book Antiqua', Palatino; }
.aui_close:hover { background:#C72015; color:#FFF; }
.aui_close:active { box-shadow: none; }
.aui_content { color:#666; }
.aui_state_focus .aui_content { color:#000; }
.aui_buttons { background-color:#F6F6F6; border-top:solid 1px #DADEE5; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_se { width:3px; height:3px; }
.aui_state_noTitle .aui_inner { border:1px solid #666; background:#FFF; }
.aui_state_noTitle .aui_outer { border:none 0; box-shadow:none; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_n, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_w, .aui_state_noTitle .aui_e, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_s, .aui_state_noTitle .aui_se { background:rgba(0, 0, 0, .05); background:#000\9!important; filter:alpha(opacity=5)!important; }
.aui_state_noTitle .aui_titleBar { bottom:0; _bottom:0; _margin-top:0; }
.aui_state_noTitle .aui_close { top:0; right:0; width:18px; height:18px; line-height:25px; text-align:center; text-indent:0; font-size:18px; text-decoration:none; color:#214FA3; background:none; filter:!important; }
.aui_state_noTitle .aui_close:hover, .aui_state_noTitle .aui_close:active { text-decoration:none; color:#900; }
.aui_state_noTitle .aui_dialog { box-shadow: none; }
.withdraw_rule{font-size:12px;color:red;padding-left:5px;display:none;}