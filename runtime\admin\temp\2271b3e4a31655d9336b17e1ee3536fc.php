<?php /*a:0:{}*/ ?>
<div class="layui-form-item">
    <label class="layui-form-label" style="width: 130px;"><?php echo fy('未跟进回收周期'); ?></label>
    <div class="layui-input-inline" style="width: auto;">
        <input type="number" style="width:60px;display: inline-block;" class="layui-input" name="kfrecycleday" value="<?php echo htmlentities((isset($row['kfrecycleday']) && ($row['kfrecycleday'] !== '')?$row['kfrecycleday']:'')); ?>"> <?php echo fy('天'); ?>

    </div>
    <div class="layui-input-inline" style="width: auto;">
        <label class="layui-form-label" style="width: 70px;"><?php echo fy('已成交客户'); ?></label>
        <div class="layui-input-block">
            <input type="radio" name="genjing_success" value="1" title="<?php echo fy('执行'); ?>" <?php if($row['genjing_success'])echo 'checked="checked"'; ?>>
            <input type="radio" name="genjing_success" value="0" title="<?php echo fy('不执行'); ?>" <?php if(!$row['genjing_success'])echo 'checked="checked"'; ?>>

        </div>
    </div>
    <div class="layui-form-mid layui-word-aux"><?php echo fy('未跟进客户是否回收至公海，填写0表示不执行回收机制，如果已成交客户选择不执行，超过周期也不会被回收'); ?></div>
</div><div class="layui-form-item">
    <label class="layui-form-label" style="width: 130px;"><?php echo fy('未成交回收周期'); ?></label>
    <div class="layui-input-inline" style="width: auto;">
        <input type="number" style="width:60px;display: inline-block;" class="layui-input" name="wchjhuishouday" value="<?php echo htmlentities((isset($row['wchjhuishouday']) && ($row['wchjhuishouday'] !== '')?$row['wchjhuishouday']:'')); ?>"><?php echo fy('天'); ?>

    </div>
    <div class="layui-form-mid layui-word-aux"><?php echo fy('未成交客户是否回收至公海，填写0表示不执行回收机制'); ?></div>
</div><div class="layui-form-item">
    <label class="layui-form-label" style="width: 130px;"><?php echo fy('最大领客户数'); ?></label>
    <div class="layui-input-block">
        <div class="layui-input-inline" style="width: 80px;margin-right: 0.5em;">
            <select name="customer_limit_condition">
                <option value="day"  <?php if($row['customer_limit_condition']=='day')echo 'selected="selected"'; ?>><?php echo fy('当天'); ?></option>
                <option value="week"  <?php if($row['customer_limit_condition']=='week')echo 'selected="selected"'; ?> ><?php echo fy('当周'); ?></option>
                <option value="month"   <?php if($row['customer_limit_condition']=='month')echo 'selected="selected"'; ?>><?php echo fy('当月'); ?></option>
            </select>
        </div>
        <div class="layui-input-inline" style="width: auto;">
            <?php echo fy('内限制领取'); ?> <input type="number" style="width:60px;display: inline-block;" class="layui-input" name="customer_limit_counts" value="<?php echo htmlentities((isset($row['customer_limit_counts']) && ($row['customer_limit_counts'] !== '')?$row['customer_limit_counts']:'0')); ?>"> <?php echo fy('次'); ?></div>
        <div class="layui-form-mid layui-word-aux"><?php echo fy('填写0表示不限制'); ?></div>
    </div>
</div><div class="layui-form-item">
    <label class="layui-form-label" style="width: 130px;"><?php echo fy('客户唯一字段'); ?></label>
    <div class="layui-input-block" style="margin-left: 160px;">
        <input type="text" name="customer_unique" data-toggle="selectPage" class="layui-input" data-source='<?php echo json_encode($fields_lst,256); ?>'  data-field="name"  data-format-item="{name}"  data-multiple="true"    data-primary-key="field"  placeholder="<?php echo fy('限制唯一字段'); ?>"   value="<?php echo htmlentities((isset($row['customer_unique']) && ($row['customer_unique'] !== '')?$row['customer_unique']:'')); ?>" >
        <div class="layui-form-mid layui-word-aux"><?php echo fy('勾选的唯一字段信息将不能重复'); ?></div>

    </div>
</div><div class="layui-form-item">
    <label class="layui-form-label" style="width: 130px;"><?php echo fy('客户记录字段'); ?></label>
    <div class="layui-input-block" style="margin-left: 160px;">
        <input type="text" name="customer_record_fields" data-toggle="selectPage" class="layui-input" data-source='<?php echo json_encode($record_fields_lst,256); ?>'  data-field="name"  data-format-item="{name}"  data-multiple="true"    data-primary-key="field"  placeholder="<?php echo fy('需要记录的操作字段'); ?>"   value="<?php echo htmlentities((isset($row['customer_record_fields']) && ($row['customer_record_fields'] !== '')?$row['customer_record_fields']:'')); ?>" >
        <div class="layui-form-mid layui-word-aux"><?php echo fy('勾选对客户信息操作时需要记录的字段'); ?></div>

    </div>
</div><div class="layui-form-item">
    <label class="layui-form-label" style="width: 130px;"><?php echo fy('大屏展示字段'); ?></label>
    <div class="layui-input-block" style="margin-left: 160px;">
        <input type="text" name="customer_big_fields" data-toggle="selectPage" class="layui-input" data-source='<?php echo json_encode($big_fields_lst,256); ?>'  data-field="name"  data-format-item="{name}"  data-multiple="true"    data-primary-key="field"  placeholder="<?php echo fy('需要记录的操作字段'); ?>"   value="<?php echo htmlentities((isset($row['customer_big_fields']) && ($row['customer_big_fields'] !== '')?$row['customer_big_fields']:'')); ?>" >
        <div class="layui-form-mid layui-word-aux"><?php echo fy('勾选大屏展示字段'); ?></div>

    </div>
</div><div class="layui-form-item"><label class="layui-form-label" style="width: 130px;"><?php echo fy('成交客户独立展示'); ?></label><div class="layui-input-block" style="margin-left: 160px;"><select  name="chjkhdlzhsh" lay-filter="chjkhdlzhsh" class="layui-select" lay-search><option value=""><?php echo fy("Please select"); ?></option><option value="0" <?php if(isset($row["chjkhdlzhsh"])&&$row["chjkhdlzhsh"]=="0"): ?>selected<?php endif; ?>>否</option><option value="1" <?php if(isset($row["chjkhdlzhsh"])&&$row["chjkhdlzhsh"]=="1"): ?>selected<?php endif; ?>>是</option></select><tip>成交客户是否独立展示，独立展示的将不再客户列表展示了</tip></div></div>