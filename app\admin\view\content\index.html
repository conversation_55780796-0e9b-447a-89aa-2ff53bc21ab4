{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{:lang('list')}</legend>
    </fieldset>
    <div class="demoTable">
        <div class="layui-inline">
            <input class="layui-input" name="key" id="key" placeholder="{:lang('Please enter')}关键字">
        </div>
        <button class="layui-btn" id="search" data-type="reload">{:lang('search')}</button>
        <a href="{:url('index',['catid'=>input('catid')])}" class="layui-btn">显示全部</a>

        <div style="clear: both;"></div>
    </div>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="order">
    <input name="{{d.id}}" data-id="{{d.id}}" class="list_order layui-input" value=" {{d.sort}}" size="10"/>
</script>
<script type="text/html" id="title">
    <span style="{{d.title_style}}">{{d.title}}</span>
    {{# if(d.thumb){ }}<img src="__MY_PUBLIC__/static/admin/images/image.gif" onmouseover="layer.tips('<img src={{d.thumb}}>',this,{tips: [1, '#fff']});" onmouseout="layer.closeAll();">{{# } }}
</script>
<script type="text/html" id="action">
    {{# if(d.is_show==1){ }}
    <a href="{{d.url}}" target="_blank" class="layui-btn layui-btn-xs layui-btn-normal">预览</a>
    {{# } }}
    <a href="{:url('edit')}?id={{d.id}}&catid={{d.catid}}" class="layui-btn layui-btn-xs">{:fy('Edit')}</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">{:fy('Delete')}</a>
</script>
<script type="text/html" id="topBtn">
    <a href="{:url('add',array('catid'=>input('catid')))}" class="layui-btn layui-btn-sm">{:lang('add')}</a>
    <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" id="delAll">批量删除</button>
    <a href="{:url('Category/index')}" class="layui-btn layui-btn-normal layui-btn-sm">返回栏目</a>
</script>
<script>
    layui.use('table', function() {
        var table = layui.table, $ = layui.jquery;
        var tableIn = table.render({
            id: 'content',
            elem: '#list',
            url: '{:url("index")}',
            where:{catid:'{:input("catid")}'},
            method: 'post',
            toolbar: '#topBtn',
            page: true,
            cols: [[
                {type: "checkbox", fixed: true},
                {field: 'id', title: '{:lang("id")}', width: 80, fixed: true},
                {field: 'title', title: '{:lang("title")}', width: 400, templet: '#title'},
                {field: 'hits',  title: '{:lang("hit")}', width: 80},
                {field: 'createtime', title: '{:lang("add")}{:lang("time")}', width: 180},
                {field: 'sort', align: 'center', title: '{:lang("order")}', width: 120, templet: '#order'},
                {width: 160, align: 'center', toolbar: '#action',title:"{:fy('Operate')}"}
            ]],
            limit: {$system['admin_pagesize']}
        });
        //搜索
        $('#search').on('click', function () {
            var key = $('#key').val();
            if ($.trim(key) === '') {
                layer.msg('{:lang("pleaseEnter")}关键字！', {icon: 0});
                return;
            }
            tableIn.reload({ page: {page: 1}, where: {key: key,catid:'{:input("catid")}'} });
        });
        $('body').on('blur','.list_order',function() {
            var id = $(this).attr('data-id');
            var sort = $(this).val();
            var loading = layer.load(1, {shade: [0.1, '#fff']});
            $.post('{:url("listorder")}',{id:id,sort:sort,catid:'{:input("catid")}'},function(res){
                layer.close(loading);
                if(res.code === 1){
                    layer.msg(res.msg, {time: 1000, icon: 1}, function () {
                        location.href = res.url;
                    });
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                }
            })
        });
        table.on('tool(list)', function(obj) {
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('您确定要删除该内容吗？', function(index){
                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                    $.post("{:url('listDel')}",{id:data.id},function(res){
                        layer.close(loading);
                        if(res.code===1){
                            layer.msg(res.msg,{time:2000,icon:1});
                            tableIn.reload({where:{catid:'{:input("catid")}'}});
                        }else{
                            layer.msg('{:fy("Operation failed")}'+'！',{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
        $('body').on('click','#delAll',function() {
            layer.confirm('确认要删除选中的内容吗？', {icon: 3}, function(index) {
                layer.close(index);
                var checkStatus = table.checkStatus('content'); //content即为参数id设定的值
                var ids = [];
                $(checkStatus.data).each(function (i, o) {
                    ids.push(o.id);
                });
                var loading = layer.load(1, {shade: [0.1, '#fff']});
                $.post("{:url('delAll')}", {ids: ids,catid:'{:input("catid")}'}, function (data) {
                    layer.close(loading);
                    if (data.code === 1) {
                        layer.msg(data.msg, {time: 1000, icon: 1});
                        tableIn.reload({where:{catid:'{:input("catid")}'}});
                    } else {
                        layer.msg(data.msg, {time: 1000, icon: 2});
                    }
                });
            });
        })
    });
</script>