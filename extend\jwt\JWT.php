<?php

namespace jwt;

/**
 * JWT认证类
 * 用于生成和验证JWT令牌
 */
class JWT
{
    /**
     * 头部
     * @var array
     */
    private static $header = [
        'alg' => 'HS256', // 加密算法
        'typ' => 'JWT'    // 类型
    ];

    /**
     * 生成JWT Token
     * @param array $payload 载荷信息
     * @param string $key 密钥
     * @param int $expire 过期时间（秒）
     * @return string
     */
    public static function getToken(array $payload, string $key, int $expire = 7200): string
    {
        $time = time();
        $payload['iat'] = $time; // 签发时间
        $payload['exp'] = $time + $expire; // 过期时间
        
        // 计算JWT
        $base64header = self::base64UrlEncode(json_encode(self::$header, JSON_UNESCAPED_UNICODE));
        $base64payload = self::base64UrlEncode(json_encode($payload, JSON_UNESCAPED_UNICODE));
        $signature = self::signature($base64header . '.' . $base64payload, $key, self::$header['alg']);
        $base64signature = self::base64UrlEncode($signature);
        
        return $base64header . '.' . $base64payload . '.' . $base64signature;
    }

    /**
     * 验证JWT Token
     * @param string $token JWT令牌
     * @param string $key 密钥
     * @return array|bool 验证通过返回payload数据，失败返回false
     */
    public static function verifyToken(string $token, string $key)
    {
        if (empty($token)) {
            return false;
        }
        
        $tokenArr = explode('.', $token);
        if (count($tokenArr) != 3) {
            return false;
        }
        
        list($base64header, $base64payload, $base64signature) = $tokenArr;
        
        // 验证签名
        $signature = self::signature($base64header . '.' . $base64payload, $key, self::$header['alg']);
        $base64signatureNew = self::base64UrlEncode($signature);
        
        if ($base64signatureNew !== $base64signature) {
            return false;
        }
        
        // 解析payload
        $payload = json_decode(self::base64UrlDecode($base64payload), true);
        
        // 验证是否过期
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }

    /**
     * 生成签名
     * @param string $input 输入
     * @param string $key 密钥
     * @param string $alg 算法
     * @return string
     */
    private static function signature(string $input, string $key, string $alg): string
    {
        $alg_config = [
            'HS256' => 'sha256'
        ];
        
        return hash_hmac($alg_config[$alg], $input, $key, true);
    }

    /**
     * base64url编码
     * @param string $data 数据
     * @return string
     */
    private static function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * base64url解码
     * @param string $data 数据
     * @return string
     */
    private static function base64UrlDecode(string $data): string
    {
        $base64 = strtr($data, '-_', '+/');
        $base64 = str_pad($base64, strlen($base64) % 4, '=', STR_PAD_RIGHT);
        return base64_decode($base64);
    }
}