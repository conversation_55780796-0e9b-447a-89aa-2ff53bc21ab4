define(["jquery","easy-admin","vue"],function(t,l,e){var r=layui.tableSelect;var i={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"crm.contract/index",add_url:"crm.contract/add",edit_url:"crm.contract/edit",delete_url:"crm.contract/delete",export_url:"crm.contract/export",modify_url:"crm.contract/modify"};var d={index:function(){l.table.render({init:i,limit:CONFIG.ADMINPAGESIZE,toolbar:["refresh",[{text:"添加",url:i.add_url,method:"open",auth:"add",class:"layui-btn layui-btn-normal layui-btn-sm",icon:"fa fa-plus ",extend:'data-full="true"'}],"delete"],cols:[[{type:"checkbox"},{field:"id",title:"id"},{field:"customer_id",title:"客户ID"},{field:"business_id",title:"商机ID"},{field:"contacts_id",title:"客户签约人（联系人ID）"},{field:"source_id",title:"来源ID"},{field:"name",title:"合同名称"},{field:"number",title:"合同编号"},{field:"order_time",title:"下单时间"},{field:"money",title:"合同金额"},{field:"total_price",title:"产品总金额"},{field:"return_money",title:"已收到款项"},{field:"discount_rate",title:"整单折扣"},{field:"check_status",title:"0待审核、1审核中、2审核通过、3审核未通过"},{field:"flow_id",title:"审核流程ID"},{field:"step_id",title:"审核步骤ID"},{field:"check_admin_id",title:"已经审批人IDs"},{field:"flow_admin_id",title:"当前需要审批的人"},{field:"start_time",title:"开始时间"},{field:"end_time",title:"结束时间"},{field:"order_admin_id",title:"公司签约人"},{field:"remark",title:fy("Remark"),templet:l.table.text},{field:"create_user_id",title:"创建人ID"},{field:"owner_user_id",title:"负责人ID"},{field:"ro_user_id",title:"只读权限"},{field:"rw_user_id",title:"读写权限"},{field:"next_time",title:fy("Next contact time")},{field:"create_time",title:fy("Creation time")},{field:"expire_handle",title:"0合同过期未处理1已续签2不再合作"},{field:"invoice_money",title:"已开票金额"},{width:250,title:fy("Operate"),templet:l.table.tool}]],done:function(e,t,i){if(i===undefined&&e.msg&&e.url){l.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}}});l.listen()},add:function(){t(document).on("click","#create_numbering",function(){l.request.post({url:l.url("crm.contract/create_numbering")},function(e){if(e.code){t('input[name="numbering"]').val(e.data.numbering)}else{l.msg.error(e.msg,function(){})}})});this.renderPro();l.listen()},edit:function(){this.renderPro();l.listen()},renderPro:function(){var i=new e({el:"#app",data:{pro_list:[],cost_sum:0,sale_sum:0},methods:{entryTime(e){if(!this.pro_list[e]["create_time"]){this.pro_list[e]["create_time"]=Date.parse(new Date)/1e3}return layui.util.toDateString(this.pro_list[e]["create_time"]*1e3,"yyyy-MM-dd HH:mm")},removePro(t,e){that=this;if(e){l.request.ajax("get",{url:l.url("contract/delproduct"),data:{product_id:e}},function(e){if(e.code){that.pro_list.splice(t,1)}else{l.msg.error(fy("Delete failed"))}})}else{that.pro_list.splice(t,1)}}},computed:{getTotal(){var t=this.pro_list;var i=0,l=0,r=0,d=0;for(let e=0;e<t.length;e++){i+=t[e].cost_price*t[e].nums;r+=t[e].sale_price*t[e].nums;l+=parseFloat(t[e].discount);d+=parseInt(t[e].nums)}if(r){real_sale_sum=r-l}else{real_sale_sum=0}return{cost_sum:i.toFixed(2),nums_sum:d,discount_sum:l.toFixed(2),sale_sum:r.toFixed(2),real_sale_sum:real_sale_sum.toFixed(2)}}}});id=t("#table-pro").data("id");if(id){l.request.ajax("get",{url:l.url("contract/product"),data:{id:id}},function(e){i.pro_list=e.data})}r.render({elem:"#select-pro",searchType:"more",checkedKey:"id",searchList:[{searchKey:"name",searchPlaceholder:"请输入产品名"}],table:{url:l.url("Product/index"),cols:[[{type:"checkbox"},{field:"id",title:"ID"},{field:"type.title",width:90,title:"产品分类",templet:function(e){return"<span>"+e.type.title+"</span>"}},{field:"name",title:"产品名称",width:200},{field:"thumb",width:90,title:"产品图片",templet:l.table.image},{field:"specification",title:"规格"},{field:"inventory",title:"产品库存",width:90,templet:function(e){if(e.inventory<=e.min_warning){return'<span class="layui-font-red">'+e.inventory+"</span>"}else if(e.inventory>=e.max_warning){return'<span class="layui-font-orange">'+e.inventory+"</span>"}}},{field:"cost_price",width:90,title:"成本价格"},{field:"sale_price",width:90,title:"出售价格"}]]},done:function(e,t){for(let e=0;e<t.data.length;e++){t.data[e].product_id=t.data[e].id;t.data[e].id=0;t.data[e].remark="";t.data[e].nums=t.data[e].nums?t.data[e].nums:1;t.data[e].discount=t.data[e].discount?t.data[e].discount:0}i.pro_list=i.pro_list.concat(t.data)},where:{status:1}})}};return d});