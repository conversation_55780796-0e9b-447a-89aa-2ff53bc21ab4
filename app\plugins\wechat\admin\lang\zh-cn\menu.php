<?php
return [
    'Support menu switching, open the access status will be synchronized to the official account, if you edit the menu, please re-open it to synchronize to the official account'=>'支持菜单切换，开启接入状态才会同步到公众号，如果编辑了菜单请重新开启即可同步到公众号',
    'Please configure the official account first'=>'请先配置公众号',
    'Please configure the official account Appid and AppSecret correctly'=>'请先正确配置公众号Appid和AppSecret',
    'Default menu'=>'默认菜单',
    'Level %s menu'=>'%s级菜单',
    'Menu editing'=>'菜单编辑中',
    'Delete the submenu'=>'删除子菜单',
    'Menu title'=>'菜单标题',
    'The number of words should not exceed 4 Chinese characters or 8 letters'=>'字数不超过4个汉字或8个字母',
    'OK to delete the menu'=>'确定删除菜单',
    'Menu events'=>'菜单事件',
    'send message'=>'发送消息',
    'Jump to a web page'=>'跳转网页',
    'Jump applet'=>'跳转小程序',
    'Scanning code'=>'扫码',
    'Wait for information'=>'等待信息',
    'geographical position'=>'地理位置',
    'Take pictures and send pictures'=>'拍照发图',
    'Photo album'=>'拍照相册',
    'Album posting pictures'=>'相册发图',
    'Message keywords'=>'消息关键字',
    'Link address'=>'链接地址',
    'Mini program'=>'小程序',
    'Page'=>'页面',
    'Please fill in the correct URL address'=>'请输入正确的url地址',
    'The menu name or menu type cannot be empty'=>'菜单名或菜单类型不能为空',
    'Please enter the correct URL address'=>'请输入正确的url地址',
    'The menu name cannot be empty'=>'菜单名字不能为空',
    'If there is a submenu, please delete the submenu first'=>'有子菜单请先删除子菜单',
    'Please select the menu you want to delete'=>'请选择要删除的菜单',
    'Subscribers clicking this submenu will jump to the Mini Program address'=>'订阅者点击该子菜单会跳到小程序地址',
    'Subscribers clicking on this submenu will jump to the following link'=>'订阅者点击该子菜单会跳到以下链接',
];
