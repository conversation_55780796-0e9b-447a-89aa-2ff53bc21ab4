<?php /*a:2:{s:57:"C:\wwwroot\127.0.0.1\app\admin\view\auth\group\index.html";i:1664811772;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<style>
    .layui-btn:not(.layui-btn-lg ):not(.layui-btn-sm):not(.layui-btn-xs) {
        height: 34px;
        line-height: 34px;
        padding: 0 8px;
    }
</style>
<link rel="stylesheet" href="/static/plugs/lay-module/treetable-lay/treetable.css" media="all">
<div class="layuimini-container">

    <div class="layuimini-main">
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="<?php echo auth('auth.group/add'); ?>"
               data-auth-edit="<?php echo auth('auth.group/edit'); ?>"
               data-auth-delete="<?php echo auth('auth.group/delete'); ?>"
               data-auth-access="<?php echo auth('Auth/groupAccess'); ?>"
               lay-filter="currentTable">
        </table>
    </div>
</div>
<script type="text/html" id="toolbar">
    <button class="layui-btn layui-btn-sm layuimini-btn-primary" data-treetable-refresh><i class="fa fa-refresh"></i> </button>
</script>
</body>
</html>