<?php

namespace app\api\traits;

use think\facade\Db;

/**
 * API CRUD操作特性
 */
trait Curd
{
    /**
     * 列表查询
     */
    public function index()
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        list($page, $limit) = $this->getPageParams();
        
        $where = $this->buildWhere();
        $order = $this->buildOrder();
        
        $total = $this->model->where($where)->count();
        $list = $this->model
            ->where($where)
            ->order($order)
            ->page($page, $limit)
            ->select();
            
        $data = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
        
        $this->jsonSuccess('获取成功', $data);
    }
    
    /**
     * 详情查询
     */
    public function read($id)
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        $row = $this->model->find($id);
        if (!$row) {
            $this->jsonError('数据不存在');
        }
        
        $this->jsonSuccess('获取成功', $row);
    }
    
    /**
     * 新增数据
     */
    public function save()
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $data = $this->request->post();
        $data = $this->filterData($data);
        
        try {
            $result = $this->model->save($data);
            if ($result) {
                $this->jsonSuccess('保存成功', ['id' => $this->model->id]);
            } else {
                $this->jsonError('保存失败');
            }
        } catch (\Exception $e) {
            $this->jsonError('保存失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新数据
     */
    public function update($id)
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $row = $this->model->find($id);
        if (!$row) {
            $this->jsonError('数据不存在');
        }
        
        $data = $this->request->post();
        $data = $this->filterData($data);
        
        try {
            $result = $row->save($data);
            if ($result !== false) {
                $this->jsonSuccess('更新成功');
            } else {
                $this->jsonError('更新失败');
            }
        } catch (\Exception $e) {
            $this->jsonError('更新失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除数据
     */
    public function delete($id)
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $row = $this->model->find($id);
        if (!$row) {
            $this->jsonError('数据不存在');
        }
        
        try {
            $result = $row->delete();
            if ($result) {
                $this->jsonSuccess('删除成功');
            } else {
                $this->jsonError('删除失败');
            }
        } catch (\Exception $e) {
            $this->jsonError('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 构建查询条件
     */
    protected function buildWhere()
    {
        $where = [];
        
        // 可以在子类中重写此方法
        return $where;
    }
    
    /**
     * 构建排序条件
     */
    protected function buildOrder()
    {
        return isset($this->sort) ? $this->sort : ['id' => 'desc'];
    }
    
    /**
     * 过滤数据
     */
    protected function filterData($data)
    {
        // 移除不允许修改的字段
        $forbiddenFields = ['id', 'create_time', 'update_time'];
        foreach ($forbiddenFields as $field) {
            unset($data[$field]);
        }
        
        return $data;
    }
}
