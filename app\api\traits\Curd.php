<?php
namespace app\api\traits;

use think\facade\Db;

/**
 * CRUD特性
 * 用于API控制器的增删改查操作
 */
trait Curd
{
    /**
     * 获取列表数据
     * @return void
     */
    public function index()
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 15);
        $where = $this->buildSearchWhere();
        
        $count = $this->model->where($where)->count();
        $list = $this->model->where($where)
            ->page($page, $limit)
            ->order($this->sort)
            ->select();
        
        $data = [
            'count' => $count,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 获取单条数据
     * @return void
     */
    public function read()
    {
        $id = $this->request->param('id/d');
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        $data = $this->model->find($id);
        if (empty($data)) {
            $this->error('数据不存在');
        }
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 添加数据
     * @return void
     */
    public function save()
    {
        $post = $this->request->post();
        
        // 数据验证
        if (method_exists($this, 'validate')) {
            $this->validate($post);
        }
        
        // 添加数据
        $result = $this->model->save($post);
        if ($result) {
            $this->success('添加成功');
        } else {
            $this->error('添加失败');
        }
    }
    
    /**
     * 更新数据
     * @return void
     */
    public function update()
    {
        $id = $this->request->param('id/d');
        $post = $this->request->post();
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        // 数据验证
        if (method_exists($this, 'validate')) {
            $this->validate($post);
        }
        
        // 更新数据
        $result = $this->model->where('id', $id)->update($post);
        if ($result) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败或数据无变化');
        }
    }
    
    /**
     * 删除数据
     * @return void
     */
    public function delete()
    {
        $id = $this->request->param('id/d');
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        // 删除数据
        $result = $this->model->destroy($id);
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 构建搜索条件
     * @return array
     */
    protected function buildSearchWhere()
    {
        $where = [];
        $params = $this->request->param();
        
        // 子类可以重写此方法，实现自定义搜索条件
        
        return $where;
    }
}