<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-tab" lay-filter="nav-tabs-index">
            <ul class="layui-tab-title">
                <li class="layui-this" data-value="1" data-field="scope">{:fy("It's mine")}</li>
                <li  data-value="2" data-field="scope">{:fy('Subordinate')}</li>
                <li  data-value="3" data-field="scope">{:fy('All')}</li>
                <li  data-value="10" data-field="scope">{:fy('To be followed')}</li>
                <li  data-value="11" data-field="scope">{:fy('Followed up today')}</li>
                <li  data-value="12" data-field="scope">{:fy('Never followed up')}</li>
            </ul>
        </div>
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="{:auth('business/add')}"
               data-auth-edit="{:auth('business/edit')}"
               data-auth-delete="{:auth('business/delete')}"
               data-auth-record_add="{:auth('business.record/add')}"
               lay-filter="currentTable">
        </table>
    </div>
</div>