<?php
namespace app\api\controller;

use think\facade\Db;
use think\facade\Validate;
use app\admin\model\AdminDevice;

/**
 * 用户认证控制器
 */
class Auth extends Common
{
    /**
     * 是否需要登录验证
     * @var bool
     */
    protected $needLogin = false;
    
    /**
     * 用户登录
     * @return void
     */
    public function login()
    {
        // 获取参数
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $deviceId = $this->request->post('device_id', '');
        
        // 参数验证
        $validate = Validate::rule([
            'username' => 'require',
            'password' => 'require',
            'device_id' => 'require',
        ])->message([
            'username.require' => '用户名不能为空',
            'password.require' => '密码不能为空',
            'device_id.require' => '设备ID不能为空',
        ]);
        
        if (!$validate->check(['username' => $username, 'password' => $password, 'device_id' => $deviceId])) {
            $this->error($validate->getError());
        }
        
        // 查询用户
        $admin = Db::name('admin')->where('username', $username)->find();
        if (empty($admin)) {
            $this->error('用户不存在');
        }
        
        // 验证密码
        if (md5(md5($password) . $admin['salt']) != $admin['pwd']) {
            $this->error('密码错误');
        }
        
        // 检查用户状态
        if ($admin['is_open'] != 1) {
            $this->error('账号已被禁用');
        }
        
        // 断开该用户所有设备连接
        AdminDevice::disconnectAllDevices($admin['admin_id']);
        
        // 设置当前设备在线
        AdminDevice::setDeviceOnline($admin['admin_id'], $deviceId);
        
        // 生成token
        $token = $this->createToken($admin, $deviceId);
        
        // 记录登录日志
        $this->recordLoginLog($admin['admin_id']);
        
        // 返回用户信息
        unset($admin['pwd'], $admin['salt']);
        $data = [
            'token' => $token,
            'admin_info' => $admin,
        ];
        
        $this->success('登录成功', $data);
    }
    
    /**
     * 退出登录
     * @return void
     */
    public function logout()
    {
        // 获取token
        $token = $this->request->header('Authorization');
        if (empty($token)) {
            $this->error('未登录状态');
        }
        
        // 验证token
        $payload = \jwt\JWT::verifyToken($token, $this->jwtKey);
        if ($payload === false) {
            $this->error('登录已过期');
        }
        
        // 断开设备连接
        if (!empty($payload['device_id'])) {
            Db::name('admin_device')
                ->where('admin_id', $payload['admin_id'])
                ->where('device_id', $payload['device_id'])
                ->update(['status' => 0]);
        }
        
        $this->success('退出成功');
    }
    
    /**
     * 获取当前登录用户信息
     * @return void
     */
    public function info()
    {
        $this->success('获取成功', $this->adminInfo);
    }
    
    /**
     * 修改密码
     * @return void
     */
    public function changePassword()
    {
        // 获取参数
        $oldPassword = $this->request->post('old_password');
        $newPassword = $this->request->post('new_password');
        $confirmPassword = $this->request->post('confirm_password');
        
        // 参数验证
        $validate = Validate::rule([
            'old_password' => 'require',
            'new_password' => 'require|min:6',
            'confirm_password' => 'require|confirm:new_password',
        ])->message([
            'old_password.require' => '原密码不能为空',
            'new_password.require' => '新密码不能为空',
            'new_password.min' => '新密码不能少于6个字符',
            'confirm_password.require' => '确认密码不能为空',
            'confirm_password.confirm' => '两次输入的密码不一致',
        ]);
        
        if (!$validate->check([
            'old_password' => $oldPassword,
            'new_password' => $newPassword,
            'confirm_password' => $confirmPassword,
        ])) {
            $this->error($validate->getError());
        }
        
        // 查询用户
        $admin = Db::name('admin')->where('admin_id', $this->adminInfo['admin_id'])->find();
        
        // 验证原密码
        if (md5(md5($oldPassword) . $admin['salt']) != $admin['pwd']) {
            $this->error('原密码错误');
        }
        
        // 生成新密码
        $salt = random_string(6);
        $pwd = md5(md5($newPassword) . $salt);
        
        // 更新密码
        $result = Db::name('admin')->where('admin_id', $this->adminInfo['admin_id'])->update([
            'pwd' => $pwd,
            'salt' => $salt,
        ]);
        
        if ($result) {
            // 断开所有设备连接
            AdminDevice::disconnectAllDevices($this->adminInfo['admin_id']);
            
            $this->success('密码修改成功，请重新登录');
        } else {
            $this->error('密码修改失败');
        }
    }
    
    /**
     * 记录登录日志
     * @param int $adminId 管理员ID
     * @return void
     */
    protected function recordLoginLog($adminId)
    {
        $data = [
            'admin_id' => $adminId,
            'login_ip' => request()->ip(),
            'login_time' => time(),
            'login_ua' => request()->header('user-agent'),
        ];
        
        Db::name('login_log')->insert($data);
    }
}

/**
 * 生成随机字符串
 * @param int $length 长度
 * @return string
 */
function random_string($length = 6)
{
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}