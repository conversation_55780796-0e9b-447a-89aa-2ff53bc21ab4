<?php

namespace app\api\controller;

use think\facade\Db;
use think\facade\Cache;

/**
 * 认证控制器
 */
class Auth extends Common
{
    /**
     * 用户登录
     * POST /api/auth/login
     */
    public function login()
    {
        $this->checkPostRequest();
        
        $params = $this->request->post();
        $this->validateRequired($params, ['username', 'password']);
        
        $username = trim($params['username']);
        $password = trim($params['password']);
        $deviceId = $params['device_id'] ?? '';
        $deviceType = $params['device_type'] ?? 'mobile';
        
        // 查询用户信息
        $admin = Db::name('admin')
            ->field('admin_id,username,salt,realname,pwd,group_id,avatar,isphone,is_open,role_id')
            ->where('username', $username)
            ->find();
            
        if (!$admin) {
            $this->jsonError('用户名或密码错误');
        }
        
        // 检查用户状态
        if ($admin['is_open'] != 1) {
            $this->jsonError('当前用户已被禁用，禁止登录');
        }
        
        // 验证密码
        if ($admin['pwd'] !== md5($password . $admin['salt'])) {
            $this->jsonError('用户名或密码错误');
        }
        
        // 生成JWT Token
        $token = $this->generateToken($admin['admin_id'], $deviceId);
        
        // 记录登录日志
        $this->recordLoginLog($admin, $deviceId, $deviceType);
        
        // 清理敏感信息
        unset($admin['pwd'], $admin['salt']);
        
        // 获取用户权限信息
        $groupInfo = Db::name('auth_group')
            ->where('id', $admin['group_id'])
            ->find();
            
        $data = [
            'token' => $token,
            'expire_time' => time() + $this->tokenExpire,
            'user_info' => $admin,
            'group_info' => $groupInfo,
            'device_id' => $deviceId
        ];
        
        $this->jsonSuccess('登录成功', $data);
    }
    
    /**
     * 刷新Token
     * POST /api/auth/refresh
     */
    public function refresh()
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $deviceId = $this->request->post('device_id', '');
        
        // 生成新的Token
        $newToken = $this->generateToken($this->admin['admin_id'], $deviceId);
        
        $data = [
            'token' => $newToken,
            'expire_time' => time() + $this->tokenExpire
        ];
        
        $this->jsonSuccess('Token刷新成功', $data);
    }
    
    /**
     * 用户登出
     * POST /api/auth/logout
     */
    public function logout()
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        // 清除缓存中的Token
        Cache::delete('admin_token_' . $this->admin['admin_id']);
        
        $this->jsonSuccess('登出成功');
    }
    
    /**
     * 获取当前用户信息
     * GET /api/auth/userinfo
     */
    public function userinfo()
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        // 获取用户详细信息
        $admin = Db::name('admin')
            ->field('admin_id,username,realname,group_id,avatar,isphone,role_id')
            ->where('admin_id', $this->admin['admin_id'])
            ->find();
            
        // 获取权限组信息
        $groupInfo = Db::name('auth_group')
            ->where('id', $admin['group_id'])
            ->find();
            
        $data = [
            'user_info' => $admin,
            'group_info' => $groupInfo
        ];
        
        $this->jsonSuccess('获取成功', $data);
    }
    
    /**
     * 修改密码
     * POST /api/auth/changePassword
     */
    public function changePassword()
    {
        $this->checkPostRequest();
        $this->checkAuth();
        
        $params = $this->request->post();
        $this->validateRequired($params, ['old_password', 'new_password']);
        
        $oldPassword = trim($params['old_password']);
        $newPassword = trim($params['new_password']);
        
        // 验证新密码长度
        if (strlen($newPassword) < 6) {
            $this->jsonError('新密码长度不能少于6位');
        }
        
        // 获取当前用户完整信息
        $admin = Db::name('admin')
            ->where('admin_id', $this->admin['admin_id'])
            ->find();
            
        // 验证旧密码
        if ($admin['pwd'] !== md5($oldPassword . $admin['salt'])) {
            $this->jsonError('原密码错误');
        }
        
        // 生成新的盐值和密码
        $newSalt = substr(md5(time() . mt_rand()), 0, 6);
        $newPwd = md5($newPassword . $newSalt);
        
        // 更新密码
        $result = Db::name('admin')
            ->where('admin_id', $this->admin['admin_id'])
            ->update([
                'pwd' => $newPwd,
                'salt' => $newSalt,
                'update_time' => time()
            ]);
            
        if ($result) {
            // 清除所有设备的Token，强制重新登录
            Cache::delete('admin_token_' . $this->admin['admin_id']);
            
            $this->jsonSuccess('密码修改成功，请重新登录');
        } else {
            $this->jsonError('密码修改失败');
        }
    }
    
    /**
     * 记录登录日志
     */
    private function recordLoginLog($admin, $deviceId, $deviceType)
    {
        $logData = [
            'admin_id' => $admin['admin_id'],
            'username' => $admin['username'],
            'device_id' => $deviceId,
            'device_type' => $deviceType,
            'login_ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent'),
            'login_time' => time()
        ];
        
        // 这里可以记录到登录日志表
        // Db::name('admin_login_log')->insert($logData);
    }
}
