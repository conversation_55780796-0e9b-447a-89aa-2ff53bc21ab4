(function(h,C){var t,M,y=typeof C,O=h.location,g=h.document,F=g.documentElement,B=h.jQuery,P=h.$,n={},p=[],R="1.10.2",W=p.concat,$=p.push,c=p.slice,I=p.indexOf,z=n.toString,m=n.hasOwnProperty,X=R.trim,Ne=function(e,t){return new Ne.fn.init(e,t,M)},e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,N=/\S+/g,U=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,V=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,Y=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,J=/^[\],:{}\s]*$/,G=/(?:^|:|,)(?:\s*\[)+/g,Q=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,K=/"[^"\\\r\n]*"|true|false|null|-?(?:\d+\.|)\d+(?:[eE][+-]?\d+|)/g,Z=/^-ms-/,ee=/-([\da-z])/gi,te=function(e,t){return t.toUpperCase()},r=function(e){(g.addEventListener||"load"===e.type||"complete"===g.readyState)&&(ne(),Ne.ready())},ne=function(){g.addEventListener?(g.removeEventListener("DOMContentLoaded",r,!1),h.removeEventListener("load",r,!1)):(g.detachEvent("onreadystatechange",r),h.detachEvent("onload",r))};Ne.fn=Ne.prototype={jquery:R,constructor:Ne,init:function(e,t,n){var r,i;if(!e)return this;if("string"==typeof e){if(r="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:V.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof Ne?t[0]:t,Ne.merge(this,Ne.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:g,!0)),Y.test(r[1])&&Ne.isPlainObject(t))for(r in t)Ne.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}if(i=g.getElementById(r[2]),i&&i.parentNode){if(i.id!==r[2])return n.find(e);this.length=1,this[0]=i}return this.context=g,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):Ne.isFunction(e)?n.ready(e):(e.selector!==C&&(this.selector=e.selector,this.context=e.context),Ne.makeArray(e,this))},selector:"",length:0,toArray:function(){return c.call(this)},get:function(e){return null==e?this.toArray():0>e?this[this.length+e]:this[e]},pushStack:function(e){var t=Ne.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return Ne.each(this,e,t)},ready:function(e){return Ne.ready.promise().done(e),this},slice:function(){return this.pushStack(c.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},map:function(n){return this.pushStack(Ne.map(this,function(e,t){return n.call(e,t,e)}))},end:function(){return this.prevObject||this.constructor(null)},push:$,sort:[].sort,splice:[].splice},Ne.fn.init.prototype=Ne.fn,Ne.extend=Ne.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[1]||{},s=2),"object"==typeof a||Ne.isFunction(a)||(a={}),l===s&&(a=this,--s);l>s;s++)if(null!=(i=arguments[s]))for(r in i)e=a[r],n=i[r],a!==n&&(u&&n&&(Ne.isPlainObject(n)||(t=Ne.isArray(n)))?(t?(t=!1,o=e&&Ne.isArray(e)?e:[]):o=e&&Ne.isPlainObject(e)?e:{},a[r]=Ne.extend(u,o,n)):n!==C&&(a[r]=n));return a},Ne.extend({expando:"jQuery"+(R+Math.random()).replace(/\D/g,""),noConflict:function(e){return h.$===Ne&&(h.$=P),e&&h.jQuery===Ne&&(h.jQuery=B),Ne},isReady:!1,readyWait:1,holdReady:function(e){e?Ne.readyWait++:Ne.ready(!0)},ready:function(e){if(e===!0?!--Ne.readyWait:!Ne.isReady){if(!g.body)return setTimeout(Ne.ready);Ne.isReady=!0,e!==!0&&--Ne.readyWait>0||(t.resolveWith(g,[Ne]),Ne.fn.trigger&&Ne(g).trigger("ready").off("ready"))}},isFunction:function(e){return"function"===Ne.type(e)},isArray:Array.isArray||function(e){return"array"===Ne.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[z.call(e)]||"object":typeof e},isPlainObject:function(e){var t;if(!e||"object"!==Ne.type(e)||e.nodeType||Ne.isWindow(e))return!1;try{if(e.constructor&&!m.call(e,"constructor")&&!m.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(Ne.support.ownLast)for(t in e)return m.call(e,t);for(t in e);return t===C||m.call(e,t)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw Error(e)},parseHTML:function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||g;var r=Y.exec(e),i=!n&&[];return r?[t.createElement(r[1])]:(r=Ne.buildFragment([e],t,i),i&&Ne(i).remove(),Ne.merge([],r.childNodes))},parseJSON:function(e){return h.JSON&&h.JSON.parse?h.JSON.parse(e):null===e?e:"string"==typeof e&&(e=Ne.trim(e),e&&J.test(e.replace(Q,"@").replace(K,"]").replace(G,"")))?Function("return "+e)():(Ne.error("Invalid JSON: "+e),C)},parseXML:function(e){var t,n;if(!e||"string"!=typeof e)return null;try{h.DOMParser?(n=new DOMParser,t=n.parseFromString(e,"text/xml")):(t=new ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e))}catch(e){t=C}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||Ne.error("Invalid XML: "+e),t},noop:function(){},globalEval:function(e){e&&Ne.trim(e)&&(h.execScript||function(e){h.eval.call(h,e)})(e)},camelCase:function(e){return e.replace(Z,"ms-").replace(ee,te)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r,i=0,o=e.length,a=re(e);if(n){if(a){for(;o>i;i++)if(r=t.apply(e[i],n),r===!1)break}else for(i in e)if(r=t.apply(e[i],n),r===!1)break}else if(a){for(;o>i;i++)if(r=t.call(e[i],i,e[i]),r===!1)break}else for(i in e)if(r=t.call(e[i],i,e[i]),r===!1)break;return e},trim:X&&!X.call("\ufeff ")?function(e){return null==e?"":X.call(e)}:function(e){return null==e?"":(e+"").replace(U,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(re(Object(e))?Ne.merge(n,"string"==typeof e?[e]:e):$.call(n,e)),n},inArray:function(e,t,n){var r;if(t){if(I)return I.call(t,e,n);for(r=t.length,n=n?0>n?Math.max(0,r+n):n:0;r>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){var n=t.length,r=e.length,i=0;if("number"==typeof n)for(;n>i;i++)e[r++]=t[i];else while(t[i]!==C)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){var r,i=[],o=0,a=e.length;for(n=!!n;a>o;o++)r=!!t(e[o],o),n!==r&&i.push(e[o]);return i},map:function(e,t,n){var r,i=0,o=e.length,a=re(e),s=[];if(a)for(;o>i;i++)r=t(e[i],i,n),null!=r&&(s[s.length]=r);else for(i in e)r=t(e[i],i,n),null!=r&&(s[s.length]=r);return W.apply([],s)},guid:1,proxy:function(e,t){var n,r,i;return"string"==typeof t&&(i=e[t],t=e,e=i),Ne.isFunction(e)?(n=c.call(arguments,2),r=function(){return e.apply(t||this,n.concat(c.call(arguments)))},r.guid=e.guid=e.guid||Ne.guid++,r):C},access:function(e,t,n,r,i,o,a){var s=0,l=e.length,u=null==n;if("object"===Ne.type(n)){i=!0;for(s in n)Ne.access(e,t,s,n[s],!0,o,a)}else if(r!==C&&(i=!0,Ne.isFunction(r)||(a=!0),u&&(a?(t.call(e,r),t=null):(u=t,t=function(e,t,n){return u.call(Ne(e),n)})),t))for(;l>s;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:u?t.call(e):l?t(e[0],n):o},now:function(){return(new Date).getTime()},swap:function(e,t,n,r){var i,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];i=n.apply(e,r||[]);for(o in t)e.style[o]=a[o];return i}}),Ne.ready.promise=function(e){if(!t)if(t=Ne.Deferred(),"complete"===g.readyState)setTimeout(Ne.ready);else if(g.addEventListener)g.addEventListener("DOMContentLoaded",r,!1),h.addEventListener("load",r,!1);else{g.attachEvent("onreadystatechange",r),h.attachEvent("onload",r);var n=!1;try{n=null==h.frameElement&&g.documentElement}catch(e){}n&&n.doScroll&&function t(){if(!Ne.isReady){try{n.doScroll("left")}catch(e){return setTimeout(t,50)}ne(),Ne.ready()}}()}return t.promise(e)},Ne.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});function re(e){var t=e.length,n=Ne.type(e);return Ne.isWindow(e)?!1:1===e.nodeType&&t?!0:"array"===n||"function"!==n&&(0===t||"number"==typeof t&&t>0&&t-1 in e)}M=Ne(g),function(O,i){var e,h,w,T,o,F,B,C,u,g,N,n,m,y,r,a,v,b="sizzle"+-new Date,x=O.document,k=0,P=0,R=ce(),W=ce(),$=ce(),c=!1,I=function(e,t){return e===t?(c=!0,0):0},s=typeof i,z=1<<31,X={}.hasOwnProperty,t=[],U=t.pop,V=t.push,E=t.push,Y=t.slice,S=t.indexOf||function(e){var t=0,n=this.length;for(;n>t;t++)if(this[t]===e)return t;return-1},J="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",p="[\\x20\\t\\r\\n\\f]",l="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",G=l.replace("w","w#"),Q="\\["+p+"*("+l+")"+p+"*(?:([*^$|!~]?=)"+p+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+G+")|)|)"+p+"*\\]",K=":("+l+")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+Q.replace(3,8)+")*)|.*)\\)|)",A=RegExp("^"+p+"+|((?:^|[^\\\\])(?:\\\\.)*)"+p+"+$","g"),Z=RegExp("^"+p+"*,"+p+"*"),ee=RegExp("^"+p+"*([>+~]|"+p+")"+p+"*"),te=RegExp(p+"*[+~]"),ne=RegExp("="+p+"*([^\\]'\"]*)"+p+"*\\]","g"),re=RegExp(K),ie=RegExp("^"+G+"$"),f={ID:RegExp("^#("+l+")"),CLASS:RegExp("^\\.("+l+")"),TAG:RegExp("^("+l.replace("w","w*")+")"),ATTR:RegExp("^"+Q),PSEUDO:RegExp("^"+K),CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+p+"*(even|odd|(([+-]|)(\\d*)n|)"+p+"*(?:([+-]|)"+p+"*(\\d+)|))"+p+"*\\)|)","i"),bool:RegExp("^(?:"+J+")$","i"),needsContext:RegExp("^"+p+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+p+"*((?:-\\d)?\\d*)"+p+"*\\)|)(?=[^-]|$)","i")},oe=/^[^{]+\{\s*\[native \w/,ae=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,se=/^(?:input|select|textarea|button)$/i,le=/^h\d$/i,ue=/'|\\/g,d=RegExp("\\\\([\\da-f]{1,6}"+p+"?|("+p+")|.)","ig"),j=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:0>r?String.fromCharCode(r+65536):String.fromCharCode(55296|r>>10,56320|1023&r)};try{E.apply(t=Y.call(x.childNodes),x.childNodes),t[x.childNodes.length].nodeType}catch(e){E={apply:t.length?function(e,t){V.apply(e,Y.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}function D(e,t,n,r){var i,o,a,s,l,u,c,p,f,d;if((t?t.ownerDocument||t:x)!==N&&g(t),t=t||N,n=n||[],!e||"string"!=typeof e)return n;if(1!==(s=t.nodeType)&&9!==s)return[];if(m&&!r){if(i=ae.exec(e))if(a=i[1]){if(9===s){if(o=t.getElementById(a),!o||!o.parentNode)return n;if(o.id===a)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(a))&&v(t,o)&&o.id===a)return n.push(o),n}else{if(i[2])return E.apply(n,t.getElementsByTagName(e)),n;if((a=i[3])&&h.getElementsByClassName&&t.getElementsByClassName)return E.apply(n,t.getElementsByClassName(a)),n}if(h.qsa&&(!y||!y.test(e))){if(p=c=b,f=t,d=9===s&&e,1===s&&"object"!==t.nodeName.toLowerCase()){u=_(e),(c=t.getAttribute("id"))?p=c.replace(ue,"\\$&"):t.setAttribute("id",p),p="[id='"+p+"'] ",l=u.length;while(l--)u[l]=p+M(u[l]);f=te.test(e)&&t.parentNode||t,d=u.join(",")}if(d)try{return E.apply(n,f.querySelectorAll(d)),n}catch(e){}finally{c||t.removeAttribute("id")}}}return Ce(e.replace(A,"$1"),t,n,r)}function ce(){var n=[];function r(e,t){return n.push(e+=" ")>T.cacheLength&&delete r[n.shift()],r[e]=t}return r}function L(e){return e[b]=!0,e}function H(e){var t=N.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function pe(e,t){var n=e.split("|"),r=e.length;while(r--)T.attrHandle[n[r]]=t}function fe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||z)-(~e.sourceIndex||z);if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function de(n){return function(e){var t=e.nodeName.toLowerCase();return"input"===t&&e.type===n}}function he(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function q(a){return L(function(o){return o=+o,L(function(e,t){var n,r=a([],e.length,o),i=r.length;while(i--)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}F=D.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?"HTML"!==t.nodeName:!1},h=D.support={},g=D.setDocument=function(e){var l=e?e.ownerDocument||e:x,t=l.defaultView;return l!==N&&9===l.nodeType&&l.documentElement?(N=l,n=l.documentElement,m=!F(l),t&&t.attachEvent&&t!==t.top&&t.attachEvent("onbeforeunload",function(){g()}),h.attributes=H(function(e){return e.className="i",!e.getAttribute("className")}),h.getElementsByTagName=H(function(e){return e.appendChild(l.createComment("")),!e.getElementsByTagName("*").length}),h.getElementsByClassName=H(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),h.getById=H(function(e){return n.appendChild(e).id=b,!l.getElementsByName||!l.getElementsByName(b).length}),h.getById?(T.find.ID=function(e,t){if(typeof t.getElementById!==s&&m){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},T.filter.ID=function(e){var t=e.replace(d,j);return function(e){return e.getAttribute("id")===t}}):(delete T.find.ID,T.filter.ID=function(e){var n=e.replace(d,j);return function(e){var t=typeof e.getAttributeNode!==s&&e.getAttributeNode("id");return t&&t.value===n}}),T.find.TAG=h.getElementsByTagName?function(e,t){return typeof t.getElementsByTagName!==s?t.getElementsByTagName(e):i}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){while(n=o[i++])1===n.nodeType&&r.push(n);return r}return o},T.find.CLASS=h.getElementsByClassName&&function(e,t){return typeof t.getElementsByClassName!==s&&m?t.getElementsByClassName(e):i},r=[],y=[],(h.qsa=oe.test(l.querySelectorAll))&&(H(function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||y.push("\\["+p+"*(?:value|"+J+")"),e.querySelectorAll(":checked").length||y.push(":checked")}),H(function(e){var t=l.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("t",""),e.querySelectorAll("[t^='']").length&&y.push("[*^$]="+p+"*(?:''|\"\")"),e.querySelectorAll(":enabled").length||y.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),y.push(",.*:")})),(h.matchesSelector=oe.test(a=n.webkitMatchesSelector||n.mozMatchesSelector||n.oMatchesSelector||n.msMatchesSelector))&&H(function(e){h.disconnectedMatch=a.call(e,"div"),a.call(e,"[s!='']:x"),r.push("!=",K)}),y=y.length&&RegExp(y.join("|")),r=r.length&&RegExp(r.join("|")),v=oe.test(n.contains)||n.compareDocumentPosition?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},I=n.compareDocumentPosition?function(e,t){if(e===t)return c=!0,0;var n=t.compareDocumentPosition&&e.compareDocumentPosition&&e.compareDocumentPosition(t);return n?1&n||!h.sortDetached&&t.compareDocumentPosition(e)===n?e===l||v(x,e)?-1:t===l||v(x,t)?1:u?S.call(u,e)-S.call(u,t):0:4&n?-1:1:e.compareDocumentPosition?-1:1}:function(e,t){var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(e===t)return c=!0,0;if(!i||!o)return e===l?-1:t===l?1:i?-1:o?1:u?S.call(u,e)-S.call(u,t):0;if(i===o)return fe(e,t);n=e;while(n=n.parentNode)a.unshift(n);n=t;while(n=n.parentNode)s.unshift(n);while(a[r]===s[r])r++;return r?fe(a[r],s[r]):a[r]===x?-1:s[r]===x?1:0},l):N},D.matches=function(e,t){return D(e,null,null,t)},D.matchesSelector=function(e,t){if((e.ownerDocument||e)!==N&&g(e),t=t.replace(ne,"='$1']"),!(!h.matchesSelector||!m||r&&r.test(t)||y&&y.test(t)))try{var n=a.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return D(t,N,null,[e]).length>0},D.contains=function(e,t){return(e.ownerDocument||e)!==N&&g(e),v(e,t)},D.attr=function(e,t){(e.ownerDocument||e)!==N&&g(e);var n=T.attrHandle[t.toLowerCase()],r=n&&X.call(T.attrHandle,t.toLowerCase())?n(e,t,!m):i;return r===i?h.attributes||!m?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null:r},D.error=function(e){throw Error("Syntax error, unrecognized expression: "+e)},D.uniqueSort=function(e){var t,n=[],r=0,i=0;if(c=!h.detectDuplicates,u=!h.sortStable&&e.slice(0),e.sort(I),c){while(t=e[i++])t===e[i]&&(r=n.push(i));while(r--)e.splice(n[r],1)}return e},o=D.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r];r++)n+=o(t);return n},T=D.selectors={cacheLength:50,createPseudo:L,match:f,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(d,j),e[3]=(e[4]||e[5]||"").replace(d,j),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||D.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&D.error(e[0]),e},PSEUDO:function(e){var t,n=!e[5]&&e[2];return f.CHILD.test(e[0])?null:(e[3]&&e[4]!==i?e[2]=e[4]:n&&re.test(n)&&(t=_(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(d,j).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=R[e+" "];return t||(t=RegExp("(^|"+p+")"+e+"("+p+"|$)"))&&R(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==s&&e.getAttribute("class")||"")})},ATTR:function(n,r,i){return function(e){var t=D.attr(e,n);return null==t?"!="===r:r?(t+="","="===r?t===i:"!="===r?t!==i:"^="===r?i&&0===t.indexOf(i):"*="===r?i&&t.indexOf(i)>-1:"$="===r?i&&t.slice(-i.length)===i:"~="===r?(" "+t+" ").indexOf(i)>-1:"|="===r?t===i||t.slice(0,i.length+1)===i+"-":!1):!0}},CHILD:function(d,e,t,h,g){var m="nth"!==d.slice(0,3),y="last"!==d.slice(-4),v="of-type"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,l,u=m!==y?"nextSibling":"previousSibling",c=e.parentNode,p=v&&e.nodeName.toLowerCase(),f=!n&&!v;if(c){if(m){while(u){o=e;while(o=o[u])if(v?o.nodeName.toLowerCase()===p:1===o.nodeType)return!1;l=u="only"===d&&!l&&"nextSibling"}return!0}if(l=[y?c.firstChild:c.lastChild],y&&f){i=c[b]||(c[b]={}),r=i[d]||[],s=r[0]===k&&r[1],a=r[0]===k&&r[2],o=s&&c.childNodes[s];while(o=++s&&o&&o[u]||(a=s=0)||l.pop())if(1===o.nodeType&&++a&&o===e){i[d]=[k,s,a];break}}else if(f&&(r=(e[b]||(e[b]={}))[d])&&r[0]===k)a=r[1];else while(o=++s&&o&&o[u]||(a=s=0)||l.pop())if((v?o.nodeName.toLowerCase()===p:1===o.nodeType)&&++a&&(f&&((o[b]||(o[b]={}))[d]=[k,a]),o===e))break;return a-=g,a===h||0===a%h&&a/h>=0}}},PSEUDO:function(e,o){var t,a=T.pseudos[e]||T.setFilters[e.toLowerCase()]||D.error("unsupported pseudo: "+e);return a[b]?a(o):a.length>1?(t=[e,e,"",o],T.setFilters.hasOwnProperty(e.toLowerCase())?L(function(e,t){var n,r=a(e,o),i=r.length;while(i--)n=S.call(e,r[i]),e[n]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:L(function(e){var r=[],i=[],s=B(e.replace(A,"$1"));return s[b]?L(function(e,t,n,r){var i,o=s(e,null,r,[]),a=e.length;while(a--)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),!i.pop()}}),has:L(function(t){return function(e){return D(t,e).length>0}}),contains:L(function(t){return function(e){return(e.textContent||e.innerText||o(e)).indexOf(t)>-1}}),lang:L(function(n){return ie.test(n||"")||D.error("unsupported lang: "+n),n=n.replace(d,j).toLowerCase(),function(e){var t;do{if(t=m?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return t=t.toLowerCase(),t===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=O.location&&O.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===n},focus:function(e){return e===N.activeElement&&(!N.hasFocus||N.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeName>"@"||3===e.nodeType||4===e.nodeType)return!1;return!0},parent:function(e){return!T.pseudos.empty(e)},header:function(e){return le.test(e.nodeName)},input:function(e){return se.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||t.toLowerCase()===e.type)},first:q(function(){return[0]}),last:q(function(e,t){return[t-1]}),eq:q(function(e,t,n){return[0>n?n+t:n]}),even:q(function(e,t){var n=0;for(;t>n;n+=2)e.push(n);return e}),odd:q(function(e,t){var n=1;for(;t>n;n+=2)e.push(n);return e}),lt:q(function(e,t,n){var r=0>n?n+t:n;for(;--r>=0;)e.push(r);return e}),gt:q(function(e,t,n){var r=0>n?n+t:n;for(;t>++r;)e.push(r);return e})}},T.pseudos.nth=T.pseudos.eq;for(e in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})T.pseudos[e]=de(e);for(e in{submit:!0,reset:!0})T.pseudos[e]=he(e);function ge(){}ge.prototype=T.filters=T.pseudos,T.setFilters=new ge;function _(e,t){var n,r,i,o,a,s,l,u=W[e+" "];if(u)return t?0:u.slice(0);a=e,s=[],l=T.preFilter;while(a){(!n||(r=Z.exec(a)))&&(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=ee.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(A," ")}),a=a.slice(n.length));for(o in T.filter)!(r=f[o].exec(a))||l[o]&&!(r=l[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?D.error(e):W(e,s).slice(0)}function M(e){var t=0,n=e.length,r="";for(;n>t;t++)r+=e[t].value;return r}function me(s,e,t){var l=e.dir,u=t&&"parentNode"===l,c=P++;return e.first?function(e,t,n){while(e=e[l])if(1===e.nodeType||u)return s(e,t,n)}:function(e,t,n){var r,i,o,a=k+" "+c;if(n){while(e=e[l])if((1===e.nodeType||u)&&s(e,t,n))return!0}else while(e=e[l])if(1===e.nodeType||u)if(o=e[b]||(e[b]={}),(i=o[l])&&i[0]===a){if((r=i[1])===!0||r===w)return r===!0}else if(i=o[l]=[a],i[1]=s(e,t,n)||w,i[1]===!0)return!0}}function ye(i){return i.length>1?function(e,t,n){var r=i.length;while(r--)if(!i[r](e,t,n))return!1;return!0}:i[0]}function ve(e,t,n,r,i){var o,a=[],s=0,l=e.length,u=null!=t;for(;l>s;s++)(o=e[s])&&(!n||n(o,r,i))&&(a.push(o),u&&t.push(s));return a}function be(d,h,g,m,y,e){return m&&!m[b]&&(m=be(m)),y&&!y[b]&&(y=be(y,e)),L(function(e,t,n,r){var i,o,a,s=[],l=[],u=t.length,c=e||Te(h||"*",n.nodeType?[n]:n,[]),p=!d||!e&&h?c:ve(c,s,d,n,r),f=g?y||(e?d:u||m)?[]:t:p;if(g&&g(p,f,n,r),m){i=ve(f,l),m(i,[],n,r),o=i.length;while(o--)(a=i[o])&&(f[l[o]]=!(p[l[o]]=a))}if(e){if(y||d){if(y){i=[],o=f.length;while(o--)(a=f[o])&&i.push(p[o]=a);y(null,f=[],i,r)}o=f.length;while(o--)(a=f[o])&&(i=y?S.call(e,a):s[o])>-1&&(e[i]=!(t[i]=a))}}else f=ve(f===t?f.splice(u,f.length):f),y?y(null,t,f,r):E.apply(t,f)})}function xe(e){var r,t,n,i=e.length,o=T.relative[e[0].type],a=o||T.relative[" "],s=o?1:0,l=me(function(e){return e===r},a,!0),u=me(function(e){return S.call(r,e)>-1},a,!0),c=[function(e,t,n){return!o&&(n||t!==C)||((r=t).nodeType?l(e,t,n):u(e,t,n))}];for(;i>s;s++)if(t=T.relative[e[s].type])c=[me(ye(c),t)];else{if(t=T.filter[e[s].type].apply(null,e[s].matches),t[b]){for(n=++s;i>n;n++)if(T.relative[e[n].type])break;return be(s>1&&ye(c),s>1&&M(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(A,"$1"),t,n>s&&xe(e.slice(s,n)),i>n&&xe(e=e.slice(n)),i>n&&M(e))}c.push(t)}return ye(c)}function we(m,y){var v=0,b=y.length>0,x=m.length>0,e=function(e,t,n,r,i){var o,a,s,l=[],u=0,c="0",p=e&&[],f=null!=i,d=C,h=e||x&&T.find.TAG("*",i&&t.parentNode||t),g=k+=null==d?1:Math.random()||.1;for(f&&(C=t!==N&&t,w=v);null!=(o=h[c]);c++){if(x&&o){a=0;while(s=m[a++])if(s(o,t,n)){r.push(o);break}f&&(k=g,w=++v)}b&&((o=!s&&o)&&u--,e&&p.push(o))}if(u+=c,b&&c!==u){a=0;while(s=y[a++])s(p,l,t,n);if(e){if(u>0)while(c--)p[c]||l[c]||(l[c]=U.call(r));l=ve(l)}E.apply(r,l),f&&!e&&l.length>0&&u+y.length>1&&D.uniqueSort(r)}return f&&(k=g,C=d),p};return b?L(e):e}B=D.compile=function(e,t){var n,r=[],i=[],o=$[e+" "];if(!o){t||(t=_(e)),n=t.length;while(n--)o=xe(t[n]),o[b]?r.push(o):i.push(o);o=$(e,we(i,r))}return o};function Te(e,t,n){var r=0,i=t.length;for(;i>r;r++)D(e,t[r],n);return n}function Ce(e,t,n,r){var i,o,a,s,l,u=_(e);if(!r&&1===u.length){if(o=u[0]=u[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&h.getById&&9===t.nodeType&&m&&T.relative[o[1].type]){if(t=(T.find.ID(a.matches[0].replace(d,j),t)||[])[0],!t)return n;e=e.slice(o.shift().value.length)}i=f.needsContext.test(e)?0:o.length;while(i--){if(a=o[i],T.relative[s=a.type])break;if((l=T.find[s])&&(r=l(a.matches[0].replace(d,j),te.test(o[0].type)&&t.parentNode||t))){if(o.splice(i,1),e=r.length&&M(o),!e)return E.apply(n,r),n;break}}}return B(e,u)(r,t,!m,n,te.test(e)),n}h.sortStable=b.split("").sort(I).join("")===b,h.detectDuplicates=c,g(),h.sortDetached=H(function(e){return 1&e.compareDocumentPosition(N.createElement("div"))}),H(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||pe("type|href|height|width",function(e,t,n){return n?i:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),h.attributes&&H(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||pe("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?i:e.defaultValue}),H(function(e){return null==e.getAttribute("disabled")})||pe(J,function(e,t,n){var r;return n?i:(r=e.getAttributeNode(t))&&r.specified?r.value:e[t]===!0?t.toLowerCase():null}),Ne.find=D,Ne.expr=D.selectors,Ne.expr[":"]=Ne.expr.pseudos,Ne.unique=D.uniqueSort,Ne.text=D.getText,Ne.isXMLDoc=D.isXML,Ne.contains=D.contains}(h);var ie={};function oe(e){var n=ie[e]={};return Ne.each(e.match(N)||[],function(e,t){n[t]=!0}),n}Ne.Callbacks=function(i){i="string"==typeof i?ie[i]||oe(i):Ne.extend({},i);var r,t,n,o,a,s,l=[],u=!i.once&&[],c=function(e){for(t=i.memory&&e,n=!0,a=s||0,s=0,o=l.length,r=!0;l&&o>a;a++)if(l[a].apply(e[0],e[1])===!1&&i.stopOnFalse){t=!1;break}r=!1,l&&(u?u.length&&c(u.shift()):t?l=[]:p.disable())},p={add:function(){if(l){var e=l.length;(function r(e){Ne.each(e,function(e,t){var n=Ne.type(t);"function"===n?i.unique&&p.has(t)||l.push(t):t&&t.length&&"string"!==n&&r(t)})})(arguments),r?o=l.length:t&&(s=e,c(t))}return this},remove:function(){return l&&Ne.each(arguments,function(e,t){var n;while((n=Ne.inArray(t,l,n))>-1)l.splice(n,1),r&&(o>=n&&o--,a>=n&&a--)}),this},has:function(e){return e?Ne.inArray(e,l)>-1:!(!l||!l.length)},empty:function(){return l=[],o=0,this},disable:function(){return l=u=t=C,this},disabled:function(){return!l},lock:function(){return u=C,t||p.disable(),this},locked:function(){return!u},fireWith:function(e,t){return!l||n&&!u||(t=t||[],t=[e,t.slice?t.slice():t],r?u.push(t):c(t)),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!n}};return p},Ne.extend({Deferred:function(e){var a=[["resolve","done",Ne.Callbacks("once memory"),"resolved"],["reject","fail",Ne.Callbacks("once memory"),"rejected"],["notify","progress",Ne.Callbacks("memory")]],i="pending",s={state:function(){return i},always:function(){return l.done(arguments).fail(arguments),this},then:function(){var o=arguments;return Ne.Deferred(function(i){Ne.each(a,function(e,t){var n=t[0],r=Ne.isFunction(o[e])&&o[e];l[t[1]](function(){var e=r&&r.apply(this,arguments);e&&Ne.isFunction(e.promise)?e.promise().done(i.resolve).fail(i.reject).progress(i.notify):i[n+"With"](this===s?i.promise():this,r?[e]:arguments)})}),o=null}).promise()},promise:function(e){return null!=e?Ne.extend(e,s):s}},l={};return s.pipe=s.then,Ne.each(a,function(e,t){var n=t[2],r=t[3];s[t[1]]=n.add,r&&n.add(function(){i=r},a[1^e][2].disable,a[2][2].lock),l[t[0]]=function(){return l[t[0]+"With"](this===l?s:this,arguments),this},l[t[0]+"With"]=n.fireWith}),s.promise(l),e&&e.call(l,l),l},when:function(e){var t=0,n=c.call(arguments),r=n.length,i=1!==r||e&&Ne.isFunction(e.promise)?r:0,o=1===i?e:Ne.Deferred(),a=function(t,n,r){return function(e){n[t]=this,r[t]=arguments.length>1?c.call(arguments):e,r===s?o.notifyWith(n,r):--i||o.resolveWith(n,r)}},s,l,u;if(r>1)for(s=Array(r),l=Array(r),u=Array(r);r>t;t++)n[t]&&Ne.isFunction(n[t].promise)?n[t].promise().done(a(t,u,n)).fail(o.reject).progress(a(t,l,s)):--i;return i||o.resolveWith(u,n),o.promise()}}),Ne.support=function(o){var e,t,n,r,i,a,s,l,u,c=g.createElement("div");if(c.setAttribute("className","t"),c.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",e=c.getElementsByTagName("*")||[],t=c.getElementsByTagName("a")[0],!t||!t.style||!e.length)return o;r=g.createElement("select"),a=r.appendChild(g.createElement("option")),n=c.getElementsByTagName("input")[0],t.style.cssText="top:1px;float:left;opacity:.5",o.getSetAttribute="t"!==c.className,o.leadingWhitespace=3===c.firstChild.nodeType,o.tbody=!c.getElementsByTagName("tbody").length,o.htmlSerialize=!!c.getElementsByTagName("link").length,o.style=/top/.test(t.getAttribute("style")),o.hrefNormalized="/a"===t.getAttribute("href"),o.opacity=/^0.5/.test(t.style.opacity),o.cssFloat=!!t.style.cssFloat,o.checkOn=!!n.value,o.optSelected=a.selected,o.enctype=!!g.createElement("form").enctype,o.html5Clone="<:nav></:nav>"!==g.createElement("nav").cloneNode(!0).outerHTML,o.inlineBlockNeedsLayout=!1,o.shrinkWrapBlocks=!1,o.pixelPosition=!1,o.deleteExpando=!0,o.noCloneEvent=!0,o.reliableMarginRight=!0,o.boxSizingReliable=!0,n.checked=!0,o.noCloneChecked=n.cloneNode(!0).checked,r.disabled=!0,o.optDisabled=!a.disabled;try{delete c.test}catch(e){o.deleteExpando=!1}n=g.createElement("input"),n.setAttribute("value",""),o.input=""===n.getAttribute("value"),n.value="t",n.setAttribute("type","radio"),o.radioValue="t"===n.value,n.setAttribute("checked","t"),n.setAttribute("name","t"),i=g.createDocumentFragment(),i.appendChild(n),o.appendChecked=n.checked,o.checkClone=i.cloneNode(!0).cloneNode(!0).lastChild.checked,c.attachEvent&&(c.attachEvent("onclick",function(){o.noCloneEvent=!1}),c.cloneNode(!0).click());for(u in{submit:!0,change:!0,focusin:!0})c.setAttribute(s="on"+u,"t"),o[u+"Bubbles"]=s in h||c.attributes[s].expando===!1;c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",o.clearCloneStyle="content-box"===c.style.backgroundClip;for(u in Ne(o))break;return o.ownLast="0"!==u,Ne(function(){var e,t,n,r="padding:0;margin:0;border:0;display:block;box-sizing:content-box;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;",i=g.getElementsByTagName("body")[0];i&&(e=g.createElement("div"),e.style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",i.appendChild(e).appendChild(c),c.innerHTML="<table><tr><td></td><td>t</td></tr></table>",n=c.getElementsByTagName("td"),n[0].style.cssText="padding:0;margin:0;border:0;display:none",l=0===n[0].offsetHeight,n[0].style.display="",n[1].style.display="none",o.reliableHiddenOffsets=l&&0===n[0].offsetHeight,c.innerHTML="",c.style.cssText="box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%;",Ne.swap(i,null!=i.style.zoom?{zoom:1}:{},function(){o.boxSizing=4===c.offsetWidth}),h.getComputedStyle&&(o.pixelPosition="1%"!==(h.getComputedStyle(c,null)||{}).top,o.boxSizingReliable="4px"===(h.getComputedStyle(c,null)||{width:"4px"}).width,t=c.appendChild(g.createElement("div")),t.style.cssText=c.style.cssText=r,t.style.marginRight=t.style.width="0",c.style.width="1px",o.reliableMarginRight=!parseFloat((h.getComputedStyle(t,null)||{}).marginRight)),typeof c.style.zoom!==y&&(c.innerHTML="",c.style.cssText=r+"width:1px;padding:1px;display:inline;zoom:1",o.inlineBlockNeedsLayout=3===c.offsetWidth,c.style.display="block",c.innerHTML="<div></div>",c.firstChild.style.width="5px",o.shrinkWrapBlocks=3!==c.offsetWidth,o.inlineBlockNeedsLayout&&(i.style.zoom=1)),i.removeChild(e),e=c=n=t=null)}),e=r=i=a=t=n=null,o}({});var ae=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,se=/([A-Z])/g;function le(e,t,n,r){if(Ne.acceptData(e)){var i,o,a=Ne.expando,s=e.nodeType,l=s?Ne.cache:e,u=s?e[a]:e[a]&&a;if(u&&l[u]&&(r||l[u].data)||n!==C||"string"!=typeof t)return u||(u=s?e[a]=p.pop()||Ne.guid++:a),l[u]||(l[u]=s?{}:{toJSON:Ne.noop}),("object"==typeof t||"function"==typeof t)&&(r?l[u]=Ne.extend(l[u],t):l[u].data=Ne.extend(l[u].data,t)),o=l[u],r||(o.data||(o.data={}),o=o.data),n!==C&&(o[Ne.camelCase(t)]=n),"string"==typeof t?(i=o[t],null==i&&(i=o[Ne.camelCase(t)])):i=o,i}}function ue(e,t,n){if(Ne.acceptData(e)){var r,i,o=e.nodeType,a=o?Ne.cache:e,s=o?e[Ne.expando]:Ne.expando;if(a[s]){if(t&&(r=n?a[s]:a[s].data)){Ne.isArray(t)?t=t.concat(Ne.map(t,Ne.camelCase)):t in r?t=[t]:(t=Ne.camelCase(t),t=t in r?[t]:t.split(" ")),i=t.length;while(i--)delete r[t[i]];if(n?!pe(r):!Ne.isEmptyObject(r))return}(n||(delete a[s].data,pe(a[s])))&&(o?Ne.cleanData([e],!0):Ne.support.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}Ne.extend({cache:{},noData:{applet:!0,embed:!0,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){return e=e.nodeType?Ne.cache[e[Ne.expando]]:e[Ne.expando],!!e&&!pe(e)},data:function(e,t,n){return le(e,t,n)},removeData:function(e,t){return ue(e,t)},_data:function(e,t,n){return le(e,t,n,!0)},_removeData:function(e,t){return ue(e,t,!0)},acceptData:function(e){if(e.nodeType&&1!==e.nodeType&&9!==e.nodeType)return!1;var t=e.nodeName&&Ne.noData[e.nodeName.toLowerCase()];return!t||t!==!0&&e.getAttribute("classid")===t}}),Ne.fn.extend({data:function(e,t){var n,r,i=null,o=0,a=this[0];if(e===C){if(this.length&&(i=Ne.data(a),1===a.nodeType&&!Ne._data(a,"parsedAttrs"))){for(n=a.attributes;n.length>o;o++)r=n[o].name,0===r.indexOf("data-")&&(r=Ne.camelCase(r.slice(5)),ce(a,r,i[r]));Ne._data(a,"parsedAttrs",!0)}return i}return"object"==typeof e?this.each(function(){Ne.data(this,e)}):arguments.length>1?this.each(function(){Ne.data(this,e,t)}):a?ce(a,e,Ne.data(a,e)):null},removeData:function(e){return this.each(function(){Ne.removeData(this,e)})}});function ce(e,t,n){if(n===C&&1===e.nodeType){var r="data-"+t.replace(se,"-$1").toLowerCase();if(n=e.getAttribute(r),"string"==typeof n){try{n="true"===n?!0:"false"===n?!1:"null"===n?null:+n+""===n?+n:ae.test(n)?Ne.parseJSON(n):n}catch(e){}Ne.data(e,t,n)}else n=C}return n}function pe(e){var t;for(t in e)if(("data"!==t||!Ne.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}Ne.extend({queue:function(e,t,n){var r;return e?(t=(t||"fx")+"queue",r=Ne._data(e,t),n&&(!r||Ne.isArray(n)?r=Ne._data(e,t,Ne.makeArray(n)):r.push(n)),r||[]):C},dequeue:function(e,t){t=t||"fx";var n=Ne.queue(e,t),r=n.length,i=n.shift(),o=Ne._queueHooks(e,t),a=function(){Ne.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Ne._data(e,n)||Ne._data(e,n,{empty:Ne.Callbacks("once memory").add(function(){Ne._removeData(e,t+"queue"),Ne._removeData(e,n)})})}}),Ne.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),e>arguments.length?Ne.queue(this[0],t):n===C?this:this.each(function(){var e=Ne.queue(this,t,n);Ne._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&Ne.dequeue(this,t)})},dequeue:function(e){return this.each(function(){Ne.dequeue(this,e)})},delay:function(r,e){return r=Ne.fx?Ne.fx.speeds[r]||r:r,e=e||"fx",this.queue(e,function(e,t){var n=setTimeout(e,r);t.stop=function(){clearTimeout(n)}})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=Ne.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=C),e=e||"fx";while(a--)n=Ne._data(o[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var a,fe,de=/[\t\r\n\f]/g,he=/\r/g,ge=/^(?:input|select|textarea|button|object)$/i,me=/^(?:a|area)$/i,ye=/^(?:checked|selected)$/i,s=Ne.support.getSetAttribute,l=Ne.support.input;Ne.fn.extend({attr:function(e,t){return Ne.access(this,Ne.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){Ne.removeAttr(this,e)})},prop:function(e,t){return Ne.access(this,Ne.prop,e,t,arguments.length>1)},removeProp:function(e){return e=Ne.propFix[e]||e,this.each(function(){try{this[e]=C,delete this[e]}catch(e){}})},addClass:function(t){var e,n,r,i,o,a=0,s=this.length,l="string"==typeof t&&t;if(Ne.isFunction(t))return this.each(function(e){Ne(this).addClass(t.call(this,e,this.className))});if(l)for(e=(t||"").match(N)||[];s>a;a++)if(n=this[a],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(de," "):" ")){o=0;while(i=e[o++])0>r.indexOf(" "+i+" ")&&(r+=i+" ");n.className=Ne.trim(r)}return this},removeClass:function(t){var e,n,r,i,o,a=0,s=this.length,l=0===arguments.length||"string"==typeof t&&t;if(Ne.isFunction(t))return this.each(function(e){Ne(this).removeClass(t.call(this,e,this.className))});if(l)for(e=(t||"").match(N)||[];s>a;a++)if(n=this[a],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(de," "):"")){o=0;while(i=e[o++])while(r.indexOf(" "+i+" ")>=0)r=r.replace(" "+i+" "," ");n.className=t?Ne.trim(r):""}return this},toggleClass:function(i,t){var o=typeof i;return"boolean"==typeof t&&"string"===o?t?this.addClass(i):this.removeClass(i):Ne.isFunction(i)?this.each(function(e){Ne(this).toggleClass(i.call(this,e,this.className,t),t)}):this.each(function(){if("string"===o){var e,t=0,n=Ne(this),r=i.match(N)||[];while(e=r[t++])n.hasClass(e)?n.removeClass(e):n.addClass(e)}else(o===y||"boolean"===o)&&(this.className&&Ne._data(this,"__className__",this.className),this.className=this.className||i===!1?"":Ne._data(this,"__className__")||"")})},hasClass:function(e){var t=" "+e+" ",n=0,r=this.length;for(;r>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(de," ").indexOf(t)>=0)return!0;return!1},val:function(n){var e,r,i,t=this[0];{if(arguments.length)return i=Ne.isFunction(n),this.each(function(e){var t;1===this.nodeType&&(t=i?n.call(this,e,Ne(this).val()):n,null==t?t="":"number"==typeof t?t+="":Ne.isArray(t)&&(t=Ne.map(t,function(e){return null==e?"":e+""})),r=Ne.valHooks[this.type]||Ne.valHooks[this.nodeName.toLowerCase()],r&&"set"in r&&r.set(this,t,"value")!==C||(this.value=t))});if(t)return r=Ne.valHooks[t.type]||Ne.valHooks[t.nodeName.toLowerCase()],r&&"get"in r&&(e=r.get(t,"value"))!==C?e:(e=t.value,"string"==typeof e?e.replace(he,""):null==e?"":e)}}}),Ne.extend({valHooks:{option:{get:function(e){var t=Ne.find.attr(e,"value");return null!=t?t:e.text}},select:{get:function(e){var t,n,r=e.options,i=e.selectedIndex,o="select-one"===e.type||0>i,a=o?null:[],s=o?i+1:r.length,l=0>i?s:o?i:0;for(;s>l;l++)if(n=r[l],!(!n.selected&&l!==i||(Ne.support.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&Ne.nodeName(n.parentNode,"optgroup"))){if(t=Ne(n).val(),o)return t;a.push(t)}return a},set:function(e,t){var n,r,i=e.options,o=Ne.makeArray(t),a=i.length;while(a--)r=i[a],(r.selected=Ne.inArray(Ne(r).val(),o)>=0)&&(n=!0);return n||(e.selectedIndex=-1),o}}},attr:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===y?Ne.prop(e,t,n):(1===o&&Ne.isXMLDoc(e)||(t=t.toLowerCase(),r=Ne.attrHooks[t]||(Ne.expr.match.bool.test(t)?fe:a)),n===C?r&&"get"in r&&null!==(i=r.get(e,t))?i:(i=Ne.find.attr(e,t),null==i?C:i):null!==n?r&&"set"in r&&(i=r.set(e,n,t))!==C?i:(e.setAttribute(t,n+""),n):(Ne.removeAttr(e,t),C))},removeAttr:function(e,t){var n,r,i=0,o=t&&t.match(N);if(o&&1===e.nodeType)while(n=o[i++])r=Ne.propFix[n]||n,Ne.expr.match.bool.test(n)?l&&s||!ye.test(n)?e[r]=!1:e[Ne.camelCase("default-"+n)]=e[r]=!1:Ne.attr(e,n,""),e.removeAttribute(s?n:r)},attrHooks:{type:{set:function(e,t){if(!Ne.support.radioValue&&"radio"===t&&Ne.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},propFix:{for:"htmlFor",class:"className"},prop:function(e,t,n){var r,i,o,a=e.nodeType;if(e&&3!==a&&8!==a&&2!==a)return o=1!==a||!Ne.isXMLDoc(e),o&&(t=Ne.propFix[t]||t,i=Ne.propHooks[t]),n!==C?i&&"set"in i&&(r=i.set(e,n,t))!==C?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=Ne.find.attr(e,"tabindex");return t?parseInt(t,10):ge.test(e.nodeName)||me.test(e.nodeName)&&e.href?0:-1}}}}),fe={set:function(e,t,n){return t===!1?Ne.removeAttr(e,n):l&&s||!ye.test(n)?e.setAttribute(!s&&Ne.propFix[n]||n,n):e[Ne.camelCase("default-"+n)]=e[n]=!0,n}},Ne.each(Ne.expr.match.bool.source.match(/\w+/g),function(e,t){var o=Ne.expr.attrHandle[t]||Ne.find.attr;Ne.expr.attrHandle[t]=l&&s||!ye.test(t)?function(e,t,n){var r=Ne.expr.attrHandle[t],i=n?C:(Ne.expr.attrHandle[t]=C)!=o(e,t,n)?t.toLowerCase():null;return Ne.expr.attrHandle[t]=r,i}:function(e,t,n){return n?C:e[Ne.camelCase("default-"+t)]?t.toLowerCase():null}}),l&&s||(Ne.attrHooks.value={set:function(e,t,n){return Ne.nodeName(e,"input")?(e.defaultValue=t,C):a&&a.set(e,t,n)}}),s||(a={set:function(e,t,n){var r=e.getAttributeNode(n);return r||e.setAttributeNode(r=e.ownerDocument.createAttribute(n)),r.value=t+="","value"===n||t===e.getAttribute(n)?t:C}},Ne.expr.attrHandle.id=Ne.expr.attrHandle.name=Ne.expr.attrHandle.coords=function(e,t,n){var r;return n?C:(r=e.getAttributeNode(t))&&""!==r.value?r.value:null},Ne.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:C},set:a.set},Ne.attrHooks.contenteditable={set:function(e,t,n){a.set(e,""===t?!1:t,n)}},Ne.each(["width","height"],function(e,n){Ne.attrHooks[n]={set:function(e,t){return""===t?(e.setAttribute(n,"auto"),t):C}}})),Ne.support.hrefNormalized||Ne.each(["href","src"],function(e,t){Ne.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),Ne.support.style||(Ne.attrHooks.style={get:function(e){return e.style.cssText||C},set:function(e,t){return e.style.cssText=t+""}}),Ne.support.optSelected||(Ne.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),Ne.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){Ne.propFix[this.toLowerCase()]=this}),Ne.support.enctype||(Ne.propFix.enctype="encoding"),Ne.each(["radio","checkbox"],function(){Ne.valHooks[this]={set:function(e,t){return Ne.isArray(t)?e.checked=Ne.inArray(Ne(e).val(),t)>=0:C}},Ne.support.checkOn||(Ne.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var ve=/^(?:input|select|textarea)$/i,be=/^key/,xe=/^(?:mouse|contextmenu)|click/,we=/^(?:focusinfocus|focusoutblur)$/,Te=/^([^.]*)(?:\.(.+)|)$/;function i(){return!0}function u(){return!1}function Ce(){try{return g.activeElement}catch(e){}}Ne.event={global:{},add:function(e,t,n,r,i){var o,a,s,l,u,c,p,f,d,h,g,m=Ne._data(e);if(m){n.handler&&(l=n,n=l.handler,i=l.selector),n.guid||(n.guid=Ne.guid++),(a=m.events)||(a=m.events={}),(c=m.handle)||(c=m.handle=function(e){return typeof Ne===y||e&&Ne.event.triggered===e.type?C:Ne.event.dispatch.apply(c.elem,arguments)},c.elem=e),t=(t||"").match(N)||[""],s=t.length;while(s--)o=Te.exec(t[s])||[],d=g=o[1],h=(o[2]||"").split(".").sort(),d&&(u=Ne.event.special[d]||{},d=(i?u.delegateType:u.bindType)||d,u=Ne.event.special[d]||{},p=Ne.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&Ne.expr.match.needsContext.test(i),namespace:h.join(".")},l),(f=a[d])||(f=a[d]=[],f.delegateCount=0,u.setup&&u.setup.call(e,r,h,c)!==!1||(e.addEventListener?e.addEventListener(d,c,!1):e.attachEvent&&e.attachEvent("on"+d,c))),u.add&&(u.add.call(e,p),p.handler.guid||(p.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,p):f.push(p),Ne.event.global[d]=!0);e=null}},remove:function(e,t,n,r,i){var o,a,s,l,u,c,p,f,d,h,g,m=Ne.hasData(e)&&Ne._data(e);if(m&&(c=m.events)){t=(t||"").match(N)||[""],u=t.length;while(u--)if(s=Te.exec(t[u])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d){p=Ne.event.special[d]||{},d=(r?p.delegateType:p.bindType)||d,f=c[d]||[],s=s[2]&&RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=o=f.length;while(o--)a=f[o],!i&&g!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||r&&r!==a.selector&&("**"!==r||!a.selector)||(f.splice(o,1),a.selector&&f.delegateCount--,p.remove&&p.remove.call(e,a));l&&!f.length&&(p.teardown&&p.teardown.call(e,h,m.handle)!==!1||Ne.removeEvent(e,d,m.handle),delete c[d])}else for(d in c)Ne.event.remove(e,d+t[u],n,r,!0);Ne.isEmptyObject(c)&&(delete m.handle,Ne._removeData(e,"events"))}},trigger:function(e,t,n,r){var i,o,a,s,l,u,c,p=[n||g],f=m.call(e,"type")?e.type:e,d=m.call(e,"namespace")?e.namespace.split("."):[];if(a=u=n=n||g,3!==n.nodeType&&8!==n.nodeType&&!we.test(f+Ne.event.triggered)&&(f.indexOf(".")>=0&&(d=f.split("."),f=d.shift(),d.sort()),o=0>f.indexOf(":")&&"on"+f,e=e[Ne.expando]?e:new Ne.Event(f,"object"==typeof e&&e),e.isTrigger=r?2:3,e.namespace=d.join("."),e.namespace_re=e.namespace?RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=C,e.target||(e.target=n),t=null==t?[e]:Ne.makeArray(t,[e]),l=Ne.event.special[f]||{},r||!l.trigger||l.trigger.apply(n,t)!==!1)){if(!r&&!l.noBubble&&!Ne.isWindow(n)){for(s=l.delegateType||f,we.test(s+f)||(a=a.parentNode);a;a=a.parentNode)p.push(a),u=a;u===(n.ownerDocument||g)&&p.push(u.defaultView||u.parentWindow||h)}c=0;while((a=p[c++])&&!e.isPropagationStopped())e.type=c>1?s:l.bindType||f,i=(Ne._data(a,"events")||{})[e.type]&&Ne._data(a,"handle"),i&&i.apply(a,t),i=o&&a[o],i&&Ne.acceptData(a)&&i.apply&&i.apply(a,t)===!1&&e.preventDefault();if(e.type=f,!r&&!e.isDefaultPrevented()&&(!l._default||l._default.apply(p.pop(),t)===!1)&&Ne.acceptData(n)&&o&&n[f]&&!Ne.isWindow(n)){u=n[o],u&&(n[o]=null),Ne.event.triggered=f;try{n[f]()}catch(e){}Ne.event.triggered=C,u&&(n[o]=u)}return e.result}},dispatch:function(e){e=Ne.event.fix(e);var t,n,r,i,o,a=[],s=c.call(arguments),l=(Ne._data(this,"events")||{})[e.type]||[],u=Ne.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!u.preDispatch||u.preDispatch.call(this,e)!==!1){a=Ne.event.handlers.call(this,e,l),t=0;while((i=a[t++])&&!e.isPropagationStopped()){e.currentTarget=i.elem,o=0;while((r=i.handlers[o++])&&!e.isImmediatePropagationStopped())(!e.namespace_re||e.namespace_re.test(r.namespace))&&(e.handleObj=r,e.data=r.data,n=((Ne.event.special[r.origType]||{}).handle||r.handler).apply(i.elem,s),n!==C&&(e.result=n)===!1&&(e.preventDefault(),e.stopPropagation()))}return u.postDispatch&&u.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,o,a=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&(!e.button||"click"!==e.type))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(l.disabled!==!0||"click"!==e.type)){for(i=[],o=0;s>o;o++)r=t[o],n=r.selector+" ",i[n]===C&&(i[n]=r.needsContext?Ne(n,this).index(l)>=0:Ne.find(n,this,null,[l]).length),i[n]&&i.push(r);i.length&&a.push({elem:l,handlers:i})}return t.length>s&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[Ne.expando])return e;var t,n,r,i=e.type,o=e,a=this.fixHooks[i];a||(this.fixHooks[i]=a=xe.test(i)?this.mouseHooks:be.test(i)?this.keyHooks:{}),r=a.props?this.props.concat(a.props):this.props,e=new Ne.Event(o),t=r.length;while(t--)n=r[t],e[n]=o[n];return e.target||(e.target=o.srcElement||g),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,o=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(r=e.target.ownerDocument||g,i=r.documentElement,n=r.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||o===C||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==Ce()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){return this===Ce()&&this.blur?(this.blur(),!1):C},delegateType:"focusout"},click:{trigger:function(){return Ne.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):C},_default:function(e){return Ne.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){e.result!==C&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var i=Ne.extend(new Ne.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?Ne.event.trigger(i,null,t):Ne.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},Ne.removeEvent=g.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(typeof e[r]===y&&(e[r]=null),e.detachEvent(r,n))},Ne.Event=function(e,t){return this instanceof Ne.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.returnValue===!1||e.getPreventDefault&&e.getPreventDefault()?i:u):this.type=e,t&&Ne.extend(this,t),this.timeStamp=e&&e.timeStamp||Ne.now(),this[Ne.expando]=!0,C):new Ne.Event(e,t)},Ne.Event.prototype={isDefaultPrevented:u,isPropagationStopped:u,isImmediatePropagationStopped:u,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=i,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=i,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=i,this.stopPropagation()}},Ne.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,o){Ne.event.special[e]={delegateType:o,bindType:o,handle:function(e){var t,n=this,r=e.relatedTarget,i=e.handleObj;return(!r||r!==n&&!Ne.contains(n,r))&&(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=o),t}}}),Ne.support.submitBubbles||(Ne.event.special.submit={setup:function(){return Ne.nodeName(this,"form")?!1:(Ne.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=Ne.nodeName(t,"input")||Ne.nodeName(t,"button")?t.form:C;n&&!Ne._data(n,"submitBubbles")&&(Ne.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),Ne._data(n,"submitBubbles",!0))}),C)},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&Ne.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return Ne.nodeName(this,"form")?!1:(Ne.event.remove(this,"._submit"),C)}}),Ne.support.changeBubbles||(Ne.event.special.change={setup:function(){return ve.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(Ne.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),Ne.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),Ne.event.simulate("change",this,e,!0)})),!1):(Ne.event.add(this,"beforeactivate._change",function(e){var t=e.target;ve.test(t.nodeName)&&!Ne._data(t,"changeBubbles")&&(Ne.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||Ne.event.simulate("change",this.parentNode,e,!0)}),Ne._data(t,"changeBubbles",!0))}),C)},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):C},teardown:function(){return Ne.event.remove(this,"._change"),!ve.test(this.nodeName)}}),Ne.support.focusinBubbles||Ne.each({focus:"focusin",blur:"focusout"},function(e,t){var n=0,r=function(e){Ne.event.simulate(t,e.target,Ne.event.fix(e),!0)};Ne.event.special[t]={setup:function(){0===n++&&g.addEventListener(e,r,!0)},teardown:function(){0===--n&&g.removeEventListener(e,r,!0)}}}),Ne.fn.extend({on:function(e,t,n,r,i){var o,a;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=C);for(o in e)this.on(o,t,n,e[o],i);return this}if(null==n&&null==r?(r=t,n=t=C):null==r&&("string"==typeof t?(r=n,n=C):(r=n,n=t,t=C)),r===!1)r=u;else if(!r)return this;return 1===i&&(a=r,r=function(e){return Ne().off(e),a.apply(this,arguments)},r.guid=a.guid||(a.guid=Ne.guid++)),this.each(function(){Ne.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,Ne(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=C),n===!1&&(n=u),this.each(function(){Ne.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){Ne.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?Ne.event.trigger(e,t,n,!0):C}});var ke=/^.[^:#\[\.,]*$/,Ee=/^(?:parents|prev(?:Until|All))/,Se=Ne.expr.match.needsContext,Ae={children:!0,contents:!0,next:!0,prev:!0};Ne.fn.extend({find:function(e){var t,n=[],r=this,i=r.length;if("string"!=typeof e)return this.pushStack(Ne(e).filter(function(){for(t=0;i>t;t++)if(Ne.contains(r[t],this))return!0}));for(t=0;i>t;t++)Ne.find(e,r[t],n);return n=this.pushStack(i>1?Ne.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},has:function(e){var t,n=Ne(e,this),r=n.length;return this.filter(function(){for(t=0;r>t;t++)if(Ne.contains(this,n[t]))return!0})},not:function(e){return this.pushStack(De(this,e||[],!0))},filter:function(e){return this.pushStack(De(this,e||[],!1))},is:function(e){return!!De(this,"string"==typeof e&&Se.test(e)?Ne(e):e||[],!1).length},closest:function(e,t){var n,r=0,i=this.length,o=[],a=Se.test(e)||"string"!=typeof e?Ne(e,t||this.context):0;for(;i>r;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(11>n.nodeType&&(a?a.index(n)>-1:1===n.nodeType&&Ne.find.matchesSelector(n,e))){n=o.push(n);break}return this.pushStack(o.length>1?Ne.unique(o):o)},index:function(e){return e?"string"==typeof e?Ne.inArray(this[0],Ne(e)):Ne.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){var n="string"==typeof e?Ne(e,t):Ne.makeArray(e&&e.nodeType?[e]:e),r=Ne.merge(this.get(),n);return this.pushStack(Ne.unique(r))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}});function je(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}Ne.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return Ne.dir(e,"parentNode")},parentsUntil:function(e,t,n){return Ne.dir(e,"parentNode",n)},next:function(e){return je(e,"nextSibling")},prev:function(e){return je(e,"previousSibling")},nextAll:function(e){return Ne.dir(e,"nextSibling")},prevAll:function(e){return Ne.dir(e,"previousSibling")},nextUntil:function(e,t,n){return Ne.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return Ne.dir(e,"previousSibling",n)},siblings:function(e){return Ne.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return Ne.sibling(e.firstChild)},contents:function(e){return Ne.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:Ne.merge([],e.childNodes)}},function(r,i){Ne.fn[r]=function(e,t){var n=Ne.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=Ne.filter(t,n)),this.length>1&&(Ae[r]||(n=Ne.unique(n)),Ee.test(r)&&(n=n.reverse())),this.pushStack(n)}}),Ne.extend({filter:function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?Ne.find.matchesSelector(r,e)?[r]:[]:Ne.find.matches(e,Ne.grep(t,function(e){return 1===e.nodeType}))},dir:function(e,t,n){var r=[],i=e[t];while(i&&9!==i.nodeType&&(n===C||1!==i.nodeType||!Ne(i).is(n)))1===i.nodeType&&r.push(i),i=i[t];return r},sibling:function(e,t){var n=[];for(;e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}});function De(e,n,r){if(Ne.isFunction(n))return Ne.grep(e,function(e,t){return!!n.call(e,t,e)!==r});if(n.nodeType)return Ne.grep(e,function(e){return e===n!==r});if("string"==typeof n){if(ke.test(n))return Ne.filter(n,e,r);n=Ne.filter(n,e)}return Ne.grep(e,function(e){return Ne.inArray(e,n)>=0!==r})}function Le(e){var t=He.split("|"),n=e.createDocumentFragment();if(n.createElement)while(t.length)n.createElement(t.pop());return n}var He="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",qe=/ jQuery\d+="(?:null|\d+)"/g,_e=RegExp("<(?:"+He+")[\\s/>]","i"),Me=/^\s+/,Oe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Fe=/<([\w:]+)/,Be=/<tbody/i,Pe=/<|&#?\w+;/,Re=/<(?:script|style|link)/i,We=/^(?:checkbox|radio)$/i,$e=/checked\s*(?:[^=]|=\s*.checked.)/i,Ie=/^$|\/(?:java|ecma)script/i,ze=/^true\/(.*)/,Xe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,v={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:Ne.support.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Ue=Le(g),Ve=Ue.appendChild(g.createElement("div"));v.optgroup=v.option,v.tbody=v.tfoot=v.colgroup=v.caption=v.thead,v.th=v.td,Ne.fn.extend({text:function(e){return Ne.access(this,function(e){return e===C?Ne.text(this):this.empty().append((this[0]&&this[0].ownerDocument||g).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ye(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ye(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){var n,r=e?Ne.filter(e,this):this,i=0;for(;null!=(n=r[i]);i++)t||1!==n.nodeType||Ne.cleanData(b(n)),n.parentNode&&(t&&Ne.contains(n.ownerDocument,n)&&Qe(b(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){var e,t=0;for(;null!=(e=this[t]);t++){1===e.nodeType&&Ne.cleanData(b(e,!1));while(e.firstChild)e.removeChild(e.firstChild);e.options&&Ne.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return Ne.clone(this,e,t)})},html:function(e){return Ne.access(this,function(e){var t=this[0]||{},n=0,r=this.length;if(e===C)return 1===t.nodeType?t.innerHTML.replace(qe,""):C;if(!("string"!=typeof e||Re.test(e)||!Ne.support.htmlSerialize&&_e.test(e)||!Ne.support.leadingWhitespace&&Me.test(e)||v[(Fe.exec(e)||["",""])[1].toLowerCase()])){e=e.replace(Oe,"<$1></$2>");try{for(;r>n;n++)t=this[n]||{},1===t.nodeType&&(Ne.cleanData(b(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var r=Ne.map(this,function(e){return[e.nextSibling,e.parentNode]}),i=0;return this.domManip(arguments,function(e){var t=r[i++],n=r[i++];n&&(t&&t.parentNode!==n&&(t=this.nextSibling),Ne(this).remove(),n.insertBefore(e,t))},!0),i?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(n,r,i){n=W.apply([],n);var e,t,o,a,s,l,u=0,c=this.length,p=this,f=c-1,d=n[0],h=Ne.isFunction(d);if(h||!(1>=c||"string"!=typeof d||Ne.support.checkClone)&&$e.test(d))return this.each(function(e){var t=p.eq(e);h&&(n[0]=d.call(this,e,t.html())),t.domManip(n,r,i)});if(c&&(l=Ne.buildFragment(n,this[0].ownerDocument,!1,!i&&this),e=l.firstChild,1===l.childNodes.length&&(l=e),e)){for(a=Ne.map(b(l,"script"),Je),o=a.length;c>u;u++)t=l,u!==f&&(t=Ne.clone(t,!0,!0),o&&Ne.merge(a,b(t,"script"))),r.call(this[u],t,u);if(o)for(s=a[a.length-1].ownerDocument,Ne.map(a,Ge),u=0;o>u;u++)t=a[u],Ie.test(t.type||"")&&!Ne._data(t,"globalEval")&&Ne.contains(s,t)&&(t.src?Ne._evalUrl(t.src):Ne.globalEval((t.text||t.textContent||t.innerHTML||"").replace(Xe,"")));l=e=null}return this}});function Ye(e,t){return Ne.nodeName(e,"table")&&Ne.nodeName(1===t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function Je(e){return e.type=(null!==Ne.find.attr(e,"type"))+"/"+e.type,e}function Ge(e){var t=ze.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Qe(e,t){var n,r=0;for(;null!=(n=e[r]);r++)Ne._data(n,"globalEval",!t||Ne._data(t[r],"globalEval"))}function Ke(e,t){if(1===t.nodeType&&Ne.hasData(e)){var n,r,i,o=Ne._data(e),a=Ne._data(t,o),s=o.events;if(s){delete a.handle,a.events={};for(n in s)for(r=0,i=s[n].length;i>r;r++)Ne.event.add(t,n,s[n][r])}a.data&&(a.data=Ne.extend({},a.data))}}function Ze(e,t){var n,r,i;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!Ne.support.noCloneEvent&&t[Ne.expando]){i=Ne._data(t);for(r in i.events)Ne.removeEvent(t,r,i.handle);t.removeAttribute(Ne.expando)}"script"===n&&t.text!==e.text?(Je(t).text=e.text,Ge(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),Ne.support.html5Clone&&e.innerHTML&&!Ne.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&We.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}}Ne.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){Ne.fn[e]=function(e){var t,n=0,r=[],i=Ne(e),o=i.length-1;for(;o>=n;n++)t=n===o?this:this.clone(!0),Ne(i[n])[a](t),$.apply(r,t.get());return this.pushStack(r)}});function b(e,t){var n,r,i=0,o=typeof e.getElementsByTagName!==y?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==y?e.querySelectorAll(t||"*"):C;if(!o)for(o=[],n=e.childNodes||e;null!=(r=n[i]);i++)!t||Ne.nodeName(r,t)?o.push(r):Ne.merge(o,b(r,t));return t===C||t&&Ne.nodeName(e,t)?Ne.merge([e],o):o}function et(e){We.test(e.type)&&(e.defaultChecked=e.checked)}Ne.extend({clone:function(e,t,n){var r,i,o,a,s,l=Ne.contains(e.ownerDocument,e);if(Ne.support.html5Clone||Ne.isXMLDoc(e)||!_e.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Ve.innerHTML=e.outerHTML,Ve.removeChild(o=Ve.firstChild)),!(Ne.support.noCloneEvent&&Ne.support.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||Ne.isXMLDoc(e)))for(r=b(o),s=b(e),a=0;null!=(i=s[a]);++a)r[a]&&Ze(i,r[a]);if(t)if(n)for(s=s||b(e),r=r||b(o),a=0;null!=(i=s[a]);a++)Ke(i,r[a]);else Ke(e,o);return r=b(o,"script"),r.length>0&&Qe(r,!l&&b(e,"script")),r=s=i=null,o},buildFragment:function(e,t,n,r){var i,o,a,s,l,u,c,p=e.length,f=Le(t),d=[],h=0;for(;p>h;h++)if(o=e[h],o||0===o)if("object"===Ne.type(o))Ne.merge(d,o.nodeType?[o]:o);else if(Pe.test(o)){s=s||f.appendChild(t.createElement("div")),l=(Fe.exec(o)||["",""])[1].toLowerCase(),c=v[l]||v._default,s.innerHTML=c[1]+o.replace(Oe,"<$1></$2>")+c[2],i=c[0];while(i--)s=s.lastChild;if(!Ne.support.leadingWhitespace&&Me.test(o)&&d.push(t.createTextNode(Me.exec(o)[0])),!Ne.support.tbody){o="table"!==l||Be.test(o)?"<table>"!==c[1]||Be.test(o)?0:s:s.firstChild,i=o&&o.childNodes.length;while(i--)Ne.nodeName(u=o.childNodes[i],"tbody")&&!u.childNodes.length&&o.removeChild(u)}Ne.merge(d,s.childNodes),s.textContent="";while(s.firstChild)s.removeChild(s.firstChild);s=f.lastChild}else d.push(t.createTextNode(o));s&&f.removeChild(s),Ne.support.appendChecked||Ne.grep(b(d,"input"),et),h=0;while(o=d[h++])if((!r||-1===Ne.inArray(o,r))&&(a=Ne.contains(o.ownerDocument,o),s=b(f.appendChild(o),"script"),a&&Qe(s),n)){i=0;while(o=s[i++])Ie.test(o.type||"")&&n.push(o)}return s=null,f},cleanData:function(e,t){var n,r,i,o,a=0,s=Ne.expando,l=Ne.cache,u=Ne.support.deleteExpando,c=Ne.event.special;for(;null!=(n=e[a]);a++)if((t||Ne.acceptData(n))&&(i=n[s],o=i&&l[i])){if(o.events)for(r in o.events)c[r]?Ne.event.remove(n,r):Ne.removeEvent(n,r,o.handle);l[i]&&(delete l[i],u?delete n[s]:typeof n.removeAttribute!==y?n.removeAttribute(s):n[s]=null,p.push(i))}},_evalUrl:function(e){return Ne.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})}}),Ne.fn.extend({wrapAll:function(t){if(Ne.isFunction(t))return this.each(function(e){Ne(this).wrapAll(t.call(this,e))});if(this[0]){var e=Ne(t,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){var e=this;while(e.firstChild&&1===e.firstChild.nodeType)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(n){return Ne.isFunction(n)?this.each(function(e){Ne(this).wrapInner(n.call(this,e))}):this.each(function(){var e=Ne(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=Ne.isFunction(t);return this.each(function(e){Ne(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(){return this.parent().each(function(){Ne.nodeName(this,"body")||Ne(this).replaceWith(this.childNodes)}).end()}});var o,f,d,tt=/alpha\([^)]*\)/i,nt=/opacity\s*=\s*([^)]*)/,rt=/^(top|right|bottom|left)$/,it=/^(none|table(?!-c[ea]).+)/,ot=/^margin/,at=RegExp("^("+e+")(.*)$","i"),x=RegExp("^("+e+")(?!px)[a-z%]+$","i"),st=RegExp("^([+-])=("+e+")","i"),lt={BODY:"block"},ut={position:"absolute",visibility:"hidden",display:"block"},ct={letterSpacing:0,fontWeight:400},w=["Top","Right","Bottom","Left"],pt=["Webkit","O","Moz","ms"];function ft(e,t){if(t in e)return t;var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=pt.length;while(i--)if(t=pt[i]+n,t in e)return t;return r}function T(e,t){return e=t||e,"none"===Ne.css(e,"display")||!Ne.contains(e.ownerDocument,e)}function dt(e,t){var n,r,i,o=[],a=0,s=e.length;for(;s>a;a++)r=e[a],r.style&&(o[a]=Ne._data(r,"olddisplay"),n=r.style.display,t?(o[a]||"none"!==n||(r.style.display=""),""===r.style.display&&T(r)&&(o[a]=Ne._data(r,"olddisplay",yt(r.nodeName)))):o[a]||(i=T(r),(n&&"none"!==n||!i)&&Ne._data(r,"olddisplay",i?n:Ne.css(r,"display"))));for(a=0;s>a;a++)r=e[a],r.style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?o[a]||"":"none"));return e}Ne.fn.extend({css:function(e,t){return Ne.access(this,function(e,t,n){var r,i,o={},a=0;if(Ne.isArray(t)){for(i=f(e),r=t.length;r>a;a++)o[t[a]]=Ne.css(e,t[a],!1,i);return o}return n!==C?Ne.style(e,t,n):Ne.css(e,t)},e,t,arguments.length>1)},show:function(){return dt(this,!0)},hide:function(){return dt(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){T(this)?Ne(this).show():Ne(this).hide()})}}),Ne.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=d(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:Ne.support.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=Ne.camelCase(t),l=e.style;if(t=Ne.cssProps[s]||(Ne.cssProps[s]=ft(l,s)),a=Ne.cssHooks[t]||Ne.cssHooks[s],n===C)return a&&"get"in a&&(i=a.get(e,!1,r))!==C?i:l[t];if(o=typeof n,"string"===o&&(i=st.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(Ne.css(e,t)),o="number"),!(null==n||"number"===o&&isNaN(n)||("number"!==o||Ne.cssNumber[s]||(n+="px"),Ne.support.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&(n=a.set(e,n,r))===C)))try{l[t]=n}catch(e){}}},css:function(e,t,n,r){var i,o,a,s=Ne.camelCase(t);return t=Ne.cssProps[s]||(Ne.cssProps[s]=ft(e.style,s)),a=Ne.cssHooks[t]||Ne.cssHooks[s],a&&"get"in a&&(o=a.get(e,!0,n)),o===C&&(o=d(e,t,r)),"normal"===o&&t in ct&&(o=ct[t]),""===n||n?(i=parseFloat(o),n===!0||Ne.isNumeric(i)?i||0:o):o}}),h.getComputedStyle?(f=function(e){return h.getComputedStyle(e,null)},d=function(e,t,n){var r,i,o,a=n||f(e),s=a?a.getPropertyValue(t)||a[t]:C,l=e.style;return a&&(""!==s||Ne.contains(e.ownerDocument,e)||(s=Ne.style(e,t)),x.test(s)&&ot.test(t)&&(r=l.width,i=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=s,s=a.width,l.width=r,l.minWidth=i,l.maxWidth=o)),s}):g.documentElement.currentStyle&&(f=function(e){return e.currentStyle},d=function(e,t,n){var r,i,o,a=n||f(e),s=a?a[t]:C,l=e.style;return null==s&&l&&l[t]&&(s=l[t]),x.test(s)&&!rt.test(t)&&(r=l.left,i=e.runtimeStyle,o=i&&i.left,o&&(i.left=e.currentStyle.left),l.left="fontSize"===t?"1em":s,s=l.pixelLeft+"px",l.left=r,o&&(i.left=o)),""===s?"auto":s});function ht(e,t,n){var r=at.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function gt(e,t,n,r,i){var o=n===(r?"border":"content")?4:"width"===t?1:0,a=0;for(;4>o;o+=2)"margin"===n&&(a+=Ne.css(e,n+w[o],!0,i)),r?("content"===n&&(a-=Ne.css(e,"padding"+w[o],!0,i)),"margin"!==n&&(a-=Ne.css(e,"border"+w[o]+"Width",!0,i))):(a+=Ne.css(e,"padding"+w[o],!0,i),"padding"!==n&&(a+=Ne.css(e,"border"+w[o]+"Width",!0,i)));return a}function mt(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,o=f(e),a=Ne.support.boxSizing&&"border-box"===Ne.css(e,"boxSizing",!1,o);if(0>=i||null==i){if(i=d(e,t,o),(0>i||null==i)&&(i=e.style[t]),x.test(i))return i;r=a&&(Ne.support.boxSizingReliable||i===e.style[t]),i=parseFloat(i)||0}return i+gt(e,t,n||(a?"border":"content"),r,o)+"px"}function yt(e){var t=g,n=lt[e];return n||(n=vt(e,t),"none"!==n&&n||(o=(o||Ne("<iframe frameborder='0' width='0' height='0'/>").css("cssText","display:block !important")).appendTo(t.documentElement),t=(o[0].contentWindow||o[0].contentDocument).document,t.write("<!doctype html><html><body>"),t.close(),n=vt(e,t),o.detach()),lt[e]=n),n}function vt(e,t){var n=Ne(t.createElement(e)).appendTo(t.body),r=Ne.css(n[0],"display");return n.remove(),r}Ne.each(["height","width"],function(e,i){Ne.cssHooks[i]={get:function(e,t,n){return t?0===e.offsetWidth&&it.test(Ne.css(e,"display"))?Ne.swap(e,ut,function(){return mt(e,i,n)}):mt(e,i,n):C},set:function(e,t,n){var r=n&&f(e);return ht(e,t,n?gt(e,i,n,Ne.support.boxSizing&&"border-box"===Ne.css(e,"boxSizing",!1,r),r):0)}}}),Ne.support.opacity||(Ne.cssHooks.opacity={get:function(e,t){return nt.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,r=e.currentStyle,i=Ne.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=r&&r.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===Ne.trim(o.replace(tt,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||r&&!r.filter)||(n.filter=tt.test(o)?o.replace(tt,i):o+" "+i)}}),Ne(function(){Ne.support.reliableMarginRight||(Ne.cssHooks.marginRight={get:function(e,t){return t?Ne.swap(e,{display:"inline-block"},d,[e,"marginRight"]):C}}),!Ne.support.pixelPosition&&Ne.fn.position&&Ne.each(["top","left"],function(e,n){Ne.cssHooks[n]={get:function(e,t){return t?(t=d(e,n),x.test(t)?Ne(e).position()[n]+"px":t):C}}})}),Ne.expr&&Ne.expr.filters&&(Ne.expr.filters.hidden=function(e){return 0>=e.offsetWidth&&0>=e.offsetHeight||!Ne.support.reliableHiddenOffsets&&"none"===(e.style&&e.style.display||Ne.css(e,"display"))},Ne.expr.filters.visible=function(e){return!Ne.expr.filters.hidden(e)}),Ne.each({margin:"",padding:"",border:"Width"},function(i,o){Ne.cssHooks[i+o]={expand:function(e){var t=0,n={},r="string"==typeof e?e.split(" "):[e];for(;4>t;t++)n[i+w[t]+o]=r[t]||r[t-2]||r[0];return n}},ot.test(i)||(Ne.cssHooks[i+o].set=ht)});var bt=/%20/g,xt=/\[\]$/,wt=/\r?\n/g,Tt=/^(?:submit|button|image|reset|file)$/i,Ct=/^(?:input|select|textarea|keygen)/i;Ne.fn.extend({serialize:function(){return Ne.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=Ne.prop(this,"elements");return e?Ne.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!Ne(this).is(":disabled")&&Ct.test(this.nodeName)&&!Tt.test(e)&&(this.checked||!We.test(e))}).map(function(e,t){var n=Ne(this).val();return null==n?null:Ne.isArray(n)?Ne.map(n,function(e){return{name:t.name,value:e.replace(wt,"\r\n")}}):{name:t.name,value:n.replace(wt,"\r\n")}}).get()}}),Ne.param=function(e,t){var n,r=[],i=function(e,t){t=Ne.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(t===C&&(t=Ne.ajaxSettings&&Ne.ajaxSettings.traditional),Ne.isArray(e)||e.jquery&&!Ne.isPlainObject(e))Ne.each(e,function(){i(this.name,this.value)});else for(n in e)Nt(n,e[n],t,i);return r.join("&").replace(bt,"+")};function Nt(n,e,r,i){var t;if(Ne.isArray(e))Ne.each(e,function(e,t){r||xt.test(n)?i(n,t):Nt(n+"["+("object"==typeof t?e:"")+"]",t,r,i)});else if(r||"object"!==Ne.type(e))i(n,e);else for(t in e)Nt(n+"["+t+"]",e[t],r,i)}Ne.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,n){Ne.fn[n]=function(e,t){return arguments.length>0?this.on(n,null,e,t):this.trigger(n)}}),Ne.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var k,E,kt=Ne.now(),Et=/\?/,St=/#.*$/,At=/([?&])_=[^&]*/,jt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Dt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Lt=/^(?:GET|HEAD)$/,Ht=/^\/\//,qt=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,_t=Ne.fn.load,Mt={},Ot={},Ft="*/".concat("*");try{E=O.href}catch(e){E=g.createElement("a"),E.href="",E=E.href}k=qt.exec(E.toLowerCase())||[];function Bt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(N)||[];if(Ne.isFunction(t))while(n=i[r++])"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Pt(t,i,o,a){var s={},l=t===Ot;function u(e){var r;return s[e]=!0,Ne.each(t[e]||[],function(e,t){var n=t(i,o,a);return"string"!=typeof n||l||s[n]?l?!(r=n):C:(i.dataTypes.unshift(n),u(n),!1)}),r}return u(i.dataTypes[0])||!s["*"]&&u("*")}function Rt(e,t){var n,r,i=Ne.ajaxSettings.flatOptions||{};for(r in t)t[r]!==C&&((i[r]?e:n||(n={}))[r]=t[r]);return n&&Ne.extend(!0,e,n),e}Ne.fn.load=function(e,t,n){if("string"!=typeof e&&_t)return _t.apply(this,arguments);var r,i,o,a=this,s=e.indexOf(" ");return s>=0&&(r=e.slice(s,e.length),e=e.slice(0,s)),Ne.isFunction(t)?(n=t,t=C):t&&"object"==typeof t&&(o="POST"),a.length>0&&Ne.ajax({url:e,type:o,dataType:"html",data:t}).done(function(e){i=arguments,a.html(r?Ne("<div>").append(Ne.parseHTML(e)).find(r):e)}).complete(n&&function(e,t){a.each(n,i||[e.responseText,t,e])}),this},Ne.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){Ne.fn[t]=function(e){return this.on(t,e)}}),Ne.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:E,type:"GET",isLocal:Dt.test(k[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ft,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":Ne.parseJSON,"text xml":Ne.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Rt(Rt(e,Ne.ajaxSettings),t):Rt(Ne.ajaxSettings,e)},ajaxPrefilter:Bt(Mt),ajaxTransport:Bt(Ot),ajax:function(e,t){"object"==typeof e&&(t=e,e=C),t=t||{};var n,r,c,p,f,d,h,i,g=Ne.ajaxSetup({},t),m=g.context||g,y=g.context&&(m.nodeType||m.jquery)?Ne(m):Ne.event,v=Ne.Deferred(),b=Ne.Callbacks("once memory"),x=g.statusCode||{},o={},a={},w=0,s="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(2===w){if(!i){i={};while(t=jt.exec(p))i[t[1].toLowerCase()]=t[2]}t=i[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===w?p:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return w||(e=a[n]=a[n]||e,o[e]=t),this},overrideMimeType:function(e){return w||(g.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>w)for(t in e)x[t]=[x[t],e[t]];else T.always(e[T.status]);return this},abort:function(e){var t=e||s;return h&&h.abort(t),l(0,t),this}};if(v.promise(T).complete=b.add,T.success=T.done,T.error=T.fail,g.url=((e||g.url||E)+"").replace(St,"").replace(Ht,k[1]+"//"),g.type=t.method||t.type||g.method||g.type,g.dataTypes=Ne.trim(g.dataType||"*").toLowerCase().match(N)||[""],null==g.crossDomain&&(n=qt.exec(g.url.toLowerCase()),g.crossDomain=!(!n||n[1]===k[1]&&n[2]===k[2]&&(n[3]||("http:"===n[1]?"80":"443"))===(k[3]||("http:"===k[1]?"80":"443")))),g.data&&g.processData&&"string"!=typeof g.data&&(g.data=Ne.param(g.data,g.traditional)),Pt(Mt,g,t,T),2===w)return T;d=g.global,d&&0===Ne.active++&&Ne.event.trigger("ajaxStart"),g.type=g.type.toUpperCase(),g.hasContent=!Lt.test(g.type),c=g.url,g.hasContent||(g.data&&(c=g.url+=(Et.test(c)?"&":"?")+g.data,delete g.data),g.cache===!1&&(g.url=At.test(c)?c.replace(At,"$1_="+kt++):c+(Et.test(c)?"&":"?")+"_="+kt++)),g.ifModified&&(Ne.lastModified[c]&&T.setRequestHeader("If-Modified-Since",Ne.lastModified[c]),Ne.etag[c]&&T.setRequestHeader("If-None-Match",Ne.etag[c])),(g.data&&g.hasContent&&g.contentType!==!1||t.contentType)&&T.setRequestHeader("Content-Type",g.contentType),T.setRequestHeader("Accept",g.dataTypes[0]&&g.accepts[g.dataTypes[0]]?g.accepts[g.dataTypes[0]]+("*"!==g.dataTypes[0]?", "+Ft+"; q=0.01":""):g.accepts["*"]);for(r in g.headers)T.setRequestHeader(r,g.headers[r]);if(g.beforeSend&&(g.beforeSend.call(m,T,g)===!1||2===w))return T.abort();s="abort";for(r in{success:1,error:1,complete:1})T[r](g[r]);if(h=Pt(Ot,g,t,T)){T.readyState=1,d&&y.trigger("ajaxSend",[T,g]),g.async&&g.timeout>0&&(f=setTimeout(function(){T.abort("timeout")},g.timeout));try{w=1,h.send(o,l)}catch(e){if(!(2>w))throw e;l(-1,e)}}else l(-1,"No Transport");function l(e,t,n,r){var i,o,a,s,l,u=t;2!==w&&(w=2,f&&clearTimeout(f),h=C,p=r||"",T.readyState=e>0?4:0,i=e>=200&&300>e||304===e,n&&(s=Wt(g,T,n)),s=$t(g,s,T,i),i?(g.ifModified&&(l=T.getResponseHeader("Last-Modified"),l&&(Ne.lastModified[c]=l),l=T.getResponseHeader("etag"),l&&(Ne.etag[c]=l)),204===e||"HEAD"===g.type?u="nocontent":304===e?u="notmodified":(u=s.state,o=s.data,a=s.error,i=!a)):(a=u,(e||!u)&&(u="error",0>e&&(e=0))),T.status=e,T.statusText=(t||u)+"",i?v.resolveWith(m,[o,u,T]):v.rejectWith(m,[T,u,a]),T.statusCode(x),x=C,d&&y.trigger(i?"ajaxSuccess":"ajaxError",[T,g,i?o:a]),b.fireWith(m,[T,u]),d&&(y.trigger("ajaxComplete",[T,g]),--Ne.active||Ne.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return Ne.get(e,t,n,"json")},getScript:function(e,t){return Ne.get(e,C,t,"script")}}),Ne.each(["get","post"],function(e,i){Ne[i]=function(e,t,n,r){return Ne.isFunction(t)&&(r=r||n,n=t,t=C),Ne.ajax({url:e,type:i,dataType:r,data:t,success:n})}});function Wt(e,t,n){var r,i,o,a,s=e.contents,l=e.dataTypes;while("*"===l[0])l.shift(),i===C&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(a in s)if(s[a]&&s[a].test(i)){l.unshift(a);break}if(l[0]in n)o=l[0];else{for(a in n){if(!l[0]||e.converters[a+" "+l[0]]){o=a;break}r||(r=a)}o=o||r}return o?(o!==l[0]&&l.unshift(o),n[o]):C}function $t(e,t,n,r){var i,o,a,s,l,u={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(a=u[l+" "+o]||u["* "+o],!a)for(i in u)if(s=i.split(" "),s[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){a===!0?a=u[i]:u[i]!==!0&&(o=s[0],c.unshift(s[1]));break}if(a!==!0)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}Ne.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return Ne.globalEval(e),e}}}),Ne.ajaxPrefilter("script",function(e){e.cache===C&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),Ne.ajaxTransport("script",function(t){if(t.crossDomain){var r,i=g.head||Ne("head")[0]||g.documentElement;return{send:function(e,n){r=g.createElement("script"),r.async=!0,t.scriptCharset&&(r.charset=t.scriptCharset),r.src=t.url,r.onload=r.onreadystatechange=function(e,t){(t||!r.readyState||/loaded|complete/.test(r.readyState))&&(r.onload=r.onreadystatechange=null,r.parentNode&&r.parentNode.removeChild(r),r=null,t||n(200,"success"))},i.insertBefore(r,i.firstChild)},abort:function(){r&&r.onload(C,!0)}}}});var It=[],zt=/(=)\?(?=&|$)|\?\?/;Ne.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=It.pop()||Ne.expando+"_"+kt++;return this[e]=!0,e}}),Ne.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=e.jsonp!==!1&&(zt.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&zt.test(e.data)&&"data");return a||"jsonp"===e.dataTypes[0]?(r=e.jsonpCallback=Ne.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(zt,"$1"+r):e.jsonp!==!1&&(e.url+=(Et.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||Ne.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=h[r],h[r]=function(){o=arguments},n.always(function(){h[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,It.push(r)),o&&Ne.isFunction(i)&&i(o[0]),o=i=C}),"script"):C});var S,A,Xt=0,Ut=h.ActiveXObject&&function(){var e;for(e in S)S[e](C,!0)};function Vt(){try{return new h.XMLHttpRequest}catch(e){}}function Yt(){try{return new h.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}Ne.ajaxSettings.xhr=h.ActiveXObject?function(){return!this.isLocal&&Vt()||Yt()}:Vt,A=Ne.ajaxSettings.xhr(),Ne.support.cors=!!A&&"withCredentials"in A,A=Ne.support.ajax=!!A,A&&Ne.ajaxTransport(function(u){if(!u.crossDomain||Ne.support.cors){var c;return{send:function(e,a){var s,t,l=u.xhr();if(u.username?l.open(u.type,u.url,u.async,u.username,u.password):l.open(u.type,u.url,u.async),u.xhrFields)for(t in u.xhrFields)l[t]=u.xhrFields[t];u.mimeType&&l.overrideMimeType&&l.overrideMimeType(u.mimeType),u.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest");try{for(t in e)l.setRequestHeader(t,e[t])}catch(e){}l.send(u.hasContent&&u.data||null),c=function(e,t){var n,r,i,o;try{if(c&&(t||4===l.readyState))if(c=C,s&&(l.onreadystatechange=Ne.noop,Ut&&delete S[s]),t)4!==l.readyState&&l.abort();else{o={},n=l.status,r=l.getAllResponseHeaders(),"string"==typeof l.responseText&&(o.text=l.responseText);try{i=l.statusText}catch(e){i=""}n||!u.isLocal||u.crossDomain?1223===n&&(n=204):n=o.text?200:404}}catch(e){t||a(-1,e)}o&&a(n,i,o,r)},u.async?4===l.readyState?setTimeout(c):(s=++Xt,Ut&&(S||(S={},Ne(h).unload(Ut)),S[s]=c),l.onreadystatechange=c):c()},abort:function(){c&&c(C,!0)}}}});var j,D,Jt=/^(?:toggle|show|hide)$/,Gt=RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),Qt=/queueHooks$/,L=[nn],H={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),i=Gt.exec(t),o=i&&i[3]||(Ne.cssNumber[e]?"":"px"),a=(Ne.cssNumber[e]||"px"!==o&&+r)&&Gt.exec(Ne.css(n.elem,e)),s=1,l=20;if(a&&a[3]!==o){o=o||a[3],i=i||[],a=+r||1;do{s=s||".5",a/=s,Ne.style(n.elem,e,a+o)}while(s!==(s=n.cur()/r)&&1!==s&&--l)}return i&&(a=n.start=+a||+r||0,n.unit=o,n.end=i[1]?a+(i[1]+1)*i[2]:+i[2]),n}]};function Kt(){return setTimeout(function(){j=C}),j=Ne.now()}function Zt(e,t,n){var r,i=(H[t]||[]).concat(H["*"]),o=0,a=i.length;for(;a>o;o++)if(r=i[o].call(n,t,e))return r}function en(a,e,t){var n,s,r=0,i=L.length,l=Ne.Deferred().always(function(){delete o.elem}),o=function(){if(s)return!1;var e=j||Kt(),t=Math.max(0,u.startTime+u.duration-e),n=t/u.duration||0,r=1-n,i=0,o=u.tweens.length;for(;o>i;i++)u.tweens[i].run(r);return l.notifyWith(a,[u,r,t]),1>r&&o?t:(l.resolveWith(a,[u]),!1)},u=l.promise({elem:a,props:Ne.extend({},e),opts:Ne.extend(!0,{specialEasing:{}},t),originalProperties:e,originalOptions:t,startTime:j||Kt(),duration:t.duration,tweens:[],createTween:function(e,t){var n=Ne.Tween(a,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);return u.tweens.push(n),n},stop:function(e){var t=0,n=e?u.tweens.length:0;if(s)return this;for(s=!0;n>t;t++)u.tweens[t].run(1);return e?l.resolveWith(a,[u,e]):l.rejectWith(a,[u,e]),this}}),c=u.props;for(tn(c,u.opts.specialEasing);i>r;r++)if(n=L[r].call(u,a,c,u.opts))return n;return Ne.map(c,Zt,u),Ne.isFunction(u.opts.start)&&u.opts.start.call(a,u),Ne.fx.timer(Ne.extend(o,{elem:a,anim:u,queue:u.opts.queue})),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always)}function tn(e,t){var n,r,i,o,a;for(n in e)if(r=Ne.camelCase(n),i=t[r],o=e[n],Ne.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),a=Ne.cssHooks[r],a&&"expand"in a){o=a.expand(o),delete e[r];for(n in o)n in e||(e[n]=o[n],t[n]=i)}else t[r]=i}Ne.Animation=Ne.extend(en,{tweener:function(e,t){Ne.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");var n,r=0,i=e.length;for(;i>r;r++)n=e[r],H[n]=H[n]||[],H[n].unshift(t)},prefilter:function(e,t){t?L.unshift(e):L.push(e)}});function nn(t,e,n){var r,i,o,a,s,l,u=this,c={},p=t.style,f=t.nodeType&&T(t),d=Ne._data(t,"fxshow");n.queue||(s=Ne._queueHooks(t,"fx"),null==s.unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,u.always(function(){u.always(function(){s.unqueued--,Ne.queue(t,"fx").length||s.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],"inline"===Ne.css(t,"display")&&"none"===Ne.css(t,"float")&&(Ne.support.inlineBlockNeedsLayout&&"inline"!==yt(t.nodeName)?p.zoom=1:p.display="inline-block")),n.overflow&&(p.overflow="hidden",Ne.support.shrinkWrapBlocks||u.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(r in e)if(i=e[r],Jt.exec(i)){if(delete e[r],o=o||"toggle"===i,i===(f?"hide":"show"))continue;c[r]=d&&d[r]||Ne.style(t,r)}if(!Ne.isEmptyObject(c)){d?"hidden"in d&&(f=d.hidden):d=Ne._data(t,"fxshow",{}),o&&(d.hidden=!f),f?Ne(t).show():u.done(function(){Ne(t).hide()}),u.done(function(){var e;Ne._removeData(t,"fxshow");for(e in c)Ne.style(t,e,c[e])});for(r in c)a=Zt(f?d[r]:0,r,u),r in d||(d[r]=a.start,f&&(a.end=a.start,a.start="width"===r||"height"===r?1:0))}}function q(e,t,n,r,i){return new q.prototype.init(e,t,n,r,i)}Ne.Tween=q,q.prototype={constructor:q,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(Ne.cssNumber[n]?"":"px")},cur:function(){var e=q.propHooks[this.prop];return e&&e.get?e.get(this):q.propHooks._default.get(this)},run:function(e){var t,n=q.propHooks[this.prop];return this.pos=t=this.options.duration?Ne.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):q.propHooks._default.set(this),this}},q.prototype.init.prototype=q.prototype,q.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=Ne.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){Ne.fx.step[e.prop]?Ne.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[Ne.cssProps[e.prop]]||Ne.cssHooks[e.prop])?Ne.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},q.propHooks.scrollTop=q.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},Ne.each(["toggle","show","hide"],function(e,r){var i=Ne.fn[r];Ne.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(_(r,!0),e,t,n)}}),Ne.fn.extend({fadeTo:function(e,t,n,r){return this.filter(T).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=Ne.isEmptyObject(t),o=Ne.speed(e,n,r),a=function(){var e=en(this,Ne.extend({},t),o);(i||Ne._data(this,"finish"))&&e.stop(!0)};return a.finish=a,i||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(i,e,o){var a=function(e){var t=e.stop;delete e.stop,t(o)};return"string"!=typeof i&&(o=e,e=i,i=C),e&&i!==!1&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=Ne.timers,r=Ne._data(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&Qt.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));(e||!o)&&Ne.dequeue(this,i)})},finish:function(a){return a!==!1&&(a=a||"fx"),this.each(function(){var e,t=Ne._data(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=Ne.timers,o=n?n.length:0;for(t.finish=!0,Ne.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;o>e;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}});function _(e,t){var n,r={height:e},i=0;for(t=t?1:0;4>i;i+=2-t)n=w[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}Ne.each({slideDown:_("show"),slideUp:_("hide"),slideToggle:_("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){Ne.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),Ne.speed=function(e,t,n){var r=e&&"object"==typeof e?Ne.extend({},e):{complete:n||!n&&t||Ne.isFunction(e)&&e,duration:e,easing:n&&t||t&&!Ne.isFunction(t)&&t};return r.duration=Ne.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in Ne.fx.speeds?Ne.fx.speeds[r.duration]:Ne.fx.speeds._default,(null==r.queue||r.queue===!0)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){Ne.isFunction(r.old)&&r.old.call(this),r.queue&&Ne.dequeue(this,r.queue)},r},Ne.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},Ne.timers=[],Ne.fx=q.prototype.init,Ne.fx.tick=function(){var e,t=Ne.timers,n=0;for(j=Ne.now();t.length>n;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||Ne.fx.stop(),j=C},Ne.fx.timer=function(e){e()&&Ne.timers.push(e)&&Ne.fx.start()},Ne.fx.interval=13,Ne.fx.start=function(){D||(D=setInterval(Ne.fx.tick,Ne.fx.interval))},Ne.fx.stop=function(){clearInterval(D),D=null},Ne.fx.speeds={slow:600,fast:200,_default:400},Ne.fx.step={},Ne.expr&&Ne.expr.filters&&(Ne.expr.filters.animated=function(t){return Ne.grep(Ne.timers,function(e){return t===e.elem}).length}),Ne.fn.offset=function(t){if(arguments.length)return t===C?this:this.each(function(e){Ne.offset.setOffset(this,t,e)});var e,n,r={top:0,left:0},i=this[0],o=i&&i.ownerDocument;if(o)return e=o.documentElement,Ne.contains(e,i)?(typeof i.getBoundingClientRect!==y&&(r=i.getBoundingClientRect()),n=rn(o),{top:r.top+(n.pageYOffset||e.scrollTop)-(e.clientTop||0),left:r.left+(n.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):r},Ne.offset={setOffset:function(e,t,n){var r=Ne.css(e,"position");"static"===r&&(e.style.position="relative");var i=Ne(e),o=i.offset(),a=Ne.css(e,"top"),s=Ne.css(e,"left"),l=("absolute"===r||"fixed"===r)&&Ne.inArray("auto",[a,s])>-1,u={},c={},p,f;l?(c=i.position(),p=c.top,f=c.left):(p=parseFloat(a)||0,f=parseFloat(s)||0),Ne.isFunction(t)&&(t=t.call(e,n,o)),null!=t.top&&(u.top=t.top-o.top+p),null!=t.left&&(u.left=t.left-o.left+f),"using"in t?t.using.call(e,u):i.css(u)}},Ne.fn.extend({position:function(){if(this[0]){var e,t,n={top:0,left:0},r=this[0];return"fixed"===Ne.css(r,"position")?t=r.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),Ne.nodeName(e[0],"html")||(n=e.offset()),n.top+=Ne.css(e[0],"borderTopWidth",!0),n.left+=Ne.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-Ne.css(r,"marginTop",!0),left:t.left-n.left-Ne.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent||F;while(e&&!Ne.nodeName(e,"html")&&"static"===Ne.css(e,"position"))e=e.offsetParent;return e||F})}}),Ne.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o=/Y/.test(i);Ne.fn[t]=function(e){return Ne.access(this,function(e,t,n){var r=rn(e);return n===C?r?i in r?r[i]:r.document.documentElement[t]:e[t]:(r?r.scrollTo(o?Ne(r).scrollLeft():n,o?n:Ne(r).scrollTop()):e[t]=n,C)},t,e,arguments.length,null)}});function rn(e){return Ne.isWindow(e)?e:9===e.nodeType?e.defaultView||e.parentWindow:!1}Ne.each({Height:"height",Width:"width"},function(o,a){Ne.each({padding:"inner"+o,content:a,"":"outer"+o},function(r,e){Ne.fn[e]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(e===!0||t===!0?"margin":"border");return Ne.access(this,function(e,t,n){var r;return Ne.isWindow(e)?e.document.documentElement["client"+o]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+o],r["scroll"+o],e.body["offset"+o],r["offset"+o],r["client"+o])):n===C?Ne.css(e,t,i):Ne.style(e,t,n,i)},a,n?e:C,n,null)}})}),Ne.fn.size=function(){return this.length},Ne.fn.andSelf=Ne.fn.addBack,"object"==typeof module&&module&&"object"==typeof module.exports?module.exports=Ne:(h.jQuery=h.$=Ne,"function"==typeof define&&define.amd&&define("jquery",[],function(){return Ne}))})(window);