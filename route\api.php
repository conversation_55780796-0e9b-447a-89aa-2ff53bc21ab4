<?php
// +----------------------------------------------------------------------
// | CRM API路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API路由组
Route::group('api', function () {
    
    // 认证相关路由（无需token验证）
    Route::group('auth', function () {
        Route::post('login', 'api/Auth/login');           // 用户登录
        Route::post('refresh', 'api/Auth/refresh');       // 刷新Token
        Route::post('logout', 'api/Auth/logout');         // 用户登出
        Route::get('userinfo', 'api/Auth/userinfo');      // 获取用户信息
        Route::post('changePassword', 'api/Auth/changePassword'); // 修改密码
    });
    
    // 客户管理相关路由
    Route::group('customer', function () {
        Route::get('list', 'api/Customer/list');          // 获取客户列表
        Route::get('detail/:id', 'api/Customer/detail');  // 获取客户详情
        Route::get('statistics', 'api/Customer/statistics'); // 获取客户统计
    });
    
    // 通话统计相关路由
    Route::group('call', function () {
        Route::post('statistics', 'api/CallStatistics/upload');        // 上传统计数据
        Route::get('statistics', 'api/CallStatistics/index');          // 获取统计数据
        Route::get('statistics/summary', 'api/CallStatistics/summary'); // 获取统计汇总
        Route::delete('statistics/:date', 'api/CallStatistics/delete'); // 删除统计数据
        Route::post('statistics/batch', 'api/CallStatistics/batchUpload'); // 批量上传
    });
    
})->allowCrossDomain([
    'Access-Control-Allow-Origin'        => '*',
    'Access-Control-Allow-Methods'       => 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers'       => 'Content-Type,Authorization,X-Requested-With',
    'Access-Control-Allow-Credentials'   => 'true'
]);

// 处理OPTIONS预检请求
Route::options('api/:path', function() {
    return response('', 200, [
        'Access-Control-Allow-Origin' => '*',
        'Access-Control-Allow-Methods' => 'GET,POST,PUT,DELETE,OPTIONS',
        'Access-Control-Allow-Headers' => 'Content-Type,Authorization,X-Requested-With',
    ]);
})->pattern(['path' => '.*']);
