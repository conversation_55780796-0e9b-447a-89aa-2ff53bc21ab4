{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>登录插件</legend>
    </fieldset>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="action">
    {{# if(d.status==0){ }}
    <a class="layui-btn layui-btn-xs" lay-event="install">一键安装</a>
    {{# }else{  }}
    <a class="layui-btn layui-btn-xs"  href="{:url('set')}?type={{d.type}}&code={{d.code}}" title="配置">配置</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="install">卸载</a>
    {{# } }}
</script>
<script>
    layui.use('table', function() {
        var table = layui.table, $ = layui.jquery;
        var tableIn = table.render({
            id: 'login',
            elem: '#list',
            url: '{:url("index")}',
            method: 'post',
            cols: [[
                {field: 'name', title: '插件名称', width: 120,fixed: true},
                {field: 'type', align: 'center', title: '类别', width: 120},
                {field: 'code', align: 'center', title: 'CODE', width: 120},
                {field: 'desc', title: '插件描述', width:400},
                {width: 160, align: 'center', toolbar: '#action'}
            ]]
        });
        table.on('tool(list)', function(obj) {
            var data = obj.data;
            if (obj.event === 'install') {
                loading = layer.load(1, {shade: [0.1, '#fff']});
                var install = data.status===1?0:1;
                var type = data.type,code=data.code;
                $.post('{:url("install")}', {'type':type,code:code,install:install}, function (res) {
                    layer.close(loading);
                    if (res.code == 1) {
                        layer.msg(res.msg,{time:2000,icon:1});
                        tableIn.reload();
                    } else {
                        layer.msg(res.msg, {time: 1000, icon: 2});
                        return false;
                    }
                })
            }
        })
    });
</script>