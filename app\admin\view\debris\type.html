{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>碎片分类管理</legend>
    </fieldset>
    <div class="demoTable">
        <div class="layui-inline">
            <input class="layui-input" name="key" id="key" placeholder="{:lang('Please enter')}关键字">
        </div>
        <button class="layui-btn" id="search" data-type="reload">{:lang('search')}</button>
        <a href="{:url('type')}" class="layui-btn">显示全部</a>
        <a href="{:url('addType')}" class="layui-btn" style="float:right;"><i class="fa fa-plus" aria-hidden="true"></i>{:lang('add')}碎片分类</a>
        <div style="clear: both;"></div>
    </div>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="order">
    <input name="{{d.id}}" data-id="{{d.id}}" class="list_order layui-input" value=" {{d.sort}}" size="10"/>
</script>
<script type="text/html" id="action">
    <a href="{:url('editType')}?id={{d.id}}" class="layui-btn layui-btn-xs">{:fy('Edit')}</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">{:fy('Delete')}</a>
</script>
<script>
    layui.use('table', function() {
        var table = layui.table, $ = layui.jquery;
        var tableIn = table.render({
            id: 'type',
            elem: '#list',
            url: '{:url("type")}',
            method: 'post',
            cols: [[
                {field: 'id', title: '{:lang("id")}', width: 80, fixed: true, sort: true},
                {field: 'title', title: '碎片{:fy('Category Name')}', width: 400},
                {field: 'sort', align: 'center', title: '{:lang("order")}', width: 120, templet: '#order', sort: true},
                {width: 160, align: 'center', toolbar: '#action'}
            ]]
        });
        //搜索
        $('#search').on('click', function () {
            var key = $('#key').val();
            if ($.trim(key) === '') {
                layer.msg('{:lang("pleaseEnter")}关键字！', {icon: 0});
                return;
            }
            tableIn.reload({where: {key: key}});
        });
        //{:fy('Sort')}
        $('body').on('blur','.list_order',function() {
            var id = $(this).attr('data-id');
            var sort = $(this).val();
            var loading = layer.load(1, {shade: [0.1, '#fff']});
            $.post('{:url("typeOrder")}',{id:id,sort:sort},function(res){
                layer.close(loading);
                if(res.code === 1){
                    layer.msg(res.msg, {time: 1000, icon: 1});
                    tableIn.reload();
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                }
            })
        });
        table.on('tool(list)', function(obj) {
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('您确定要删除该碎片分类吗？', function(index){
                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                    $.post("{:url('delType')}",{id:data.id},function(res){
                        layer.close(loading);
                        if(res.code===1){
                            layer.msg(res.msg,{time:2000,icon:1});
                            obj.del();
                        }else{
                            layer.msg('{:fy("Operation failed")}'+'！',{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
    });
</script>