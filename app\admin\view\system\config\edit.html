<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Variable Title')}</label>
            <div class="layui-input-block">
                <input type="text" name="name" class="layui-input" lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Variable Title')}" value="{$row.name|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Variable name')}</label>
            <div class="layui-input-block">
                <input type="text" name="field" class="layui-input" lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Variable name')}" value="{$row.field|default=''}">
            </div>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">{:fy('Must start with an English letter, followed by supported numbers and _')}</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Group Name')}</label>
            <div class="layui-input-block">
                <select name="identification" class="layui-select" lay-search>

                   <?php
$groupLst=\think\facade\Db::name('system_config_group')->field('name,identification')->where('status','=',1)->select();
                    foreach($groupLst as $v){
?>
                    <option value="{$v['identification']}" <?php if($row['identification']==$v['identification'])echo 'selected'; ?>>{:fy($v['name'])}</option>
<?php } ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Default value')}</label>
            <div class="layui-input-block">
                <input type="text" name="value" class="layui-input"  placeholder="{:fy('Please enter')}{:fy('Default value')}" value="{$row.value|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Description')}</label>
            <div class="layui-input-block">
                <textarea name="describe" style="height: 50px;" class="layui-textarea" placeholder="{:fy('Please enter')}{:fy('Description')}">{$row.describe|default=''}</textarea>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">{:fy('Remark')}</label>
            <div class="layui-input-block">
                <textarea name="remark" rows="2"  class="layui-textarea" placeholder="{:fy('Please enter')}{:fy('note information')}">{$row.remark|default=''}</textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Sort')}</label>
            <div class="layui-input-block">
                <input type="number" name="sort" class="layui-input" lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Sort')}值" value="{$row.sort|default='100'}">
            </div>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">{:fy('The smaller the sorting value, the higher the ranking')}</div>
            </div>
        </div>
        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required">{:fy('form types')}</label>
            <div class="layui-input-block">
                <select name="formtype" class="layui-select" lay-search>

                    {foreach $allData as $key=>$vo}
                    <option value="{$key}" <?php if($row['formtype']==$key)echo 'selected'; ?>>{:fy($vo)}</option>
                    {/foreach}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Options')}</label>
            <div class="layui-input-block">
                <textarea type="text" id="option" name="option" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Options')}" placeholder="{:fy('Please enter')}{:fy('Options')}" >{$row.option|default=''}</textarea>
                <tip>{:fy('The form type is a single choice box, a multiple choice box, or a drop-down box. Example: 0: close, 1: open')}</tip>
            </div>
        </div>




        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>{:fy('Confirm')}</button>
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">{:fy('Reset')}</button>
        </div>

    </form>
</div>