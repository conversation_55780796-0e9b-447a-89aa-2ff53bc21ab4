<?php

namespace app\plugins\wechat\library\easywechat;



use EasyWeChat\Kernel\BaseClient;

/**
 * Class Externalcontact.
 */
class Externalcontact extends BaseClient
{
    /**
     * 批量获取客户详情
     * @param array $userid_list 企业成员的userid列表，字符串类型，最多支持100个
     * @param string $cursor 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用可不填
     * @param int $limit 返回的最大记录数，整型，最大值100，默认值50，超过最大值时取最大值
     * @return array|\EasyWeChat\Kernel\Support\Collection|object|\Psr\Http\Message\ResponseInterface|string
     */
    public function batch(array $userid_list,string $cursor="",int $limit=100)
    {
        $params = [
            'userid_list' => $userid_list,
            'cursor' => $cursor,
            'limit' => $limit,
        ];

        return $this->httpPostJson('cgi-bin/externalcontact/batch/get_by_user', $params);
    }


}
