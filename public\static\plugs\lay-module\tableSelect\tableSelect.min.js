layui.define(["table","jquery","form"],function(e){"use strict";var t="tableSelect",g=layui.jquery,k=layui.table,x=layui.form;var a=function(){this.v="1.1.0"};a.prototype.render=function(p){var y=g(p.elem);var b=p.table.done||function(){};p.searchKey=p.searchKey||"keyword";p.searchPlaceholder=p.searchPlaceholder||"关键词搜索";p.checkedKey=p.checkedKey||"";p.table.page=p.table.page||true;p.table.height=p.height||315;p.table.where=p.where||{};p.width=p.width||"800px";var e=g(window).width();if(e>=1e3){p.width="900px"}else{p.width="96%"}p.searchType=p.searchType||"one";p.searchList=p.searchList||[{key:p.search<PERSON><PERSON>,placeholder:p.searchPlaceholder}];function v(e){var i={},r={};g.each(e,function(e,t){if(t!==""){i[e]=t;var a=g("#c-"+e).attr("data-search-op");a=a||"%*%";r[e]=a}});var t={filter:JSON.stringify(i),op:JSON.stringify(r)};if(p.where){for(var a in p.where){if(p.where.hasOwnProperty(a)){t[a]=p.where[a]}}}return t}y.off("click").on("click",function(e){e.stopPropagation();if(g("div.tableSelect").length>=1){return false}var t=y.offset().top+y.outerHeight()+"px";var a=y.offset().left+"px";var d="tableSelect_table_"+(new Date).getTime();var i='<div class="tableSelect layui-anim layui-anim-upbit" style="left:'+a+";top:"+t+";border: 1px solid #d2d2d2;background-color: #fff;box-shadow: 0 2px 4px rgba(0,0,0,.12);padding:10px 10px 0 10px;position: absolute;z-index:66666666;margin: 5px 0;border-radius: 2px;width:"+p.width+';">';i+='<div class="tableSelectBar">';i+='<form class="layui-form" action="" style="display:inline-block;">';if(p.searchType=="more"){g.each(p.searchList,function(e,t){i+='<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="'+t.searchKey+'" placeholder="'+t.searchPlaceholder+'" autocomplete="off" class="layui-input">'})}else{i+='<input style="display:inline-block;width:190px;height:30px;vertical-align:middle;margin-right:-1px;border: 1px solid #C9C9C9;" type="text" name="'+p.searchKey+'" placeholder="'+p.searchPlaceholder+'" autocomplete="off" class="layui-input">'}i+='<button class="layui-btn layui-btn-sm layui-btn-primary tableSelect_btn_search" lay-submit lay-filter="tableSelect_btn_search"><i class="layui-icon layui-icon-search"></i></button>';i+="</form>";i+='<button class="layui-btn layui-btn-sm tableSelect_btn_select">选择<span></span></button>';i+="</div>";i+='<table id="'+d+'" lay-filter="'+d+'"></table>';i+="</div>";i=g(i);g("body").append(i);var h=[];p.table.elem="#"+d;p.table.id=d;p.table.done=function(e,t,a){l(e,t,a);c(e,t,a);b(e,t,a)};var r=k.render(p.table);k.on("radio("+d+")",function(e){if(p.checkedKey){h=k.checkStatus(d).data}s(k.checkStatus(d).data.length)});k.on("checkbox("+d+")",function(e){if(p.checkedKey){if(e.checked){for(var t=0;t<k.checkStatus(d).data.length;t++){h.push(k.checkStatus(d).data[t])}}else{if(e.type=="all"){for(var a=0;a<k.cache[d].length;a++){for(var t=0;t<h.length;t++){if(h[t][p.checkedKey]==k.cache[d][a][p.checkedKey]){h.splice(t,1)}}}}else{var i=function(){var e="";for(var t=0;t<k.cache[d].length;t++){if(!k.cache[d][t].LAY_CHECKED){e=k.cache[d][t][p.checkedKey]}}return e};var r=e.data[p.checkedKey]||i();for(var t=0;t<h.length;t++){if(h[t][p.checkedKey]==r){h.splice(t,1)}}}}h=n(h,p.checkedKey);s(h.length)}else{s(k.checkStatus(d).data.length)}});function c(e,t,a){for(var i=0;i<e.data.length;i++){for(var r=0;r<h.length;r++){if(e.data[i][p.checkedKey]==h[r][p.checkedKey]){e.data[i].LAY_CHECKED=true;var c=e.data[i]["LAY_TABLE_INDEX"];var l=g("#"+d+"").next().find("tr[data-index="+c+'] input[type="checkbox"]');l.prop("checked",true).next().addClass("layui-form-checked");var n=g("#"+d+"").next().find("tr[data-index="+c+'] input[type="radio"]');n.prop("checked",true).next().addClass("layui-form-radioed").find("i").html("&#xe643;")}}}var o=k.checkStatus(d);if(o.isAll){g("#"+d+"").next().find('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop("checked",true);g("#"+d+"").next().find('.layui-table-header th[data-field="0"] input[type="checkbox"]').next().addClass("layui-form-checked")}s(h.length)}function l(e,t,a){if(p.checkedKey&&y.attr("ts-selected")){var i=y.attr("ts-selected").split(",");for(var r=0;r<e.data.length;r++){for(var c=0;c<i.length;c++){if(e.data[r][p.checkedKey]==i[c]){h.push(e.data[r])}}}h=n(h,p.checkedKey)}}function s(e){i.find(".tableSelect_btn_select span").html(e==0?"":"("+e+")")}function n(e,t){var a=[];var i=[];if(e.length==0){return e}else{if(t){for(var r=0;r<e.length;r++){if(!i[e[r][t]]){a.push(e[r]);i[e[r][t]]=true}}return a}else{for(var r=0;r<e.length;r++){if(!i[e[r]]){a.push(e[r]);i[e[r]]=true}}return a}}}var o=y.offset().top+y.outerHeight()+i.outerHeight()-g(window).scrollTop()>g(window).height();var f=y.offset().left+i.outerWidth()>g(window).width();o&&i.css({top:"auto",bottom:"0px"});f&&i.css({left:"auto",right:"5px"});x.on("submit(tableSelect_btn_search)",function(e){r.reload({where:v(e.field),page:{curr:1}});return false});k.on("rowDouble("+d+")",function(e){var t={data:[e.data]};u(t)});i.find(".tableSelect_btn_select").on("click",function(){var e=k.checkStatus(d);if(h.length>1){e.data=h}u(e)});function u(e){if(p.checkedKey){var t=[];for(var a=0;a<e.data.length;a++){t.push(e.data[a][p.checkedKey])}y.attr("ts-selected",t.join(","))}p.done(y,e);i.remove();delete k.cache[d];h=[]}g(document).mouseup(function(e){var t=g(""+p.elem+",.tableSelect");if(!t.is(e.target)&&t.has(e.target).length===0){i.remove();delete k.cache[d];h=[]}})})};a.prototype.hide=function(e){g(".tableSelect").remove()};var a=new a;if(window.top==window.self){g(window).scroll(function(){a.hide()})}e(t,a)});