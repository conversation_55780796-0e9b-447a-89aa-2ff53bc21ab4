<?xml version="1.0"?>
<ruleset>
    <arg name="basepath" value="."/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="80"/>
    <arg name="cache" value=".phpcs-cache"/>
    <arg name="colors"/>

    <!-- Ignore warnings, show progress of the run and show sniff names -->
    <arg value="nps"/>

    <config name="php_version" value="70100"/>

    <!-- Directories to be checked -->
    <file>lib</file>
    <file>tests</file>

    <!-- Include full Doctrine Coding Standard -->
    <rule ref="Doctrine">
        <exclude name="SlevomatCodingStandard.TypeHints.PropertyTypeHint.MissingNativeTypeHint" />
    </rule>
</ruleset>
