<?php

declare (strict_types=1);

namespace app\plugins\wechat\admin\controller;


use app\common\controller\PluginsController;
use think\App;

/**
 * @ControllerAnnotation ('Index')
 */
class Account extends PluginsController
{

    public function index(){

        if($this->request->post()){
            $post = $this->request->post('',[],'trim');
            \think\facade\Db::name('weixin')->where('name','=','account')->update(['value'=>json_encode($post)]);
            $this->success(fy('Save successfully'));
        }else{

            $row=\think\facade\Db::name('weixin')->field('`value`')->where('name','=','account')->find();
            $data=[];

            if(!empty($row['value'])){
                $data=json_decode($row['value'],true);
                if($data){
                    try{
                        $token=\WeChat\Contracts\BasicWeChat::instance([
                            'appid'=>$data['appid'],
                            'appsecret'=>$data['appsecret']
                        ])->getAccessToken();
                        if($token){
                            $data['token_msg']='配置正确';
                        }
                    }catch (\Exception $e) {
                        $data['token_msg']=$e->getMessage();
                    }

                }
            }
            if(!isset($data['token_msg'])){
                $data['token_msg']='请先配置';
            }


            $this->assign('row',$data);
            return $this->fetch();
        }

    }
}