<?php /*a:2:{s:50:"C:\wwwroot\127.0.0.1\app\admin\view\order\add.html";i:1686294484;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
    <blockquote class="layui-elem-quote">
    </blockquote>
        <form id="app-form" class="layui-form layuimini-form">

            <div class="layui-form-item">
                <label class="layui-form-label"><?php echo fy('customer'); ?></label>
                <div class="layui-input-block">
                    <input type="text" name="customer_id" data-toggle="selectPage" class="layui-input" data-source="<?php echo url('crm.customer/selectpage'); ?>"  data-field="name"  data-format-item="{name}"  data-primary-key="id"  placeholder="<?php echo fy('Please select'); ?>订单对应客户" data-params='{"custom[status]":1,"custom[pr_user]":"<?php echo htmlentities($admin['username']); ?>"}'  lay-verify="required" value="<?php echo htmlentities($customer_id); ?>" >
                </div>
            </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Order number'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="orderno" name="orderno"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Order number'); ?>" class="layui-input" value="<?php echo htmlentities($orderno); ?>" readonly>
                <tip id="checklabel"></tip>
            </div>
        </div>


        <?php if($admin['admin_id'] == 1): ?>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Client Principal'); ?></label>
            <div class="layui-input-block">
                <select name="pr_user" id="pr_user">
                    <option value=""><?php echo fy('Please select'); ?><?php echo fy('Client Principal'); ?></option>
                    <?php if(is_array($userlist) || $userlist instanceof \think\Collection || $userlist instanceof \think\Paginator): $i = 0; $__LIST__ = $userlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($vo['username']); ?>"><?php echo htmlentities($vo['username']); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>

                </select>
            </div>
        </div>
        <?php else: ?>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Client Principal'); ?></label>
            <div class="layui-input-block">
                <input type="text" readonly id="pr_user" name="pr_user" lay-verify="required" value="<?php echo htmlentities($username); ?>" class="layui-input">
            </div>
        </div>
        <?php endif; ?>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('amount'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="money" name="money"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('amount'); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Freight'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="freight" name="freight"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Freight'); ?>" class="layui-input">
            </div>
        </div>
            <?php echo $fields_str; ?>

        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Remark'); ?></label>
            <div class="layui-input-block">
                <textarea placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Order Remarks'); ?>" class="layui-textarea" name="remark"></textarea>
            </div>
        </div>

            <div class="hr-line"></div>
            <div class="layui-form-item text-center">
                <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit><?php echo fy('Save'); ?></button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm"><?php echo fy('Reset'); ?></button>
            </div>

    </form>
</div>

</body>
</html>