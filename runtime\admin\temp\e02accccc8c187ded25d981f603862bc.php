<?php /*a:3:{s:58:"C:\wwwroot\127.0.0.1\app\admin\view\auth\group_access.html";i:1665288752;s:52:"C:\wwwroot\127.0.0.1\app\admin\view\common\head.html";i:1671106604;s:52:"C:\wwwroot\127.0.0.1\app\admin\view\common\foot.html";i:1672069288;}*/ ?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/static/plugs/layui-v2.7.6/css/layui.css" media="all">

    <link rel="stylesheet" href="/static/plugs/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/global.css" media="all">
    <script src="/static/common/js/jquery.min.js"></script>

    <script src="/static/admin/js/common.js"></script>
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
</head>
<body >
<link rel="stylesheet" href="/static/plugs/zTree/css/zTreeStyle.css" type="text/css">
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field">
        <legend>配置权限</legend>
        <div class="layui-field-box">
            <form class="layui-form layui-form-pane">
                <ul id="treeDemo" class="ztree"></ul>
                <div class="layui-form-item text-center">
                    <button type="button" class="layui-btn" lay-submit="" lay-filter="submit"><?php echo lang('submit'); ?></button>
                </div>
            </form>
        </div>
    </fieldset>
</div>


<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script type="text/javascript" src="/static/common/js/jquery.2.1.1.min.js"></script>
<script type="text/javascript" src="/static/plugs/zTree/js/jquery.ztree.core.min.js"></script>
<script type="text/javascript" src="/static/plugs/zTree/js/jquery.ztree.excheck.min.js"></script>
<script type="text/javascript">
    var setting = {
        check:{enable: true},
        view: {showLine: false, showIcon: false, dblClickExpand: false},
        data: {
            simpleData: {enable: true, pIdKey:'pid', idKey:'id'},
            key:{name:'title'}
        }
    };
    var zNodes =<?php echo $data; ?>;
    function setCheck() {
        var zTree = $.fn.zTree.getZTreeObj("treeDemo");
        zTree.setting.check.chkboxType = { "Y":"ps", "N":"s"};

    }
    $.fn.zTree.init($("#treeDemo"), setting, zNodes);
    setCheck();
    layui.use(['form', 'layer'], function () {
        var form = layui.form, layer = layui.layer;
        form.on('submit(submit)', function () {
            loading =layer.load(1, {shade: [0.1,'#fff']});
            // 提交到方法 默认为本身
            var treeObj=$.fn.zTree.getZTreeObj("treeDemo"),
                nodes=treeObj.getCheckedNodes(true),
                v="";
            for(var i=0;i<nodes.length;i++){
                v+=nodes[i].id + ",";
            }
            var id = "<?php echo input('id'); ?>";
            $.post("<?php echo url('groupSetaccess'); ?>", {'rules':v,'id':id}, function (res) {
                layer.close(loading);
                if (res.code > 0) {
                    layer.msg(res.msg, {time: 1800, icon: 1}, function () {
                            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                            parent.layer.close(index); // 关闭layer
                    });
                } else {
                    layer.msg(res.msg, {time: 2800, icon: 2});
                }
            });
        })
    });
</script>