<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form layui-form-pane">
        
        <div class="layui-form-item">
            <label class="layui-form-label">角色名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" class="layui-input" lay-verify="required" placeholder="{:fy('Please enter')}角色名称" value="{$row.name|default=''}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">操作权限</label>
            <div class="layui-input-block">
                {foreach $getTypeList as $k=>$v}
                <input type="radio" name="type" value="{$k}" title="{$v}" {in name="k" value="$row.type"}checked=""{/in}>
                {/foreach}
                <tip>控制对用户数据的编辑查看权限</tip>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Status')}</label>
            <div class="layui-input-block">
                {foreach $getStatusList as $k=>$v}
                <input type="radio" name="status" value="{$k}" title="{$v}" {in name="k" value="$row.status"}checked=""{/in}>
                {/foreach}
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Sort')}</label>
            <div class="layui-input-block">
                <input type="text" name="sort" class="layui-input" lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Sort')}" value="{$row.sort|default=''}">
            </div>
        </div>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>{:fy('Confirm')}</button>
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">{:fy('Reset')}</button>
        </div>

    </form>
</div>