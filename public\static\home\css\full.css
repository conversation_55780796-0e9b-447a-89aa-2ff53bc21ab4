/*! 默认风格 */

html body{margin-top:0; margin-left: 136px;}
html .fly-full{margin-top: 0;}

.main{width:auto; margin:15px 24px 15px 24px;}
.content{margin-right: 360px;}
.jie-row li .jie-title{max-width:70%;}

/* 头部 */
.header{width:136px; height:100%;}
.header .main{position: static; width:auto;}
.logo{top:20px; left:50%; width:86px; height:74px; margin-left:-43px; background:url(../images/logo-1.png);}
.nav{position:relative; left:0; top:110px; border-top:1px solid #282C35;}
.nav a{display:block; height:50px; line-height:50px; padding:0; text-align:center;}
.nav a:first-child{border-top:1px solid #424857;}
.nav-user span{margin-top: 10px;}

.icon-touxiang{font-size: 60px;}

.nav-user{top: auto; bottom:20px; left:0; width:100%;}
.nav-user span, 
.nav-user .unlogin, 
.out-login,
.avatar,
.avatar cite,
.nav-user .nav{display:block; *display:block; text-align:center;}
.nav-user span{top: 0;}
.nav-user span a{padding:0 6px;}
.nav-user .unlogin{margin-right: 0;}
.out-login{margin-left:0; margin-top:20px;}
.out-login a{ padding:0 5px;}
.nav-user .nav{position:relative; margin-left: 0; margin-top: 15px;}

.avatar img{width:60px; height:60px;}
.avatar cite{margin-left: 0; margin-top:10px; }
.avatar i{margin-left: 0;}

.nav-message{left: auto; right: 10px;}

/* 适配 */
@media screen and (max-width: 1024px) {
  html body{margin-left: 140px;}
  .main{margin: 15px 10px;}
  .content{margin: 0;}
  .edge{display:none}
}

@media screen and (max-width: 750px) {
  html body{margin-left:0;}
  .header{left:-140px;}
  .edge{display: block;}
}
