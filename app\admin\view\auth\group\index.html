<style>
    .layui-btn:not(.layui-btn-lg ):not(.layui-btn-sm):not(.layui-btn-xs) {
        height: 34px;
        line-height: 34px;
        padding: 0 8px;
    }
</style>
<link rel="stylesheet" href="__MY_PUBLIC__/static/plugs/lay-module/treetable-lay/treetable.css" media="all">
<div class="layuimini-container">

    <div class="layuimini-main">
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="{:auth('auth.group/add')}"
               data-auth-edit="{:auth('auth.group/edit')}"
               data-auth-delete="{:auth('auth.group/delete')}"
               data-auth-access="{:auth('Auth/groupAccess')}"
               lay-filter="currentTable">
        </table>
    </div>
</div>
<script type="text/html" id="toolbar">
    <button class="layui-btn layui-btn-sm layuimini-btn-primary" data-treetable-refresh><i class="fa fa-refresh"></i> </button>
</script>