<?php
namespace app\admin\controller;
use think\facade\View;
use function MongoDB\BSON\toJSON;
use think\facade\Db;
use clt\Leftnav;
use app\admin\model\Admin;
use app\admin\model\AuthGroup;
use app\admin\model\authRule;
use think\facade\Request;
use think\Validate;

class General extends Common
{
    //个人信息修改
    public function profile(){
        if($this->request->isPost()){
             $data['avatar']=$this->request->post('avatar','/static/admin/images/0.jpg','trim');
            $data['email']=$this->request->post('email','','trim');
            $data['tel']=$this->request->post('tel','','trim');
            $pwd=$this->request->post('pwd','','trim');
            if ($pwd){
                $data['salt']=rand_string(12);
                $data['pwd']=md5($pwd.$data['salt']);
            }
            Db::name('admin')->where('admin_id', '=',$this->admin['admin_id'])->update($data);
            $admin=Db::name('admin')->withoutField('pwd,salt,ip')->where('admin_id', '=',$this->admin['admin_id'])->find();
            session('admin',$admin);
            $this->success(fy("Modification succeeded"));
        }else{

            $admin=Db::name('admin')->withoutField('pwd,salt,ip')->where('admin_id', '=',$this->admin['admin_id'])->find();
            View::assign('info_raw', $admin);
            View::assign('info', json_encode( $admin,true));
            View::assign('title',lang('edit').lang('admin'));
            return view();
        }
    }

}