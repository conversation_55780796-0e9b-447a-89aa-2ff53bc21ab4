{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>数据{:lang('list')}</legend>
    </fieldset>
    <blockquote class="layui-elem-quote">
       数据库中共有<i class="count"></i>张表，共计<i class="size"></i>
        <a href="javascript:void(0)" id="backUp" class="layui-btn layui-btn-sm pull-right">备份</a>
        <a href="javascript:void(0)" id="optimize" class="layui-btn layui-btn-sm pull-right layui-btn-normal">优化</a>
        <a href="javascript:void(0)" id="repair" class="layui-btn layui-btn-sm pull-right layui-btn-warm">修复</a>
    </blockquote>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="size">
    {{d.size}}
</script>
<script>
    layui.use(['table','util'], function() {
        var table = layui.table, $ = layui.jquery,util = layui.util;
        util.fixbar();
        var tableIn = table.render({
            id: 'database',
            elem: '#list',
            url: '{:url("database")}',
            method: 'post',
            cols: [[
                {checkbox:true,fixed: true},
                {field: 'name', title: '数据库表', width: 150, fixed: true,sort: true},
                {field: 'rows', title: '记录条数', width: 150,sort: true},
                {field: 'size', title: '占用空间', width: 150,templet:'#size',sort: true},
                {field: 'engine', title: '类型', width: 110,sort: true},
                {field: 'collation', title: '编码', width: 150,sort: true},
                {field: 'create_time', title: '{:fy('Creation time')}', width: 180,sort: true},
                {field: 'comment', title: '说明', width: 180},
            ]],
            done: function(res, curr, count){
                $('.count').html(res.tableNum);
                $('.size').html(res.total);
            }
        });
        $('#backUp').click(function(){
            var obj = $(this);
            var checkStatus = table.checkStatus('database'); //test即为参数id设定的值
            var a = [];
            $(checkStatus.data).each(function(i,o){
                a.push(o.name);
            });
            obj.addClass('layui-btn-disabled');
            obj.html('备份进行中...');
            $.post("{:url('database/backup')}",{tables:a},function(data){
                if(data.code==1){
                    layer.msg(data.msg,{time:2000,icon:1},function(){
                        tableIn.reload();
                    });
                }else{
                    layer.msg(data.msg,{time:2000,icon:2});
                }
                obj.removeClass('layui-btn-disabled');
                obj.html('备份');
            });
        });
        $('#optimize').click(function(){
            var obj = $(this);
            var checkStatus = table.checkStatus('database'); //test即为参数id设定的值
            var a = [];
            $(checkStatus.data).each(function(i,o){
                a.push(o.name);
            });
            obj.addClass('layui-btn-disabled');
            obj.html('优化进行中...');
            $.post("{:url('database/optimize')}",{tables:a},function(data){
                if(data.code==1){
                    layer.msg(data.msg,{time:2000,icon:1},function(){
                        tableIn.reload();
                    });
                }else{
                    layer.msg(data.msg,{time:2000,icon:2});
                }
                obj.removeClass('layui-btn-disabled');
                obj.html('优化');
            });
        });
        $('#repair').click(function(){
            var obj = $(this);
            var checkStatus = table.checkStatus('database'); //test即为参数id设定的值
            var a = [];
            $(checkStatus.data).each(function(i,o){
                a.push(o.name);
            });
            obj.addClass('layui-btn-disabled');
            obj.html('修复进行中...');
            $.post("{:url('database/repair')}",{tables:a},function(data){
                if(data.code==1){
                    layer.msg(data.msg,{time:2000,icon:1},function(){
                        tableIn.reload();
                    });
                }else{
                    layer.msg(data.msg,{time:2000,icon:2});
                }
                obj.removeClass('layui-btn-disabled');
                obj.html('修复');
            });
        })
    });
</script>