define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: '{%controllerUrl%}/index' + location.search,
                    add_url: '{%controllerUrl%}/add',
                    edit_url: '{%controllerUrl%}/edit',
                    del_url: '{%controllerUrl%}/del',
                    multi_url: '{%controllerUrl%}/multi',
                    import_url: '{%controllerUrl%}/import',
                    table: '{%table%}',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: '{%pk%}',
                sortName: '{%order%}',
                columns: [
                    [
                        {%javascriptList%}
                    ]
                ],showExport:0
            });

            var submitForm = function (ids, layero) {
                            var options = table.bootstrapTable('getOptions');
                            console.log(options);
                            var columns = [];
                            $.each(options.columns[0], function (i, j) {
                                if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                                    columns.push(j.field);
                                }
                            });
                            var search = options.queryParams({});
                            $("input[name=search]", layero).val(options.searchText);
                            $("input[name=ids]", layero).val(ids);
                            $("input[name=filter]", layero).val(search.filter);
                            $("input[name=op]", layero).val(search.op);
                            $("input[name=columns]", layero).val(columns.join(','));
                            $("form", layero).submit();
                        };
                        $(document).on("click", ".btn-export", function () {
                            var ids = Table.api.selectedids(table);
                            var page = table.bootstrapTable('getData');
                            var all = table.bootstrapTable('getOptions').totalRows;
                            console.log(ids, page, all);
                            Layer.confirm("请选择导出的选项<form action='" + Fast.api.fixurl("{%controllerUrl%}/export") + "' method='post' target='_blank'><input type='hidden' name='ids' value='' /><input type='hidden' name='filter' ><input type='hidden' name='op'><input type='hidden' name='search'><input type='hidden' name='columns'></form>", {
                                title: '导出数据',
                                btn: ["选中项(" + ids.length + "条)", "本页(" + page.length + "条)", "全部(" + all + "条)"],
                                success: function (layero, index) {
                                    $(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
                                }
                                , yes: function (index, layero) {
                                    submitForm(ids.join(","), layero);
                                    return false;
                                }
                                ,
                                btn2: function (index, layero) {
                                    var ids = [];
                                    $.each(page, function (i, j) {
                                        ids.push(j.id);
                                    });
                                    submitForm(ids.join(","), layero);
                                    return false;
                                }
                                ,
                                btn3: function (index, layero) {
                                    submitForm("all", layero);
                                    return false;
                                }
                            });
                        });
            // 为表格绑定事件
            Table.api.bindevent(table);
        },{%recyclebinJs%}
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});