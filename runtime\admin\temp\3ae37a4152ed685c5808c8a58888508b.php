<?php /*a:2:{s:59:"C:\wwwroot\127.0.0.1\app\admin\view\crm\customer\index.html";i:1753352427;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-tab" lay-filter="nav-tabs-index">
            <ul class="layui-tab-title">
                <li class="layui-this" data-value="1" data-field="scope"><?php echo fy("It's mine"); ?></li>
                <li  data-value="2" data-field="scope"><?php echo fy('Subordinate'); ?></li>
                <li  data-value="3" data-field="scope"><?php echo fy('All'); ?></li>

                <li  data-value="20" data-field="scope"><?php echo fy('Share with others'); ?></li>
                <li  data-value="21" data-field="scope"><?php echo fy('Shared with me'); ?></li>

                <li  data-value="10" data-field="scope"><?php echo fy('To be followed'); ?></li>
                <li  data-value="11" data-field="scope"><?php echo fy('Followed up today'); ?></li>
                <li  data-value="12" data-field="scope"><?php echo fy('Never followed up'); ?></li>
            </ul>
        </div>
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="<?php echo auth('crm.customer/add'); ?>"
               data-auth-addOrder="<?php echo auth('crm.customer/add'); ?>"
               data-auth-edit="<?php echo auth('crm.customer/edit'); ?>"
               data-auth-delete="<?php echo auth('crm.customer/delete'); ?>"
               data-auth-import="<?php echo auth('crm.customer/import'); ?>"
               data-auth-export="<?php echo auth('crm.customer/export'); ?>"
               data-auth-fields="<?php echo auth('system.fields/index'); ?>"
               data-auth-to_move_gh="<?php echo auth('crm.customer/to_move_gh'); ?>"
               data-auth-alter_pr_user="<?php echo auth('crm.customer/alter_pr_user'); ?>"
               data-auth-dialogue="<?php echo auth('client/dialogue'); ?>"
               data-auth-share="<?php echo auth('crm.customer/share'); ?>"
               data-auth-del_share="<?php echo auth('crm.customer/del_share'); ?>"
               data-auth-reduplicate="<?php echo auth('crm.customer/reduplicate'); ?>"
               data-auth-hangup="<?php echo auth('crm.customer/hangup'); ?>"
               data-auth-dial="<?php echo auth('crm.customer/dial'); ?>"
               lay-filter="currentTable">
        </table>
    </div>
</div>
</body>
</html>