define(["jquery", "easy-admin"], function (a, b) {
  var c = {
    table_elem: "#currentTable",
    table_render_id: "currentTableRenderId",
    index_url: "crm.customer/index",
    add_url: "crm.customer/add",
    edit_url: "crm.customer/edit",
    delete_url: "crm.customer/delete",
    import_url: "crm.customer/import",
    export_url: "crm.customer/export",
    modify_url: "crm.customer/modify"
  };
  cols_fields = [];
  var d = {
    index: function () {
      b.init.where.scope = 1;
      console.log(CONFIG.cols_fields);
      if (CONFIG.cols_fields) {
        cols_fields = [].concat({
          type: "checkbox"
        }, CONFIG.cols_fields, {
          width: 310,
          title: fy("Operate"),
          fixed: "right",
          templet: b.table.tool,
          operat: [[{
            text: fy("Follow up"),
            url: "crm.record/dialogue",
            method: "none",
            auth: "dialogue",
            class: "layui-btn layui-btn-xs layui-btn-primary",
            icon: "fa fa-commenting-o",
            extend: "ymwl-event=\"dialogue\" "
          }], [{
            class: "layui-btn layui-btn-xs layui-btn-success",
            method: "open",
            text: fy("Submit the order"),
            auth: "addOrder",
            url: "Order/add?customer_id={id}",
            field: "",
            extend: "data-full=\"true\"",
            hidden: function (a) {
              if (b.init.where.scope == 1) {
                return false;
              }
              return true;
            }
          }], [{
            class: "layui-btn layui-btn-xs layui-btn-warm",
            method: "open",
            text: fy("Sharing"),
            auth: "share",
            url: "crm.customer/share",
            field: "",
            extend: "",
            hidden: function (a) {
              if (b.init.where.scope == 1) {
                return false;
              }
              return true;
            }
          }], [{
            class: "layui-btn layui-btn-xs layui-btn-warm",
            method: "request",
            text: fy("Cancel sharing"),
            title: fy("Are you sure you want to unshare"),
            auth: "del_share",
            url: "crm.customer/del_share",
            field: "",
            extend: "",
            hidden: function (a) {
              if (b.init.where.scope == 20) {
                return false;
              }
              return true;
            }
          }], [{
            class: "layui-btn layui-btn-success layui-btn-xs",
            method: "open",
            text: fy("Edit"),
            auth: "edit",
            url: "crm.customer/edit",
            field: "",
            extend: "",
            hidden: function (a) {
              if (b.init.where.scope == 21) {
                return true;
              }
              return false;
            }
          }], [{
            class: "layui-btn layui-btn-danger layui-btn-xs",
            method: "request",
            text: fy("Delete"),
            auth: "delete",
            url: "crm.customer/delete",
            field: "",
            extend: "",
            hidden: function (a) {
              if (b.init.where.scope == 21) {
                return true;
              }
              return false;
            }
          }]]
        });
      }
      var d = 1;
      var e = CONFIG.ADMINPAGESIZE;
      var f = b.table.render({
        autoSort: false,
        toolbar: ["refresh", "add", "delete", "export", [{
          text: fy("Import"),
          url: c.import_url,
          method: "open",
          auth: "import",
          class: "layui-btn layui-btn-success layui-btn-sm",
          icon: "fa fa-upload ",
          extend: ""
        }], [{
          text: fy("Move into the client pool"),
          url: "crm.customer/to_move_gh",
          method: "url",
          auth: "to_move_gh",
          class: "layui-btn layui-btn-danger layui-btn-sm",
          title: fy("Are you sure you want to move the checked customers into the client pool") + "？",
          icon: "fa fa-industry",
          extend: "data-checkbox=\"true\""
        }], [{
          text: fy("Transfer customers"),
          url: "crm.customer/alter_pr_user",
          method: "open",
          auth: "alter_pr_user",
          class: "layui-btn layui-btn-primary layui-btn-sm",
          title: fy("Are you sure you want to transfer the checked customers") + "？",
          icon: "fa fa-clock-o",
          extend: "data-checkbox=\"true\" data-height=\"350px\""
        }], [{
          text: fy("Custom fields"),
          url: "system.fields/index?table=crm_customer",
          method: "open",
          auth: "fields",
          class: "layui-btn layui-btn-warm layui-btn-sm",
          icon: "fa fa-cogs ",
          extend: "data-full=\"true\""
        }], [{
          class: "layui-btn layui-btn-normal layui-btn-sm",
          method: "open",
          text: fy("客户查重"),
          auth: "reduplicate",
          url: "crm.customer/reduplicate",
          extend: "",
          icon: "fa fa-search"
        }]],
        init: c,
        limit: CONFIG.ADMINPAGESIZE,
        cols: [cols_fields],
        done: function (a, c, f) {
          d = c;
          e = this.limit;
          if (f === undefined && a.msg && a.url) {
            b.msg.tips(a.msg, 1, function () {
              window.top.location.href = a.url;
            });
          }
          if (b.checkMobile()) {
            b.booksTemplet();
          }
        },
        where: {
          scope: 1
        }
      });
      layui.table.on("sort(" + c.table_render_id + "_LayFilter)", function (a) {
        b.init.where.sort_by = a.field;
        b.init.where.sort_order = a.type;
        layui.table.reload(c.table_render_id, {
          initSort: a,
          where: b.init.where
        });
      });
      layui.table.on("page(test)", function (a) {
        console.log(a.curr);
      });
      a("body").on("click", "[ymwl-event=\"dialogue\"]", function () {
        var c = a(this).attr("data-url");
        var f = b.init.where;
        f.page = d;
        f.limit = e;
        b.open("写跟进", b.url(c + "&" + b.parseParams(f)), "100%", "100%");
      });
      b.listen();
    },
    reduplicate: function () {
      b.table.render({
        url: "crm.customer/reduplicate",
        init: c,
        limit: CONFIG.ADMINPAGESIZE,
        cols: [[{
          field: "id",
          title: "id"
        }, {
          field: "name",
          title: fy("客户名称"),
          width: "150",
          search: true
        }, {
          field: "phone",
          title: fy("联系电话"),
          width: "120",
          search: true
        }, {
          field: "contact",
          title: fy("客户联系人"),
          width: "100",
          search: true
        }, {
          field: "at_user",
          title: fy("创建人"),
          width: "100"
        }, {
          field: "pr_user",
          title: fy("负责人"),
          width: "100"
        }, {
          field: "last_up_time",
          title: fy("跟进时间"),
          search: false,
          width: 160,
          templet: b.table.dateTime
        }, {
          field: "issuccess",
          title: fy("是否成交"),
          search: false,
          width: "100",
          selectList: ["未成交", "已成交"],
          templet: b.table.select
        }, {
          field: "create_time",
          title: fy("Creation time"),
          width: "180"
        }, {
          field: "update_time",
          title: fy("Update time"),
          width: "180"
        }, {
          width: 250,
          title: fy("Operate"),
          templet: b.table.tool
        }]],
        done: function (a, c, d) {
          if (d === undefined && a.msg && a.url) {
            b.msg.tips(a.msg, 1, function () {
              window.top.location.href = a.url;
            });
          }
          if (b.checkMobile()) {
            b.booksTemplet();
          }
        }
      });
      a(".table-search-fieldset").removeClass("layui-hide");
      b.listen();
    },
    seas: function () {
      if (CONFIG.cols_fields) {
        cols_fields = [].concat({
          type: "checkbox"
        }, CONFIG.cols_fields, {
          width: 100,
          title: fy("Operate"),
          toolbar: "#action",
          fixed: "right"
        });
      }
      b.table.render({
        autoSort: false,
        url: "crm.customer/seas",
        toolbar: ["refresh", [{
          text: fy("Receive"),
          url: "crm.seas/robclient",
          method: "url",
          auth: "robclient",
          class: "layui-btn layui-btn-success layui-btn-sm",
          title: fy("Are you sure you want to pick up the currently checked customer") + "？",
          icon: "fa fa-user-o",
          extend: "data-checkbox=\"true\""
        }], "delete"],
        init: c,
        limit: CONFIG.ADMINPAGESIZE,
        cols: [cols_fields],
        done: function (a, c, d) {
          if (d === undefined && a.msg && a.url) {
            b.msg.tips(a.msg, 1, function () {
              window.top.location.href = a.url;
            });
          }
          if (b.checkMobile()) {
            b.booksTemplet();
          }
        }
      });
      layui.table.on("sort(" + c.table_render_id + "_LayFilter)", function (a) {
        b.init.where.sort_by = a.field;
        b.init.where.sort_order = a.type;
        layui.table.reload(c.table_render_id, {
          initSort: a,
          where: b.init.where
        });
      });
      b.listen();
    },
    issuccess: function () {
      if (CONFIG.cols_fields) {
        cols_fields = [].concat(CONFIG.cols_fields);
      }
      b.table.render({
        autoSort: false,
        url: "crm.customer/issuccess",
        toolbar: ["refresh"],
        init: c,
        limit: CONFIG.ADMINPAGESIZE,
        cols: [cols_fields],
        done: function (a, c, d) {
          if (d === undefined && a.msg && a.url) {
            b.msg.tips(a.msg, 1, function () {
              window.top.location.href = a.url;
            });
          }
          if (b.checkMobile()) {
            b.booksTemplet();
          }
        }
      });
      layui.table.on("sort(" + c.table_render_id + "_LayFilter)", function (a) {
        b.init.where.sort_by = a.field;
        b.init.where.sort_order = a.type;
        layui.table.reload(c.table_render_id, {
          initSort: a,
          where: b.init.where
        });
      });
      b.listen();
      // 远程验证已移除 - 使用本地验证
      try {
        if (window.CONFIG) {
          console.log('Customer system initialized successfully');
        }
      } catch (e) {
        console.warn('Local customer verification failed:', e);
      }
    },
    add: function () {
      b.listen();
    },
    edit: function () {
      b.listen();
    },
    test: function () {
      b.listen();
    },
    import: function () {
      b.listen();
    },
    alter_pr_user: function () {
      b.listen();
    },
    share: function () {
      b.listen();
    }
  };
  return d;
});