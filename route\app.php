<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// WebSocket API路由
Route::group('admin', function () {
    Route::any('websocket_api/testParams', 'admin/WebSocketApi/testParams');
    Route::post('websocket_api/dial', 'admin/WebSocketApi/dial');
    Route::post('websocket_api/hangup', 'admin/WebSocketApi/hangup');
    Route::get('websocket_api/getDeviceStatus', 'admin/WebSocketApi/getDeviceStatus');
});
