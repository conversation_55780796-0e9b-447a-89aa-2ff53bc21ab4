<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// WebSocket API路由
Route::group('admin', function () {
    Route::any('websocket_api/testParams', 'admin/WebSocketApi/testParams');
    Route::post('websocket_api/dial', 'admin/WebSocketApi/dial');
    Route::post('websocket_api/hangup', 'admin/WebSocketApi/hangup');
    Route::get('websocket_api/getDeviceStatus', 'admin/WebSocketApi/getDeviceStatus');
});

// API接口路由
Route::group('api', function () {
    // 认证相关
    Route::post('auth/login', 'api/Auth/login');
    Route::post('auth/logout', 'api/Auth/logout');
    Route::get('auth/info', 'api/Auth/info');
    Route::post('auth/changePassword', 'api/Auth/changePassword');
    
    // 客户管理
    Route::get('customer/index', 'api/Customer/index');
    Route::get('customer/read', 'api/Customer/read');
    Route::get('customer/getStatusList', 'api/Customer/getStatusList');
    Route::get('customer/getHangyeList', 'api/Customer/getHangyeList');
    Route::get('customer/getRankList', 'api/Customer/getRankList');
    Route::get('customer/getSourceList', 'api/Customer/getSourceList');
    
    // 通话统计
    Route::post('callStatistics/upload', 'api/CallStatistics/upload');
    Route::get('callStatistics/index', 'api/CallStatistics/index');
    Route::get('callStatistics/chart', 'api/CallStatistics/chart');
    Route::post('callStatistics/delete', 'api/CallStatistics/delete');
    Route::get('callStatistics/growth', 'api/CallStatistics/growth');
});
