<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        <table class="table table-responsive fieldlist" data-name="row[test]" data-template="testtpl" data-tag="tr">
            <tr>
                <td>姓名</td>
                <td>性别</td>
                <td>年龄</td>
                <td>成绩</td>
                <td></td>
            </tr>
            <tr>
                <td colspan="5"><a href="javascript:;" class="layui-btn layui-btn-sm layui-btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
            </tr>
            <textarea name="row[test]" class="hide" cols="30" rows="5">[{"name":"张三","gender":"男","age":"23","score":"80"},{"name":"李四","gender":"男","age":"26","score":"90"}]</textarea>
        </table>
        <!--定义模板，模板语法使用Art-Template模板语法-->
        <script type="text/html" id="testtpl">
            <tr class="form-inline">
                <td><input type="text" name="row[<%=name%>][<%=index%>][name]" class="layui-input" value="<%=row['name']%>" size="10"></td>
                <td><input type="text" name="row[<%=name%>][<%=index%>][gender]" class="layui-input" value="<%=row['gender']%>" size="30"></td>
                <td><input type="text" name="row[<%=name%>][<%=index%>][age]" class="layui-input" value="<%=row['age']%>" size="30"></td>
                <td><input type="text" name="row[<%=name%>][<%=index%>][score]" class="layui-input" value="<%=row['score']%>" size="30"></td>
                <td><span class="layui-btn layui-btn-sm layui-btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="layui-btn layui-btn-sm layui-btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></td>
            </tr>
        </script>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>{:fy('Confirm')}</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">{:fy('Reset')}</button>
        </div>

    </form>
</div>