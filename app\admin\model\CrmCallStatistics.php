<?php

namespace app\admin\model;

use think\Model;

/**
 * 通话统计模型
 */
class CrmCallStatistics extends Model
{
    protected $name = 'crm_call_statistics';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'admin_id'      => 'int',
        'username'      => 'string',
        'stat_date'     => 'date',
        'call_count'    => 'int',
        'avg_duration'  => 'int',
        'total_duration'=> 'int',
        'connect_rate'  => 'float',
        'create_time'   => 'int',
        'update_time'   => 'int',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 时间字段取出后的默认时间格式
    protected $dateFormat = 'Y-m-d H:i:s';
    
    /**
     * 获取用户指定日期范围的统计数据
     */
    public function getStatsByDateRange($adminId, $startDate, $endDate)
    {
        return $this->where('admin_id', $adminId)
            ->where('stat_date', 'between', [$startDate, $endDate])
            ->order('stat_date', 'desc')
            ->select();
    }
    
    /**
     * 获取用户最近N天的统计数据
     */
    public function getRecentStats($adminId, $days = 7)
    {
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->getStatsByDateRange($adminId, $startDate, $endDate);
    }
    
    /**
     * 保存或更新统计数据
     */
    public function saveOrUpdateStats($data)
    {
        $existing = $this->where('admin_id', $data['admin_id'])
            ->where('stat_date', $data['stat_date'])
            ->find();
            
        if ($existing) {
            // 更新现有记录
            $data['update_time'] = time();
            return $existing->save($data);
        } else {
            // 创建新记录
            $data['create_time'] = time();
            $data['update_time'] = time();
            return $this->save($data);
        }
    }
    
    /**
     * 获取用户统计汇总
     */
    public function getStatsSummary($adminId, $startDate = null, $endDate = null)
    {
        $query = $this->where('admin_id', $adminId);
        
        if ($startDate && $endDate) {
            $query->where('stat_date', 'between', [$startDate, $endDate]);
        }
        
        $stats = $query->select();
        
        if ($stats->isEmpty()) {
            return [
                'total_days' => 0,
                'total_calls' => 0,
                'total_duration' => 0,
                'avg_calls_per_day' => 0,
                'avg_duration_per_call' => 0,
                'avg_connect_rate' => 0
            ];
        }
        
        $totalDays = $stats->count();
        $totalCalls = $stats->sum('call_count');
        $totalDuration = $stats->sum('total_duration');
        $avgConnectRate = $stats->avg('connect_rate');
        
        return [
            'total_days' => $totalDays,
            'total_calls' => $totalCalls,
            'total_duration' => $totalDuration,
            'avg_calls_per_day' => $totalDays > 0 ? round($totalCalls / $totalDays, 2) : 0,
            'avg_duration_per_call' => $totalCalls > 0 ? round($totalDuration / $totalCalls, 2) : 0,
            'avg_connect_rate' => round($avgConnectRate, 2)
        ];
    }
    
    /**
     * 关联管理员信息
     */
    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'admin_id');
    }
}
