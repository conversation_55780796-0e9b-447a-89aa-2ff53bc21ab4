define(["jquery","easy-admin"],function(d,m){var t={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"wechat/admin/menu/index",add_url:"wechat/admin/menu/add",edit_url:"wechat/admin/menu/edit",delete_url:"wechat/admin/menu/delete",export_url:"wechat/admin/menu/export",modify_url:"wechat/admin/menu/modify"};var e={index:function(){m.table.render({toolbar:["refresh",[{class:"layui-btn layui-btn-normal layui-btn-sm",method:"open",text:fy("Add"),auth:"add",url:t.add_url,extend:'data-full="true"',icon:"fa fa-plus"}],"delete"],init:t,limit:CONFIG.ADMINPAGESIZE,modifyReload:true,cols:[[{type:"checkbox"},{field:"id",title:"ID"},{field:"menu_name",title:fy("Menu name"),align:"center",sort:"sort"},{field:"status",filter:"status",search:"select",selectList:{0:"disabled",1:"enabled"},title:fy("Status"),sort:true,templet:m.table.switch},{field:"create_time",title:fy("Creation time"),align:"center",sort:"sort"},{field:"update_time",title:fy("Update time"),align:"center",sort:"sort"},{width:250,title:fy("Operate"),templet:m.table.tool,operat:[[{class:"layui-btn layui-btn-success layui-btn-xs",method:"open",text:fy("Edit"),auth:"edit",url:"wechat/admin/menu/edit",extend:'data-full="true"',icon:""}],"delete"]}]],done:function(e,t,n){if(n===undefined&&e.msg&&e.url){m.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}if(m.checkMobile()){m.booksTemplet()}}});m.listen()},add:function(){e.api.bindevent();m.listen()},edit:function(){e.api.bindevent();m.listen()},api:{bindevent:function(){var a=fy("Default menu"),u;function l(e){this.name=e;this.type="click";this.sub_button=[]}function r(e){this.name=e;this.type="click"}d(document).on("click",".con-body .left .wx-menu-add",function(e){d(this).removeClass("wx-menu-add");d(this).addClass("active");d(this).siblings().removeClass("active");var t=d(this).attr("data-id");var n=t.split("_");var i=d(this).siblings().length;if(n.length===1){if(i<2){var s=i+2;d(this).parent().append('<li class="menu-list-view wx-menu-add" data-id="'+s+'"><span class="layui-icon layui-icon-close"></span></li>');d(this).children().remove();d(this).append("<span>"+fy("Level %s menu",1)+"</span>")}if(i===2){d(this).children().remove();d(this).append("<span>"+fy("Level %s menu",1)+"</span>")}s=n[0]+"_"+0;if(d(this).find("ul").length===0){d(this).append('<ul style="display: block"><li data-id="'+s+'" class="menu-list-sub wx-menu-add"><span class="layui-icon layui-icon-close"></span></li></ul>')}menuList.push(new l(fy("Level %s menu",1)))}else{if(i<4){s=n[0]+"_"+(1+parseInt(n[1]));d(this).parent().append('<li class="menu-list-sub wx-menu-add" data-id="'+s+'"><span class="layui-icon layui-icon-close"></span></li>');d(this).children().remove();d(this).append("<span>"+fy("Level %s menu",2)+"</span>")}if(i===4){d(this).children().remove();d(this).append("<span>"+fy("Level %s menu",2)+"</span>")}if(!menuList[n[0]].hasOwnProperty("sub_button")){var a={sub_button:{name:fy("Level %s menu",2),type:"click"}};menuList[n[0]].append(a)}menuList[n[0]].sub_button.push(new r(fy("Level %s menu",2)))}e.stopPropagation();e.preventDefault()});d(document).on("click",".menu-list-view,.menu-list-sub",function(e){d(".menu-list").find(".active").removeClass("active");d(this).addClass("active");var t=d(this).attr("data-id"),n;ids=t.split("_");if(ids.length===1){d(".media").show();n=menuList[ids[0]]["type"];d('input[value="'+n+'"]').prop("checked","checked");d(".delmenu").html(fy("Delete")+fy("Menu"))}else{n=menuList[ids[0]]["sub_button"][ids[1]]["type"];d('input[name="type"][value="'+n+'"]').prop("checked","checked");d(".media").show();d(".delmenu").html(fy("Delete the submenu"))}var i=d(".layui-tab-title").find('input[name="type"]').index(d('input[value="'+n+'"]'));if(n==="click"||n==="view"||n==="miniprogram"){d(".layui-tab-content").find(".layui-tab-item").eq(i).addClass("layui-show").siblings().removeClass("layui-show");var s=ids.length>1?menuList[ids[0]]["sub_button"][ids[1]]["key"]:menuList[ids[0]]["key"];var a=d(".layui-tab-content").find(".layui-tab-item").eq(i).find("input").eq(0);switch(n){case"click":a.val(s);break;case"view":s=ids.length>1?menuList[ids[0]]["sub_button"][ids[1]]["url"]:menuList[ids[0]]["url"];a.val(s);break;case"miniprogram":var u=ids.length>1?menuList[ids[0]]["sub_button"][ids[1]]["appid"]:menuList[ids[0]]["appid"];var l=ids.length>1?menuList[ids[0]]["sub_button"][ids[1]]["pagepath"]:menuList[ids[0]]["pagepath"];a.val(u);d(".layui-tab-content").find(".layui-tab-item").eq(i).find("input").eq(1).val(l);break}}else{d(".layui-tab-content").find(".layui-tab-item").removeClass("layui-show")}layui.form.render();d('input[name="title"]').val(d(this).children("span").text());e.stopPropagation();e.preventDefault()});layui.form.on("radio(type)",function(){var e=d(this).val();var t=d(this).data("key");n(3,e,t)});d("input").keyup(function(){var e=d(this).val();var t=d(this).attr("name");if(t==="title"){d(".menu-list").find(".active").children("span").text(e);n()}else if(t==="menu_name"){a=e}else if(t==="url"){n(2,"view",t)}else if(t==="keyword"){n(2,"click",t)}else if(t==="miniprogram"){n(2,"miniprogram",t)}else{n(2,"click",t)}});function n(e=1,t="view",n=""){u=d(".menu-list").find(".active");if(u.length===0)return false;var i=u.attr("data-id");var s=i.split("_");var a=u.children("span").text();if(e===1){if(s.length===1){menuList[s[0]].name=a}else{menuList[s[0]].sub_button[s[1]].name=a}}else if(e===2){a=d('input[name="'+n+'"]').val();if(s.length===1){menuList[s[0]].type=t;menuList[s[0]][n]=a}else{menuList[s[0]].sub_button[s[1]].type=t;menuList[s[0]].sub_button[s[1]][n]=a}}else if(e===3){if(s.length===1){menuList[s[0]].name=a;menuList[s[0]].type=t;if(!menuList[s[0]].hasOwnProperty("key")){menuList[s[0]].key=n}}else{menuList[s[0]].sub_button[s[1]].name=a;menuList[s[0]].sub_button[s[1]].type=t;if(!menuList[s[0]].sub_button[s[1]].hasOwnProperty("key")){menuList[s[0]].sub_button[s[1]].key=n}}}}d(document).on("click",".delmenu",function(e){console.log(menuList);layer.confirm(fy("OK to delete the menu")+"？",{btn:[fy("Ok"),fy("Cancel")],title:fy("Tips")},function(){$menu=d(".menu-list").find(".active");if($menu.length===0){m.msg.error(fy("Please select the menu you want to delete"),function(){layer.closeAll()});return false}if($menu.find("ul>li").length>1){m.msg.error(fy("If there is a submenu, please delete the submenu first"),function(){layer.closeAll()});return false}var e=$menu.attr("data-id");ids=e.split("_");if(ids.length>1){menuList[ids[0]]["sub_button"].splice(ids[1],1);if($menu.parent("ul").children(".wx-menu-add").length===0){t=ids[0]+"_"+(parseInt(ids[1])-1);$menu.parent("ul").append('<li class="menu-list-sub wx-menu-add" data-id="'+t+'"><span class="layui-icon layui-icon-close"></span></li>')}}else{menuList.splice(ids[0],1);if($menu.parent("ul").children(".wx-menu-add").length===0){var t=ids[0];$menu.parent("ul").append('<li class="menu-list-view wx-menu-add" data-id="'+t+'"><span class="layui-icon layui-icon-close"></span></li>')}}$menu.remove();layer.closeAll()})});d(document).on("click","#save",function(){let n=null;let e="(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";let i=new RegExp(e);let s;if(menuList.length===0)m.msg.error(fy("Please enter")+fy("Menu name"));s=false;for(let t=0;t<menuList.length;t++){console.log(menuList[t].sub_button.length);if(menuList[t].sub_button.length){for(let e=0;e<menuList[t].sub_button.length;e++){console.log(menuList[t].sub_button[e]);if(menuList[t].sub_button[e]==undefined||!menuList[t].sub_button[e]["name"]||!menuList[t].sub_button[e]["type"]){m.msg.error(fy("The menu name cannot be empty"));return false}else if(menuList[t].sub_button[e]["type"]==="view"&&menuList[t].sub_button[e].hasOwnProperty("url")){s=true;n=menuList[t].sub_button[e].url;if(!i.test(n)){m.msg.error(fy("Please enter the correct URL address"));return false}}else if(menuList[t].sub_button[e].hasOwnProperty("appid")&&menuList[t].sub_button[e].hasOwnProperty("pagepath")){s=true}else{s=true}}}else{if(!menuList[t]["name"]||!menuList[t]["type"]){m.msg.error(fy("The menu name or menu type cannot be empty"));return false}else if(menuList[t]["type"]==="view"&&menuList[t].hasOwnProperty("url")){s=true;n=menuList[t].url;if(!i.test(n)){m.msg.error(fy("Please fill in the correct URL address"));return false}}else if(menuList[t].hasOwnProperty("appid")&&menuList[t].hasOwnProperty("pagepath")){s=true}else{s=true}}}if(s){m.request.post({url:window.location.href,data:{menu_data:JSON.stringify(menuList),menu_name:a,__token__:d('input[name="__token__"]').val()}},function(e){m.msg.success(e.msg,function(){parent.layui.table.reload(t.table_render_id)})},function(e){m.msg.error(e.msg)})}return false})}}};return e});