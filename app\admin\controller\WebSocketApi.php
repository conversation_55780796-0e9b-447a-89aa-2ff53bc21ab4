<?php

namespace app\admin\controller;

use app\common\controller\AdminController;
use think\facade\Db;
use think\facade\Request;

/**
 * WebSocket API控制器
 * 处理前端的拨号和挂断请求
 */
class WebSocketApi extends AdminController
{
    /**
     * 测试参数传递
     */
    public function testParams()
    {
        $customerId = $this->request->param('customer_id', '');
        $phone = $this->request->param('phone', '');
        $allParams = $this->request->param();

        $this->success('参数测试', [
            'customer_id' => $customerId,
            'phone' => $phone,
            'all_params' => $allParams,
            'method' => $this->request->method()
        ]);
    }

    /**
     * 发送拨号指令
     */
    public function dial()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        $customerId = $this->request->post('customer_id', 0);
        $phone = $this->request->post('phone', '');

        // 添加调试信息
        \think\facade\Log::info('拨号请求参数', [
            'customer_id' => $customerId,
            'customer_id_raw' => $this->request->post('customer_id'),
            'phone' => $phone,
            'admin_id' => $this->admin['admin_id'],
            'username' => $this->admin['username'],
            'all_post' => $this->request->post()
        ]);

        // 检查是否是占位符未替换
        if ($customerId === '{id}' || $phone === '{phone}') {
            $this->error('参数占位符未替换：客户ID=' . $customerId . ', 电话=' . $phone);
        }

        if (empty($customerId) || empty($phone)) {
            $this->error('参数错误：客户ID=' . $customerId . ', 电话=' . $phone);
        }

        // 获取客户信息
        $customer = Db::name('crm_customer')->find($customerId);
        if (!$customer) {
            // 查询是否存在该ID的客户，并获取所有客户信息用于调试
            $customerCount = Db::name('crm_customer')->count();
            $allCustomers = Db::name('crm_customer')->field('id,name,phone,pr_user')->limit(5)->select()->toArray();
            $specificCustomer = Db::name('crm_customer')->where('id', $customerId)->find();

            \think\facade\Log::info('客户查询失败', [
                'customer_id' => $customerId,
                'customer_id_type' => gettype($customerId),
                'customer_count' => $customerCount,
                'all_customers' => $allCustomers,
                'specific_customer' => $specificCustomer,
                'table_name' => Db::name('crm_customer')->getTable()
            ]);

            $this->error('客户不存在，客户ID: ' . $customerId . ' (类型: ' . gettype($customerId) . '), 数据库中客户总数: ' . $customerCount . ', 表名: ' . Db::name('crm_customer')->getTable());
        }
        
        // 验证权限：只能操作自己负责的客户
        if ($customer['pr_user'] !== $this->admin['username']) {
            $this->error('无权限操作此客户');
        }
        
        // 检查设备是否在线（状态为1且5分钟内有活跃）
        $expiredTime = date('Y-m-d H:i:s', time() - 300); // 5分钟前
        $device = Db::name('admin_device')
            ->where('admin_id', $this->admin['admin_id'])
            ->where('status', 1)
            ->where('last_active', '>=', $expiredTime)
            ->find();
            
        if (!$device) {
            // 清理过期的设备连接
            Db::name('admin_device')
                ->where('admin_id', $this->admin['admin_id'])
                ->where('last_active', '<', $expiredTime)
                ->update(['status' => 0]);
            $this->error('设备未连接或已离线，请先在手机端登录');
        }
        
        // 创建跟进记录
        $recordId = Db::name('crm_record')->insertGetId([
            'khname' => $customer['name'],
            'khphone' => $phone,
            'admin_id' => $this->admin['admin_id'],
            'pr_user' => $this->admin['username'],
            'customer_id' => $customerId,
            'content' => '发起拨号：' . $phone,
            'create_time' => time(),
            'record_type' => '拨号'
        ]);
        
        // 发送WebSocket消息到指定端口
        $result = $this->sendWebSocketMessage([
            'type' => 'dial',
            'admin_id' => $this->admin['admin_id'],
            'data' => [
                'phone' => $phone,
                'client_name' => $customer['name'],
                'client_id' => $customerId,
                'record_id' => $recordId
            ]
        ]);
        
        if ($result) {
            $this->success('拨号指令发送成功');
        } else {
            $this->error('拨号指令发送失败');
        }
    }
    
    /**
     * 发送挂断指令
     */
    public function hangup()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        // 检查设备是否在线（状态为1且5分钟内有活跃）
        $expiredTime = date('Y-m-d H:i:s', time() - 300); // 5分钟前
        $device = Db::name('admin_device')
            ->where('admin_id', $this->admin['admin_id'])
            ->where('status', 1)
            ->where('last_active', '>=', $expiredTime)
            ->find();
            
        if (!$device) {
            // 清理过期的设备连接
            Db::name('admin_device')
                ->where('admin_id', $this->admin['admin_id'])
                ->where('last_active', '<', $expiredTime)
                ->update(['status' => 0]);
            $this->error('设备未连接或已离线，请先在手机端登录');
        }
        
        // 发送WebSocket消息
        $result = $this->sendWebSocketMessage([
            'type' => 'hangup',
            'admin_id' => $this->admin['admin_id'],
            'data' => []
        ]);
        
        if ($result) {
            $this->success('挂断指令发送成功');
        } else {
            $this->error('挂断指令发送失败');
        }
    }
    
    /**
     * 获取设备连接状态
     */
    public function getDeviceStatus()
    {
        // 检查设备是否在线（状态为1且5分钟内有活跃）
        $expiredTime = date('Y-m-d H:i:s', time() - 300); // 5分钟前
        $device = Db::name('admin_device')
            ->where('admin_id', $this->admin['admin_id'])
            ->where('status', 1)
            ->where('last_active', '>=', $expiredTime)
            ->find();
            
        // 如果没有找到在线设备，清理过期的设备连接
        if (!$device) {
            Db::name('admin_device')
                ->where('admin_id', $this->admin['admin_id'])
                ->where('last_active', '<', $expiredTime)
                ->update(['status' => 0]);
        }
            
        $status = [
            'online' => !empty($device),
            'device_id' => $device['device_id'] ?? '',
            'last_active' => $device['last_active'] ?? ''
        ];
        
        $this->success('获取成功', $status);
    }
    
    /**
     * 发送WebSocket消息到服务器
     */
    private function sendWebSocketMessage($message)
    {
        try {
            // 连接到WebSocket服务器的内部API端口
            $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            if (!$socket) {
                return false;
            }
            
            // 连接到WebSocket服务器
            $result = socket_connect($socket, '127.0.0.1', 8081);
            if (!$result) {
                socket_close($socket);
                return false;
            }
            
            // 发送消息
            $data = json_encode($message);
            socket_write($socket, $data, strlen($data));
            
            // 读取响应
            $response = socket_read($socket, 1024);
            socket_close($socket);
            
            return !empty($response);
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
