<?php

namespace app\admin\controller\crm;

use app\common\controller\AdminController;

use think\App;
use think\facade\Db;

/**
 * @ControllerAnnotation(title="crm_contract")
 */
class Contract extends AdminController
{


    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->model = new \app\admin\model\CrmContract();
        
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $rule = [
                'customer_id|必须选择对应客户'    => 'require',
                'money|合同金额'    => 'require',
                'name|合同名称' => 'require|unique:crm_contract',
                'numbering|合同编号' => 'require|unique:crm_contract',
            ];

            $this->validater($post['contract'], $rule);
            $row_customer=Db::name('crm_customer')->field('id,source')->where([['id','=',$post['contract']['customer_id']],['pr_user','=',$this->admin['username']]])->find();
            if(empty($row_customer['id'])){
                $this->error('合同只能选择自己的客户');
            }
            Db::startTrans();
            try {

                $post['contract']['order_time'] = $post['contract']['order_time'] ? strtotime($post['contract']['order_time']) : 0;
                $post['contract']['start_time'] = $post['contract']['start_time'] ? strtotime($post['contract']['start_time']) : 0;
                $post['contract']['end_time'] = $post['contract']['end_time'] ? strtotime($post['contract']['end_time']) : 0;
                if($post['contract']['start_time'] > $post['contract']['end_time']){
                    throw new \Exception("合同开始时间不能大于结束时间!");
                }
                $post['contract']['create_user_id'] =$this->admin['admin_id'];
                $post['contract']['owner_user_id'] =$this->admin['admin_id'];
                $post['contract']['check_status'] =0;
                $post['contract']['source'] =$row_customer['source'];
                $save = $this->model->allowField($this->model->getTableFields())->save($post['contract']);
                $total_price=0;
                if(!empty($post['product'])){
                    $i=1;
                    foreach ($post['product'] as &$row) {
                        unset($row['id']);
                        if(!is_numeric($row['nums']) || $row['nums'] < 1){
                            throw new \Exception('合同记录的产品'.$row['product_extend']['name'].'数量不对，'.$i.'行');
                        }

                        $row_product=Db::name('product')->field('id,inventory')->where('id', '=',$row['product_id'])->where('status', '=',1)->find();
                        if(empty($row_product['id'])){
                            throw new \Exception("不存在的产品");
                        }
                        if ($row_product['inventory'] < $row['nums']) {

                            throw new \Exception($row_product['name'] . "库存不足");
                        }
                        $row['product_extend']=json_encode($row['product_extend'],256);
                        $row['contract_id'] =$this->model->id;
                        ;

                        $total_price=bcadd($total_price,bcsub(bcmul($row['sale_price'],$row['nums'],2),$row['discount'],2),2);
//                        $total_price=$total_price+$row['sale_price']*$row['nums']-$row['discount'];
                        $i++;
                        //减少库存
                        Db::name('product')->where('id', '=',$row['product_id'])->where('status', '=',1)->dec('inventory',$row['nums'])->update();
                    }
                    Db::name('crm_contract_product')->insertAll($post['product']);
                }


                $tudo_id=Db::name('todo')->insertGetId([
                    'title'=>'合同审核',
                    'mess'=>$this->admin['username'].'添加合同：'.$post['contract']['name'],
//                    'url'=>myurl('admin/order/editAudit',['id'=>$id]),
                    'url'=>'crm.contract/editAudit.html?id='.$this->model->id,
                    'createtime'=>time(),
                    'result'=>'待审核',
                    'admin_id'=>$this->admin['admin_id'],
                    'show_auth_group_id'=>'',
                    'show_auth_admin_id'=>$post['contract']['flow_admin_id'],
                ]);
                $this->model->save(['total_price'=>$total_price,'tudo_id'=>$tudo_id]);
            } catch (\Exception $e) {
                Db::rollback();
                $this->error(fy('Save failed').':'.$e->getMessage());
            }
            if($save){
                Db::commit();
                $this->success(fy('Save successfully')) ;
            }else{
                Db::rollback();
                $this->error(fy('Save failed'));
            }
        }
        $numbering=$this->model->autoNo($this->system['ht_prefix']);
        $this->assign(['numbering'=>$numbering]);
        return $this->fetch();
    }

    public function create_numbering(){
        $numbering=$this->model->autoNo($this->system['ht_prefix']);
        $this->success('生成成功',null,['numbering'=>$numbering]);
    }
//    获取合同对应的产品
    public function product(){
        if($this->request->isAjax()){
            $id=$this->request->get('id');
            if($id){
                $info=Db::name('crm_contract_product')->where('contract_id','=',$id)->order('create_time ASC')->select()->toArray();
                foreach ($info as &$row){
                    $row=$row+ json_decode($row['product_extend'],true);
                    unset($row['product_extend']);
                }
                $this->success(fy('Get successful'),null,$info);
            }

        }
    }

    //    传入商机产品id 删除对应商机产品
    public function delproduct(){
        if($this->request->isAjax()){
            $product_id=$this->request->get('product_id');
            if($product_id){
                $res=Db::name('crm_contract_product')->where('id','=',$product_id)->delete();
                if($res){
                    $this->success(fy('Delete succeeded'));
                }else{
                    $this->success(fy('Delete failed'));
                }

            }

        }
    }


    
}