{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit" ng-app="hd" ng-controller="ctrl">
    <form class="layui-form layui-form-pane">
       <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Field type')}</label>
            <div class="layui-input-block">
                <select id="type" name="type" lay-filter="type" ng-model="field.type" class="required" lay-verify="required" {if condition="ACTION eq 'fieldedit'"}disabled{/if}>
                    <option value='' >{:fy('Please select')}{:fy('Field type')}</option>
                   <!-- <option value="catid">栏目</option>
                    {if condition="ACTION_NAME eq 'fieldedit'"}<option value="title">标题</option>{/if}-->
                    <option value="text" >单行文本</option>
                    <option value="textarea" >多行文本</option>
                    <option value="select" >下拉列表</option>
                    <option value="radio" >单选按钮</option>
                    <option value="checkbox" >复选框</option>
                    <option value="image" >单张图片</option>
                    <option value="images" >多张图片</option>
                    <option value="file" >文件上传</option>
                    <option value="number" >数字</option>
                    <option value="datetime" >日期和时间</option>
                    <!--<option value="posid" >推荐位</option>-->
                    <!--<option value="groupid" >会员组</option>-->
                    <option value="linkage" >地址联动</option>
                <option value="editor" >编辑器</option>
                   <!-- <option value="template">模板选择</option>-->
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Field')}{:fy('Name')}</label>
            <div class="layui-input-inline">
                <input type="text" name="name" ng-model="field.name" lay-verify="required" placeholder="必填：对应表单名" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">例如 : {:fy('Client name')} </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label required">字段{:fy('Identification')}</label>
            <div class="layui-input-inline">
                <input type="text" name="field" ng-model="field.field" lay-verify="required" placeholder="必填：对应数据表字段" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">例如 : client_name <span style="color: red;">请使用字母开头</span></div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">字段设置</label>
            <div class="layui-input-block" id="field_setup" style="padding-left: 10px;">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">样式名称</label>
            <div class="layui-input-inline">
                <input type="text" name="class" ng-model="field.class" value="" placeholder="class名称" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">字段附加样式效果</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">必填</label>
            <div class="layui-input-block">
                <input type="radio" name="required" ng-model="field.required" ng-checked="field.required==1" ng-value="1" title="是">
                <input type="radio" name="required" ng-model="field.required" ng-checked="field.required==0" ng-value="0" title="否">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Validation rules')}</label>
            <div class="layui-input-block">
                <select name="pattern" lay-verify="required" ng-model="field.pattern" ng-options="v.name as v.title for v in pattern"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">字符长度</label>
            <div class="layui-input-inline" style="width: 100px;">
                <input type="text" name="minlength" ng-model="field.minlength"  class="layui-input">
            </div>
            <div class="layui-form-mid">-</div>
            <div class="layui-input-inline" style="width: 100px;">
                <input type="text" name="maxlength" ng-model="field.maxlength" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">错误提示</label>
            <div class="layui-input-block">
                <input type="text" name="errormsg" ng-model="field.errormsg" value="" placeholder="验证失败错误信息" class="layui-input">
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label">显示搜索</label>
            <div class="layui-input-block">
                    <input type="radio" name="isfilter"  title="启用" ng-model="field.isfilter" ng-checked="field.isfilter==1" ng-value="1">
                    <input type="radio" name="isfilter"  title={:fy('Close')} ng-model="field.isfilter" ng-checked="field.isfilter==0" ng-value="0">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">列表显示</label>
            <div class="layui-input-block">
                    <input type="radio" name="islist" value="1" title="启用" ng-model="field.islist" ng-checked="field.islist==1" ng-value="1">
                    <input type="radio" name="islist" value="0" title={:fy('Close')} ng-model="field.isfilter" ng-checked="field.islist==0" ng-value="0">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否启用</label>
            <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" ng-model="field.status" ng-checked="field.status==1" ng-value="1">
                    <input type="radio" name="status" value="0" title={:fy('Close')} ng-model="field.status" ng-checked="field.status==0" ng-value="0">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">{:lang('submit')}</button>
                <a href="{:url('field',['id'=>$moduleid])}" class="layui-btn layui-btn-primary">{:lang('back')}</a>
            </div>
        </div>
    </form>
</div>
{include file="common/foot"/}
<script src="__MY_PUBLIC__/static/common/js/jquery.2.1.1.min.js"></script>
<script src="__MY_PUBLIC__/static/common/js/angular.min.js"></script>
<script>
    var m = angular.module('hd',[]);
    m.controller('ctrl',['$scope',function($scope) {
        $scope.field = '{$info|raw}'!='null'?{$info|raw}:{type:'',field:'',name:'',required:1,minlength:'',maxlength:'',errormsg:'',pattern:''};
        $scope.pattern = {$pattern|raw};
        var oldfield = $scope.field.field;
        layui.use(['form', 'layer'], function () {
            var form = layui.form, layer = layui.layer;
            field_setting($scope.field,'edit',form);
            //事件监听
            form.on('select(type)', function(type){
                field_setting(type,'add',form);
            });
            form.on('submit(submit)', function (data) {
                var loading = layer.load(1, {shade: [0.1, '#fff']});
                data.field.moduleid = "{$moduleid}";
                if($scope.field.id){
                    data.field.oldfield = oldfield;
                    data.field.id = $scope.field.id;
                }
                $.post("", data.field, function (res) {
                    layer.close(loading);
                    if (res.code > 0) {
                        layer.msg(res.msg, {time: 2800, icon: 1}, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {time: 2800, icon: 2});
                    }
                });
            });

        });
    }]);
    function field_setting(info,action,form) {
        if(action=='add'){
            var data =  '';
            var url =  "{:url('fieldAdd')}?isajax=1&moduleid={$moduleid}&type="+type.value;
        }else{
            var data = info.setup;
            var url =  "{:url('fieldAdd')}?isajax=1&moduleid={$moduleid}&type="+info.type+"&name="+info.field;
        }
        $.ajax({
            type: "POST",
            url: url,
            data: data,
            beforeSend:function(){
                $('#field_setup').html('<i class="fa fa-spinner fa-spin fa-fw"></i>');
                form.render()
            },
            success: function(msg){
                $('#field_setup').html(msg);
                form.render()
            },
            complete:function(){
            },
            error:function(){
            }
        });
    }
</script>