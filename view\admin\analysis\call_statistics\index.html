<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>通话统计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__ADMIN_PATH__layui/css/layui.css" media="all">
    <link rel="stylesheet" href="__ADMIN_PATH__css/admin.css" media="all">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 统计卡片 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">统计概览</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10" id="summaryCards">
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body layui-text-center">
                                    <div class="layui-font-32 layui-text-blue" id="totalDays">0</div>
                                    <div>统计天数</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body layui-text-center">
                                    <div class="layui-font-32 layui-text-green" id="totalCalls">0</div>
                                    <div>总通话量</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body layui-text-center">
                                    <div class="layui-font-32 layui-text-orange" id="avgCallsPerDay">0</div>
                                    <div>日均通话</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body layui-text-center">
                                    <div class="layui-font-32 layui-text-red" id="totalDurationText">0</div>
                                    <div>总时长</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body layui-text-center">
                                    <div class="layui-font-32 layui-text-cyan" id="avgDurationPerCallText">0</div>
                                    <div>平均时长</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2">
                            <div class="layui-card">
                                <div class="layui-card-body layui-text-center">
                                    <div class="layui-font-32 layui-text-purple" id="avgConnectRate">0%</div>
                                    <div>平均接通率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 图表展示 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    趋势图表
                    <div class="layui-btn-group" style="float: right;">
                        <button class="layui-btn layui-btn-sm" onclick="loadChart(7)">最近7天</button>
                        <button class="layui-btn layui-btn-sm" onclick="loadChart(30)">最近30天</button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="loadChart(90)">最近90天</button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div id="chartContainer" style="height: 400px;"></div>
                </div>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    通话统计数据
                    <div class="layui-btn-group" style="float: right;">
                        <button class="layui-btn layui-btn-sm" onclick="exportData()">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <!-- 搜索表单 -->
                    <form class="layui-form" lay-filter="searchForm">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md3">
                                <input type="text" name="username" placeholder="用户名" class="layui-input">
                            </div>
                            <div class="layui-col-md3">
                                <input type="text" name="start_date" placeholder="开始日期" class="layui-input" id="startDate">
                            </div>
                            <div class="layui-col-md3">
                                <input type="text" name="end_date" placeholder="结束日期" class="layui-input" id="endDate">
                            </div>
                            <div class="layui-col-md3">
                                <button type="button" class="layui-btn" onclick="searchData()">搜索</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 数据表格 -->
                    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">刷新</button>
    </div>
</script>

<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
</script>

<script src="__ADMIN_PATH__layui/layui.js"></script>
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
<script>
layui.use(['table', 'form', 'laydate', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var laydate = layui.laydate;
    var layer = layui.layer;
    
    // 初始化日期选择器
    laydate.render({
        elem: '#startDate',
        type: 'date'
    });
    
    laydate.render({
        elem: '#endDate',
        type: 'date'
    });
    
    // 初始化表格
    table.render({
        elem: '#dataTable',
        url: '{:url("index")}',
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'username', title: '用户名', width: 120},
            {field: 'stat_date', title: '统计日期', width: 120, sort: true},
            {field: 'call_count', title: '通话量', width: 100, sort: true},
            {field: 'avg_duration_text', title: '平均时长', width: 120},
            {field: 'total_duration_text', title: '总时长', width: 120},
            {field: 'connect_rate_text', title: '接通率', width: 100, sort: true},
            {field: 'create_time', title: '创建时间', width: 160, sort: true},
            {fixed: 'right', title: '操作', toolbar: '#barDemo', width: 100}
        ]],
        page: true,
        height: 'full-320'
    });
    
    // 监听工具栏事件
    table.on('toolbar(dataTable)', function(obj){
        switch(obj.event){
            case 'refresh':
                table.reload('dataTable');
                loadSummary();
                break;
        }
    });
    
    // 监听行工具事件
    table.on('tool(dataTable)', function(obj){
        var data = obj.data;
        if(obj.event === 'del'){
            layer.confirm('确定删除这条记录吗？', function(index){
                $.post('{:url("delete")}/' + data.id, function(res){
                    if(res.code == 1){
                        obj.del();
                        layer.close(index);
                        layer.msg('删除成功');
                        loadSummary();
                    } else {
                        layer.msg(res.msg);
                    }
                });
            });
        }
    });
    
    // 加载统计汇总
    loadSummary();
    
    // 加载图表
    loadChart(30);
});

// 搜索数据
function searchData() {
    layui.table.reload('dataTable', {
        where: layui.form.val('searchForm')
    });
    loadSummary();
}

// 加载统计汇总
function loadSummary() {
    var formData = layui.form.val('searchForm');
    $.get('{:url("summary")}', formData, function(res){
        if(res.code == 200) {
            var data = res.data;
            $('#totalDays').text(data.total_days);
            $('#totalCalls').text(data.total_calls);
            $('#avgCallsPerDay').text(data.avg_calls_per_day);
            $('#totalDurationText').text(data.total_duration_text);
            $('#avgDurationPerCallText').text(data.avg_duration_per_call_text);
            $('#avgConnectRate').text(data.avg_connect_rate + '%');
        }
    });
}

// 加载图表
function loadChart(days) {
    var formData = layui.form.val('searchForm');
    formData.days = days;
    
    $.get('{:url("chart")}', formData, function(res){
        if(res.code == 200) {
            var data = res.data;
            var chartDom = document.getElementById('chartContainer');
            var myChart = echarts.init(chartDom);
            
            var option = {
                title: {
                    text: '通话统计趋势'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['通话量', '平均时长(分钟)', '接通率(%)']
                },
                xAxis: {
                    type: 'category',
                    data: data.dates
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '通话量',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '时长/接通率',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '通话量',
                        type: 'bar',
                        data: data.call_counts,
                        yAxisIndex: 0
                    },
                    {
                        name: '平均时长(分钟)',
                        type: 'line',
                        data: data.avg_durations,
                        yAxisIndex: 1
                    },
                    {
                        name: '接通率(%)',
                        type: 'line',
                        data: data.connect_rates,
                        yAxisIndex: 1
                    }
                ]
            };
            
            myChart.setOption(option);
        }
    });
}

// 导出数据
function exportData() {
    var formData = layui.form.val('searchForm');
    var params = new URLSearchParams(formData).toString();
    window.open('{:url("export")}?' + params);
}
</script>
</body>
</html>
