<link rel="stylesheet" href="__MY_PUBLIC__/static/admin/css/global.css" media="all">
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>菜单管理</legend>
    </fieldset>

    <blockquote class="layui-elem-quote" style="color:#FFB800; ">
        {:fy('Non professionals are not allowed to operate this page, otherwise the background page menu may not be displayed')}. <span class="red">{:fy('Note: Actions such as deletion, editing, modification, etc. are operation buttons, no right navigation display, no need to open the menu status')}</span>
    </blockquote>
    <blockquote class="layui-elem-quote">
        <a  class="layui-btn layui-btn-sm" data-open="{:url('ruleAdd')}" data-title="{:lang('add')}{:lang('node')}" >{:lang('add')}{:lang('node')}</a>
        <a class="layui-btn layui-btn-sm layui-btn-danger" data-request="{:url('clear')}" data-title="{:fy('Are you sure you want to clear the node')}？">{:fy('Clear the node')}</a>
        <a class="layui-btn layui-btn-normal layui-btn-sm"  onclick="openAll();">{:fy('Expand or collapse all')}</a>

    </blockquote>
    <table class="layui-table" id="treeTable" lay-filter="treeTable"></table>
</div>
<script type="text/html" id="auth">
    <input type="checkbox" name="authopen" value="{{d.id}}" lay-skin="switch" lay-text="{:fy('yes')}|{:fy('no')}" lay-filter="authopen" {{ d.authopen == 1 ? 'checked' : '' }}>
</script>
<script type="text/html" id="status">
    <input type="checkbox" name="menustatus" value="{{d.id}}" lay-skin="switch" lay-text="是|否" lay-filter="menustatus" {{ d.menustatus == 1 ? 'checked' : '' }}>
</script>
<script type="text/html" id="order">
    <input name="{{d.id}}" data-id="{{d.id}}" class="list_order layui-input" value=" {{d.sort}}" size="10"/>
</script>
<script type="text/html" id="icon">
    <span class="icon {{d.icon}}"></span>
</script>
<script type="text/html" id="action">
    <a class="layui-btn layui-btn-xs layui-btn-normal" data-open="{:url('ruleAdd')}?id={{d.id}}" data-title="{:lang('Add child nodes')}">{:lang('Add child nodes')}</a>
    <a class="layui-btn layui-btn-xs" data-open="{:url('ruleEdit')}?id={{d.id}}" data-title={:lang('Edit')}>{:lang('Edit')}</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" data-request="{:url('ruleDel')}?id={{d.id}}" data-title="{:fy('Are you sure you want to delete the node')}？">{:lang('del')}</a>
</script>
<script type="text/html" id="topBtn">
   <a href="{:url('ruleAdd')}" class="layui-btn layui-btn-sm">{:lang('add')} {:fy('Permissions')}</a>
</script>
{include file="common/foot"/}
<script>
var editObj=null,ptable=null,treeGrid=null,tableId='treeTable',layer=null;
    layui.config({
        base: '__MY_PUBLIC__/static/plugs/layui-v2.7.6/extend/'
    }).extend({
        treeGrid:'treeGrid'
    }).use(['jquery','treeGrid','layer','form'], function(){
        var $=layui.jquery;
        treeGrid = layui.treeGrid;
        layer=layui.layer;
		form = layui.form;
        ptable=treeGrid.render({
            id:tableId
            ,elem: '#'+tableId
            ,idField:'id'
            ,url:'{:url("adminRule")}'
            ,cellMinWidth: 100
            ,treeId:'id'//树形id字段名称
            ,treeUpId:'pid'//树形父id字段名称
            ,treeShowName:'title'//以树形式显示的字段
            ,height:'full-140'
            ,isFilter:false
            ,iconOpen:true//是否显示图标【默认显示】
            ,isOpenDefault:true//节点默认是展开还是折叠【默认展开】
            ,cols: [[
                {field: 'id', title: 'ID', width: 70, fixed: true},
                {field: 'icon', align: 'center',title: '{:lang("icon")}', width: 60,templet: '#icon'},
                {field: 'title', title: "{:fy('Menu name')}", width: 200},
                {field: 'href', title: '{:fy("Controller")}/{:fy("Method")}', width: 200},
                {field: 'authopen',align: 'center', title: '{:fy("Whether to verify permissions")}', width: 150,toolbar: '#auth'},
                {field: 'menustatus',align: 'center',title: '菜单', width: 150,toolbar: '#status'},
                {field: 'sort',align: 'center', title: '{:lang("order")}', width: 80, templet: '#order'},
                {width: 200,align: 'center', toolbar: '#action'}
            ]]
            ,page:false
        });
        treeGrid.on('tool('+tableId+')',function (obj) {
			var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('{:fy("Are you sure you want to delete the record")}？', function(index){
                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                    $.post("{:url('ruleDel')}",{id:data.id},function(res){
                        layer.close(loading);
                        if(res.code==1){
                            layer.msg(res.msg,{time:2000,icon:1});
                            obj.del();
                        }else{
                            layer.msg(res.msg,{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
		form.on('switch(authopen)', function(obj){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var id = this.value;
            var authopen = obj.elem.checked===true?1:0;
            $.post('{:url("ruleTz")}',{'id':id,'authopen':authopen},function (res) {
                layer.close(loading);
                if (res.status==1) {
                    treeGrid.render;
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    treeGrid.render;
                    return false;
                }
            })
        });
		form.on('switch(menustatus)', function(obj){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var id = this.value;
            var menustatus = obj.elem.checked===true?1:0;
            $.post('{:url("ruleState")}',{'id':id,'menustatus':menustatus},function (res) {
                layer.close(loading);
                if (res.status==1) {
                    treeGrid.render;
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    treeGrid.render;
                    return false;
                }
            })
        });
		$('body').on('blur','.list_order',function() {
           var id = $(this).attr('data-id');
           var sort = $(this).val();
           $.post('{:url("ruleOrder")}',{id:id,sort:sort},function(res){
                if(res.code==1){
                    layer.msg(res.msg,{time:2000,icon:1},function(){
                        location.href = res.url;
                    });
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    treeGrid.render;
                }
           })
        })
    });
function openAll() {
    var treedata=treeGrid.getDataTreeList(tableId);
    treeGrid.treeOpenAll(tableId,!treedata[0][treeGrid.config.cols.isOpen]);
}
</script>
