<?php

use fast\Form;
use fast\Tree;
use think\facade\Db;
use app\common\model\Category;



if (! function_exists('tp5ControllerToTp6Controller')) {
    /**
     * TP5二级目录转TP6二级目录.
     *
     * @param  string  $class
     *
     * @return string
     */
    function tp5ControllerToTp6Controller($class = '')
    {
        $_arr = explode('/', $class);
        $route = $class;
        if (count($_arr) >= 3) {
            $route = '';
            foreach ($_arr as $_k => $_v) {
                $route .= $_v;
                ($_k == 0) ? $route .= '.' : $route .= '/';
            }
            $route = rtrim($route, '/');
        } elseif (count($_arr) == 2) {
            $route = implode('.', $_arr).'/index';
        }

        return $route;
    }
}

if (! function_exists('build_select')) {

    /**
     * 生成下拉列表.
     *
     * @param  string  $name
     * @param  mixed  $options
     * @param  mixed  $selected
     * @param  mixed  $attr
     *
     * @return string
     */
    function build_select($name, $options, $selected = [], $attr = [])
    {
        $options = is_array($options) ? $options : explode(',', $options);
        $selected = is_array($selected) ? $selected : explode(',', $selected);

        return Form::select($name, $options, $selected, $attr);
    }
}

if (! function_exists('build_radios')) {

    /**
     * 生成单选按钮组.
     *
     * @param  string  $name
     * @param  array  $list
     * @param  mixed  $selected
     *
     * @return string
     */
    function build_radios($name, $list = [], $selected = null)
    {
        $html = [];
        $selected = is_null($selected) ? key($list) : $selected;
        $selected = is_array($selected) ? $selected : explode(',', $selected);
        foreach ($list as $k => $v) {
            $html[] = sprintf(Form::label("{$name}-{$k}", "%s {$v}"),
                Form::radio($name, $k, in_array($k, $selected), ['id' => "{$name}-{$k}"]));
        }

        return '<div class="radio">'.implode(' ', $html).'</div>';
    }
}

if (! function_exists('build_checkboxs')) {

    /**
     * 生成复选按钮组.
     *
     * @param  string  $name
     * @param  array  $list
     * @param  mixed  $selected
     *
     * @return string
     */
    function build_checkboxs($name, $list = [], $selected = null)
    {
        $html = [];
        $selected = is_null($selected) ? [] : $selected;
        $selected = is_array($selected) ? $selected : explode(',', $selected);
        foreach ($list as $k => $v) {
            $html[] = sprintf(Form::label("{$name}-{$k}", "%s {$v}"),
                Form::checkbox($name, $k, in_array($k, $selected), ['id' => "{$name}-{$k}"]));
        }

        return '<div class="checkbox">'.implode(' ', $html).'</div>';
    }
}

if (! function_exists('build_category_select')) {

    /**
     * 生成分类下拉列表框.
     *
     * @param  string  $name
     * @param  string  $type
     * @param  mixed  $selected
     * @param  array  $attr
     * @param  array  $header
     *
     * @return string
     */
    function build_category_select($name, $type, $selected = null, $attr = [], $header = [])
    {
        $tree = Tree::instance();
        $tree->init(Category::getCategoryArray($type), 'pid');
        $categorylist = $tree->getTreeList($tree->getTreeArray(0), 'name');
        $categorydata = $header ? $header : [];
        foreach ($categorylist as $k => $v) {
            $categorydata[$v['id']] = $v['name'];
        }
        $attr = array_merge(['id' => "c-{$name}", 'class' => 'form-control selectpicker'], $attr);

        return build_select($name, $categorydata, $selected, $attr);
    }
}

if (! function_exists('build_toolbar')) {

    /**
     * 生成表格操作按钮栏.
     *
     * @param  array  $btns  按钮组
     * @param  array  $attr  按钮属性值
     *
     * @return string
     */
    function build_toolbar($btns = null, $attr = [])
    {
        $auth = \app\admin\library\Auth::instance();
        $controller = str_replace('.', '/', strtolower(think\facade\Request::instance()->controller()));
        $btns = $btns ? $btns : ['refresh', 'add', 'edit', 'del', 'import'];
        $btns = is_array($btns) ? $btns : explode(',', $btns);
        $index = array_search('delete', $btns);
        if ($index !== false) {
            $btns[$index] = 'del';
        }
        $btnAttr = [
            'refresh' => ['javascript:;', 'btn btn-primary btn-refresh', 'fa fa-refresh', '', __('Refresh')],
            'add'     => ['javascript:;', 'btn btn-success btn-add', 'fa fa-plus', __('Add'), __('Add')],
            'edit'    => [
                'javascript:;',
                'btn btn-success btn-edit btn-disabled disabled',
                'fa fa-pencil',
                __('Edit'),
                __('Edit'),
            ],
            'del'     => [
                'javascript:;',
                'btn btn-danger btn-del btn-disabled disabled',
                'fa fa-trash',
                __('Delete'),
                __('Delete'),
            ],
            'import'  => ['javascript:;', 'btn btn-info btn-import', 'fa fa-upload', __('Import'), __('Import')],
        ];
        $btnAttr = array_merge($btnAttr, $attr);
        $html = [];
        foreach ($btns as $k => $v) {
            //如果未定义或没有权限
            if (! isset($btnAttr[$v]) || ($v !== 'refresh' && ! $auth->check("{$controller}/{$v}"))) {
                continue;
            }
            [$href, $class, $icon, $text, $title] = $btnAttr[$v];
            //$extend = $v == 'import' ? 'id="btn-import-file" data-url="ajax/upload" data-mimetype="csv,xls,xlsx" data-multiple="false"' : '';
            //$html[] = '<a href="' . $href . '" class="' . $class . '" title="' . $title . '" ' . $extend . '><i class="' . $icon . '"></i> ' . $text . '</a>';
            if ($v == 'import') {
                $template = str_replace('/', '_', $controller);
                $download = '';
                if (file_exists("./template/{$template}.xlsx")) {
                    $download .= "<li><a href=\"/template/{$template}.xlsx\" target=\"_blank\">XLSX模版</a></li>";
                }
                if (file_exists("./template/{$template}.xls")) {
                    $download .= "<li><a href=\"/template/{$template}.xls\" target=\"_blank\">XLS模版</a></li>";
                }
                if (file_exists("./template/{$template}.csv")) {
                    $download .= empty($download) ? '' : '<li class="divider"></li>';
                    $download .= "<li><a href=\"/template/{$template}.csv\" target=\"_blank\">CSV模版</a></li>";
                }
                $download .= empty($download) ? '' : "\n                            ";
                if (! empty($download)) {
                    $html[] = <<<EOT
                        <div class="btn-group">
                            <button type="button" href="{$href}" class="btn btn-info btn-import" title="{$title}" id="btn-import-file" data-url="ajax/upload" data-mimetype="csv,xls,xlsx" data-multiple="false"><i class="{$icon}"></i> {$text}</button>
                            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" title="下载批量导入模版">
                                <span class="caret"></span>
                                <span class="sr-only">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu" role="menu">{$download}</ul>
                        </div>
EOT;
                } else {
                    $html[] = '<a href="'.$href.'" class="'.$class.'" title="'.$title.'" id="btn-import-file" data-url="ajax/upload" data-mimetype="csv,xls,xlsx" data-multiple="false"><i class="'.$icon.'"></i> '.$text.'</a>';
                }
            } else {
                $html[] = '<a href="'.$href.'" class="'.$class.'" title="'.$title.'"><i class="'.$icon.'"></i> '.$text.'</a>';
            }
        }

        return implode(' ', $html);
    }
}

if (! function_exists('build_heading')) {

    /**
     * 生成页面Heading.
     *
     * @param  string  $path  指定的path
     *
     * @return string
     */
    function build_heading($path = null, $container = true)
    {
        $title = $content = '';
        if (is_null($path)) {
            $action = request()->action();
            $controller = str_replace('.', '/', request()->controller());
            $path = strtolower($controller.($action && $action != 'index' ? '/'.$action : ''));
        }
        // 根据当前的URI自动匹配父节点的标题和备注
        $data = Db::name('auth_rule')->where('name', $path)->field('title,remark')->find();
        if ($data) {
            $title = __($data['title']);
            $content = __($data['remark']);
        }
        if (! $content) {
            return '';
        }
        $result = '<div class="panel-lead"><em>'.$title.'</em>'.$content.'</div>';
        if ($container) {
            $result = '<div class="panel-heading">'.$result.'</div>';
        }

        return $result;
    }
}

if (!function_exists('build_suffix_image')) {
    /**
     * 生成文件后缀图片
     * @param string $suffix 后缀
     * @param null   $background
     * @return string
     */
    function build_suffix_image($suffix, $background = null)
    {
        $suffix = mb_substr(strtoupper($suffix), 0, 4);
        $total = unpack('L', hash('adler32', $suffix, true))[1];
        $hue = $total % 360;
        list($r, $g, $b) = hsv2rgb($hue / 360, 0.3, 0.9);

        $background = $background ? $background : "rgb({$r},{$g},{$b})";

        $icon = <<<EOT
        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
            <path style="fill:#E2E5E7;" d="M128,0c-17.6,0-32,14.4-32,32v448c0,17.6,14.4,32,32,32h320c17.6,0,32-14.4,32-32V128L352,0H128z"/>
            <path style="fill:#B0B7BD;" d="M384,128h96L352,0v96C352,113.6,366.4,128,384,128z"/>
            <polygon style="fill:#CAD1D8;" points="480,224 384,128 480,128 "/>
            <path style="fill:{$background};" d="M416,416c0,8.8-7.2,16-16,16H48c-8.8,0-16-7.2-16-16V256c0-8.8,7.2-16,16-16h352c8.8,0,16,7.2,16,16 V416z"/>
            <path style="fill:#CAD1D8;" d="M400,432H96v16h304c8.8,0,16-7.2,16-16v-16C416,424.8,408.8,432,400,432z"/>
            <g><text><tspan x="220" y="380" font-size="124" font-family="Verdana, Helvetica, Arial, sans-serif" fill="white" text-anchor="middle">{$suffix}</tspan></text></g>
        </svg>
EOT;
        return $icon;
    }
}

function build_select_list($table,$primary_key,$foreign_key){
    $res=Db::name($table)->field($primary_key.','.$foreign_key)->where('status','=',1)->order('sort ASC,id DESC')->column($foreign_key,$primary_key);
    return json_encode($res,320);
}

function build_option_input($table,$primary_key,$foreign_key,$value){
    $res=Db::name($table)->field($primary_key.','.$foreign_key)->where('status','=',1)->order('sort ASC,id DESC')->cache($table)->select();
    $str='';
    foreach ($res as $v){
        if($v[$primary_key]==$value){
            $selected='selected';
        }else{
            $selected='';
        }
        $str .= '<option value="'.$v[$primary_key].'" '.$selected.'>'.$v[$foreign_key].'</option>';
    }
    return $str;
}
//生成联动复选框
function build_checkbox_input($table,$primary_key,$foreign_key,$field,$value){
    $order='sort ASC,id DESC';
    $where=[['status','=',1]];
    if($table=='admin'){
        $order='admin_id ASC';
        $where=[['is_open','=',1]];
    }
    $res=Db::name($table)->field($primary_key.','.$foreign_key)->where($where)->order($order)->cache($table)->select();
    $str='';
    foreach ($res as $v){
        if($v[$primary_key]==$value){
            $checked='checked';
        }else{
            $checked='';
        }
        $str .= '<input lay-skin="primary" type="checkbox" name="'.$field.'[]" title="'.$v[$primary_key].'" value="'.$v[$primary_key].'" '.$checked.'>';
    }
    return $str;
}

//实现删除指定目录下几天前的数据
function deleteFilesOlderThanDays($directory, $days = 1) {
    // 计算指定天数前的日期时间戳（秒）
    $cutoffTime = strtotime(date('Y-m-d 00:00:00') . ' -'.$days.' day');

    // 使用目录迭代器遍历文件夹
    $dirIterator = new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS);
    $filesToDelete = [];

    foreach (new RecursiveIteratorIterator($dirIterator) as $file) {
        // 检查文件是否为文件且创建时间在指定日期之前
        if ($file->isFile() && $file->getCTime() < $cutoffTime) {
            unlink( $file->getPathname());
        }
    }

}