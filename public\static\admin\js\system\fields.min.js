define(["jquery","easy-admin","xm-select","treetable","iconPickerFa","autocomplete"],function(n,a,i){if(typeof String.prototype.indexOf!=="function"){String.prototype.indexOf=function(e){var t=this.length;while(t--){if(this[t]===e){return t}}return-1}}var s=layui.table,e=layui.treetable,t=layui.iconPickerFa,l=layui.autocomplete;var d={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"system.fields/index"+parameter,add_url:"system.fields/add"+parameter,delete_url:"system.fields/delete",edit_url:"system.fields/edit"+parameter,modify_url:"system.fields/modify"};var r={index:function(){a.table.render({init:d,limits:[15,20,30,40,50,60,70,80,90,100,500,1e3,5e3,1e4],toolbar:["refresh","add"],limit:CONFIG.ADMINPAGESIZE,cols:[[{field:"sort",title:fy("Sort"),edit:"text",width:100,search:false},{field:"name",title:fy("Field Name"),width:150,search:true},{field:"xsname",Width:150,title:fy("Show")+" "+fy("Name"),search:false,edit:"text"},{field:"width",Width:150,title:"宽度",search:false,edit:"text"},{field:"field",width:200,title:fy("Field")},{field:"show",width:120,title:fy("List display"),filter:"show",search:false,templet:function(e,t){if(e.status.indexOf("noshow")>-1){return"-"}else{return a.table.switch(e,t)}}},{field:"edit",width:100,title:fy("Form"),filter:"edit",search:false,templet:function(e,t){if(e.status.indexOf("noedit")!==-1){return"-"}else{return a.table.switch(e,t)}}},{field:"search",width:100,title:fy("Search"),filter:"search",search:false,selectList:{0:fy("Close"),1:fy("Open")},templet:function(e,t){if(e.status.indexOf("nosearch")!==-1){return"-"}else{return a.table.switch(e,t)}}},{field:"export",width:100,title:fy("Import"),filter:"export",search:false,selectList:{0:fy("Close"),1:fy("Open")},templet:function(e,t){if(e.status.indexOf("noimport")!==-1){return"-"}else{return a.table.switch(e,t)}}},{field:"require",width:100,title:"必填",filter:"require",search:false,tips:"是|否",selectList:{0:"否",1:"是"},templet:function(e,t){if(e.status.indexOf("norequire")!==-1){return"-"}if(e.rule.indexOf("require")!==-1){e.require=1}else{e.require=0}return a.table.switch(e,t)}},{field:"edit_readonly",width:100,title:"不可编辑",filter:"edit_readonly",search:false,tips:"是|否",selectList:{0:"是",1:"否"},templet:function(e,t){if(e.field=="id"||e.field=="create_time"||e.field=="update_time"||e.field=="status"||e.field=="sort"||e.field=="issystem"||e.is_key||e.status.indexOf("noedit")!==-1){return"-"}return a.table.switch(e,t)}},{field:"create_time",minWidth:200,title:fy("Creation time"),search:"range"},{width:150,title:fy("Operate"),templet:function(e,t){if(e.issystem===1){return"-"}else{return a.table.tool(e,t)}},operat:["edit","delete"],fixed:"right"}]],done:function(e,t,i){if(i===undefined&&e.msg&&e.url){a.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}if(a.checkMobile()){a.booksTemplet()}}});n("body").on("click","[data-treetable-refresh]",function(){renderTable()});n("body").on("click","[data-treetable-delete]",function(){var e=n(this).attr("data-treetable-delete"),t=n(this).attr("data-url");e=e||d.table_render_id;t=t!=undefined?a.url(t):window.location.href;var i=s.checkStatus(e),l=i.data;if(l.length<=0){a.msg.error(fy("Please check the data to be deleted"));return false}var r=[];n.each(l,function(e,t){r.push(t.id)});a.msg.confirm(fy("Confirm the deletion")+"?",function(){a.request.post({url:t,data:{id:r}},function(e){a.msg.success(e.msg,function(){renderTable()})})});return false});a.listen()},add:function(){t.render({elem:"#icon",url:PATH_CONFIG.iconLess,limit:12,click:function(e){n("#icon").val("fa "+e.icon)},success:function(e){console.log(e)}});l.render({elem:n("#href")[0],url:a.url("system.menu/getMenuTips"),template_val:"{{d.node}}",template_txt:"{{d.node}} <span class='layui-badge layui-bg-gray'>{{d.title}}</span>",onselect:function(e){}});a.listen();this.multiple_select()},edit:function(){t.render({elem:"#icon",url:PATH_CONFIG.iconLess,limit:12,click:function(e){n("#icon").val("fa "+e.icon)},success:function(e){console.log(e)}});l.render({elem:n("#href")[0],url:a.url("system.menu/getMenuTips"),template_val:"{{d.node}}",template_txt:"{{d.node}} <span class='layui-badge layui-bg-gray'>{{d.title}}</span>",onselect:function(e){}});a.listen();this.multiple_select()},multiple_select:function(){n(document).on("blur","#name",function(){var e=n(this).val();var t=n("#field").val();if(!a.empty(e)&&a.empty(t)){a.request.ajax("post",{url:a.url("ajax/pinyin"),data:{name:e}},function(e){n("#field").val(e.data)})}});var e="zn";if(CONFIG.LANG=="en-us"){e="en"}var t=i.render({el:"#multiple-select",language:e,data:CONFIG.ruleLst,name:"rule"})}};return r});