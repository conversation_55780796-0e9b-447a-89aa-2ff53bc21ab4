build: false
clone_depth: 1

environment:
  PHP_CHOCO_VERSION: 7.2.0
  PHP_CACHE_DIR: C:\tools\php

cache:
  - '%PHP_CACHE_DIR% -> appveyor.yml'

init:
  - SET PATH=%PHP_CACHE_DIR%;%PATH%
  - SET COMPOSER_CACHE_DIR=%PHP_CACHE_DIR%
  - SET COMPOSER_NO_INTERACTION=1
  - SET PHP=0
  - SET ANSICON=121x90 (121x90)

install:
  - IF EXIST %PHP_CACHE_DIR% (SET PHP=1)
  - IF %PHP%==0 cinst php -y --version %PHP_CHOCO_VERSION%  --params "/InstallDir:%PHP_CACHE_DIR%"
  - IF %PHP%==0 cinst composer -y --ia "/DEV=%PHP_CACHE_DIR%"
  - cd %PHP_CACHE_DIR%
  - IF %PHP%==0 copy php.ini-production php.ini
  - IF %PHP%==0 echo extension_dir=ext >> php.ini
  - IF %PHP%==0 echo extension=php_fileinfo.dll >> php.ini
  - IF %PHP%==0 echo extension=php_mbstring.dll >> php.ini
  - IF %PHP%==0 echo extension=php_openssl.dll >> php.ini
  - php -v
  - IF %PHP%==0 (composer --version) ELSE (composer self-update)
  - cd %APPVEYOR_BUILD_FOLDER%
  - composer install --prefer-dist --no-progress

test_script:
  - cd %APPVEYOR_BUILD_FOLDER%
  - vendor\bin\phpunit --colors=always
