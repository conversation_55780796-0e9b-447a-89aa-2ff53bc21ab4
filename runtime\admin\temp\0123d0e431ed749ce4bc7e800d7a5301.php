<?php /*a:0:{}*/ ?>
<input type="hidden" name="id" value="<?php echo htmlentities((isset($row['id']) && ($row['id'] !== '')?$row['id']:'')); ?>"><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('客户名称'); ?></label><div class="layui-input-block"><input type="text" id="name" name="name" lay-filter="name" class="layui-input"  lay-verify="required"  disabled="disabled" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('客户名称'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('客户名称'); ?>" value="<?php echo htmlentities((isset($row['name']) && ($row['name'] !== '')?$row['name']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('联系电话'); ?></label><div class="layui-input-block"><input type="text" id="phone" name="phone" class="layui-input"  lay-verify="required" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('联系电话'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('联系电话'); ?>" value="<?php echo htmlentities((isset($row['phone']) && ($row['phone'] !== '')?$row['phone']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('客户联系人'); ?></label><div class="layui-input-block"><input type="text" id="contact" name="contact" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('客户联系人'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('客户联系人'); ?>" value="<?php echo htmlentities((isset($row['contact']) && ($row['contact'] !== '')?$row['contact']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('邮箱'); ?></label><div class="layui-input-block"><input type="text" id="email" name="email" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('邮箱'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('邮箱'); ?>" value="<?php echo htmlentities((isset($row['email']) && ($row['email'] !== '')?$row['email']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('中国地区'); ?></label><div class="layui-input-block"><input type="text" data-toggle="city-picker" data-level="district" data-placeholder="请选择省/市/区" id="area" name="area" class="layui-input"  value="<?php echo htmlentities((isset($row['area']) && ($row['area'] !== '')?$row['area']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('详细地址'); ?></label><div class="layui-input-block"><input type="text" id="address" name="address" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('详细地址'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('详细地址'); ?>" value="<?php echo htmlentities((isset($row['address']) && ($row['address'] !== '')?$row['address']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('行业类别'); ?></label><div class="layui-input-block"><select  name="kh_hangye" class="layui-select" lay-search><option value=""><?php echo fy("Please select"); ?></option><?php echo build_option_input('crm_hangye','name','name',$row["kh_hangye"]); ?></select></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('客户等级'); ?></label><div class="layui-input-block"><select  name="kh_rank" class="layui-select" lay-search><option value=""><?php echo fy("Please select"); ?></option><?php echo build_option_input('crm_rank','name','name',$row["kh_rank"]); ?></select></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('是否成交'); ?></label><div class="layui-input-block"><select  name="issuccess" class="layui-select" lay-search><option value=""><?php echo fy("Please select"); ?></option><option value="0" <?php if(isset($row["issuccess"])&&$row["issuccess"]=="0"): ?>selected<?php endif; ?>>未成交</option><option value="1" <?php if(isset($row["issuccess"])&&$row["issuccess"]=="1"): ?>selected<?php endif; ?>>已成交</option></select></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('客户来源'); ?></label><div class="layui-input-block"><select  name="source" class="layui-select" lay-search><option value=""><?php echo fy("Please select"); ?></option><?php echo build_option_input('crm_source','name','name',$row["source"]); ?></select></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('客户状态'); ?></label><div class="layui-input-block"><select  name="kh_status" class="layui-select" lay-search><option value=""><?php echo fy("Please select"); ?></option><?php echo build_option_input('crm_status','name','name',$row["kh_status"]); ?></select></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('备注'); ?></label><div class="layui-input-block"><textarea  name="remark" class="layui-textarea textarea" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('备注'); ?>"><?php echo htmlentities((isset($row['remark']) && ($row['remark'] !== '')?$row['remark']:'')); ?></textarea></div></div>