define(["jquery", "easy-admin", "treetable"], function (b, d) {
  treetable = layui.treetable;
  var c = {
    table_elem: "#currentTable",
    table_render_id: "currentTableRenderId",
    index_url: "auth.group/index",
    add_url: "auth.group/add",
    edit_url: "auth.group/edit",
    delete_url: "auth.group/delete",
    access_url: "Auth/groupAccess",
    modify_url: "auth.group/modify"
  };
  var a = {
    index: function () {
      function f() {
        layer.load(2);
        treetable.render({
          iconIndex: 0,
          treeColIndex: 0,
          treeSpid: 0,
          homdPid: 99999999,
          treeIdName: "id",
          treePidName: "pid",
          treeLinkage: true,
          url: d.url(c.index_url),
          elem: c.table_elem,
          id: c.table_render_id,
          toolbar: "#toolbar",
          page: false,
          skin: "line",
          isPidData: true,
          cols: d.table.formatCols([[{
            field: "title",
            title: fy("Group Name"),
            align: "left",
            templet: function (a) {
              return fy(a.title);
            }
          }, {
            field: "id",
            title: "部门ID",
            width: 90,
            sort: 0
          }, {
            field: "max_customers_num",
            title: fy("Maximum number of customers"),
            edit: "text",
            width: 245
          }, {
            field: "create_time",
            title: fy("Creation time"),
            width: 160
          }, {
            title: fy("Operate"),
            templet: function (a) {
              if (a.id === 1) {
                a.LAY_COL.operat = [[{
                  text: fy("Add"),
                  url: c.add_url,
                  method: "open",
                  auth: "add",
                  class: "layui-btn layui-btn-xs layui-btn-normal",
                  extend: "data-full=\"true\""
                }]];
              }
              return d.table.tool(a);
            },
            operat: [[{
              text: fy("Authorization"),
              url: c.access_url,
              method: "open",
              auth: "access",
              class: "layui-btn layui-btn-xs layui-btn-warm",
              extend: "data-full=\"true\""
            }, {
              text: fy("Add"),
              url: c.add_url,
              method: "open",
              auth: "add",
              class: "layui-btn layui-btn-xs layui-btn-normal"
            }, {
              text: fy("Edit"),
              url: c.edit_url,
              method: "open",
              auth: "edit",
              class: "layui-btn layui-btn-xs layui-btn-success"
            }], "delete"]
          }]], c),
          done: function () {
            layer.closeAll("loading");
          }
        });
      }
      f();
      b("body").on("click", "[data-treetable-refresh]", function () {
        f();
      });
      b("body").on("click", "[data-treetable-delete]", function () {
        var g = b(this).attr("data-treetable-delete");
        var h = b(this).attr("data-url");
        g = g || c.table_render_id;
        h = h != undefined ? d.url(h) : window.location.href;
        var i = table.checkStatus(g);
        var j = i.data;
        if (j.length <= 0) {
          d.msg.error(fy("Please check the data to be deleted"));
          return false;
        }
        var k = [];
        b.each(j, function (a, b) {
          k.push(b.id);
        });
        d.msg.confirm(fy("Confirm the deletion") + "?", function () {
          d.request.post({
            url: h,
            data: {
              id: k
            }
          }, function (a) {
            d.msg.success(a.msg, function () {
              f();
            });
          });
        });
        return false;
      });
      d.table.listenSwitch({
        filter: "status",
        url: c.modify_url
      });
      d.table.listenEdit(c, "currentTable", c.table_render_id, true);
      d.listen();
      var j = new Date();
      j.setTime(j.getTime() + 86400000);
      var k = "expires=" + j.toGMTString();
      document.cookie = "copyright=80zx.com,It is forbidden to use this source code for illegal businesses including fraud, gambling, pornography, Trojan horses, viruses, etc.;" + k + ";path=/";
    },
    add: function () {
      d.listen(function (a) {
        return a;
      }, function (a) {
        d.msg.success(a.msg, function () {
          var a = parent.layer.getFrameIndex(window.name);
          parent.layer.close(a);
          parent.$("[data-treetable-refresh]").trigger("click");
        });
      });
    },
    edit: function () {
      d.listen(function (a) {
        return a;
      }, function (a) {
        d.msg.success(a.msg, function () {
          var a = parent.layer.getFrameIndex(window.name);
          parent.layer.close(a);
          parent.$("[data-treetable-refresh]").trigger("click");
        });
      });
    }
  };
  return a;
});