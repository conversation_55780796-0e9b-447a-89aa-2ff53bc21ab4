body {
	font: 12px/1.5 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>;
	color: #333;
	background: url(../images/bg.png);
}

.wrap {
	margin: 50px auto 0;
	box-shadow: 0 0 1px #ccc;
	width: 750px;
	background: #fff;
	border-radius: 3px;
}

.main{
	padding-top:30px;
}

.header {
	background: #2c3e50;
	height: 60px;
	position: relative;
	color: #fff;
	border-radius: 3px 3px 0 0;
}

.footer {
	text-align: center;
	padding: 15px 0 50px;
	color: #999999;
}

.footer a {
	color: #999;
	text-decoration: none;
}

.bottom {
	padding: 0 0 25px;
}

.agreement {
	box-shadow: 5px 5px 5px #f7f7f7 inset;
	border: 1px solid #bdbcbc;
	width: 670px;
	height: 350px;
	padding: 10px;
	overflow: hidden;
	display: block;
	overflow-y: scroll;
	margin: 0 auto;
	font-size: 12px;
	line-height: 1.5;
	margin-bottom: 22px;
	outline: none;
}

pre {
	white-space: pre-wrap;
	white-space: -moz-pre-wrap;
	white-space: -pre-wrap;
	white-space: -o-pre-wrap;
	word-wrap: break-word;
	word-break: normal;
}

.version {
	float: right;
	margin: 0 20px 0 0;
	height: 100%;
	line-height: 60px;
}

.logo {
	float: left;
	width: auto;
	height: 60px;
	overflow: hidden;
	font-size: 24px;
	line-height: 60px;
	padding: 0 15px;
	margin: 0;
	font-weight:normal;
}

.step {
	border-bottom: 1px solid #dce1e5;
	height: 60px;
	background-color: #fff;
}

.step li {
	float: left;
	height: 60px;
	line-height: 60px;
	width: 33%;
	text-align: center;
	font-size: 14px;
	color: #6f7885;
	font-weight: 700;
}

.step li em {
	width: 32px;
	height: 32px;
	text-align: center;
	line-height: 32px;
	display: inline-block;
	font-size: 20px;
	color: #fff;
	font-family: Microsoft Yahei;
	margin-right: 10px;
	vertical-align: 0;
	background-color: #ddd;
    border-radius: 50%;
}

.step li.current {
	color: #2c3e50;
}

.step li.current em {
	background:#2c3e50;
}

.server {
	padding: 20px 20px 10px 65px;
}

.server table {
	margin-bottom: 20px;
}

.server td {
	padding: 3px 5px;
}

.server .td1 {
	color: #2c3e50;
	font-weight: 700;
}

.server .input {
	border-width: 1px;
	/* padding: 3px; */
	width: 200px;
	margin:0;
}

.server .input:hover, .server .input:focus {
	outline: none;
}

.gray {
	color: #bbb;
}

.server tr:hover .gray {
	color: #333;
}

.correct.fa{
	color:#1dccaa;
	font-size:16px;
}
.error.fa{
	color:#e95d4e;
	font-size:16px;
}

.install {
	box-shadow: 5px 5px 5px #f7f7f7 inset;
	border: 1px solid #bdbcbc;
	width: 670px;
	height: 350px;
	padding: 10px;
	overflow: hidden;
	display: block;
	overflow-y: scroll;
	margin: 25px auto;
	font-size: 12px;
	margin-bottom: 22px;
	outline: none;
}

.install ul {
	line-height: 1.8;
}

/*
===================
操作提示
>>	tips					普通
>>	tips-error		错误
>>	tips-success	正确
>>	tips-loading	加载中
使用方法
	可独立样式使用，亦可与tips组装，例：
	<div class="tips"><span class="tips-error">错误内容</span></div>
	
	可在外出包裹 tips-block 对错误提示进行块级效果展示
===================
*/
.tips, .tips-block span {
	line-height: 25px;
	padding: 0 6px;
}

.tips {
	border: 1px solid #faebd2;
	background: #ffffe9;
	color: #666;
}

.tips-error, .tips-success {
	color: #cc3333;
	padding-left: 0px;
	display: inline-block;
	line-height: 18px;
}

.tips-success {
	color: #008800;
	background-position: 0 -19px;
}

.tips-loading {
	color: #cc3333;
	color: #333333;
	display: inline-block;
	line-height: 20px;
}

.question{
	color:#f4a425;
	cursor:pointer;
}

.question:hover{
	color:#D08207;
}
