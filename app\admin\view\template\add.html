{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{$title}</legend>
    </fieldset>
    <form class="layui-form layui-form-pane">
        <input TYPE="hidden" name="type" value="{:input('param.type')}">
        <div class="layui-form-item">
            <label class="layui-form-label">文件名称</label>
            <div class="layui-input-block">
                <input type="text" name="file" value="" placeholder="{:lang('Please enter')}文件名称" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">文件类型</label>
            <div class="layui-input-block">
                <input type="radio" name="type" checked lay-filter="is_open" value="{$viewSuffix}" title="模版文件">
                <input type="radio" name="type" lay-filter="is_open" value="css" title="CSS文件">
                <input type="radio" name="type" lay-filter="is_open" value="js" title="JS文件">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">内容</label>
            <div class="layui-input-block">
                <textarea placeholder="{:lang('Please enter')}内容" name="content" rows="15" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">{:lang('submit')}</button>
                <a href="{:url('index',array('type'=>input('param.type')))}" class="layui-btn layui-btn-primary">{:lang('back')}</a>
            </div>
        </div>
    </form>
</div>
{include file="common/foot"/}
<script>
    layui.use('form', function () {
        var form = layui.form, $ = layui.jquery;
        form.on('submit(submit)', function (data) {
            var loading = layer.load(1, {shade: [0.1, '#fff']});
            $.post("{:url('insert')}", data.field, function (res) {
                layer.close(loading);
                if (res.code > 0) {
                    layer.msg(res.msg, {time: 1000, icon: 1}, function () {
                        location.href = res.url;
                    });
                } else {
                    layer.msg(res.msg, {time: 1000, icon: 2});
                }
            });
        })
    });
</script>