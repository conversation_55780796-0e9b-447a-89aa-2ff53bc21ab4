<?php

namespace app\plugins\wechat\library;

use think\Exception;

/**
 * 回调事件类
 * Class Event
 */
class Event
{
//    protected $error=null;

    public  static  function execute(&$params){
        $self=new  self();
        try {
            $event=$params['Event'];
            return $self->$event($params);
        }catch (\Exception $e){

//            $self->error=('操作失败:'.$e->getMessage());
            \think\facade\Log::write($e->getMessage().$e->getFile().$e->getLine(),'error');
            return  false;

        }

    }


}