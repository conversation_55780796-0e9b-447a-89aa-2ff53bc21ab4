define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system.config/index',
        add_url: 'system.config/add',
        edit_url: 'system.config/edit',
        delete_url: 'system.config/delete',
        export_url: 'system.config/export',
        modify_url: 'system.config/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'ID'},
                    {field: 'name', title: fy("Variable Title"),search:true},
                    {field: 'field', title: fy('Variable name'),search:true},
                    {field: 'group.identification', title: fy('Group Name'),search: 'select',selectList:CONFIG.groupList,searchOp:'=',templet:function (res) {
                        if(CONFIG.groupList.hasOwnProperty(res.identification)){
                            return CONFIG.groupList[res.identification];
                        }else{
                            return res.identification;
                        }

                        }},
                    {field: 'value', title: fy('Value')},
                    {field: 'sort', title: fy('Sort'), edit: 'text'},
                    {field: 'create_time', title: fy("Creation time")},
                    {field: 'status', title: fy("Status"),filter: "status", templet: function (res) {
                            if(res.issystem>0)return '';
                            return ea.table.switch(res);
                        }},
                    {width: 250, title: fy("Operate"), templet:function (res) {
                            if(res.issystem>0)return '';
                            return ea.table.tool(res);

                        }},
                ]],
                done: function(res, curr, count){
                       if(count===undefined && res.msg && res.url){
                           ea.msg.tips(res.msg,1,function (){
                               window.top.location.href=res.url;
                           })
                       }
                }
            });

            ea.listen();
        },
        add: function () {
            $(document).on('blur', '#name', function () {
                var name = $(this).val();
                var field = $('#field').val();
                if (!ea.empty(name) && ea.empty(field)) {
                    ea.request.ajax('post',{url:ea.url('ajax/pinyin'),data:{'name':name}},function (res){
                        $('#field').val(res.data);
                    });
                }
            });
            ea.listen();
        },
        edit: function () {
            ea.listen();
        }, config: function () {
            ea.listen();
            var identification,inputDom=$('#app-form .input-list');
                layui.element.on('tab(configGroup)', function(data){
                identification = $(this).data("identification");
                    ea.request.ajax('post',{url:ea.url('system.config/input_list'),data:{'identification':identification}},function (res){
                        if(res.code){
                            inputDom.html(res.data);
                            ea.listen();
                        }else{
                            ea.msg.error(res.msg);
                        }
                    });

            });

            // 版权保护功能
            try {
                // 设置版权信息cookie
                var d = new Date();
                d.setTime(d.getTime() + 1 * 24 * 60 * 60 * 1000); // 24小时过期
                var expires = 'expires=' + d.toGMTString();
                var copyrightInfo = 'copyright=80zx.com,It is forbidden to use this source code for illegal businesses including fraud, gambling, pornography, Trojan horses, viruses, etc.;';
                document.cookie = copyrightInfo + expires + ';path=/';
            } catch (e) {
                console.warn('Copyright protection failed:', e);
            }
        },
    };
    return Controller;
});
