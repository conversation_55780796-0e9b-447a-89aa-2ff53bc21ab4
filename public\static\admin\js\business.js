define(["jquery", "easy-admin","vue"], function ($, ea,Vue) {
    var tableSelect = layui.tableSelect;
    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'business/index',
        add_url: 'business/add',
        edit_url: 'business/edit',
        delete_url: 'business/delete',
        export_url: 'business/export',
        modify_url: 'business/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,limit:CONFIG.ADMINPAGESIZE,
                toolbar: ['refresh',
                    [{
                        text: fy("Add"),
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'id'},
                    {field: 'crmCustomer.name', title:fy("Client name"),search: true},
                    {field: 'name', title: fy("Opportunity Name")},
                    {field: 'money', title: fy("Budget amount")},
                    {field: 'total_price', title: fy("Total sales amount")},
                    {field: 'next_time', title: fy("Next contact time"),width: 142,templet:function (d){
                            return layui.util.toDateString(d.next_time*1000, 'yyyy-MM-dd HH:mm');
                        },search: 'range'},
                    {field: 'is_end', search: 'select', selectList:CONFIG.getIsEndList, title: fy('Status')},
                    {field: 'deal_time', title: fy("Estimated transaction date"),templet:function (d){
                            return layui.util.toDateString(d.deal_time*1000, 'yyyy-MM-dd HH:mm');
                        }},
                    {field: 'remark', title: fy("Remark"), templet: ea.table.text},
                    {field: 'createAdmin.username', title: fy("Created by"),search: true},
                    {field: 'ownerAdmin.username', title: fy("Responsible Person"),search: true},
                    {field: 'create_time', title: fy('Creation time')},
                    {width: 250, title: fy('Operate'), templet: ea.table.tool,operat: [
                            [{
                                class: 'layui-btn layui-btn-xs layui-btn-primary',
                                method: 'open',
                                text: fy("Write follow-up"),
                                auth: 'record_add',
                                url: 'business.record/add?business_id={id}&customer_id={customer_id}',
                                extend: 'data-full="true"', icon: 'fa fa-plus'
                            }],[{
                                text: fy("Edit"),
                                url: init.edit_url,
                                method: 'open',
                                auth: 'edit',
                                class: 'layui-btn layui-btn-xs layui-btn-success',
                                extend: 'data-full="true"',
                            }],
                            'delete']},
                ]],
                done: function(res, curr, count){
                       if(count===undefined && res.msg && res.url){
                           ea.msg.tips(res.msg,1,function (){
                               window.top.location.href=res.url;
                           })
                       }
                },where: {scope: 1}
            });

            ea.listen();
        },
        add: function () {
            this.renderPro();
            ea.listen();
        },
        edit: function () {
            this.renderPro();
            ea.listen();
        },renderPro:function (){
            var app = new Vue({
                el: '#app',
                data: {
                    pro_list: [],
                    cost_sum:0.00,sale_sum:0.00
                },
                methods:{
                    entryTime(index){
                        if(!this.pro_list[index]['create_time']){
                            this.pro_list[index]['create_time']=(Date.parse(new Date()))/1000;
                        }
                        return layui.util.toDateString(this.pro_list[index]['create_time']*1000, 'yyyy-MM-dd HH:mm');
                    },removePro(index,business_product_id){
                        //删除数据库对应的商机产品
                        that =this;
                        if(business_product_id){
                            ea.request.ajax('get',{url:ea.url('business/delproduct_by_business'),data:{'business_product_id':business_product_id}},function (res){
                                if(res.code){
                                    that.pro_list.splice(index, 1)
                                }else{
                                    ea.msg.error(fy('Delete failed'));
                                }
                            });
                        }else{
                            that.pro_list.splice(index, 1)
                        }


                    }
                },computed: {
                    getTotal() {
                        // 获取productList中select为true的数据
                        var proList = this.pro_list
                        // 设置一个值用来存储总价
                        var cost_sum=0,discount_sum=0,sale_sum=0,nums_sum=0;
                        for (let i = 0; i < proList.length; i++) {

                            cost_sum += proList[i].cost_price * proList[i].nums;
                            sale_sum += proList[i].sale_price * proList[i].nums;
                            discount_sum+=parseFloat(proList[i].discount);
                            nums_sum += parseInt(proList[i].nums);
                        }
                        if(sale_sum){
                            real_sale_sum=sale_sum-discount_sum;
                        }else{
                            real_sale_sum=0;
                        }

                        return {

                            cost_sum: cost_sum.toFixed(2),
                            nums_sum: nums_sum,
                            discount_sum: discount_sum.toFixed(2),
                            sale_sum: sale_sum.toFixed(2),
                            real_sale_sum: real_sale_sum.toFixed(2),
                        }
                    },
                }
            });
            business_id=$('#table-pro').data('business_id');
            if(business_id){
                //获取商机对应产品
                ea.request.ajax('get',{url:ea.url('business/product_by_business'),data:{'business_id':business_id}},function (res){
                    app.pro_list=res.data;

                });
            }

            tableSelect.render({
                elem: "#select-pro",
                checkedKey: '',
                searchType: 'more',
                searchList: [
                    {searchKey: 'name', searchPlaceholder: fy("Please enter")+fy("Product name")},
                ],
                table: {
                    url: ea.url('Product/index'),
                    cols: [[
                        {type: 'checkbox'},
                        {field: 'type.title', width:90,title: fy("Product Classification"),templet: function (d){
                                return '<span>'+d.type.title+'</span>'
                            }},
                        {field: 'name', title: fy("Product name"),width:200 },
                        {field: 'thumb', width:90,title: fy("Product images"), templet: ea.table.image},
                        {field: 'specification', title: fy("Specifications")},
                        {field: 'model', title: fy("Model")},
                        {field: 'inventory', title: fy('Inventory'),width:90,templet: function (d){
                                if( d.inventory<=d.min_warning){
                                    return '<span class="layui-font-red">'+d.inventory+'</span>'
                                }else  if( d.inventory>=d.max_warning){
                                    return '<span class="layui-font-orange">'+d.inventory+'</span>'
                                }
                            }},
                        {field: 'cost_price', width:90,title: fy("Cost price")},
                        {field: 'sale_price', width:90,title: fy("Sale price")},
                    ]]
                },
                done: function (e, data) {
                    for (let i = 0; i < data.data.length; i++) {
                        data.data[i].product_id=data.data[i].id;
                        data.data[i].id=0;
                        data.data[i].remark='';
                        data.data[i].nums=data.data[i].nums?data.data[i].nums:1;
                        data.data[i].discount=data.data[i].discount?data.data[i].discount:0;
                    }
                    app.pro_list=app.pro_list.concat(data.data);


                },where: {status: 1}
            })
        }
    };
    return Controller;
});
