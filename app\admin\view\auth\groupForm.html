{include file="common/head"/}
<div class="admin-main fadeInUp animated" ng-app="hd" ng-controller="ctrl">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{$title}</legend>
    </fieldset>
    <form class="layui-form layui-form-pane">
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Parent')}</label>
            <div class="layui-input-block">
                <select name="pid" lay-verify="required" lay-filter="pid" >
                    {volist name="authGroupTree" id="vo"}
                    <option value="{$vo.id}">{$vo.lefthtml}{$vo.title}</option>
                    {/volist}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Group Name')}</label>
            <div class="layui-input-block">
                <input type="text" name="title" ng-model="field.title" lay-verify="required" placeholder="{:lang('Please enter')}{:fy('Group Name')}" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Maximum number of customers')}</label>
            <div class="layui-input-block">
                <input type="number" name="max_customers_num" ng-model="field.max_customers_num" lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Maximum number of customers')}" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">当前用户组人员可最大拥有的客户数量，填写0表示不限制</div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">{:lang('submit')}</button>
                <a href="{:url('adminGroup')}" class="layui-btn layui-btn-primary">{:lang('back')}</a>
            </div>
        </div>
    </form>
</div>
{include file="common/foot"/}
<script src="__MY_PUBLIC__/static/common/js/angular.min.js"></script>
<script>
    var m = angular.module('hd',[]);
    m.controller('ctrl',['$scope',function($scope) {
        $scope.field = '{$info|raw}'!='null'?{$info|raw}:{title:'',max_customers_num:0};
        layui.use(['form', 'layer'], function () {
            var form = layui.form, layer = layui.layer,$= layui.jquery;
            form.on('submit(submit)', function (data) {
                loading = layer.load(1,{shade:[0.1,'#fff']});
                // 提交到方法 默认为本身
                data.field.group_id = $scope.field.group_id;
                $.post("", data.field, function (res) {
                    layer.close(loading);
                    if (res.code > 0) {
                        layer.msg(res.msg, {time: 2800, icon: 1}, function () {
                            reloadPage();
                        });
                    } else {
                        layer.msg(res.msg, {time: 2800, icon: 2});
                    }
                });
            })
        });
    }]);
</script>