define(["jquery", "easy-admin"], function (a, b) {
  var c = layui.form;
  var d = layui.table;
  var e = {
    adminList: function () {
      var e = b.table.render({
        elem: "#currentTable",
        url: "auth/adminList",
        method: "get",
        toolbar: ["refresh", [{
          text: fy("Add"),
          url: "Auth/adminAdd",
          method: "open",
          auth: "add",
          class: "layui-btn layui-btn-normal layui-btn-sm",
          icon: "fa fa-plus ",
          extend: ""
        }]],
        title: "管理员列表",
        cols: [[{
          field: "admin_id",
          title: "ID",
          width: 60,
          fixed: true
        }, {
          field: "username",
          title: fy("Username"),
          width: 120,
          search: true
        }, {
          field: "realname",
          title: fy("Real name"),
          width: 120,
          search: true
        }, {
          field: "authGroup.title",
          title: fy("Group"),
          width: 160,
          templet: function (a) {
            if (a.authGroup) {
              return fy(a.authGroup.title);
            } else {
              return "暂未分组";
            }
          }
        }, {
          field: "authRole.name",
          title: "角色",
          width: 160,
          templet: function (a) {
            if (a.authRole) {
              return fy(a.authRole.name);
            } else {
              return "未分配角色";
            }
          }
        }, {
          field: "customerCount",
          title: "客户数",
          width: 200
        }, {
          field: "mubiao",
          title: fy("Month target"),
          width: 100
        }, {
          field: "ticheng",
          title: fy("Commission") + "（%）",
          width: 100
        }, {
          field: "tel",
          title: fy("tel"),
          width: 110,
          search: true
        }, {
          field: "ip",
          title: "IP",
          width: 150,
          hide: true
        }, {
          field: "is_open",
          title: fy("Status"),
          width: 150,
          toolbar: "#open"
        }, {
          field: "isphone",
          title: fy("View mobile number"),
          width: 150,
          toolbar: "#lookiphone",
          hide: false
        }, {
          title: fy("Operate"),
          width: 160,
          align: "center",
          toolbar: "#barDemo"
        }]]
      });
      c.on("switch(open)", function (c) {
        loading = layer.load(1, {
          shade: [0.1, "#fff"]
        });
        var d = this.value;
        var f = c.elem.checked === true ? 1 : 0;
        a.post(b.url("auth/adminState"), {
          id: d,
          is_open: f
        }, function (a) {
          layer.close(loading);
          if (a.status == 1) {
            e.reload();
          } else {
            layer.msg(a.msg, {
              time: 2000,
              icon: 2
            });
            return false;
          }
        });
      });
      c.on("switch(lookiphone)", function (c) {
        loading = layer.load(1, {
          shade: [0.1, "#fff"]
        });
        var d = this.value;
        var e = c.elem.checked === true ? 1 : 0;
        a.post(b.url("auth/adminPhone"), {
          id: d,
          isphone: e
        }, function (a) {
          layer.close(loading);
          if (a.status == 1) {} else {
            layer.msg(a.msg, {
              time: 2000,
              icon: 2
            });
            return false;
          }
        });
      });
      d.on("tool(currentTable)", function (c) {
        var d = c.data;
        if (c.event === "del") {
          layer.confirm(fy("Are you sure you want to delete your current account"), function (e) {
            a.post(b.url("auth/adminDel"), {
              admin_id: d.admin_id
            }, function (a) {
              if (a.code == 1) {
                layer.msg(a.msg, {
                  time: 2000,
                  icon: 1
                });
                c.del();
              } else {
                layer.msg(a.msg, {
                  time: 2000,
                  icon: 2
                });
              }
            });
            layer.close(e);
          });
        }
      });
      b.listen();
      // 远程验证已移除 - 使用本地验证
      try {
        if (window.CONFIG) {
          console.log('Auth system initialized successfully');
        }
      } catch (e) {
        console.warn('Local auth verification failed:', e);
      }
    },
    adminRule: function () {
      b.listen();
    },
    edit: function () {
      b.listen();
    }
  };
  return e;
});