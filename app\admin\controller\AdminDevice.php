<?php

namespace app\admin\controller;

use app\common\controller\AdminController;
use app\admin\model\AdminDevice as AdminDeviceModel;
use think\facade\Db;

/**
 * 设备管理控制器
 */
class AdminDevice extends AdminController
{
    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->model = new AdminDeviceModel();
    }
    
    /**
     * 设备列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 15);
            $keyword = $this->request->param('keyword', '');
            
            $where = [];
            
            // 搜索条件
            if (!empty($keyword)) {
                $where[] = ['device_id', 'like', '%' . $keyword . '%'];
            }
            
            // 权限控制：普通用户只能看到自己的设备
            if ($this->admin['group_id'] != 1) {
                $where[] = ['admin_id', '=', $this->admin['admin_id']];
            }
            
            $list = $this->model
                ->with(['admin'])
                ->where($where)
                ->order('id desc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page
                ]);
                
            $result = [
                'code' => 0,
                'msg' => '获取成功',
                'count' => $list->total(),
                'data' => $list->items()
            ];
            
            return json($result);
        }
        
        return $this->fetch();
    }
    
    /**
     * 强制下线设备
     */
    public function forceOffline()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $id = $this->request->post('id', 0);
        $device = $this->model->find($id);
        
        if (!$device) {
            $this->error('设备不存在');
        }
        
        // 权限控制：普通用户只能操作自己的设备
        if ($this->admin['group_id'] != 1 && $device['admin_id'] != $this->admin['admin_id']) {
            $this->error('无权限操作此设备');
        }
        
        // 更新设备状态
        $device->status = 0;
        $result = $device->save();
        
        if ($result) {
            $this->success('设备已强制下线');
        } else {
            $this->error('操作失败');
        }
    }
    
    /**
     * 删除设备记录
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('参数错误');
        }
        
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        
        // 权限控制：普通用户只能删除自己的设备记录
        if ($this->admin['group_id'] != 1) {
            $count = $this->model
                ->whereIn('id', $ids)
                ->where('admin_id', '!=', $this->admin['admin_id'])
                ->count();
                
            if ($count > 0) {
                $this->error('无权限删除其他用户的设备记录');
            }
        }
        
        $result = $this->model->whereIn('id', $ids)->delete();
        
        if ($result) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 获取设备统计信息
     */
    public function getStats()
    {
        $stats = AdminDeviceModel::getDeviceStats();
        $this->success('获取成功', $stats);
    }
    
    /**
     * 清理过期设备连接
     */
    public function cleanExpired()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }
        
        $count = AdminDeviceModel::cleanExpiredDevices();
        $this->success("已清理 {$count} 个过期设备连接");
    }
    
    /**
     * 获取当前用户的设备状态
     */
    public function getMyDeviceStatus()
    {
        $device = AdminDeviceModel::getOnlineDevice($this->admin['admin_id']);
        
        $status = [
            'online' => !empty($device),
            'device_id' => $device['device_id'] ?? '',
            'last_active' => $device['last_active'] ?? '',
            'socket_id' => $device['socket_id'] ?? ''
        ];
        
        $this->success('获取成功', $status);
    }
}
