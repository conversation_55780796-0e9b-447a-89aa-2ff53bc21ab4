define(["jquery","easy-admin"],function(e,i){var n={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"open.apps/index",add_url:"open.apps/add",edit_url:"open.apps/edit",delete_url:"open.apps/delete",export_url:"open.apps/export",modify_url:"open.apps/modify"};var t={index:function(){var t;i.table.render({init:n,limit:CONFIG.ADMINPAGESIZE,cols:[[{type:"checkbox"},{field:"id",title:"ID"},{field:"name",title:"名称"},{field:"",title:"通知API",templet:function(e){t=window.location.protocol+"//"+window.location.host+"/open/index/id/"+e.id+"/token/"+e.token;return'<a class="layuimini-table-url label bg-green" href="'+t+'" target="_blank">'+t+"</a>"}},{field:"status",search:"select",selectList:["禁用","启用"],title:"状态",templet:i.table.switch},{field:"create_time",title:"创建时间"},{width:250,title:"操作",templet:i.table.tool,operat:[[{class:"layui-btn layui-btn-success layui-btn-xs",method:"open",text:"对接说明",auth:"doc",url:"open.apps/doc",field:"",extend:""}],"edit","delete"]}]],done:function(e,t,n){if(n===undefined&&e.msg&&e.url){i.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}}});i.listen()},add:function(){this.common();i.listen()},edit:function(){this.common();i.listen()},common:function(){e(document).on("click",".random-char",function(){e(e(this).attr("data-target")).val(i.randomChar(e(this).attr("data-length")))})}};return t});