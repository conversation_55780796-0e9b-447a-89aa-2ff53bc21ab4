# WebSocket拨号和挂断功能使用说明

## 功能概述

本系统实现了基于PHP原生WebSocket的拨号和挂断功能，支持CRM系统与Android设备的实时通信。

## 主要特性

1. **PHP原生WebSocket实现** - 无需第三方依赖
2. **设备认证机制** - 支持用户名密码验证
3. **单设备登录限制** - 每个用户账号只允许一个设备在线
4. **安全权限控制** - 用户只能操作自己负责的客户和设备
5. **实时通信** - 支持拨号和挂断指令的实时推送
6. **心跳包机制** - 保持连接活跃状态

## 系统架构

```
Web前端 → WebSocket API → WebSocket服务器 → Android设备
```

### 组件说明

- **WebSocket服务器** (`websocket_server.php`) - 核心通信服务器
- **WebSocket API** (`app/admin/controller/WebSocketApi.php`) - Web端API接口
- **设备管理** (`app/admin/model/AdminDevice.php`) - 设备状态管理
- **前端JavaScript** (`public/static/admin/js/crm/customer.js`) - 用户界面交互

## 安装部署

### 1. 启动WebSocket服务器

```bash
# Windows环境
start_websocket.bat

# Linux环境
php websocket_server.php
```

服务器将监听以下端口：
- WebSocket端口: 8080 (设备连接)
- 内部API端口: 8081 (Web端通信)

### 2. 配置数据库

确保数据库中存在以下表：
- `ymwl_admin` - 管理员表
- `ymwl_admin_device` - 设备管理表
- `ymwl_crm_customer` - 客户表
- `ymwl_crm_record` - 跟进记录表

### 3. 权限配置

确保用户具有以下权限：
- `crm.customer/dial` - 拨号权限
- `crm.customer/hangup` - 挂断权限
- `websocket_api/dial` - WebSocket拨号API权限
- `websocket_api/hangup` - WebSocket挂断API权限
- `websocket_api/getDeviceStatus` - 设备状态查询权限

## 使用流程

### Android设备端

1. **连接WebSocket服务器**
   ```javascript
   ws://服务器IP:8080
   ```

2. **发送认证消息**
   ```json
   {
       "type": "auth",
       "username": "admin",
       "password": "123456",
       "device_id": "设备唯一标识",
       "device_type": "android"
   }
   ```

3. **接收认证响应**
   ```json
   {
       "type": "auth_result",
       "success": true,
       "message": "认证成功",
       "admin_id": "用户ID"
   }
   ```

4. **发送心跳包**（每30秒）
   ```json
   {
       "type": "heartbeat",
       "timestamp": 1642234567
   }
   ```

5. **接收拨号指令**
   ```json
   {
       "type": "dial",
       "action": "make_call",
       "data": {
           "phone": "13800138000",
           "client_name": "张三",
           "client_id": 123,
           "record_id": 456
       },
       "timestamp": 1642234567
   }
   ```

6. **接收挂断指令**
   ```json
   {
       "type": "hangup",
       "action": "hangup_call",
       "data": {
           "record_id": "52"
       },
       "timestamp": 1749381891
   }
   ```

### Web端操作

1. **查看设备状态**
   - 页面顶部显示设备连接状态
   - 绿色表示在线，红色表示离线

2. **拨号操作**
   - 在客户列表中点击"拨号"按钮
   - 系统会向对应设备发送拨号指令
   - 显示操作结果

3. **挂断操作**
   - 点击页面顶部的"挂断"按钮
   - 系统会向设备发送挂断指令
   - 显示操作结果

## 安全机制

### 1. 用户权限验证
- 只能操作自己负责的客户
- 只能向自己的设备发送指令
- 基于RBAC权限控制

### 2. 设备认证
- 用户名密码验证
- 支持MD5+盐值加密
- 设备唯一标识验证

### 3. 单设备登录
- 每个用户只能有一个设备在线
- 新设备登录会断开旧设备连接
- 防止多设备冲突

### 4. 连接管理
- 心跳包检测连接状态
- 自动清理过期连接
- 异常断开处理

## 测试验证

### 1. 运行测试脚本
```bash
php test_websocket.php
```

### 2. 测试项目
- WebSocket连接测试
- 设备认证测试
- 心跳包测试
- API接口测试

### 3. 功能验证
- 拨号功能测试
- 挂断功能测试
- 权限控制测试
- 设备状态同步测试

## 故障排除

### 常见问题

1. **WebSocket服务器无法启动**
   - 检查端口8080和8081是否被占用
   - 确认PHP环境支持socket扩展
   - 检查防火墙设置

2. **设备认证失败**
   - 确认用户名密码正确
   - 检查用户状态是否启用
   - 验证数据库连接

3. **拨号/挂断无响应**
   - 确认设备在线状态
   - 检查权限配置
   - 查看服务器日志

4. **前端显示异常**
   - 清除浏览器缓存
   - 检查JavaScript控制台错误
   - 确认API路由配置

### 日志查看

WebSocket服务器会在控制台输出详细日志：
- 连接建立/断开
- 认证成功/失败
- 指令发送状态
- 错误信息

## 性能优化

### 1. 连接管理
- 定期清理过期连接
- 优化心跳包频率
- 合理设置超时时间

### 2. 消息处理
- 异步消息处理
- 消息队列机制
- 错误重试机制

### 3. 资源占用
- 内存使用监控
- CPU占用优化
- 网络带宽控制

## 扩展功能

### 1. 多设备支持
- 修改单设备登录限制
- 实现设备分组管理
- 支持设备优先级

### 2. 消息记录
- 保存通信日志
- 统计使用情况
- 审计追踪

### 3. 集群部署
- 多服务器负载均衡
- Redis共享会话
- 消息广播机制

## 技术支持

如遇到问题，请提供以下信息：
1. 错误日志
2. 系统环境信息
3. 操作步骤
4. 预期结果vs实际结果

---

**版本**: v1.0  
**更新时间**: 2025-07-25  
**适用系统**: ThinkPHP 6.1 CRM系统
