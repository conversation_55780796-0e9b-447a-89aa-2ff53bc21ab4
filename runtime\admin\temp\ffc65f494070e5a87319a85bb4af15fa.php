<?php /*a:2:{s:52:"C:\wwwroot\127.0.0.1\app\admin\view\order\index.html";i:1681631030;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="<?php echo auth('order/add'); ?>"
               data-auth-addOrder="<?php echo auth('order/add'); ?>"
               data-auth-edit="<?php echo auth('order/edit'); ?>"
               data-auth-delete="<?php echo auth('Order/del'); ?>"
               data-auth-import="<?php echo auth('order/import'); ?>"
               data-auth-export="<?php echo auth('order/export'); ?>"
               data-auth-fields="<?php echo auth('crm.order.fields/index'); ?>"
               data-auth-desc="<?php echo auth('order/desc'); ?>"
               data-auth-alter_pr_user="<?php echo auth('order/alter_pr_user'); ?>"
               data-auth-editAudit="<?php echo auth('Order/editAudit'); ?>"
               lay-filter="currentTable">
        </table>
    </div>
</div>
</body>
</html>