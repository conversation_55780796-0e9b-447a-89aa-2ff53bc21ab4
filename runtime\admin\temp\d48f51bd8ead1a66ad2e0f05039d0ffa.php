<?php /*a:2:{s:61:"/www/wwwroot/ceshi71.cn/app/admin/view/crm/customer/seas.html";i:1680773664;s:58:"/www/wwwroot/ceshi71.cn/app/admin/view/layout/default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/public/static/js/html5.min.js"></script>
    <script src="/public/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/public/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/public/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">

    <div class="layuimini-main">
        <blockquote class="layui-elem-quote" style="color:#FFB800; ">
            <?php echo htmlentities($grabCountMsg); ?>
        </blockquote>
        <div class="layui-tab" lay-filter="nav-tabs-index">
        </div>
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="<?php echo auth('crm.customer/add'); ?>"
               data-auth-edit="<?php echo auth('crm.customer/edit'); ?>"
               data-auth-delete="<?php echo auth('crm.customer/delete'); ?>"
               data-auth-import="<?php echo auth('crm.customer/import'); ?>"
               data-auth-fields="<?php echo auth('system.fields/index'); ?>"
               data-auth-robclient="<?php echo auth('crm.seas/robclient'); ?>"
               lay-filter="currentTable">
        </table>
    </div>
</div>
<script type="text/html" id="action">
<?php if(auth('crm.seas/robclient')){ ?>
    <a class="layui-btn layui-btn-success layui-btn-xs" style="font-size: 14px" data-title="<?php echo fy('Are you sure you want to claim this customer'); ?>" data-request="crm.seas/robclient?id[]={{d.id}}"><i class="fa fa-user-o"></i><?php echo fy('Receive'); ?></a>
<?php } ?>
</script>
</body>
</html>