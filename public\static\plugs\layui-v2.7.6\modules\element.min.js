layui.define("jquery",function(t){"use strict";var f=layui.$,i=layui.hint(),y=layui.device(),d="element",h="layui-this",p="layui-show",a=function(){this.config={}};a.prototype.set=function(t){var i=this;f.extend(true,i.config,t);return i};a.prototype.on=function(t,i){return layui.onevent.call(this,d,t,i)};a.prototype.tabAdd=function(t,i){var a=".layui-tab-title",e=f(".layui-tab[lay-filter="+t+"]"),l=e.children(a),n=l.children(".layui-tab-bar"),s=e.children(".layui-tab-content"),o="<li"+function(){var a=[];layui.each(i,function(t,i){if(/^(title|content)$/.test(t))return;a.push("lay-"+t+'="'+i+'"')});if(a.length>0)a.unshift("");return a.join(" ")}()+">"+(i.title||"unnaming")+"</li>";n[0]?n.before(o):l.append(o);s.append('<div class="layui-tab-item">'+(i.content||"")+"</div>");A.hideTabMore(true);A.tabAuto();return this};a.prototype.tabDelete=function(t,i){var a=".layui-tab-title",e=f(".layui-tab[lay-filter="+t+"]"),l=e.children(a),n=l.find('>li[lay-id="'+i+'"]');A.tabDelete(null,n);return this};a.prototype.tabChange=function(t,i){var a=".layui-tab-title",e=f(".layui-tab[lay-filter="+t+"]"),l=e.children(a),n=l.find('>li[lay-id="'+i+'"]');A.tabClick.call(n[0],null,null,n);return this};a.prototype.tab=function(a){a=a||{};l.on("click",a.headerElem,function(t){var i=f(this).index();A.tabClick.call(this,t,i,null,a)})};a.prototype.progress=function(t,i){var a="layui-progress",e=f("."+a+"[lay-filter="+t+"]"),l=e.find("."+a+"-bar"),n=l.find("."+a+"-text");l.css("width",i).attr("lay-percent",i);n.text(i);return this};var b=".layui-nav",v="layui-nav-item",m="layui-nav-bar",C="layui-nav-tree",g="layui-nav-child",k="layui-nav-child-c",w="layui-nav-more",x="layui-icon-down",T="layui-anim layui-anim-upbit",A={tabClick:function(t,i,a,e){e=e||{};var l=a||f(this),i=i||l.parent().children("li").index(l),n=e.headerElem?l.parent():l.parents(".layui-tab").eq(0),s=e.bodyElem?f(e.bodyElem):n.children(".layui-tab-content").children(".layui-tab-item"),o=l.find("a"),r=o.attr("href")!=="javascript:;"&&o.attr("target")==="_blank",c=typeof l.attr("lay-unselect")==="string",u=n.attr("lay-filter");if(!(r||c)){l.addClass(h).siblings().removeClass(h);s.eq(i).addClass(p).siblings().removeClass(p)}console.log("tab("+u+")");layui.event.call(this,d,"tab("+u+")",{elem:n,index:i})},tabDelete:function(t,i){var a=i||f(this).parent(),e=a.index(),l=a.parents(".layui-tab").eq(0),n=l.children(".layui-tab-content").children(".layui-tab-item"),s=l.attr("lay-filter");if(a.hasClass(h)){if(a.next()[0]&&a.next().is("li")){A.tabClick.call(a.next()[0],null,e+1)}else if(a.prev()[0]&&a.prev().is("li")){A.tabClick.call(a.prev()[0],null,e-1)}}a.remove();n.eq(e).remove();setTimeout(function(){A.tabAuto()},50);layui.event.call(this,d,"tabDelete("+s+")",{elem:l,index:e})},tabAuto:function(){var t="layui-tab-scroll",n="layui-tab-more",s="layui-tab-bar",o="layui-tab-close",r=this;f(".layui-tab").each(function(){var t=f(this),i=t.children(".layui-tab-title"),a=t.children(".layui-tab-content").children(".layui-tab-item"),e='lay-stope="tabmore"',l=f('<span class="layui-unselect layui-tab-bar" '+e+"><i "+e+' class="layui-icon">&#xe61a;</i></span>');if(r===window&&y.ie!=8){A.hideTabMore(true)}if(t.attr("lay-allowClose")){i.find("li").each(function(){var t=f(this);if(!t.find("."+o)[0]){var i=f('<i class="layui-icon layui-icon-close layui-unselect '+o+'"></i>');i.on("click",A.tabDelete);t.append(i)}})}if(typeof t.attr("lay-unauto")==="string")return;if(i.prop("scrollWidth")>i.outerWidth()+1){if(i.find("."+s)[0])return;i.append(l);t.attr("overflow","");l.on("click",function(t){i[this.title?"removeClass":"addClass"](n);this.title=this.title?"":"收缩"})}else{i.find("."+s).remove();t.removeAttr("overflow")}})},hideTabMore:function(t){var i=f(".layui-tab-title");if(t===true||f(t.target).attr("lay-stope")!=="tabmore"){i.removeClass("layui-tab-more");i.find(".layui-tab-bar").attr("title","")}},clickThis:function(){var t=f(this),i=t.parents(b),a=i.attr("lay-filter"),e=t.parent(),l=t.siblings("."+g),n=typeof e.attr("lay-unselect")==="string";if(!(t.attr("href")!=="javascript:;"&&t.attr("target")==="_blank")&&!n){if(!l[0]){i.find("."+h).removeClass(h);e.addClass(h)}}if(i.hasClass(C)){l.removeClass(T);if(l[0]){e[l.css("display")==="none"?"addClass":"removeClass"](v+"ed");if(i.attr("lay-shrink")==="all"){e.siblings().removeClass(v+"ed")}}}layui.event.call(this,d,"nav("+a+")",t)},collapse:function(){var t=f(this),i=t.find(".layui-colla-icon"),a=t.siblings(".layui-colla-content"),e=t.parents(".layui-collapse").eq(0),l=e.attr("lay-filter"),n=a.css("display")==="none";if(typeof e.attr("lay-accordion")==="string"){var s=e.children(".layui-colla-item").children("."+p);s.siblings(".layui-colla-title").children(".layui-colla-icon").html("&#xe602;");s.removeClass(p)}a[n?"addClass":"removeClass"](p);i.html(n?"&#xe61a;":"&#xe602;");layui.event.call(this,d,"collapse("+l+")",{title:t,content:a,show:n})}};a.prototype.init=function(t,i){var a=this,n=function(){return i?'[lay-filter="'+i+'"]':""}(),e={tab:function(){A.tabAuto.call({})},nav:function(){var s=200,o={},r={},c={},u="layui-nav-title",l=function(t,i,a){var e=f(this),l=e.find("."+g);if(i.hasClass(C)){if(!l[0]){var n=e.children("."+u);t.css({top:e.offset().top-i.offset().top,height:(n[0]?n:e).outerHeight(),opacity:1})}}else{l.addClass(T);if(l.hasClass(k))l.css({left:-(l.outerWidth()-e.width())/2});if(l[0]){t.css({left:t.position().left+t.width()/2,width:0,opacity:0})}else{t.css({left:e.position().left+parseFloat(e.css("marginLeft")),top:e.position().top+e.height()-t.height()})}o[a]=setTimeout(function(){t.css({width:l[0]?0:e.width(),opacity:l[0]?0:1})},y.ie&&y.ie<10?0:s);clearTimeout(c[a]);if(l.css("display")==="block"){clearTimeout(r[a])}r[a]=setTimeout(function(){l.addClass(p);e.find("."+w).addClass(w+"d")},300)}};f(b+n).each(function(t){var i=f(this),a=f('<span class="'+m+'"></span>'),e=i.find("."+v);if(!i.find("."+m)[0]){i.append(a);(i.hasClass(C)?e.find("dd,>."+u):e).on("mouseenter",function(){l.call(this,a,i,t)}).on("mouseleave",function(){if(i.hasClass(C)){a.css({height:0,opacity:0})}else{clearTimeout(r[t]);r[t]=setTimeout(function(){i.find("."+g).removeClass(p);i.find("."+w).removeClass(w+"d")},300)}});i.on("mouseleave",function(){clearTimeout(o[t]);c[t]=setTimeout(function(){if(!i.hasClass(C)){a.css({width:0,left:a.position().left+a.width()/2,opacity:0})}},s)})}e.find("a").each(function(){var t=f(this),i=t.parent(),a=t.siblings("."+g);if(a[0]&&!t.children("."+w)[0]){t.append('<i class="layui-icon '+x+" "+w+'"></i>')}t.off("click",A.clickThis).on("click",A.clickThis)})})},breadcrumb:function(){var t=".layui-breadcrumb";f(t+n).each(function(){var t=f(this),i="lay-separator",a=t.attr(i)||"/",e=t.find("a");if(e.next("span["+i+"]")[0])return;e.each(function(t){if(t===e.length-1)return;f(this).after("<span "+i+">"+a+"</span>")});t.css("visibility","visible")})},progress:function(){var e="layui-progress";f("."+e+n).each(function(){var t=f(this),i=t.find(".layui-progress-bar"),a=i.attr("lay-percent");i.css("width",function(){return/^.+\/.+$/.test(a)?new Function("return "+a)()*100+"%":a}());if(t.attr("lay-showPercent")){setTimeout(function(){i.html('<span class="'+e+'-text">'+a+"</span>")},350)}})},collapse:function(){var t="layui-collapse";f("."+t+n).each(function(){var t=f(this).find(".layui-colla-item");t.each(function(){var t=f(this),i=t.find(".layui-colla-title"),a=t.find(".layui-colla-content"),e=a.css("display")==="none";i.find(".layui-colla-icon").remove();i.append('<i class="layui-icon layui-colla-icon">'+(e?"&#xe602;":"&#xe61a;")+"</i>");i.off("click",A.collapse).on("click",A.collapse)})})}};return e[t]?e[t]():layui.each(e,function(t,i){i()})};a.prototype.render=a.prototype.init;var e=new a,l=f(document);f(function(){e.render()});var n=".layui-tab-title li";l.on("click",n,A.tabClick);l.on("click",A.hideTabMore);f(window).on("resize",A.tabAuto);t(d,e)});