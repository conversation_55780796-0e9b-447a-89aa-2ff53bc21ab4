<?php /*a:3:{s:48:"C:\wwwroot\d.cn\app\admin\view\system\email.html";i:1671847800;s:47:"C:\wwwroot\d.cn\app\admin\view\common\head.html";i:1671106604;s:47:"C:\wwwroot\d.cn\app\admin\view\common\foot.html";i:1672069288;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/static/plugs/layui-v2.7.6/css/layui.css" media="all">

    <link rel="stylesheet" href="/static/plugs/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/global.css" media="all">
    <script src="/static/common/js/jquery.min.js"></script>

    <script src="/static/admin/js/common.js"></script>
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
</head>
<body >
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend><?php echo fy("邮箱配置"); ?></legend>
    </fieldset>
    <form class="layui-form layui-form-pane" lay-filter="form-email">
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Server"); ?></label>
            <div class="layui-input-block">
                <input type="text" lay-verify="required" name="smtp_server" placeholder="SMTP <?php echo fy('Server'); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">SMTP <?php echo fy('Port'); ?></label>
            <div class="layui-input-block">
                <input type="text" lay-verify="required" name="smtp_port" placeholder="SMTP <?php echo fy('Port'); ?>" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Sender'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="email_id" lay-verify="required" placeholder="<?php echo fy('Sender'); ?>" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Sending email address'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="smtp_user" lay-verify="required" placeholder="<?php echo fy("Sender's email address"); ?>" value="" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Authentication code'); ?></label>
            <div class="layui-input-3">
                <input type="password" name="smtp_pwd" lay-verify="required" placeholder="SMTP <?php echo fy('Authentication code'); ?>" value="" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Test mailbox'); ?></label>
            <div class="layui-input-3">
                <input type="text" name="test_eamil" id="test_eamil" placeholder="<?php echo fy('Test receiving email address'); ?>" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label"><?php echo fy('Mail test content'); ?></label>
            <div class="layui-input-block">
                <textarea name="test_eamil_info" id="test_eamil_info" placeholder="<?php echo lang('Please enter'); ?> <?php echo fy('Mail test content'); ?>" class="layui-textarea"></textarea>
            </div>
        </div>




        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit"><?php echo lang('submit'); ?></button>
                <button type="reset" class="layui-btn layui-btn-primary"><?php echo lang('Reset'); ?></button>
                <button type="button" class="layui-btn layui-btn-normal" id="trySend"><?php echo fy('Test sending'); ?></button>
            </div>
        </div>
    </form>
</div>

<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script>
    layui.use(['form', 'layer'], function () {
        var form = layui.form,layer = layui.layer,$= layui.jquery;
        //发送测试邮件
        form.val("form-email", <?php echo $info; ?>)
        $('#trySend').click(function(){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var email = $('#test_eamil').val();
            $.post("<?php echo url('trySend'); ?>",{email:email},function(res){
                layer.close(loading);
                if(res.code > 0){
                    layer.msg(res.msg,{time:1800});
                }else{
                    layer.msg(res.msg,{time:1800});
                }
            });
        });
        //提交监听
        form.on('submit(submit)', function (data) {
            loading =layer.load(1, {shade: [0.1,'#fff']});
            $.post("<?php echo url('system/email'); ?>",data.field,function(res){
                layer.close(loading);
                if(res.code > 0){
                    layer.msg(res.msg,{icon: 1, time: 1000},function(){
                        location.href = res.url;
                    });
                }else{
                    layer.msg(res.msg,{icon: 2, time: 1000});
                }
            });
        })
    })
</script>
</body>
</html>