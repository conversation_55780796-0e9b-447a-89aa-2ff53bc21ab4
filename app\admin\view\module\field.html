{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <blockquote class="layui-elem-quote">
        <a data-open="{:url('fieldAdd',array('moduleid'=>input('id')))}" class="layui-btn layui-btn-sm" data-full="true">{:lang('add')}{:lang('field')}</a>
    </blockquote>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="isSystem">
    {{# if(d.issystem==1){ }}
    <i class="fa fa-check green"></i>
    {{# }else{  }}
    <i class="fa fa-close red"></i>
    {{# } }}
</script>
<script type="text/html" id="required">
    {{# if(d.required==1){ }}
    <i class="fa fa-check green"></i>
    {{# }else{  }}
    <i class="fa fa-close red"></i>
    {{# } }}
</script>
<script type="text/html" id="action">
    {{# if(d.issystem==0){ }}
        {{# if(d.disable==1){ }}
        <a href="javascript:" class="layui-btn layui-btn-xs layui-btn-danger" lay-event="stateyes">已禁用</a>
        {{# }else if(d.disable==2){  }}
        <a href="#" class="layui-btn layui-btn-xs layui-btn-disabled">已禁用</a>
        {{# }else{  }}
        <a href="javascript:" class="layui-btn layui-btn-xs layui-btn-warm" lay-event="stateyes">已启用</a>
        {{# } }}
        <a href="{:url('fieldEdit')}?moduleid={{d.moduleid}}&id={{d.id}}" class="layui-btn layui-btn-xs">{:lang('Edit')}</a>

        {{# if(d.delStatus==1){ }}
        <a href="#" class="layui-btn layui-btn-xs layui-btn-disabled">{:fy('Delete')}</a>
        {{# }else{  }}
        <a href="#" class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">{:fy('Delete')}</a>
        {{# } }}
    {{# } }}

</script>
<script type="text/html" id="order">
    <input name="{{d.id}}" data-id="{{d.id}}" class="list_order layui-input" value=" {{d.sort}}" size="10"/>
</script>
<script>
    layui.use(['table','easyadmin'], function() {
        var table = layui.table, $ = layui.jquery;
        ea=layui.easyadmin;
        ea.listen();
        var tableIn=table.render({
            elem: '#list',
            url: '{:url("field")}',
            where: { //设定异步数据接口的参数
                id: '{:input("id")}'
            },
            method: 'post',
            cols: [[
                {field: 'field', title: '字段名', width: 120,fixed: true},
                {field: 'name', title: '别名', width: 100},
                {field: 'type', title: '{:fy('Field type')}', width: 100},
                {field: 'issystem', align: 'center', title: '系统字段', width: 100, toolbar: '#isSystem'},
                {field: 'required', align: 'center', title: '必填', width: 80, toolbar: '#required'},
                {field: 'sort', title: '{:lang("order")}', width: 100, sort: true,templet: '#order'},
                {width: 190,title: '', align: 'center', toolbar: '#action'}
            ]]
        });
        table.on('tool(list)', function(obj) {
            var data = obj.data;
            if (obj.event === 'stateyes') {
                loading = layer.load(1, {shade: [0.1, '#fff']});
                $.post('{:url("fieldStatus")}', {id: data.id}, function () {
                    window.location.href = "{:url('field')}?id={:input('id')}"
                });
            }else if(obj.event === 'del'){
                layer.confirm('你确定要删除该字段吗', function(index){
                    $.post("{:url('fieldDel')}",{id:data.id},function(res){
                        if(res.code==1){
                            layer.msg(res.msg,{time:1000,icon:1});
                            obj.del();
                        }else{
                            layer.msg(res.msg,{time:1000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
        $('body').on('blur','.list_order',function() {
            var id = $(this).attr('data-id');
            var sort = $(this).val();
            var loading = layer.load(1, {shade: [0.1, '#fff']});
            $.post('{:url("listOrder")}',{id:id,sort:sort,moduleid:"{:input('id')}"},function(res){
                layer.close(loading);
                if(res.code===1){
                    layer.msg(res.msg,{time:1000,icon:1});
                    tableIn.reload();
                }else{
                    layer.msg(res.msg,{time:1000,icon:2});
                }
            })
        })
    });
</script>