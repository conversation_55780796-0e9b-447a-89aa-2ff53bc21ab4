{"name": "模型表", "table": "system_model", "engine": "MyISAM", "tabletype": "ordinary", "prefix": "__PREFIX__", "is_page": 1, "field": {"id": {"name": "", "field": "id", "type": "bigint", "lang": "200", "is_null": 0, "formtype": "input", "table": "system_model", "show": 0, "edit": 0, "search": 0, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "name": {"name": "表名称", "field": "name", "type": "<PERSON><PERSON><PERSON>", "lang": "200", "is_null": 1, "formtype": "input", "table": "system_model", "show": 1, "edit": 1, "search": 1, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "table": {"name": "表名", "field": "table", "type": "<PERSON><PERSON><PERSON>", "lang": "200", "is_null": 1, "formtype": "input", "table": "system_model", "show": 1, "edit": 1, "search": 1, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "engine": {"name": "引擎", "field": "engine", "type": "<PERSON><PERSON><PERSON>", "lang": "30", "is_null": 1, "formtype": "select", "table": "system_model", "show": 1, "edit": 1, "search": 0, "total": 0, "export": 0, "sort": 100, "option": {"InnoDB": "InnoDB", "MyISAM": "MyISAM"}, "join_table": null}, "tabletype": {"name": "表格类型", "field": "tabletype", "type": "<PERSON><PERSON><PERSON>", "lang": "20", "is_null": 1, "formtype": "select", "table": "system_model", "show": 1, "edit": 1, "search": 0, "total": 0, "export": 0, "sort": 100, "option": {"ordinary": "普通表格", "tree": "树状表格"}, "join_table": null}, "delete_time": {"name": "", "field": "delete_time", "type": "int", "lang": "10", "is_null": 1, "formtype": "input", "table": "system_model", "show": 0, "edit": 0, "search": 0, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "create_time": {"name": "创建时间", "field": "create_time", "type": "int", "lang": "10", "is_null": 1, "formtype": "datetime", "table": "system_model", "show": 1, "edit": 0, "search": 0, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "update_time": {"name": "更新时间", "field": "update_time", "type": "int", "lang": "10", "is_null": 1, "formtype": "datetime", "table": "system_model", "show": 0, "edit": 0, "search": 0, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "prefix": {"name": "表前缀", "field": "prefix", "type": "<PERSON><PERSON><PERSON>", "lang": "10", "is_null": 1, "formtype": "input", "table": "system_model", "show": 1, "edit": 1, "search": 0, "total": 0, "export": 0, "sort": 100, "option": [], "join_table": null}, "is_page": {"name": "开启分页", "field": "is_page", "type": "int", "lang": "1", "is_null": 1, "formtype": "switch", "table": "system_model", "show": 1, "edit": 1, "search": 0, "total": 0, "export": 0, "sort": 100, "option": ["关闭", "开启"], "join_table": null, "default": 1}, "status": {"name": "状态", "field": "status", "type": 1, "lang": 1, "is_null": 1, "formtype": "switch", "show": 1, "edit": 1, "option": ["关闭", "开启"]}}}