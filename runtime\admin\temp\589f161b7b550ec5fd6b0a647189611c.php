<?php /*a:2:{s:60:"C:\wwwroot\127.0.0.1\app\admin\view\crm\customer\import.html";i:1686645188;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">


<form id="add-form" class="layui-form layuimini-form">

    <div class="layui-form-item">
        <label class="layui-form-label required"><?php echo fy('Upload'); ?></label>
        <div class="layui-input-block layuimini-upload">
            <input name="filepath" class="layui-input layui-col-xs6" lay-verify="required" lay-reqtext="<?php echo fy('Please upload'); ?> <?php echo fy('Client Data'); ?>" placeholder="<?php echo fy('Please upload'); ?> <?php echo fy('Client Data'); ?>" value="">
            <div class="layuimini-upload-btn">
                <a class="layui-btn" data-upload="filepath" data-upload-number="one" data-upload-exts="csv,xlsx" data-upload-mimetype="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv" data-upload-accept="file"><i class="fa fa-upload"></i> <?php echo fy('Upload'); ?></a>
            </div>

        </div>
        <div class="layui-input-block">
            <tip>*<?php echo fy('Please fill in the template and upload in batches'); ?>，<a href="<?php echo url('getImportTpl'); ?>" target="_blank"><?php echo fy('Click Download template'); ?></a></tip>
        </div>
    </div>





    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo fy('Responsible Person'); ?></label>
        <div class="layui-input-block">
            <select name="pr_user" lay-filter="pr_user">
                <option value=""><?php echo fy('Please select'); ?></option>
                <?php
$adminResult = \think\facade\Db::name('admin')->where('group_id','<>', 1)->field('admin_id,username')->select();
                if(is_array($adminResult) || $adminResult instanceof \think\Collection || $adminResult instanceof \think\Paginator): $i = 0; $__LIST__ = $adminResult;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                <option value="<?php echo htmlentities($vo['username']); ?>" <?php if($admin['username'] == $vo['username']): ?>selected<?php endif; ?>><?php echo htmlentities($vo['username']); ?></option>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </select>
            <tip><?php echo fy('If the person in charge is not selected, it will be directly put into the client pool'); ?></tip>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo fy('Whether to skip the error'); ?></label>
        <div class="layui-input-block">
            <input type="radio" name="skip" value="1" title="<?php echo fy('Skip'); ?>" checked="">
            <input type="radio" name="skip" value="0" title="<?php echo fy('Do not skip'); ?>">
            <tip><?php echo fy('If a piece of data is duplicated or required, selecting Skip will not interrupt the import'); ?></tip>
        </div>
    </div>

    <div class="hr-line"></div>
    <div class="layui-form-item text-center">
        <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit><?php echo fy('Confirm'); ?></button>
        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm"><?php echo fy('Reset'); ?></button>
    </div>
</form>
</div>
</body>
</html>