<style>
    .layui-iconpicker-body.layui-iconpicker-body-page .hide {
        display: none;
    }
</style>
<link rel="stylesheet" href="__MY_PUBLIC__/static/plugs/lay-module/autocomplete/autocomplete.css" media="all">
<script>
    var parameter = '';
</script>
<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        <input type="hidden" name="table" value="{$row.table|default=''}">
        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Field')}{:fy('Name')}</label>
            <div class="layui-input-block">
                <input type="text" id="name" name="name" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Field')}{:fy('Name')}"  lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Field')}{:fy('Name')}" value="{$row.name}">

            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Presentation name')}</label>
            <div class="layui-input-block">
                <input type="text" id="xsname" name="xsname" class="layui-input"  placeholder="{:fy('Please enter')}{:fy('Presentation name')}" value="{$row.xsname}">
                <tip>{:fy('Fill out the form')}{:fy('Presentation name')}</tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Field')}</label>
            <div class="layui-input-block">
                <input type="text" id="field" name="field" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Field')}" lay-verify="required"  placeholder="{:fy('Please enter')}{:fy('Field')}" value="{$row.field}" readonly>
                <tip>{:fy('Must start with an English letter, followed by supported numbers and _')}</tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Sort')}</label>
            <div class="layui-input-block">
                <input type="text" id="sort" name="sort" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Sort')}" placeholder="{:fy('Please enter')}{:fy('Sort')}" value="{$row.sort}">
            </div>
        </div>
        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required">{:fy('Field type')}</label>
            <div class="layui-input-block">
                <select name="type" class="layui-select" lay-search>
                    {if isset($alldata['field']['type']['option'])}
                    {foreach $alldata['field']['type']['option'] as $key=>$vo}
                    {if $key==$row.type}
                    <option value="{$key}" selected>{$vo|raw}</option>
                    {else}
                    <option value="{$key}">{$vo|raw}</option>
                    {/if}
                    {/foreach}
                    {/if}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Field length')}</label>
            <div class="layui-input-block">
                <input type="text" id="lang" name="lang" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Field length')}" placeholder="{:fy('Please enter')}{:fy('Field length')}" value="{$row.lang}">
                <tip>{:fy('Enter the maximum number of characters that need to be saved in this field')}</tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Default value')}</label>
            <div class="layui-input-block">
                <input type="text" id="default" name="default" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Default value')}" placeholder="{:fy('Please enter')}{:fy('Default value')}" value="{$row.default}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Validation rules')}</label>
            <div class="layui-input-block">
                <div id="multiple-select"></div>
                <tip>{:fy('Added edit data will be validated')}</tip>
            </div>
        </div>
  <!--      <div class="layui-form-item">
            <label class="layui-form-label">错误提示</label>
            <div class="layui-input-block">
                <input type="text" id="msg" name="msg" class="layui-input" value="{$row.msg}">
                <tip>验证失败的错误提示</tip>
            </div>
        </div>-->


        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required">{:fy('form types')}</label>
            <div class="layui-input-block">
                <select name="formtype" class="layui-select" lay-search>
                    {if isset($alldata['field']['formtype']['option'])}
                    {foreach $alldata['field']['formtype']['option'] as $key=>$vo}
                    <?php if(!IS_DEV && in_array($key,['video','color','none','password','optgroup','json','lradio',"lcheckbox","lselect",'lselects',"treecheckbox",  "aselect","selectgroup"])){continue; }?>
                    {if $key==$row.formtype}
                    <option value="{$key}" selected>{:fy($vo)}</option>
                    {else}
                    <option value="{$key}">{:fy($vo)}</option>
                    {/if}
                    {/foreach}
                    {/if}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Options')}</label>
            <div class="layui-input-block">
                <textarea type="text" id="option" name="option" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Options')}" placeholder="{:fy('Please enter')}{:fy('Options')}" >{$row.option|raw}</textarea>
                <tip>{:fy('The form type is a single choice box, a multiple choice box, or a drop-down box. Example: 0: close, 1: open')}</tip>
            </div>
        </div>
        <?php  if(IS_DEV){ ?>
        <div class="layui-form-item">
            <label class="layui-form-label">ajax{:fy('Drop down the request address')}</label>
            <div class="layui-input-block">
                <input type="text" id="href" name="href" class="layui-input" lay-reqtext="{:fy('Please enter')}ajax{:fy('Drop down the request address')}" placeholder="{:fy('Please enter')}ajax{:fy('Drop down the request address')}" value="{$row.href}">
                <tip>{:fy('Please enter')}ajax{:fy('Drop down the request address')}。</tip>
            </div>
        </div>
        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required">{:fy('Association Table')}</label>
            <div class="layui-input-block">
                <select name="join_table" class="layui-select" lay-search>
                    <option value="">{:fy('Please select')}</option>
                    {foreach $getTableList as $key=>$vo}

                    {if $row.join_table==$vo['value']}
                    <option value="{$vo.value}" selected>{$vo.name|raw}</option>
                    {else}
                    <option value="{$vo.value}">{$vo.name|raw}</option>
                    {/if}
                    {/foreach}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Presentation field')}</label>
            <div class="layui-input-block">
                <input type="text" id="foreign_key" name="foreign_key" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Presentation field')}" placeholder="{:fy('Please enter')}{:fy('Presentation field')}" value="{$row.foreign_key}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Storage field')}</label>
            <div class="layui-input-block">
                <input type="text" id="relationship_primary_key" name="relationship_primary_key" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Storage field')}" placeholder="{:fy('Please enter')}{:fy('Storage field')}" value="{$row.relationship_primary_key}">

            </div>
        </div>
        <?php } ?>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Description')}</label>
            <div class="layui-input-block">
                <textarea type="text" id="describe" name="describe" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Description')}" placeholder="{:fy('Please enter')}{:fy('Description')}" >{$row.describe|raw}</textarea>

            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('List display')}</label>
            <div class="layui-input-block">
                {if $row.show==1}
                <input type="checkbox" name="show" checked lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {else}
                <input type="checkbox" name="show" lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {/if}

            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Form')}</label>
            <div class="layui-input-block">
                {if $row.edit==1}
                <input type="checkbox" name="edit" checked lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {else}
                <input type="checkbox" name="edit" lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {/if}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Search')}</label>
            <div class="layui-input-block">
                {if $row.search==1}
                <input type="checkbox" name="search" checked lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {else}
                <input type="checkbox" name="search" lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {/if}
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label required">{:fy('Import')}</label>
            <div class="layui-input-block">
                {if $row.export==1}
                <input type="checkbox" name="export" checked lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {else}
                <input type="checkbox" name="export" lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}">
                {/if}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('width')}</label>
            <div class="layui-input-block">
                <input type="text" id="width" name="width" class="layui-input" lay-reqtext="{:fy('Please enter')}{:fy('Field')} {:fy('width')}" placeholder="{:fy('Please enter')}{:fy('Field')} {:fy('width')}" value="{$row.width}">
            </div>
        </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">布局类</label>
                        <div class="layui-input-block">
                            <input type="text" id="grid" name="grid" class="layui-input" placeholder="请填写栅格布局的类名" value="{$row.grid}">
                            <tip>栅格布局类名(实现1行占多少个表单)，类名参考https://layui.dev/docs/2/layout/grid.html ，通常实现中等屏幕(桌面≥992px)layui-col-md4（1行3列）  layui-col-md6（1行2列）</tip>
                        </div>
                    </div>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>{:fy('Confirm')}</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">{:fy('Reset')}</button>
        </div>

    </form>
</div>
