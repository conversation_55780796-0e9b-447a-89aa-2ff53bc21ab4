define(["jquery","miniMenu","miniTheme","miniTab"],function(n,t,a,l){var n=layui.$,r=layui.layer,s=layui.element;var o={render:function(i){i.iniUrl=i.iniUrl||null;i.clearUrl=i.clearUrl||null;i.urlHashLocation=i.urlHashLocation||false;i.bgColorDefault=i.bgColorDefault||0;i.multiModule=i.multiModule||false;i.menuChildOpen=i.menuChildOpen||false;i.loadingTime=i.loadingTime||1;i.pageAnim=i.pageAnim||false;i.maxTabNum=i.maxTabNum||20;n.getJSON(i.iniUrl,function(e){if(e==null){o.error("暂无菜单信息")}else{o.renderClear(i.clearUrl);o.renderHome(e.homeInfo);o.renderAnim(i.pageAnim);o.listen();t.render({menuList:e.menuInfo,multiModule:i.multiModule,menuChildOpen:i.menuChildOpen});l.render({filter:"layuiminiTab",urlHashLocation:i.urlHashLocation,multiModule:i.multiModule,menuChildOpen:i.menuChildOpen,maxTabNum:i.maxTabNum,menuList:e.menuInfo,homeInfo:e.homeInfo,listenSwichCallback:function(){o.renderDevice()}});a.render({bgColorDefault:i.bgColorDefault,listen:true});o.deleteLoader(i.loadingTime)}}).fail(function(){o.error("菜单接口有误")})},renderHome:function(e){sessionStorage.setItem("layuiminiHomeHref",e.href);n("#layuiminiHomeTabId").html('<span class="layuimini-tab-active"></span><span class="disable-close">'+e.title+'</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>');n("#layuiminiHomeTabId").attr("lay-id",e.href);n("#layuiminiHomeTabIframe").html('<iframe width="100%" height="100%" frameborder="no" border="0" marginwidth="0" marginheight="0"  src="'+e.href+'"></iframe>')},renderClear:function(e){n(".layuimini-clear").attr("data-href",e)},renderAnim:function(e){if(e){n("#layuimini-bg-color").after('<style id="layuimini-page-anim">'+".layui-tab-item.layui-show {animation:moveTop 1s;-webkit-animation:moveTop 1s;animation-fill-mode:both;-webkit-animation-fill-mode:both;position:relative;height:100%;-webkit-overflow-scrolling:touch;}\n"+"@keyframes moveTop {0% {opacity:0;-webkit-transform:translateY(30px);-ms-transform:translateY(30px);transform:translateY(30px);}\n"+"    100% {opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0);}\n"+"}\n"+"@-o-keyframes moveTop {0% {opacity:0;-webkit-transform:translateY(30px);-ms-transform:translateY(30px);transform:translateY(30px);}\n"+"    100% {opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0);}\n"+"}\n"+"@-moz-keyframes moveTop {0% {opacity:0;-webkit-transform:translateY(30px);-ms-transform:translateY(30px);transform:translateY(30px);}\n"+"    100% {opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0);}\n"+"}\n"+"@-webkit-keyframes moveTop {0% {opacity:0;-webkit-transform:translateY(30px);-ms-transform:translateY(30px);transform:translateY(30px);}\n"+"    100% {opacity:1;-webkit-transform:translateY(0);-ms-transform:translateY(0);transform:translateY(0);}\n"+"}"+"</style>")}},fullScreen:function(){var e=document.documentElement;var i=e.requestFullScreen||e.webkitRequestFullScreen;if(typeof i!="undefined"&&i){i.call(e)}else if(typeof window.ActiveXObject!="undefined"){var t=new ActiveXObject("WScript.Shell");if(t!=null){t.SendKeys("{F11}")}}else if(e.msRequestFullscreen){e.msRequestFullscreen()}else if(e.oRequestFullscreen){e.oRequestFullscreen()}else if(e.webkitRequestFullscreen){e.webkitRequestFullscreen()}else if(e.mozRequestFullScreen){e.mozRequestFullScreen()}else{o.error("浏览器不支持全屏调用！")}},exitFullScreen:function(){var e=document;var i=e.cancelFullScreen||e.webkitCancelFullScreen||e.exitFullScreen;if(typeof i!="undefined"&&i){i.call(e)}else if(typeof window.ActiveXObject!="undefined"){var t=new ActiveXObject("WScript.Shell");if(t!=null){t.SendKeys("{F11}")}}else if(e.msExitFullscreen){e.msExitFullscreen()}else if(e.oRequestFullscreen){e.oCancelFullScreen()}else if(e.mozCancelFullScreen){e.mozCancelFullScreen()}else if(e.webkitCancelFullScreen){e.webkitCancelFullScreen()}else{o.error("浏览器不支持全屏调用！")}},renderDevice:function(){if(o.checkMobile()){n(".layuimini-tool i").attr("data-side-fold",1);n(".layuimini-tool i").attr("class","fa fa-outdent");n(".layui-layout-body").removeClass("layuimini-mini");n(".layui-layout-body").addClass("layuimini-all")}},deleteLoader:function(e){n(".layuimini-loader").fadeOut()},success:function(e){return r.msg(e,{icon:1,shade:this.shade,scrollbar:false,time:2e3,shadeClose:true})},error:function(e){return r.msg(e,{icon:2,shade:this.shade,scrollbar:false,time:3e3,shadeClose:true})},checkMobile:function(){var e=navigator.userAgent.toLocaleLowerCase();var i=navigator.platform.toLocaleLowerCase();var t=/android/i.test(e)||/iPhone|iPod|iPad/i.test(e)&&/linux/i.test(i)||/ucweb.*linux/i.test(e);var n=/iPhone|iPod|iPad/i.test(e)&&!t;var a=/Windows Phone|ZuneWP7/i.test(e);var l=document.documentElement.clientWidth;if(!t&&!n&&!a&&l>1024){return false}else{return true}},listen:function(){n("body").on("click","[data-clear]",function(){var t=r.load(0,{shade:false,time:2*1e3});sessionStorage.clear();var e=n(this).attr("data-href");if(e!=undefined&&e!=""&&e!=null){n.getJSON(e,function(e,i){r.close(t);if(e.code!=1){return o.error(e.msg)}else{return o.success(e.msg)}}).fail(function(){r.close(t);return o.error("清理缓存接口有误")})}else{r.close(t);return o.success("清除缓存成功")}});n("body").on("click",".refresh[data-refresh]",function(){n(".layui-tab-item.layui-show").find("iframe")[0].contentWindow.location.reload();o.success(fy("Refresh succeeded"))});n("body").on("mouseenter",".layui-nav-tree .menu-li",function(){if(o.checkMobile()){return false}var e=n(this).attr("class"),i=n(this).prop("innerHTML"),t=n(".layuimini-tool i").attr("data-side-fold");if(t==0&&i){i="<ul class='layuimini-menu-left-zoom layui-nav layui-nav-tree layui-this'><li class='layui-nav-item layui-nav-itemed'>"+i+"</li></ul>";window.openTips=r.tips(i,n(this),{tips:[2,"#2f4056"],time:3e5,skin:"popup-tips",success:function(e){var i=n(e).position().left-10;n(e).css({left:i});s.render()}})}});n("body").on("mouseleave",".popup-tips",function(){if(o.checkMobile()){return false}var e=n(".layuimini-tool i").attr("data-side-fold");if(e==0){try{r.close(window.openTips)}catch(e){console.log(e.message)}}});n("body").on("click","[data-check-screen]",function(){var e=n(this).attr("data-check-screen");if(e=="full"){o.fullScreen();n(this).attr("data-check-screen","exit");n(this).html('<i class="fa fa-compress"></i>')}else{o.exitFullScreen();n(this).attr("data-check-screen","full");n(this).html('<i class="fa fa-arrows-alt"></i>')}});n("body").on("click",".layuimini-make",function(){o.renderDevice()})}};return o});