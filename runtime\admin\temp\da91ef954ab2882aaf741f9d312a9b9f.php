<?php /*a:3:{s:51:"C:\wwwroot\d.cn\app\admin\view\auth\admin_list.html";i:1678286974;s:50:"C:\wwwroot\d.cn\app\admin\view\layout\default.html";i:1683620908;s:47:"C:\wwwroot\d.cn\app\admin\view\common\foot.html";i:1672069288;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<style>
    #searchFieldset_currentTableRenderId{
        border:1px solid #eee;
    }
</style>
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend><?php echo fy("Account management"); ?></legend>
    </fieldset>

    <table id="currentTable" class="layui-table layui-hide"
           data-auth-add="<?php echo auth('Auth/adminAdd'); ?>"
           data-auth-edit="<?php echo auth('Auth/adminEdit'); ?>"
           data-auth-delete="<?php echo auth('Auth/adminDel'); ?>"
           data-auth-state="<?php echo auth('Auth/adminState'); ?>"
           lay-filter="currentTable">
    </table>
</div>

<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-success layui-btn-xs" data-open="<?php echo url('adminEdit'); ?>?admin_id={{d.admin_id}}" data-title="<?php echo fy('Edit Account'); ?>"><?php echo fy('Edit'); ?></a>
    <?php if(auth('Auth/adminDel')){ ?>
    {{# if(d.admin_id!=1 ){ }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" data-request="auth/adminDel?admin_id={{d.admin_id}}" data-title="<?php echo fy('Are you sure you want to delete the current account'); ?>？"><?php echo fy('Delete'); ?></a>
    {{# } }}
    <?php } ?>

</script>
<script type="text/html" id="open">
    {{# if(d.admin_id==1){ }}
        <input type="checkbox" disabled name="is_open" value="{{d.admin_id}}" lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>" lay-filter="open" checked>
    {{# }else{  }}
        <input type="checkbox" name="is_open" value="{{d.admin_id}}" lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>" lay-filter="open" {{ d.is_open == 1 ? 'checked' : '' }}>
    {{# } }}
</script>
<script type="text/html" id="lookiphone">
    {{# if(d.admin_id==1){ }}
        <input type="checkbox" disabled name="isphone" value="{{d.admin_id}}" lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>" lay-filter="lookiphone" checked>
    {{# }else{  }}
        <input type="checkbox" name="isphone" value="{{d.admin_id}}" lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>" lay-filter="lookiphone" {{ d.isphone == 1 ? 'checked' : '' }}>
    {{# } }}
</script>
<!--<script type="text/html" id="topBtn">
    <button type="button" class="layui-btn layui-btn-sm layuimini-btn-primary" data-table-refresh="list"><i class="fa fa-refresh"></i> </button>
    <button class="layui-btn layui-btn-normal layui-btn-sm" data-open="<?php echo url('adminAdd'); ?>" data-title="<?php echo fy('Add account'); ?>"><i class="fa fa-plus"></i><?php echo fy("Add"); ?></button>
</script>-->


</body>
</html>