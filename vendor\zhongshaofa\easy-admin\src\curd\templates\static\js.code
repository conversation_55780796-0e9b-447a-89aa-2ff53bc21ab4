define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: '{{controllerUrl}}/index',
        add_url: '{{controllerUrl}}/add',
        edit_url: '{{controllerUrl}}/edit',
        delete_url: '{{controllerUrl}}/delete',
        export_url: '{{controllerUrl}}/export',
        modify_url: '{{controllerUrl}}/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols: [[
                {{indexCols}}
                ]],
                done: function(res, curr, count){
                       if(count===undefined && res.msg && res.url){
                           ea.msg.tips(res.msg,1,function (){
                               window.top.location.href=res.url;
                           })
                       }
                }
            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});