!function(p){"use strict";var e,h=p.document,y={modules:{},status:{},timeout:10,event:{}},i=function(){this.v="2.7.6"},t=p.LAYUI_GLOBAL||{},m=(e=h.currentScript?h.currentScript.src:function(){for(var e,t=h.scripts,n=t.length-1,i=n;0<i;i--)if("interactive"===t[i].readyState){e=t[i].src;break}return e||t[n].src}(),y.dir=t.dir||e.substring(0,e.lastIndexOf("/")+1)),g=function(e,t){t=t||"log",p.console&&console[t]&&console[t]("layui error hint: "+e)},v="undefined"!=typeof opera&&"[object Opera]"===opera.toString(),b=y.builtin={lay:"lay",layer:"layer",laydate:"laydate",laypage:"laypage",laytpl:"laytpl",layedit:"layedit",form:"form",upload:"upload",dropdown:"dropdown",transfer:"transfer",tree:"tree",table:"table",element:"element",rate:"rate",colorpicker:"colorpicker",slider:"slider",carousel:"carousel",flow:"flow",util:"util",code:"code",jquery:"jquery",all:"all","layui.all":"layui.all"},c=(i.prototype.cache=y,i.prototype.define=function(e,i){return"function"==typeof e&&(i=e,e=[]),this.use(e,function(){var n=function(e,t){layui[e]=t,y.status[e]=!0};return"function"==typeof i&&i(function(e,t){n(e,t),y.callback[e]=function(){i(n)}}),this},null,"define"),this},i.prototype.use=function(n,e,t,i){var a=this,o=y.dir=y.dir||m,r=h.getElementsByTagName("head")[0],l=(n="string"==typeof n?[n]:"function"==typeof n?(e=n,["all"]):n,p.jQuery&&jQuery.fn.on&&(a.each(n,function(e,t){"jquery"===t&&n.splice(e,1)}),layui.jquery=layui.$=jQuery),n[0]),s=0;function c(e,t){var n="PLaySTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/;"load"!==e.type&&!n.test((e.currentTarget||e.srcElement).readyState)||(y.modules[l]=t,r.removeChild(d),function e(){return++s>1e3*y.timeout/4?g(l+" is not a valid module","error"):void(y.status[l]?u():setTimeout(e,4))}())}function u(){t.push(layui[l]),1<n.length?a.use(n.slice(1),e,t,i):"function"==typeof e&&(layui.jquery&&"function"==typeof layui.jquery&&"define"!==i?layui.jquery(function(){e.apply(layui,t)}):e.apply(layui,t))}if(t=t||[],y.host=y.host||(o.match(/\/\/([\s\S]+?)\//)||["//"+location.host+"/"])[0],0===n.length||layui["layui.all"]&&b[l])return u(),a;var d,f=(f=(b[l]?o+"modules/":!/^\{\/\}/.test(a.modules[l])&&y.base||"")+(a.modules[l]||l)+".js").replace(/^\{\/\}/,"");return!y.modules[l]&&layui[l]&&(y.modules[l]=f),y.modules[l]?function e(){return++s>1e3*y.timeout/4?g(l+" is not a valid module","error"):void("string"==typeof y.modules[l]&&y.status[l]?u():setTimeout(e,4))}():((d=h.createElement("script"))["async"]=!0,d.charset="utf-8",d.src=f+((o=!0===y.version?y.v||(new Date).getTime():y.version||"")?"?v="+o:""),r.appendChild(d),!d.attachEvent||d.attachEvent.toString&&d.attachEvent.toString().indexOf("[native code")<0||v?d.addEventListener("load",function(e){c(e,f)},!1):d.attachEvent("onreadystatechange",function(e){c(e,f)}),y.modules[l]=f),a},i.prototype.disuse=function(e){var n=this;return e=n.isArray(e)?e:[e],n.each(e,function(e,t){y.status[t],delete n[t],delete b[t],delete n.modules[t],delete y.status[t],delete y.modules[t]}),n},i.prototype.getStyle=function(e,t){e=e.currentStyle||p.getComputedStyle(e,null);return e[e.getPropertyValue?"getPropertyValue":"getAttribute"](t)},i.prototype.link=function(i,a,e){var o=this,t=h.getElementsByTagName("head")[0],n=h.createElement("link"),e=((e="string"==typeof a?a:e)||i).replace(/\.|\//g,""),r=n.id="layuicss-"+e,l="creating",s=0;return n.rel="stylesheet",n.href=i+(y.debug?"?v="+(new Date).getTime():""),n.media="all",h.getElementById(r)||t.appendChild(n),"function"!=typeof a||function e(t){var n=h.getElementById(r);return++s>1e3*y.timeout/100?g(i+" timeout"):void(1989===parseInt(o.getStyle(n,"width"))?(t===l&&n.removeAttribute("lay-status"),n.getAttribute("lay-status")===l?setTimeout(e,100):a()):(n.setAttribute("lay-status",l),setTimeout(function(){e(l)},100)))}(),o},i.prototype.addcss=function(e,t,n){return layui.link(y.dir+"css/"+e,t,n)},y.callback={},i.prototype.factory=function(e){if(layui[e])return"function"==typeof y.callback[e]?y.callback[e]:null},i.prototype.img=function(e,t,n){var i=new Image;if(i.src=e,i.complete)return t(i);i.onload=function(){i.onload=null,"function"==typeof t&&t(i)},i.onerror=function(e){i.onerror=null,"function"==typeof n&&n(e)}},i.prototype.config=function(e){for(var t in e=e||{})y[t]=e[t];return this},i.prototype.modules=function(){var e,t={};for(e in b)t[e]=b[e];return t}(),i.prototype.extend=function(e){for(var t in e=e||{})this[t]||this.modules[t]?g(t+" Module already exists","error"):this.modules[t]=e[t];return this},i.prototype.router=i.prototype.hash=function(e){var n={path:[],search:{},hash:((e=e||location.hash).match(/[^#](#.*$)/)||[])[1]||""};return/^#\//.test(e)&&(e=e.replace(/^#\//,""),n.href="/"+e,e=e.replace(/([^#])(#.*$)/,"$1").split("/")||[],this.each(e,function(e,t){/^\w+=/.test(t)?(t=t.split("="),n.search[t[0]]=t[1]):n.path.push(t)})),n},i.prototype.url=function(e){var a,t,n=this;return{pathname:(e?((e.match(/\.[^.]+?\/.+/)||[])[0]||"").replace(/^[^\/]+/,"").replace(/\?.+/,""):location.pathname).replace(/^\//,"").split("/"),search:(a={},t=(e?((e.match(/\?.+/)||[])[0]||"").replace(/\#.+/,""):location.search).replace(/^\?+/,"").split("&"),n.each(t,function(e,t){var n=t.indexOf("="),i=n<0?t.substr(0,t.length):0!==n&&t.substr(0,n);i&&(a[i]=0<n?t.substr(n+1):null)}),a),hash:n.router(e?(e.match(/#.+/)||[])[0]||"/":location.hash)}},i.prototype.data=function(e,t,n){if(e=e||"layui",n=n||localStorage,p.JSON&&p.JSON.parse){if(null===t)return delete n[e];t="object"==typeof t?t:{key:t};try{var i=JSON.parse(n[e])}catch(e){i={}}return"value"in t&&(i[t.key]=t.value),t.remove&&delete i[t.key],n[e]=JSON.stringify(i),t.key?i[t.key]:i}},i.prototype.sessionData=function(e,t){return this.data(e,t,sessionStorage)},i.prototype.device=function(e){var n=navigator.userAgent.toLowerCase(),t=function(e){var t=new RegExp(e+"/([^\\s\\_\\-]+)");return(e=(n.match(t)||[])[1])||!1},i={os:/windows/.test(n)?"windows":/linux/.test(n)?"linux":/iphone|ipod|ipad|ios/.test(n)?"ios":/mac/.test(n)?"mac":void 0,ie:!!(p.ActiveXObject||"ActiveXObject"in p)&&((n.match(/msie\s(\d+)/)||[])[1]||"11"),weixin:t("micromessenger")};return e&&!i[e]&&(i[e]=t(e)),i.android=/android/.test(n),i.ios="ios"===i.os,i.mobile=!(!i.android&&!i.ios),i},i.prototype.hint=function(){return{error:g}},i.prototype._typeof=i.prototype.type=function(e){return null===e?String(e):"object"==typeof e||"function"==typeof e?(t=(t=Object.prototype.toString.call(e).match(/\s(.+)\]$/)||[])[1]||"Object",new RegExp("\\b(Function|Array|Date|RegExp|Object|Error|Symbol)\\b").test(t)?t.toLowerCase():"object"):typeof e;var t},i.prototype._isArray=i.prototype.isArray=function(e){var t,n=this.type(e);return!(!e||"object"!=typeof e||e===p)&&(t="length"in e&&e.length,"array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)},i.prototype.each=function(e,n){var t,i=function(e,t){return n.call(t[e],e,t[e])};if("function"!=typeof n)return this;if(this.isArray(e=e||[]))for(t=0;t<e.length&&!i(t,e);t++);else for(t in e)if(i(t,e))break;return this},i.prototype.sort=function(e,a,t){var n=JSON.parse(JSON.stringify(e||[]));return"object"!==this.type(e)||a?"object"!=typeof e?[n]:(n.sort(function(e,t){var n=e[a],i=t[a];if(!isNaN(e)&&!isNaN(t))return e-t;if(!isNaN(e)&&isNaN(t)){if(!a||"object"!=typeof t)return-1;n=e}else if(isNaN(e)&&!isNaN(t)){if(!a||"object"!=typeof e)return 1;i=t}e=[!isNaN(n),!isNaN(i)];return e[0]&&e[1]?n&&!i&&0!==i?1:!n&&0!==n&&i?-1:n-i:e[0]||e[1]?e[0]||!e[1]?-1:!e[0]||e[1]?1:void 0:i<n?1:n<i?-1:0}),t&&n.reverse(),n):n},i.prototype.stope=function(t){t=t||p.event;try{t.stopPropagation()}catch(e){t.cancelBubble=!0}},"LAYUI-EVENT-REMOVE");i.prototype.onevent=function(e,t,n){return"string"!=typeof e||"function"!=typeof n?this:i.event(e,t,null,n)},i.prototype.event=i.event=function(e,t,n,i){var a=this,o=null,r=(t||"").match(/\((.*)\)$/)||[],e=(e+"."+t).replace(r[0],""),l=r[1]||"",s=function(e,t){!1===(t&&t.call(a,n))&&null===o&&(o=!1)};return n===c?(delete(a.cache.event[e]||{})[l],a):i?(y.event[e]=y.event[e]||{},y.event[e][l]=[i],this):(layui.each(y.event[e],function(e,t){"{*}"===l?layui.each(t,s):(""===e&&layui.each(t,s),l&&e===l&&layui.each(t,s))}),o)},i.prototype.on=function(e,t,n){return this.onevent.call(this,t,e,n)},i.prototype.off=function(e,t){return this.event.call(this,t,e,c)},p.layui=new i}(window);layui.define(function(e){var t=layui.cache;layui.config({dir:t.dir.replace(/lay\/dest\/$/,"")}),e("layui.all",layui.v)});!function(u){"use strict";var e,d=u.document,f=function(e){return new a(e)},a=function(e){for(var t=0,n="object"==typeof e?[e]:(this.selector=e,d.querySelectorAll(e||null));t<n.length;t++)this.push(n[t])};(a.prototype=[]).constructor=a,f.extend=function(){var e,t=1,n=arguments,i=function(e,t){for(var n in e=e||("array"===layui.type(t)?[]:{}),t)e[n]=t[n]&&t[n].constructor===Object?i(e[n],t[n]):t[n];return e};for(n[0]="object"==typeof n[0]?n[0]:{},e=n.length;t<e;t++)"object"==typeof n[t]&&i(n[0],n[t]);return n[0]},f.v="1.0.8",f.ie=(e=navigator.userAgent.toLowerCase(),!!(u.ActiveXObject||"ActiveXObject"in u)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")),f.layui=layui||{},f.getPath=layui.cache.dir,f.stope=layui.stope,f.each=function(){return layui.each.apply(layui,arguments),this},f.digit=function(e,t){if("string"!=typeof e&&"number"!=typeof e)return"";var n="";t=t||2;for(var i=(e=String(e)).length;i<t;i++)n+="0";return e<Math.pow(10,t)?n+e:e},f.elem=function(e,t){var n=d.createElement(e);return f.each(t||{},function(e,t){n.setAttribute(e,t)}),n},f.hasScrollbar=function(){return d.body.scrollHeight>(u.innerHeight||d.documentElement.clientHeight)},f.position=function(e,t,n){var i,a,o,r,l,s,c;t&&(n=n||{},e!==d&&e!==f("body")[0]||(n.clickType="right"),l="right"===n.clickType?{left:(l=n.e||u.event||{}).clientX,top:l.clientY,right:l.clientX,bottom:l.clientY}:e.getBoundingClientRect(),s=t.offsetWidth,c=t.offsetHeight,i=function(e){return d.body[e=e?"scrollLeft":"scrollTop"]|d.documentElement[e]},o=l.left,r=l.bottom,"center"===n.align?o-=(s-e.offsetWidth)/2:"right"===n.align&&(o=o-s+e.offsetWidth),(o=o+s+5>(a=function(e){return d.documentElement[e?"clientWidth":"clientHeight"]})("width")?a("width")-s-5:o)<5&&(o=5),r+c+5>a()&&(l.top>c+5?r=l.top-c-10:"right"===n.clickType?(r=a()-c-10)<0&&(r=0):r=5),(s=n.position)&&(t.style.position=s),t.style.left=o+("fixed"===s?0:i(1))+"px",t.style.top=r+("fixed"===s?0:i())+"px",f.hasScrollbar()||(c=t.getBoundingClientRect(),!n.SYSTEM_RELOAD&&c.bottom+5>a()&&(n.SYSTEM_RELOAD=!0,setTimeout(function(){f.position(e,t,n)},50))))},f.options=function(e,t){e=f(e),t=t||"lay-options";try{return new Function("return "+(e.attr(t)||"{}"))()}catch(e){return hint.error("parseerror："+e,"error"),{}}},f.isTopElem=function(n){var e=[d,f("body")[0]],i=!1;return f.each(e,function(e,t){if(t===n)return i=!0}),i},a.addStr=function(n,e){return n=n.replace(/\s+/," "),e=e.replace(/\s+/," ").split(" "),f.each(e,function(e,t){new RegExp("\\b"+t+"\\b").test(n)||(n=n+" "+t)}),n.replace(/^\s|\s$/,"")},a.removeStr=function(n,e){return n=n.replace(/\s+/," "),e=e.replace(/\s+/," ").split(" "),f.each(e,function(e,t){t=new RegExp("\\b"+t+"\\b");t.test(n)&&(n=n.replace(t,""))}),n.replace(/\s+/," ").replace(/^\s|\s$/,"")},a.prototype.find=function(i){var a=this,o=0,r=[],l="object"==typeof i;return this.each(function(e,t){for(var n=l?t.contains(i):t.querySelectorAll(i||null);o<n.length;o++)r.push(n[o]);a.shift()}),l||(a.selector=(a.selector?a.selector+" ":"")+i),f.each(r,function(e,t){a.push(t)}),a},a.prototype.each=function(e){return f.each.call(this,this,e)},a.prototype.addClass=function(n,i){return this.each(function(e,t){t.className=a[i?"removeStr":"addStr"](t.className,n)})},a.prototype.removeClass=function(e){return this.addClass(e,!0)},a.prototype.hasClass=function(n){var i=!1;return this.each(function(e,t){new RegExp("\\b"+n+"\\b").test(t.className)&&(i=!0)}),i},a.prototype.css=function(t,i){var e=this,a=function(e){return isNaN(e)?e:e+"px"};return"string"!=typeof t||i!==undefined?e.each(function(e,n){"object"==typeof t?f.each(t,function(e,t){n.style[e]=a(t)}):n.style[t]=a(i)}):0<e.length?e[0].style[t]:void 0},a.prototype.width=function(n){var i=this;return n!==undefined?i.each(function(e,t){i.css("width",n)}):0<i.length?i[0].offsetWidth:void 0},a.prototype.height=function(n){var i=this;return n!==undefined?i.each(function(e,t){i.css("height",n)}):0<i.length?i[0].offsetHeight:void 0},a.prototype.attr=function(n,i){var e=this;return i!==undefined?e.each(function(e,t){t.setAttribute(n,i)}):0<e.length?e[0].getAttribute(n):void 0},a.prototype.removeAttr=function(n){return this.each(function(e,t){t.removeAttribute(n)})},a.prototype.html=function(n){var e=this;return n!==undefined?this.each(function(e,t){t.innerHTML=n}):0<e.length?e[0].innerHTML:void 0},a.prototype.val=function(n){var e=this;return n!==undefined?this.each(function(e,t){t.value=n}):0<e.length?e[0].value:void 0},a.prototype.append=function(n){return this.each(function(e,t){"object"==typeof n?t.appendChild(n):t.innerHTML=t.innerHTML+n})},a.prototype.remove=function(n){return this.each(function(e,t){n?t.removeChild(n):t.parentNode.removeChild(t)})},a.prototype.on=function(n,i){return this.each(function(e,t){t.attachEvent?t.attachEvent("on"+n,function(e){e.target=e.srcElement,i.call(t,e)}):t.addEventListener(n,i,!1)})},a.prototype.off=function(n,i){return this.each(function(e,t){t.detachEvent?t.detachEvent("on"+n,i):t.removeEventListener(n,i,!1)})},u.lay=f,u.layui&&layui.define&&layui.define(function(e){e("lay",f)})}(window,window.document);layui.define(function(e){"use strict";var o={open:"{{",close:"}}"},r={exp:function(e){return new RegExp(e,"g")},query:function(e,t,n){return l((t||"")+o.open+["#([\\s\\S])+?","([^{#}])*?"][e||0]+o.close+(n||""))},escape:function(e){return e===undefined||null===e?"":/[<"'>]|&(?=#[a-zA-Z0-9]+)/g.test(e+="")?e.replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;"):e},error:function(e,t){var n="Laytpl Error: ";return"object"==typeof console&&console.error(n+e+"\n"+(t||"")),n+e}},l=r.exp,t=function(e){this.tpl=e},n=(t.pt=t.prototype,window.errors=0,t.pt.parse=function(e,t){var n=e,i=l("^"+o.open+"#",""),a=l(o.close+"$","");e='"use strict";var view = "'+(e=e.replace(/\s+|\r|\t|\n/g," ").replace(l(o.open+"#"),o.open+"# ").replace(l(o.close+"}"),"} "+o.close).replace(/\\/g,"\\\\").replace(l(o.open+"!(.+?)!"+o.close),function(e){return e=e.replace(l("^"+o.open+"!"),"").replace(l("!"+o.close),"").replace(l(o.open+"|"+o.close),function(e){return e.replace(/(.)/g,"\\$1")})}).replace(/(?="|')/g,"\\").replace(r.query(),function(e){return'";'+(e=e.replace(i,"").replace(a,"")).replace(/\\(.)/g,"$1")+';view+="'}).replace(r.query(1),function(e){var t='"+laytpl.escape(';return e.replace(/\s/g,"")===o.open+o.close?"":(e=e.replace(l(o.open+"|"+o.close),""),/^=/.test(e)?e=e.replace(/^=/,""):/^-/.test(e)&&(e=e.replace(/^-/,""),t='"+('),t+e.replace(/\\(.)/g,"$1")+')+"')}))+'";return view;';try{return this.cache=e=new Function("d, laytpl",e),e(t,r)}catch(e){return delete this.cache,r.error(e,n)}},t.pt.render=function(e,t){var n=this;return e?(e=n.cache?n.cache(e,r):n.parse(n.tpl,e),t?void t(e):e):r.error("no data")},function(e){return"string"!=typeof e?r.error("Template not found"):new t(e)});n.config=function(e){for(var t in e=e||{})o[t]=e[t]},n.v="1.2.0",e("laytpl",n)});layui.define(function(e){"use strict";var o=document,r="getElementById",u="getElementsByTagName",t="layui-disabled",n=function(e){var t=this;t.config=e||{},t.config.index=++d.index,t.render(!0)},d=(n.prototype.type=function(){var e=this.config;if("object"==typeof e.elem)return e.elem.length===undefined?2:3},n.prototype.view=function(){var n,i,a=this.config,o=a.groups="groups"in a?Number(a.groups)||0:5,r=(a.layout="object"==typeof a.layout?a.layout:["prev","page","next"],a.count=Number(a.count)||0,a.curr=Number(a.curr)||1,a.limits="object"==typeof a.limits?a.limits:[10,20,30,40,50],a.limit=Number(a.limit)||10,a.pages=Math.ceil(a.count/a.limit)||1,a.curr>a.pages?a.curr=a.pages:a.curr<1&&(a.curr=1),o<0?o=1:o>a.pages&&(o=a.pages),a.prev="prev"in a?a.prev:"&#x4E0A;&#x4E00;&#x9875;",a.next="next"in a?a.next:"&#x4E0B;&#x4E00;&#x9875;",a.pages>o?Math.ceil((a.curr+(1<o?1:0))/(0<o?o:1)):1),l={prev:a.prev?'<a href="javascript:;" class="layui-laypage-prev'+(1==a.curr?" "+t:"")+'" data-page="'+(a.curr-1)+'">'+a.prev+"</a>":"",page:function(){var e=[];if(a.count<1)return"";1<r&&!1!==a.first&&0!==o&&e.push('<a href="javascript:;" class="layui-laypage-first" data-page="1"  title="&#x9996;&#x9875;">'+(a.first||1)+"</a>");var t=Math.floor((o-1)/2),n=1<r?a.curr-t:1,i=1<r?(t=a.curr+(o-t-1))>a.pages?a.pages:t:o;for(i-n<o-1&&(n=i-o+1),!1!==a.first&&2<n&&e.push('<span class="layui-laypage-spr">&#x2026;</span>');n<=i;n++)n===a.curr?e.push('<span class="layui-laypage-curr"><em class="layui-laypage-em" '+(/^#/.test(a.theme)?'style="background-color:'+a.theme+';"':"")+"></em><em>"+n+"</em></span>"):e.push('<a href="javascript:;" data-page="'+n+'">'+n+"</a>");return a.pages>o&&a.pages>i&&!1!==a.last&&(i+1<a.pages&&e.push('<span class="layui-laypage-spr">&#x2026;</span>'),0!==o&&e.push('<a href="javascript:;" class="layui-laypage-last" title="&#x5C3E;&#x9875;"  data-page="'+a.pages+'">'+(a.last||a.pages)+"</a>")),e.join("")}(),next:a.next?'<a href="javascript:;" class="layui-laypage-next'+(a.curr==a.pages?" "+t:"")+'" data-page="'+(a.curr+1)+'">'+a.next+"</a>":"",count:'<span class="layui-laypage-count">Total '+a.count+" pages</span>",limit:(n=['<span class="layui-laypage-limits"><select lay-ignore>'],layui.each(a.limits,function(e,t){n.push('<option value="'+t+'"'+(t===a.limit?"selected":"")+">"+t+" strips/page</option>")}),n.join("")+"</select></span>"),refresh:['<a href="javascript:;" data-page="'+a.curr+'" class="layui-laypage-refresh">','<i class="layui-icon layui-icon-refresh"></i>',"</a>"].join(""),skip:['<span class="layui-laypage-skip">to page','<input type="text" min="1" value="'+a.curr+'" class="layui-input">','<button type="button" class="layui-laypage-btn">go</button>',"</span>"].join("")};return['<div class="layui-box layui-laypage layui-laypage-'+(a.theme?/^#/.test(a.theme)?"molv":a.theme:"default")+'" id="layui-laypage-'+a.index+'">',(i=[],layui.each(a.layout,function(e,t){l[t]&&i.push(l[t])}),i.join("")),"</div>"].join("")},n.prototype.jump=function(e,t){if(e){var n=this,i=n.config,a=e.children,o=e[u]("button")[0],r=e[u]("input")[0],e=e[u]("select")[0],l=function(){var e=Number(r.value.replace(/\s|\D/g,""));e&&(i.curr=e,n.render())};if(t)return l();for(var s=0,c=a.length;s<c;s++)"a"===a[s].nodeName.toLowerCase()&&d.on(a[s],"click",function(){var e=Number(this.getAttribute("data-page"));e<1||e>i.pages||(i.curr=e,n.render())});e&&d.on(e,"change",function(){var e=this.value;i.curr*e>i.count&&(i.curr=Math.ceil(i.count/e)),i.limit=e,n.render()}),o&&d.on(o,"click",function(){l()})}},n.prototype.skip=function(n){var i,e;n&&(i=this,(e=n[u]("input")[0])&&d.on(e,"keyup",function(e){var t=this.value,e=e.keyCode;/^(37|38|39|40)$/.test(e)||(/\D/.test(t)&&(this.value=t.replace(/\D/,"")),13===e&&i.jump(n,!0))}))},n.prototype.render=function(e){var t=this,n=t.config,i=t.type(),a=t.view(),i=(2===i?n.elem&&(n.elem.innerHTML=a):3===i?n.elem.html(a):o[r](n.elem)&&(o[r](n.elem).innerHTML=a),n.jump&&n.jump(n,e),o[r]("layui-laypage-"+n.index));t.jump(i),n.hash&&!e&&(location.hash="!"+n.hash+"="+n.curr),t.skip(i)},{render:function(e){return new n(e).index},index:layui.laypage?layui.laypage.index+1e4:0,on:function(t,e,n){return t.attachEvent?t.attachEvent("on"+e,function(e){e.target=e.srcElement,n.call(t,e)}):t.addEventListener(e,n,!1),this}});e("laypage",d)});!function(a,r){"use strict";var i=a.layui&&layui.define,o={getPath:a.lay&&lay.getPath?lay.getPath:"",link:function(e,t,n){p.path&&a.lay&&lay.layui&&lay.layui.link(p.path+e,t,n)}},e=a.LAYUI_GLOBAL||{},p={v:"5.3.1",config:{weekStart:0},index:a.laydate&&a.laydate.v?1e5:0,path:e.laydate_dir||o.getPath,set:function(e){var t=this;return t.config=lay.extend({},t.config,e),t},ready:function(e){var t="laydate",n=(i?"modules/laydate/":"theme/")+"default/laydate.css?v="+p.v;return i?layui.addcss(n,e,t):o.link(n,e,t),this}},s=function(){var t=this,e=t.config.id;return{hint:function(e){t.hint.call(t,e)},config:(s.that[e]=t).config}},n="laydate",k="layui-this",w="laydate-disabled",h=[100,2e5],y="layui-laydate-static",C="layui-laydate-list",l="layui-laydate-hint",T=".laydate-btns-confirm",E="laydate-time-text",D="laydate-btns-time",m="layui-laydate-preview",g=function(e){var t=this,n=(t.index=++p.index,t.config=lay.extend({},t.config,p.config,e),lay(e.elem||t.config.elem));if(1<n.length)return layui.each(n,function(){p.render(lay.extend({},t.config,{elem:this}))}),t;(e=t.config).id="id"in e?e.id:t.index,p.ready(function(){t.init()})},c="yyyy|y|MM|M|dd|d|HH|H|mm|m|ss|s";s.formatArr=function(e){return(e||"").match(new RegExp(c+"|.","g"))||[]},g.isLeapYear=function(e){return e%4==0&&e%100!=0||e%400==0},g.prototype.config={type:"date",range:!1,format:"yyyy-MM-dd",value:null,isInitValue:!0,min:"1900-1-1",max:"2099-12-31",trigger:"click",show:!1,showBottom:!0,isPreview:!0,btns:["clear","now","confirm"],lang:"cn",theme:"default",position:null,calendar:!1,mark:{},holidays:null,zIndex:null,done:null,change:null},g.prototype.lang=function(){var e={cn:{weeks:["日","一","二","三","四","五","六"],time:["时","分","秒"],timeTips:"选择时间",startTime:"开始时间",endTime:"结束时间",dateTips:"返回日期",month:["一","二","三","四","五","六","七","八","九","十","十一","十二"],tools:{confirm:"确定",clear:"清空",now:"现在"},timeout:"结束时间不能早于开始时间<br>请重新选择",invalidDate:"不在有效日期或时间范围内",formatError:["日期格式不合法<br>必须遵循下述格式：<br>","<br>已为你重置"],preview:"当前选中的结果"},en:{weeks:["Su","Mo","Tu","We","Th","Fr","Sa"],time:["Hours","Minutes","Seconds"],timeTips:"Select Time",startTime:"Start Time",endTime:"End Time",dateTips:"Select Date",month:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tools:{confirm:"Confirm",clear:"Clear",now:"Now"},timeout:"End time cannot be less than start Time<br>Please re-select",invalidDate:"Invalid date",formatError:["The date format error<br>Must be followed：<br>","<br>It has been reset"],preview:"The selected result"}};return e[this.config.lang]||e.cn},g.prototype.init=function(){var r=this,l=r.config,e="static"===l.position,t={year:"yyyy",month:"yyyy-MM",date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss"};l.elem=lay(l.elem),l.eventElem=lay(l.eventElem),l.elem[0]&&(r.rangeStr=l.range?"string"==typeof l.range?l.range:"-":"","array"===layui.type(l.range)&&(r.rangeElem=[lay(l.range[0]),lay(l.range[1])]),t[l.type]||(a.console&&console.error&&console.error("laydate type error:'"+l.type+"' is not supported"),l.type="date"),l.format===t.date&&(l.format=t[l.type]||t.date),r.format=s.formatArr(l.format),l.weekStart&&!/^[0-6]$/.test(l.weekStart)&&(t=r.lang(),l.weekStart=t.weeks.indexOf(l.weekStart),-1===l.weekStart&&(l.weekStart=0)),r.EXP_IF="",r.EXP_SPLIT="",lay.each(r.format,function(e,t){e=new RegExp(c).test(t)?"\\d{"+(new RegExp(c).test(r.format[0===e?e+1:e-1]||"")?/^yyyy|y$/.test(t)?4:t.length:/^yyyy$/.test(t)?"1,4":/^y$/.test(t)?"1,308":"1,2")+"}":"\\"+t;r.EXP_IF=r.EXP_IF+e,r.EXP_SPLIT=r.EXP_SPLIT+"("+e+")"}),r.EXP_IF_ONE=new RegExp("^"+r.EXP_IF+"$"),r.EXP_IF=new RegExp("^"+(l.range?r.EXP_IF+"\\s\\"+r.rangeStr+"\\s"+r.EXP_IF:r.EXP_IF)+"$"),r.EXP_SPLIT=new RegExp("^"+r.EXP_SPLIT+"$",""),r.isInput(l.elem[0])||"focus"===l.trigger&&(l.trigger="click"),l.elem.attr("lay-key")||(l.elem.attr("lay-key",r.index),l.eventElem.attr("lay-key",r.index)),l.mark=lay.extend({},l.calendar&&"cn"===l.lang?{"0-1-1":"元旦","0-2-14":"情人","0-3-8":"妇女","0-3-12":"植树","0-4-1":"愚人","0-5-1":"劳动","0-5-4":"青年","0-6-1":"儿童","0-9-10":"教师","0-10-1":"国庆","0-12-25":"圣诞"}:{},l.mark),lay.each(["min","max"],function(e,t){var n,i,a=[],o=[];o="number"==typeof l[t]?(i=l[t],n=new Date,n=r.newDate({year:n.getFullYear(),month:n.getMonth(),date:n.getDate(),hours:"23",minutes:"59",seconds:"59"}).getTime(),a=[(i=new Date(i?i<864e5?n+864e5*i:i:n)).getFullYear(),i.getMonth()+1,i.getDate()],[i.getHours(),i.getMinutes(),i.getSeconds()]):(a=(l[t].match(/\d+-\d+-\d+/)||[""])[0].split("-"),(l[t].match(/\d+:\d+:\d+/)||[""])[0].split(":")),l[t]={year:0|a[0]||(new Date).getFullYear(),month:a[1]?(0|a[1])-1:(new Date).getMonth(),date:0|a[2]||(new Date).getDate(),hours:0|o[0],minutes:0|o[1],seconds:0|o[2]}}),r.elemID="layui-laydate"+l.elem.attr("lay-key"),(l.show||e)&&r.render(),e||r.events(),l.value&&l.isInitValue&&("date"===layui.type(l.value)?r.setValue(r.parse(0,r.systemDate(l.value))):r.setValue(l.value)))},g.prototype.render=function(){var i,e,t=this,l=t.config,s=t.lang(),a="static"===l.position,n=t.elem=lay.elem("div",{id:t.elemID,class:["layui-laydate",l.range?" layui-laydate-range":"",a?" "+y:"",l.theme&&"default"!==l.theme&&!/^#/.test(l.theme)?" laydate-theme-"+l.theme:""].join("")}),c=t.elemMain=[],u=t.elemHeader=[],d=t.elemCont=[],f=t.table=[],o=t.footer=lay.elem("div",{class:"layui-laydate-footer"});l.zIndex&&(n.style.zIndex=l.zIndex),lay.each(new Array(2),function(e){if(!l.range&&0<e)return!0;var n=lay.elem("div",{class:"layui-laydate-header"}),t=[((t=lay.elem("i",{class:"layui-icon laydate-icon laydate-prev-y"})).innerHTML="&#xe65a;",t),((t=lay.elem("i",{class:"layui-icon laydate-icon laydate-prev-m"})).innerHTML="&#xe603;",t),(t=lay.elem("div",{class:"laydate-set-ym"}),i=lay.elem("span"),o=lay.elem("span"),t.appendChild(i),t.appendChild(o),t),((i=lay.elem("i",{class:"layui-icon laydate-icon laydate-next-m"})).innerHTML="&#xe602;",i),((o=lay.elem("i",{class:"layui-icon laydate-icon laydate-next-y"})).innerHTML="&#xe65b;",o)],i=lay.elem("div",{class:"layui-laydate-content"}),a=lay.elem("table"),o=lay.elem("thead"),r=lay.elem("tr");lay.each(t,function(e,t){n.appendChild(t)}),o.appendChild(r),lay.each(new Array(6),function(n){var i=a.insertRow(0);lay.each(new Array(7),function(e){var t;0===n&&((t=lay.elem("th")).innerHTML=s.weeks[(e+l.weekStart)%7],r.appendChild(t)),i.insertCell(e)})}),a.insertBefore(o,a.children[0]),i.appendChild(a),c[e]=lay.elem("div",{class:"layui-laydate-main laydate-main-list-"+e}),c[e].appendChild(n),c[e].appendChild(i),u.push(t),d.push(i),f.push(a)}),lay(o).html((e=[],i=[],"datetime"===l.type&&e.push('<span lay-type="datetime" class="'+D+'">'+s.timeTips+"</span>"),!l.range&&"datetime"===l.type||e.push('<span class="'+m+'" title="'+s.preview+'"></span>'),lay.each(l.btns,function(e,t){var n=s.tools[t]||"btn";l.range&&"now"===t||(a&&"clear"===t&&(n="cn"===l.lang?"重置":"Reset"),i.push('<span lay-type="'+t+'" class="laydate-btns-'+t+'">'+n+"</span>"))}),e.push('<div class="laydate-footer-btns">'+i.join("")+"</div>"),e.join(""))),lay.each(c,function(e,t){n.appendChild(t)}),l.showBottom&&n.appendChild(o),/^#/.test(l.theme)&&(e=lay.elem("style"),o=["#{{id}} .layui-laydate-header{background-color:{{theme}};}","#{{id}} .layui-this{background-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,t.elemID).replace(/{{theme}}/g,l.theme),"styleSheet"in e?(e.setAttribute("type","text/css"),e.styleSheet.cssText=o):e.innerHTML=o,lay(n).addClass("laydate-theme-molv"),n.appendChild(e)),t.remove(g.thisElemDate),p.thisId=l.id,a?l.elem.append(n):(r.body.appendChild(n),t.position()),t.checkDate().calendar(null,0,"init"),t.changeEvent(),g.thisElemDate=t.elemID,"function"==typeof l.ready&&l.ready(lay.extend({},l.dateTime,{month:l.dateTime.month+1})),t.preview()},g.prototype.remove=function(e){var t=this,n=t.config,i=lay("#"+(e||t.elemID));return i[0]&&(i.hasClass(y)||t.checkDate(function(){i.remove(),delete p.thisId,"function"==typeof n.close&&n.close(t)})),t},g.prototype.position=function(){var e=this.config;return lay.position(this.bindElem||e.elem[0],this.elem,{position:e.position}),this},g.prototype.hint=function(e){var t=this,n=(t.config,lay.elem("div",{class:l}));t.elem&&(n.innerHTML=e||"",lay(t.elem).find("."+l).remove(),t.elem.appendChild(n),clearTimeout(t.hinTimer),t.hinTimer=setTimeout(function(){lay(t.elem).find("."+l).remove()},3e3))},g.prototype.getAsYM=function(e,t,n){return n?t--:t++,t<0&&(t=11,e--),11<t&&(t=0,e++),[e,t]},g.prototype.systemDate=function(e){var t=e||new Date;return{year:t.getFullYear(),month:t.getMonth(),date:t.getDate(),hours:e?e.getHours():0,minutes:e?e.getMinutes():0,seconds:e?e.getSeconds():0}},g.prototype.checkDate=function(e){var t,l,s=this,c=(new Date,s.config),n=s.lang(),i=c.dateTime=c.dateTime||s.systemDate(),a=s.bindElem||c.elem[0],o=(s.isInput(a),function(){if(s.rangeElem){var e=[s.rangeElem[0].val(),s.rangeElem[1].val()];if(e[0]&&e[1])return e.join(" "+s.rangeStr+" ")}return s.isInput(a)?a.value:"static"===c.position?"":lay(a).attr("lay-date")}()),u=function(e){e.year>h[1]&&(e.year=h[1],l=!0),11<e.month&&(e.month=11,l=!0),59<e.seconds&&(e.seconds=0,e.minutes++,l=!0),59<e.minutes&&(e.minutes=0,e.hours++,l=!0),23<e.hours&&(e.hours=0,l=!0),t=p.getEndDate(e.month+1,e.year),e.date>t&&(e.date=t,l=!0)},r=function(i,a,o){var r=["startTime","endTime"];a=(a.match(s.EXP_SPLIT)||[]).slice(1),o=o||0,c.range&&(s[r[o]]=s[r[o]]||{}),lay.each(s.format,function(e,t){var n=parseFloat(a[e]);a[e].length<t.length&&(l=!0),/yyyy|y/.test(t)?(n<h[0]&&(n=h[0],l=!0),i.year=n):/MM|M/.test(t)?(n<1&&(n=1,l=!0),i.month=n-1):/dd|d/.test(t)?(n<1&&(n=1,l=!0),i.date=n):/HH|H/.test(t)?(n<0&&(l=!(n=0)),23<n&&(n=23,l=!0),i.hours=n,c.range&&(s[r[o]].hours=n)):/mm|m/.test(t)?(n<0&&(l=!(n=0)),59<n&&(n=59,l=!0),i.minutes=n,c.range&&(s[r[o]].minutes=n)):/ss|s/.test(t)&&(n<0&&(l=!(n=0)),59<n&&(n=59,l=!0),i.seconds=n,c.range&&(s[r[o]].seconds=n))}),u(i)};if("limit"===e)return u(i),s;"string"==typeof(o=o||c.value)&&(o=o.replace(/\s+/g," ").replace(/^\s|\s$/g,""));var d,f=function(){var e,t,n;c.range&&(s.endDate=s.endDate||lay.extend({},c.dateTime,(e={},t=c.dateTime,n=s.getAsYM(t.year,t.month),"year"===c.type?e.year=t.year+1:"time"!==c.type&&(e.year=n[0],e.month=n[1]),"datetime"!==c.type&&"time"!==c.type||(e.hours=23,e.minutes=e.seconds=59),e)))};return f(),"string"==typeof o&&o?s.EXP_IF.test(o)?c.range?(o=o.split(" "+s.rangeStr+" "),lay.each([c.dateTime,s.endDate],function(e,t){r(t,o[e],e)})):r(i,o):(s.hint(n.formatError[0]+(c.range?c.format+" "+s.rangeStr+" "+c.format:c.format)+n.formatError[1]),l=!0):o&&"date"===layui.type(o)?c.dateTime=s.systemDate(o):(c.dateTime=s.systemDate(),delete s.startTime,delete s.endDate,f(),delete s.endTime),s.rangeElem&&(n=[s.rangeElem[0].val(),s.rangeElem[1].val()],d=[c.dateTime,s.endDate],lay.each(n,function(e,t){s.EXP_IF_ONE.test(t)&&r(d[e],t,e)})),u(i),c.range&&u(s.endDate),l&&o&&s.setValue(!c.range||s.endDate?s.parse():""),s.getDateTime(i)>s.getDateTime(c.max)?i=c.dateTime=lay.extend({},c.max):s.getDateTime(i)<s.getDateTime(c.min)&&(i=c.dateTime=lay.extend({},c.min)),c.range&&((s.getDateTime(s.endDate)<s.getDateTime(c.min)||s.getDateTime(s.endDate)>s.getDateTime(c.max))&&(s.endDate=lay.extend({},c.max)),s.startTime={hours:c.dateTime.hours,minutes:c.dateTime.minutes,seconds:c.dateTime.seconds},s.endTime={hours:s.endDate.hours,minutes:s.endDate.minutes,seconds:s.endDate.seconds}),e&&e(),s},g.prototype.mark=function(e,n){var i,t=this.config;return lay.each(t.mark,function(e,t){e=e.split("-");e[0]!=n[0]&&0!=e[0]||e[1]!=n[1]&&0!=e[1]||e[2]!=n[2]||(i=t||n[2])}),i&&e.html('<span class="laydate-day-mark">'+i+"</span>"),this},g.prototype.holidays=function(i,a){var e=this.config,o=["","work"];return"array"!==layui.type(e.holidays)||lay.each(e.holidays,function(n,e){lay.each(e,function(e,t){t===i.attr("lay-ymd")&&i.html('<span class="laydate-day-holidays"'+(o[n]?'type="'+o[n]+'"':"")+">"+a[2]+"</span>")})}),this},g.prototype.limit=function(e,t,n,a){var o=this,i=o.config,r={},n=(a?0:41)<n?o.endDate:i.dateTime,n=lay.extend({},n,t||{});return lay.each({now:n,min:i.min,max:i.max},function(e,n){var i;r[e]=o.newDate(lay.extend({year:n.year,month:n.month,date:n.date},(i={},lay.each(a,function(e,t){i[t]=n[t]}),i))).getTime()}),t=r.now<r.min||r.now>r.max,e&&e[t?"addClass":"removeClass"](w),t},g.prototype.thisDateTime=function(e){var t=this.config;return e?this.endDate:t.dateTime},g.prototype.calendar=function(e,t,n){var a,o,r,l=this,i=l.config,t=t?1:0,s=e||l.thisDateTime(t),c=new Date,u=l.lang(),d="date"!==i.type&&"datetime"!==i.type,f=lay(l.table[t]).find("td"),t=lay(l.elemHeader[t][2]).find("span");return s.year<h[0]&&(s.year=h[0],l.hint(u.invalidDate)),s.year>h[1]&&(s.year=h[1],l.hint(u.invalidDate)),l.firstDate||(l.firstDate=lay.extend({},s)),c.setFullYear(s.year,s.month,1),a=(c.getDay()+(7-i.weekStart))%7,o=p.getEndDate(s.month||12,s.year),r=p.getEndDate(s.month+1,s.year),lay.each(f,function(e,t){var n=[s.year,s.month],i=0;(t=lay(t)).removeAttr("class"),e<a?(i=o-a+e,t.addClass("laydate-day-prev"),n=l.getAsYM(s.year,s.month,"sub")):a<=e&&e<r+a?(i=e-a)+1===s.date&&t.addClass(k):(i=e-r-a,t.addClass("laydate-day-next"),n=l.getAsYM(s.year,s.month)),n[1]++,n[2]=i+1,t.attr("lay-ymd",n.join("-")).html(n[2]),l.mark(t,n).holidays(t,n).limit(t,{year:n[0],month:n[1]-1,date:n[2]},e)}),lay(t[0]).attr("lay-ym",s.year+"-"+(s.month+1)),lay(t[1]).attr("lay-ym",s.year+"-"+(s.month+1)),"cn"===i.lang?(lay(t[0]).attr("lay-type","year").html(s.year+" 年"),lay(t[1]).attr("lay-type","month").html(s.month+1+" 月")):(lay(t[0]).attr("lay-type","month").html(u.month[s.month]),lay(t[1]).attr("lay-type","year").html(s.year)),d&&(i.range?e&&(l.listYM=[[i.dateTime.year,i.dateTime.month+1],[l.endDate.year,l.endDate.month+1]],l.list(i.type,0).list(i.type,1),"time"===i.type?l.setBtnStatus("时间",lay.extend({},l.systemDate(),l.startTime),lay.extend({},l.systemDate(),l.endTime)):l.setBtnStatus(!0)):(l.listYM=[[s.year,s.month+1]],l.list(i.type,0))),i.range&&"init"===n&&!e&&l.calendar(l.endDate,1),i.range||l.limit(lay(l.footer).find(T),null,0,["hours","minutes","seconds"]),l.setBtnStatus(),l},g.prototype.list=function(t,i){var a,o,e,r,l=this,s=l.config,c=s.dateTime,u=l.lang(),n=s.range&&"date"!==s.type&&"datetime"!==s.type,d=lay.elem("ul",{class:C+" "+{year:"laydate-year-list",month:"laydate-month-list",time:"laydate-time-list"}[t]}),f=l.elemHeader[i],p=lay(f[2]).find("span"),h=l.elemCont[i||0],y=lay(h).find("."+C)[0],m="cn"===s.lang,g=m?"年":"",v=l.listYM[i]||{},b=["hours","minutes","seconds"],x=["startTime","endTime"][i];return v[0]<1&&(v[0]=1),"year"===t?(e=a=v[0]-7,a<1&&(e=a=1),lay.each(new Array(15),function(e){var t=lay.elem("li",{"lay-ym":a}),n={year:a,month:0,date:1};a==v[0]&&lay(t).addClass(k),t.innerHTML=a+g,d.appendChild(t),l.limit(lay(t),n,i),a++}),lay(p[m?0:1]).attr("lay-ym",a-8+"-"+v[1]).html(e+g+" - "+(a-1)+g)):"month"===t?(lay.each(new Array(12),function(e){var t=lay.elem("li",{"lay-ym":e}),n={year:v[0],month:e,date:1};e+1==v[1]&&lay(t).addClass(k),t.innerHTML=u.month[e]+(m?"月":""),d.appendChild(t),l.limit(lay(t),n,i)}),lay(p[m?0:1]).attr("lay-ym",v[0]+"-"+v[1]).html(v[0]+g)):"time"===t&&(o=function(){lay(d).find("ol").each(function(n,e){lay(e).find("li").each(function(e,t){l.limit(lay(t),[{hours:e},{hours:l[x].hours,minutes:e},{hours:l[x].hours,minutes:l[x].minutes,seconds:e}][n],i,[["hours"],["hours","minutes"],["hours","minutes","seconds"]][n])})}),s.range||l.limit(lay(l.footer).find(T),l[x],0,["hours","minutes","seconds"])},s.range?l[x]||(l[x]="startTime"===x?c:l.endDate):l[x]=c,lay.each([24,60,60],function(t,e){var n=lay.elem("li"),i=["<p>"+u.time[t]+"</p><ol>"];lay.each(new Array(e),function(e){i.push("<li"+(l[x][b[t]]===e?' class="'+k+'"':"")+">"+lay.digit(e,2)+"</li>")}),n.innerHTML=i.join("")+"</ol>",d.appendChild(n)}),o()),y&&h.removeChild(y),h.appendChild(d),"year"===t||"month"===t?(lay(l.elemMain[i]).addClass("laydate-ym-show"),lay(d).find("li").on("click",function(){var e=0|lay(this).attr("lay-ym");lay(this).hasClass(w)||(0===i?(c[t]=e,l.limit(lay(l.footer).find(T),null,0)):l.endDate[t]=e,"year"===s.type||"month"===s.type?(lay(d).find("."+k).removeClass(k),lay(this).addClass(k),"month"===s.type&&"year"===t&&(l.listYM[i][0]=e,n&&((i?l.endDate:c).year=e),l.list("month",i))):(l.checkDate("limit").calendar(null,i),l.closeList()),l.setBtnStatus(),s.range||("month"===s.type&&"month"===t||"year"===s.type&&"year"===t)&&l.setValue(l.parse()).remove().done(),l.done(null,"change"),lay(l.footer).find("."+D).removeClass(w))})):(e=lay.elem("span",{class:E}),r=function(){lay(d).find("ol").each(function(e){var n=this,t=lay(n).find("li");n.scrollTop=30*(l[x][b[e]]-2),n.scrollTop<=0&&t.each(function(e,t){if(!lay(this).hasClass(w))return n.scrollTop=30*(e-2),!0})})},p=lay(f[2]).find("."+E),r(),e.innerHTML=s.range?[u.startTime,u.endTime][i]:u.timeTips,lay(l.elemMain[i]).addClass("laydate-time-show"),p[0]&&p.remove(),f[2].appendChild(e),lay(d).find("ol").each(function(t){var n=this;lay(n).find("li").on("click",function(){var e=0|this.innerHTML;lay(this).hasClass(w)||(s.range?l[x][b[t]]=e:c[b[t]]=e,lay(n).find("."+k).removeClass(k),lay(this).addClass(k),o(),r(),!l.endDate&&"time"!==s.type||l.done(null,"change"),l.setBtnStatus())})})),l},g.prototype.listYM=[],g.prototype.closeList=function(){var n=this;n.config;lay.each(n.elemCont,function(e,t){lay(this).find("."+C).remove(),lay(n.elemMain[e]).removeClass("laydate-ym-show laydate-time-show")}),lay(n.elem).find("."+E).remove()},g.prototype.setBtnStatus=function(e,t,n){var i=this,a=i.config,o=i.lang(),r=lay(i.footer).find(T);a.range&&"time"!==a.type&&(t=t||a.dateTime,n=n||i.endDate,a=i.newDate(t).getTime()>i.newDate(n).getTime(),i.limit(null,t)||i.limit(null,n)?r.addClass(w):r[a?"addClass":"removeClass"](w),e&&a&&i.hint("string"==typeof e?o.timeout.replace(/\u65e5\u671f/g,e):o.timeout))},g.prototype.parse=function(e,t){var n=this,i=n.config,t=t||("end"==e?lay.extend({},n.endDate,n.endTime):i.range?lay.extend({},i.dateTime,n.startTime):i.dateTime),t=p.parse(t,n.format,1);return i.range&&e===undefined?t+" "+n.rangeStr+" "+n.parse("end"):t},g.prototype.newDate=function(e){return e=e||{},new Date(e.year||1,e.month||0,e.date||1,e.hours||0,e.minutes||0,e.seconds||0)},g.prototype.getDateTime=function(e){return this.newDate(e).getTime()},g.prototype.setValue=function(e){var t=this,n=t.config,i=t.bindElem||n.elem[0];return"static"===n.position||(e=e||"",t.isInput(i)?lay(i).val(e):(n=t.rangeElem)?("array"!==layui.type(e)&&(e=e.split(" "+t.rangeStr+" ")),n[0].val(e[0]||""),n[1].val(e[1]||"")):(0===lay(i).find("*").length&&lay(i).html(e),lay(i).attr("lay-date",e))),t},g.prototype.preview=function(){var e,t=this,n=t.config;n.isPreview&&(e=lay(t.elem).find("."+m),n=!n.range||t.endDate?t.parse():"",e.html(n).css({color:"#5FB878"}),setTimeout(function(){e.css({color:"#666"})},300))},g.prototype.done=function(e,t){var n=this,i=n.config,a=lay.extend({},lay.extend(i.dateTime,n.startTime)),o=lay.extend({},lay.extend(n.endDate,n.endTime));return lay.each([a,o],function(e,t){"month"in t&&lay.extend(t,{month:t.month+1})}),n.preview(),e=e||[n.parse(),a,o],"function"==typeof i[t||"done"]&&i[t||"done"].apply(i,e),n},g.prototype.choose=function(e,n){var i=this,a=i.config,o=i.thisDateTime(n),t=(lay(i.elem).find("td"),{year:0|(t=e.attr("lay-ymd").split("-"))[0],month:(0|t[1])-1,date:0|t[2]});e.hasClass(w)||(lay.extend(o,t),a.range?(lay.each(["startTime","endTime"],function(e,t){i[t]=i[t]||{hours:e?23:0,minutes:e?59:0,seconds:e?59:0},n===e&&(i.getDateTime(lay.extend({},o,i[t]))<i.getDateTime(a.min)?(i[t]={hours:a.min.hours,minutes:a.min.minutes,seconds:a.min.seconds},lay.extend(o,i[t])):i.getDateTime(lay.extend({},o,i[t]))>i.getDateTime(a.max)&&(i[t]={hours:a.max.hours,minutes:a.max.minutes,seconds:a.max.seconds},lay.extend(o,i[t])))}),i.calendar(null,n).done(null,"change")):"static"===a.position?i.calendar().done().done(null,"change"):"date"===a.type?i.setValue(i.parse()).remove().done():"datetime"===a.type&&i.calendar().done(null,"change"))},g.prototype.tool=function(e,t){var n=this,i=n.config,a=n.lang(),o=i.dateTime,r="static"===i.position,l={datetime:function(){lay(e).hasClass(w)||(n.list("time",0),i.range&&n.list("time",1),lay(e).attr("lay-type","date").html(n.lang().dateTips))},date:function(){n.closeList(),lay(e).attr("lay-type","datetime").html(n.lang().timeTips)},clear:function(){r&&(lay.extend(o,n.firstDate),n.calendar()),i.range&&(delete i.dateTime,delete n.endDate,delete n.startTime,delete n.endTime),n.setValue("").remove(),n.done(["",{},{}])},now:function(){var e=new Date;lay.extend(o,n.systemDate(),{hours:e.getHours(),minutes:e.getMinutes(),seconds:e.getSeconds()}),n.setValue(n.parse()).remove(),r&&n.calendar(),n.done()},confirm:function(){if(i.range){if(lay(e).hasClass(w))return n.hint("time"===i.type?a.timeout.replace(/\u65e5\u671f/g,"时间"):a.timeout)}else if(lay(e).hasClass(w))return n.hint(a.invalidDate);n.setValue(n.parse()).remove(),n.done()}};l[t]&&l[t]()},g.prototype.change=function(i){var a=this,o=a.config,r=a.thisDateTime(i),l=o.range&&("year"===o.type||"month"===o.type),s=a.elemCont[i||0],c=a.listYM[i],e=function(e){var t=lay(s).find(".laydate-year-list")[0],n=lay(s).find(".laydate-month-list")[0];return t&&(c[0]=e?c[0]-15:c[0]+15,a.list("year",i)),n&&(e?c[0]--:c[0]++,a.list("month",i)),(t||n)&&(lay.extend(r,{year:c[0]}),l&&(r.year=c[0]),o.range||a.done(null,"change"),o.range||a.limit(lay(a.footer).find(T),{year:c[0]})),a.setBtnStatus(),t||n};return{prevYear:function(){e("sub")||(r.year--,a.checkDate("limit").calendar(null,i),a.done(null,"change"))},prevMonth:function(){var e=a.getAsYM(r.year,r.month,"sub");lay.extend(r,{year:e[0],month:e[1]}),a.checkDate("limit").calendar(null,i),a.done(null,"change")},nextMonth:function(){var e=a.getAsYM(r.year,r.month);lay.extend(r,{year:e[0],month:e[1]}),a.checkDate("limit").calendar(null,i),a.done(null,"change")},nextYear:function(){e()||(r.year++,a.checkDate("limit").calendar(null,i),a.done(null,"change"))}}},g.prototype.changeEvent=function(){var a=this;a.config;lay(a.elem).on("click",function(e){lay.stope(e)}).on("mousedown",function(e){lay.stope(e)}),lay.each(a.elemHeader,function(i,e){lay(e[0]).on("click",function(e){a.change(i).prevYear()}),lay(e[1]).on("click",function(e){a.change(i).prevMonth()}),lay(e[2]).find("span").on("click",function(e){var t=lay(this),n=t.attr("lay-ym"),t=t.attr("lay-type");n&&(n=n.split("-"),a.listYM[i]=[0|n[0],0|n[1]],a.list(t,i),lay(a.footer).find("."+D).addClass(w))}),lay(e[3]).on("click",function(e){a.change(i).nextMonth()}),lay(e[4]).on("click",function(e){a.change(i).nextYear()})}),lay.each(a.table,function(e,t){lay(t).find("td").on("click",function(){a.choose(lay(this),e)})}),lay(a.footer).find("span").on("click",function(){var e=lay(this).attr("lay-type");a.tool(this,e)})},g.prototype.isInput=function(e){return/input|textarea/.test(e.tagName.toLocaleLowerCase())||/INPUT|TEXTAREA/.test(e.tagName)},g.prototype.events=function(){var n=this,i=n.config,e=function(e,t){e.on(i.trigger,function(){p.thisId!==i.id&&(t&&(n.bindElem=this),n.render())})};i.elem[0]&&!i.elem[0].eventHandler&&(e(i.elem,"bind"),e(i.eventElem),i.elem[0].eventHandler=!0)},s.that={},s.getThis=function(e){var t=s.that[e];return!t&&i&&layui.hint().error(e?n+" instance with ID '"+e+"' not found":"ID argument required"),t},o.run=function(i){i(r).on("mousedown",function(e){var t,n;!p.thisId||(t=s.getThis(p.thisId))&&(n=t.config,e.target!==n.elem[0]&&e.target!==n.eventElem[0]&&e.target!==i(n.closeStop)[0]&&t.remove())}).on("keydown",function(e){var t;!p.thisId||(t=s.getThis(p.thisId))&&"static"!==t.config.position&&13===e.keyCode&&i("#"+t.elemID)[0]&&t.elemID===g.thisElemDate&&(e.preventDefault(),i(t.footer).find(T)[0].click())}),i(a).on("resize",function(){if(p.thisId){var e=s.getThis(p.thisId);if(e)return!(!e.elem||!i(".layui-laydate")[0])&&void e.position()}})},p.render=function(e){e=new g(e);return s.call(e)},p.parse=function(n,i,a){return n=n||{},i=((i="string"==typeof i?s.formatArr(i):i)||[]).concat(),lay.each(i,function(e,t){/yyyy|y/.test(t)?i[e]=lay.digit(n.year,t.length):/MM|M/.test(t)?i[e]=lay.digit(n.month+(a||0),t.length):/dd|d/.test(t)?i[e]=lay.digit(n.date,t.length):/HH|H/.test(t)?i[e]=lay.digit(n.hours,t.length):/mm|m/.test(t)?i[e]=lay.digit(n.minutes,t.length):/ss|s/.test(t)&&(i[e]=lay.digit(n.seconds,t.length))}),i.join("")},p.getEndDate=function(e,t){var n=new Date;return n.setFullYear(t||n.getFullYear(),e||n.getMonth()+1,1),new Date(n.getTime()-864e5).getDate()},p.close=function(e){e=s.getThis(e||p.thisId);if(e)return e.remove()},i?(p.ready(),layui.define("lay",function(e){p.path=layui.cache.dir,o.run(lay),e(n,p)})):"function"==typeof define&&define.amd?define(function(){return o.run(lay),p}):(p.ready(),o.run(a.lay),a.laydate=p)}(window,window.document);!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)}("undefined"!=typeof window?window:this,function(k,q){var d=[],h=k.document,u=d.slice,_=d.concat,P=d.push,B=d.indexOf,O={},R=O.toString,y=O.hasOwnProperty,m={},e="1.12.4",w=function(e,t){return new w.fn.init(e,t)},z=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,W=/^-ms-/,$=/-([\da-z])/gi,Y=function(e,t){return t.toUpperCase()};function X(e){var t=!!e&&"length"in e&&e.length,n=w.type(e);return"function"!==n&&!w.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}w.fn=w.prototype={jquery:e,constructor:w,selector:"",length:0,toArray:function(){return u.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:u.call(this)},pushStack:function(e){e=w.merge(this.constructor(),e);return e.prevObject=this,e.context=this.context,e},each:function(e){return w.each(this,e)},map:function(n){return this.pushStack(w.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(u.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:P,sort:d.sort,splice:d.splice},w.extend=w.fn.extend=function(){var e,t,n,i,a,o=arguments[0]||{},r=1,l=arguments.length,s=!1;for("boolean"==typeof o&&(s=o,o=arguments[r]||{},r++),"object"==typeof o||w.isFunction(o)||(o={}),r===l&&(o=this,r--);r<l;r++)if(null!=(i=arguments[r]))for(n in i)a=o[n],o!==(t=i[n])&&(s&&t&&(w.isPlainObject(t)||(e=w.isArray(t)))?(a=e?(e=!1,a&&w.isArray(a)?a:[]):a&&w.isPlainObject(a)?a:{},o[n]=w.extend(s,a,t)):t!==undefined&&(o[n]=t));return o},w.extend({expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===w.type(e)},isArray:Array.isArray||function(e){return"array"===w.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){var t=e&&e.toString();return!w.isArray(e)&&0<=t-parseFloat(t)+1},isEmptyObject:function(e){for(var t in e)return!1;return!0},isPlainObject:function(e){if(!e||"object"!==w.type(e)||e.nodeType||w.isWindow(e))return!1;try{if(e.constructor&&!y.call(e,"constructor")&&!y.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(!m.ownFirst)for(var t in e)return y.call(e,t);for(t in e);return t===undefined||y.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?O[R.call(e)]||"object":typeof e},globalEval:function(e){e&&w.trim(e)&&(k.execScript||function(e){k.eval.call(k,e)})(e)},camelCase:function(e){return e.replace(W,"ms-").replace($,Y)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,i=0;if(X(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(z,"")},makeArray:function(e,t){t=t||[];return null!=e&&(X(Object(e))?w.merge(t,"string"==typeof e?[e]:e):P.call(t,e)),t},inArray:function(e,t,n){var i;if(t){if(B)return B.call(t,e,n);for(i=t.length,n=n?n<0?Math.max(0,i+n):n:0;n<i;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,a=e.length;i<n;)e[a++]=t[i++];if(n!=n)for(;t[i]!==undefined;)e[a++]=t[i++];return e.length=a,e},grep:function(e,t,n){for(var i=[],a=0,o=e.length,r=!n;a<o;a++)!t(e[a],a)!=r&&i.push(e[a]);return i},map:function(e,t,n){var i,a,o=0,r=[];if(X(e))for(i=e.length;o<i;o++)null!=(a=t(e[o],o,n))&&r.push(a);else for(o in e)null!=(a=t(e[o],o,n))&&r.push(a);return _.apply([],r)},guid:1,proxy:function(e,t){var n,i;return"string"==typeof t&&(i=e[t],t=e,e=i),w.isFunction(e)?(n=u.call(arguments,2),(i=function(){return e.apply(t||this,n.concat(u.call(arguments)))}).guid=e.guid=e.guid||w.guid++,i):undefined},now:function(){return+new Date},support:m}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=d[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){O["[object "+t+"]"]=t.toLowerCase()});var e=function(q){var e,h,x,o,_,y,P,B,k,s,c,w,C,t,T,m,i,a,g,E="sizzle"+ +new Date,v=q.document,D=0,O=0,R=ce(),z=ce(),L=ce(),W=function(e,t){return e===t&&(c=!0),0},$={}.hasOwnProperty,n=[],Y=n.pop,X=n.push,S=n.push,V=n.slice,A=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},U="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",r="[\\x20\\t\\r\\n\\f]",l="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",K="\\["+r+"*("+l+")(?:"+r+"*([*^$|!~]?=)"+r+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+l+"))|)"+r+"*\\]",G=":("+l+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+K+")*)|.*)\\)|)",J=new RegExp(r+"+","g"),N=new RegExp("^"+r+"+|((?:^|[^\\\\])(?:\\\\.)*)"+r+"+$","g"),Q=new RegExp("^"+r+"*,"+r+"*"),Z=new RegExp("^"+r+"*([>+~]|"+r+")"+r+"*"),ee=new RegExp("="+r+"*([^\\]'\"]*?)"+r+"*\\]","g"),te=new RegExp(G),ne=new RegExp("^"+l+"$"),d={ID:new RegExp("^#("+l+")"),CLASS:new RegExp("^\\.("+l+")"),TAG:new RegExp("^("+l+"|[*])"),ATTR:new RegExp("^"+K),PSEUDO:new RegExp("^"+G),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+r+"*(even|odd|(([+-]|)(\\d*)n|)"+r+"*(?:([+-]|)"+r+"*(\\d+)|))"+r+"*\\)|)","i"),bool:new RegExp("^(?:"+U+")$","i"),needsContext:new RegExp("^"+r+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+r+"*((?:-\\d)?\\d*)"+r+"*\\)|)(?=[^-]|$)","i")},ie=/^(?:input|select|textarea|button)$/i,ae=/^h\d$/i,u=/^[^{]+\{\s*\[native \w/,oe=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,re=/[+~]/,le=/'|\\/g,f=new RegExp("\\\\([\\da-f]{1,6}"+r+"?|("+r+")|.)","ig"),p=function(e,t,n){var i="0x"+t-65536;return i!=i||n?t:i<0?String.fromCharCode(65536+i):String.fromCharCode(i>>10|55296,1023&i|56320)},se=function(){w()};try{S.apply(n=V.call(v.childNodes),v.childNodes),n[v.childNodes.length].nodeType}catch(e){S={apply:n.length?function(e,t){X.apply(e,V.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function j(e,t,n,i){var a,o,r,l,s,c,u,d,f=t&&t.ownerDocument,p=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==p&&9!==p&&11!==p)return n;if(!i&&((t?t.ownerDocument||t:v)!==C&&w(t),t=t||C,T)){if(11!==p&&(c=oe.exec(e)))if(a=c[1]){if(9===p){if(!(r=t.getElementById(a)))return n;if(r.id===a)return n.push(r),n}else if(f&&(r=f.getElementById(a))&&g(t,r)&&r.id===a)return n.push(r),n}else{if(c[2])return S.apply(n,t.getElementsByTagName(e)),n;if((a=c[3])&&h.getElementsByClassName&&t.getElementsByClassName)return S.apply(n,t.getElementsByClassName(a)),n}if(h.qsa&&!L[e+" "]&&(!m||!m.test(e))){if(1!==p)f=t,d=e;else if("object"!==t.nodeName.toLowerCase()){for((l=t.getAttribute("id"))?l=l.replace(le,"\\$&"):t.setAttribute("id",l=E),o=(u=y(e)).length,s=ne.test(l)?"#"+l:"[id='"+l+"']";o--;)u[o]=s+" "+I(u[o]);d=u.join(","),f=re.test(e)&&fe(t.parentNode)||t}if(d)try{return S.apply(n,f.querySelectorAll(d)),n}catch(e){}finally{l===E&&t.removeAttribute("id")}}}return B(e.replace(N,"$1"),t,n,i)}function ce(){var n=[];function i(e,t){return n.push(e+" ")>x.cacheLength&&delete i[n.shift()],i[e+" "]=t}return i}function M(e){return e[E]=!0,e}function b(e){var t=C.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function ue(e,t){for(var n=e.split("|"),i=n.length;i--;)x.attrHandle[n[i]]=t}function de(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function H(r){return M(function(o){return o=+o,M(function(e,t){for(var n,i=r([],e.length,o),a=i.length;a--;)e[n=i[a]]&&(e[n]=!(t[n]=e[n]))})})}function fe(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}for(e in h=j.support={},_=j.isXML=function(e){e=e&&(e.ownerDocument||e).documentElement;return!!e&&"HTML"!==e.nodeName},w=j.setDocument=function(e){var e=e?e.ownerDocument||e:v;return e!==C&&9===e.nodeType&&e.documentElement&&(t=(C=e).documentElement,T=!_(C),(e=C.defaultView)&&e.top!==e&&(e.addEventListener?e.addEventListener("unload",se,!1):e.attachEvent&&e.attachEvent("onunload",se)),h.attributes=b(function(e){return e.className="i",!e.getAttribute("className")}),h.getElementsByTagName=b(function(e){return e.appendChild(C.createComment("")),!e.getElementsByTagName("*").length}),h.getElementsByClassName=u.test(C.getElementsByClassName),h.getById=b(function(e){return t.appendChild(e).id=E,!C.getElementsByName||!C.getElementsByName(E).length}),h.getById?(x.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&T)return(e=t.getElementById(e))?[e]:[]},x.filter.ID=function(e){var t=e.replace(f,p);return function(e){return e.getAttribute("id")===t}}):(delete x.find.ID,x.filter.ID=function(e){var t=e.replace(f,p);return function(e){e="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}}),x.find.TAG=h.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):h.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],a=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[a++];)1===n.nodeType&&i.push(n);return i},x.find.CLASS=h.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&T)return t.getElementsByClassName(e)},i=[],m=[],(h.qsa=u.test(C.querySelectorAll))&&(b(function(e){t.appendChild(e).innerHTML="<a id='"+E+"'></a><select id='"+E+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+r+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+r+"*(?:value|"+U+")"),e.querySelectorAll("[id~="+E+"-]").length||m.push("~="),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+E+"+*").length||m.push(".#.+[+~]")}),b(function(e){var t=C.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+r+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(h.matchesSelector=u.test(a=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.msMatchesSelector))&&b(function(e){h.disconnectedMatch=a.call(e,"div"),a.call(e,"[s!='']:x"),i.push("!=",G)}),m=m.length&&new RegExp(m.join("|")),i=i.length&&new RegExp(i.join("|")),e=u.test(t.compareDocumentPosition),g=e||u.test(t.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},W=e?function(e,t){if(e===t)return c=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!h.sortDetached&&t.compareDocumentPosition(e)===n?e===C||e.ownerDocument===v&&g(v,e)?-1:t===C||t.ownerDocument===v&&g(v,t)?1:s?A(s,e)-A(s,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,i=0,a=e.parentNode,o=t.parentNode,r=[e],l=[t];if(!a||!o)return e===C?-1:t===C?1:a?-1:o?1:s?A(s,e)-A(s,t):0;if(a===o)return de(e,t);for(n=e;n=n.parentNode;)r.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;r[i]===l[i];)i++;return i?de(r[i],l[i]):r[i]===v?-1:l[i]===v?1:0}),C},j.matches=function(e,t){return j(e,null,null,t)},j.matchesSelector=function(e,t){if((e.ownerDocument||e)!==C&&w(e),t=t.replace(ee,"='$1']"),h.matchesSelector&&T&&!L[t+" "]&&(!i||!i.test(t))&&(!m||!m.test(t)))try{var n=a.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return 0<j(t,C,null,[e]).length},j.contains=function(e,t){return(e.ownerDocument||e)!==C&&w(e),g(e,t)},j.attr=function(e,t){(e.ownerDocument||e)!==C&&w(e);var n=x.attrHandle[t.toLowerCase()],n=n&&$.call(x.attrHandle,t.toLowerCase())?n(e,t,!T):undefined;return n!==undefined?n:h.attributes||!T?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},j.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},j.uniqueSort=function(e){var t,n=[],i=0,a=0;if(c=!h.detectDuplicates,s=!h.sortStable&&e.slice(0),e.sort(W),c){for(;t=e[a++];)t===e[a]&&(i=n.push(a));for(;i--;)e.splice(n[i],1)}return s=null,e},o=j.getText=function(e){var t,n="",i=0,a=e.nodeType;if(a){if(1===a||9===a||11===a){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===a||4===a)return e.nodeValue}else for(;t=e[i++];)n+=o(t);return n},(x=j.selectors={cacheLength:50,createPseudo:M,match:d,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(f,p),e[3]=(e[3]||e[4]||e[5]||"").replace(f,p),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||j.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&j.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return d.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&te.test(n)&&(t=y(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(f,p).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=R[e+" "];return t||(t=new RegExp("(^|"+r+")"+e+"("+r+"|$)"))&&R(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(e){e=j.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===i:"!="===n?e!==i:"^="===n?i&&0===e.indexOf(i):"*="===n?i&&-1<e.indexOf(i):"$="===n?i&&e.slice(-i.length)===i:"~="===n?-1<(" "+e.replace(J," ")+" ").indexOf(i):"|="===n&&(e===i||e.slice(0,i.length+1)===i+"-"))}},CHILD:function(h,e,t,y,m){var g="nth"!==h.slice(0,3),v="last"!==h.slice(-4),b="of-type"===e;return 1===y&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,a,o,r,l,s,c=g!=v?"nextSibling":"previousSibling",u=e.parentNode,d=b&&e.nodeName.toLowerCase(),f=!n&&!b,p=!1;if(u){if(g){for(;c;){for(r=e;r=r[c];)if(b?r.nodeName.toLowerCase()===d:1===r.nodeType)return!1;s=c="only"===h&&!s&&"nextSibling"}return!0}if(s=[v?u.firstChild:u.lastChild],v&&f){for(p=(l=(i=(a=(o=(r=u)[E]||(r[E]={}))[r.uniqueID]||(o[r.uniqueID]={}))[h]||[])[0]===D&&i[1])&&i[2],r=l&&u.childNodes[l];r=++l&&r&&r[c]||(p=l=0)||s.pop();)if(1===r.nodeType&&++p&&r===e){a[h]=[D,l,p];break}}else if(!1===(p=f?l=(i=(a=(o=(r=e)[E]||(r[E]={}))[r.uniqueID]||(o[r.uniqueID]={}))[h]||[])[0]===D&&i[1]:p))for(;(r=++l&&r&&r[c]||(p=l=0)||s.pop())&&((b?r.nodeName.toLowerCase()!==d:1!==r.nodeType)||!++p||(f&&((a=(o=r[E]||(r[E]={}))[r.uniqueID]||(o[r.uniqueID]={}))[h]=[D,p]),r!==e)););return(p-=m)===y||p%y==0&&0<=p/y}}},PSEUDO:function(e,o){var t,r=x.pseudos[e]||x.setFilters[e.toLowerCase()]||j.error("unsupported pseudo: "+e);return r[E]?r(o):1<r.length?(t=[e,e,"",o],x.setFilters.hasOwnProperty(e.toLowerCase())?M(function(e,t){for(var n,i=r(e,o),a=i.length;a--;)e[n=A(e,i[a])]=!(t[n]=i[a])}):function(e){return r(e,0,t)}):r}},pseudos:{not:M(function(e){var i=[],a=[],l=P(e.replace(N,"$1"));return l[E]?M(function(e,t,n,i){for(var a,o=l(e,null,i,[]),r=e.length;r--;)(a=o[r])&&(e[r]=!(t[r]=a))}):function(e,t,n){return i[0]=e,l(i,null,n,a),i[0]=null,!a.pop()}}),has:M(function(t){return function(e){return 0<j(t,e).length}}),contains:M(function(t){return t=t.replace(f,p),function(e){return-1<(e.textContent||e.innerText||o(e)).indexOf(t)}}),lang:M(function(n){return ne.test(n||"")||j.error("unsupported lang: "+n),n=n.replace(f,p).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=q.location&&q.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===t},focus:function(e){return e===C.activeElement&&(!C.hasFocus||C.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!x.pseudos.empty(e)},header:function(e){return ae.test(e.nodeName)},input:function(e){return ie.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:H(function(){return[0]}),last:H(function(e,t){return[t-1]}),eq:H(function(e,t,n){return[n<0?n+t:n]}),even:H(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:H(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:H(function(e,t,n){for(var i=n<0?n+t:n;0<=--i;)e.push(i);return e}),gt:H(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=x.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})x.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function pe(){}function I(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function he(r,e,t){var l=e.dir,s=t&&"parentNode"===l,c=O++;return e.first?function(e,t,n){for(;e=e[l];)if(1===e.nodeType||s)return r(e,t,n)}:function(e,t,n){var i,a,o=[D,c];if(n){for(;e=e[l];)if((1===e.nodeType||s)&&r(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||s){if((i=(a=(a=e[E]||(e[E]={}))[e.uniqueID]||(a[e.uniqueID]={}))[l])&&i[0]===D&&i[1]===c)return o[2]=i[2];if((a[l]=o)[2]=r(e,t,n))return!0}}}function ye(a){return 1<a.length?function(e,t,n){for(var i=a.length;i--;)if(!a[i](e,t,n))return!1;return!0}:a[0]}function F(e,t,n,i,a){for(var o,r=[],l=0,s=e.length,c=null!=t;l<s;l++)!(o=e[l])||n&&!n(o,i,a)||(r.push(o),c&&t.push(l));return r}function me(p,h,y,m,g,e){return m&&!m[E]&&(m=me(m)),g&&!g[E]&&(g=me(g,e)),M(function(e,t,n,i){var a,o,r,l=[],s=[],c=t.length,u=e||function(e,t,n){for(var i=0,a=t.length;i<a;i++)j(e,t[i],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!p||!e&&h?u:F(u,l,p,n,i),f=y?g||(e?p:c||m)?[]:t:d;if(y&&y(d,f,n,i),m)for(a=F(f,s),m(a,[],n,i),o=a.length;o--;)(r=a[o])&&(f[s[o]]=!(d[s[o]]=r));if(e){if(g||p){if(g){for(a=[],o=f.length;o--;)(r=f[o])&&a.push(d[o]=r);g(null,f=[],a,i)}for(o=f.length;o--;)(r=f[o])&&-1<(a=g?A(e,r):l[o])&&(e[a]=!(t[a]=r))}}else f=F(f===t?f.splice(c,f.length):f),g?g(null,t,f,i):S.apply(t,f)})}return pe.prototype=x.filters=x.pseudos,x.setFilters=new pe,y=j.tokenize=function(e,t){var n,i,a,o,r,l,s,c=z[e+" "];if(c)return t?0:c.slice(0);for(r=e,l=[],s=x.preFilter;r;){for(o in n&&!(i=Q.exec(r))||(i&&(r=r.slice(i[0].length)||r),l.push(a=[])),n=!1,(i=Z.exec(r))&&(n=i.shift(),a.push({value:n,type:i[0].replace(N," ")}),r=r.slice(n.length)),x.filter)!(i=d[o].exec(r))||s[o]&&!(i=s[o](i))||(n=i.shift(),a.push({value:n,type:o,matches:i}),r=r.slice(n.length));if(!n)break}return t?r.length:r?j.error(e):z(e,l).slice(0)},P=j.compile=function(e,t){var n,m,g,v,b,i,a=[],o=[],r=L[e+" "];if(!r){for(n=(t=t||y(e)).length;n--;)((r=function e(t){for(var i,n,a,o=t.length,r=x.relative[t[0].type],l=r||x.relative[" "],s=r?1:0,c=he(function(e){return e===i},l,!0),u=he(function(e){return-1<A(i,e)},l,!0),d=[function(e,t,n){return e=!r&&(n||t!==k)||((i=t).nodeType?c:u)(e,t,n),i=null,e}];s<o;s++)if(n=x.relative[t[s].type])d=[he(ye(d),n)];else{if((n=x.filter[t[s].type].apply(null,t[s].matches))[E]){for(a=++s;a<o&&!x.relative[t[a].type];a++);return me(1<s&&ye(d),1<s&&I(t.slice(0,s-1).concat({value:" "===t[s-2].type?"*":""})).replace(N,"$1"),n,s<a&&e(t.slice(s,a)),a<o&&e(t=t.slice(a)),a<o&&I(t))}d.push(n)}return ye(d)}(t[n]))[E]?a:o).push(r);(r=L(e,(m=o,v=0<(g=a).length,b=0<m.length,i=function(e,t,n,i,a){var o,r,l,s=0,c="0",u=e&&[],d=[],f=k,p=e||b&&x.find.TAG("*",a),h=D+=null==f?1:Math.random()||.1,y=p.length;for(a&&(k=t===C||t||a);c!==y&&null!=(o=p[c]);c++){if(b&&o){for(r=0,t||o.ownerDocument===C||(w(o),n=!T);l=m[r++];)if(l(o,t||C,n)){i.push(o);break}a&&(D=h)}v&&((o=!l&&o)&&s--,e&&u.push(o))}if(s+=c,v&&c!==s){for(r=0;l=g[r++];)l(u,d,t,n);if(e){if(0<s)for(;c--;)u[c]||d[c]||(d[c]=Y.call(i));d=F(d)}S.apply(i,d),a&&!e&&0<d.length&&1<s+g.length&&j.uniqueSort(i)}return a&&(D=h,k=f),u},v?M(i):i))).selector=e}return r},B=j.select=function(e,t,n,i){var a,o,r,l,s,c="function"==typeof e&&e,u=!i&&y(e=c.selector||e);if(n=n||[],1===u.length){if(2<(o=u[0]=u[0].slice(0)).length&&"ID"===(r=o[0]).type&&h.getById&&9===t.nodeType&&T&&x.relative[o[1].type]){if(!(t=(x.find.ID(r.matches[0].replace(f,p),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(a=d.needsContext.test(e)?0:o.length;a--&&(r=o[a],!x.relative[l=r.type]);)if((s=x.find[l])&&(i=s(r.matches[0].replace(f,p),re.test(o[0].type)&&fe(t.parentNode)||t))){if(o.splice(a,1),e=i.length&&I(o))break;return S.apply(n,i),n}}return(c||P(e,u))(i,t,!T,n,!t||re.test(e)&&fe(t.parentNode)||t),n},h.sortStable=E.split("").sort(W).join("")===E,h.detectDuplicates=!!c,w(),h.sortDetached=b(function(e){return 1&e.compareDocumentPosition(C.createElement("div"))}),b(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||ue("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),h.attributes&&b(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||ue("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),b(function(e){return null==e.getAttribute("disabled")})||ue(U,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),j}(k),i=(w.find=e,w.expr=e.selectors,w.expr[":"]=w.expr.pseudos,w.uniqueSort=w.unique=e.uniqueSort,w.text=e.getText,w.isXMLDoc=e.isXML,w.contains=e.contains,function(e,t,n){for(var i=[],a=n!==undefined;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(a&&w(e).is(n))break;i.push(e)}return i}),V=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},U=w.expr.match.needsContext,K=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,G=/^.[^:#\[\.,]*$/;function J(e,n,i){if(w.isFunction(n))return w.grep(e,function(e,t){return!!n.call(e,t,e)!==i});if(n.nodeType)return w.grep(e,function(e){return e===n!==i});if("string"==typeof n){if(G.test(n))return w.filter(n,e,i);n=w.filter(n,e)}return w.grep(e,function(e){return-1<w.inArray(e,n)!==i})}w.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?w.find.matchesSelector(i,e)?[i]:[]:w.find.matches(e,w.grep(t,function(e){return 1===e.nodeType}))},w.fn.extend({find:function(e){var t,n=[],i=this,a=i.length;if("string"!=typeof e)return this.pushStack(w(e).filter(function(){for(t=0;t<a;t++)if(w.contains(i[t],this))return!0}));for(t=0;t<a;t++)w.find(e,i[t],n);return(n=this.pushStack(1<a?w.unique(n):n)).selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(J(this,e||[],!1))},not:function(e){return this.pushStack(J(this,e||[],!0))},is:function(e){return!!J(this,"string"==typeof e&&U.test(e)?w(e):e||[],!1).length}});var Q,Z=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,ee=((w.fn.init=function(e,t,n){if(!e)return this;if(n=n||Q,"string"!=typeof e)return e.nodeType?(this.context=this[0]=e,this.length=1,this):w.isFunction(e)?"undefined"!=typeof n.ready?n.ready(e):e(w):(e.selector!==undefined&&(this.selector=e.selector,this.context=e.context),w.makeArray(e,this));if(!(i="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&3<=e.length?[null,e,null]:Z.exec(e))||!i[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(i[1]){if(t=t instanceof w?t[0]:t,w.merge(this,w.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:h,!0)),K.test(i[1])&&w.isPlainObject(t))for(var i in t)w.isFunction(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}if((n=h.getElementById(i[2]))&&n.parentNode){if(n.id!==i[2])return Q.find(e);this.length=1,this[0]=n}return this.context=h,this.selector=e,this}).prototype=w.fn,Q=w(h),/^(?:parents|prev(?:Until|All))/),te={children:!0,contents:!0,next:!0,prev:!0};function ne(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}w.fn.extend({has:function(e){var t,n=w(e,this),i=n.length;return this.filter(function(){for(t=0;t<i;t++)if(w.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,a=this.length,o=[],r=U.test(e)||"string"!=typeof e?w(e,t||this.context):0;i<a;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(r?-1<r.index(n):1===n.nodeType&&w.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?w.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?w.inArray(this[0],w(e)):w.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),w.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return i(e,"parentNode")},parentsUntil:function(e,t,n){return i(e,"parentNode",n)},next:function(e){return ne(e,"nextSibling")},prev:function(e){return ne(e,"previousSibling")},nextAll:function(e){return i(e,"nextSibling")},prevAll:function(e){return i(e,"previousSibling")},nextUntil:function(e,t,n){return i(e,"nextSibling",n)},prevUntil:function(e,t,n){return i(e,"previousSibling",n)},siblings:function(e){return V((e.parentNode||{}).firstChild,e)},children:function(e){return V(e.firstChild)},contents:function(e){return w.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:w.merge([],e.childNodes)}},function(i,a){w.fn[i]=function(e,t){var n=w.map(this,a,e);return(t="Until"!==i.slice(-5)?e:t)&&"string"==typeof t&&(n=w.filter(t,n)),1<this.length&&(te[i]||(n=w.uniqueSort(n)),ee.test(i)&&(n=n.reverse())),this.pushStack(n)}});var ie,ae,C=/\S+/g;function oe(){h.addEventListener?(h.removeEventListener("DOMContentLoaded",t),k.removeEventListener("load",t)):(h.detachEvent("onreadystatechange",t),k.detachEvent("onload",t))}function t(){!h.addEventListener&&"load"!==k.event.type&&"complete"!==h.readyState||(oe(),w.ready())}for(ae in w.Callbacks=function(i){var e,n;i="string"==typeof i?(e=i,n={},w.each(e.match(C)||[],function(e,t){n[t]=!0}),n):w.extend({},i);var a,t,o,r,l=[],s=[],c=-1,u=function(){for(r=i.once,o=a=!0;s.length;c=-1)for(t=s.shift();++c<l.length;)!1===l[c].apply(t[0],t[1])&&i.stopOnFalse&&(c=l.length,t=!1);i.memory||(t=!1),a=!1,r&&(l=t?[]:"")},d={add:function(){return l&&(t&&!a&&(c=l.length-1,s.push(t)),function n(e){w.each(e,function(e,t){w.isFunction(t)?i.unique&&d.has(t)||l.push(t):t&&t.length&&"string"!==w.type(t)&&n(t)})}(arguments),t&&!a&&u()),this},remove:function(){return w.each(arguments,function(e,t){for(var n;-1<(n=w.inArray(t,l,n));)l.splice(n,1),n<=c&&c--}),this},has:function(e){return e?-1<w.inArray(e,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return r=s=[],l=t="",this},disabled:function(){return!l},lock:function(){return r=!0,t||d.disable(),this},locked:function(){return!!r},fireWith:function(e,t){return r||(t=[e,(t=t||[]).slice?t.slice():t],s.push(t),a||u()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!o}};return d},w.extend({Deferred:function(e){var o=[["resolve","done",w.Callbacks("once memory"),"resolved"],["reject","fail",w.Callbacks("once memory"),"rejected"],["notify","progress",w.Callbacks("memory")]],a="pending",r={state:function(){return a},always:function(){return l.done(arguments).fail(arguments),this},then:function(){var a=arguments;return w.Deferred(function(i){w.each(o,function(e,t){var n=w.isFunction(a[e])&&a[e];l[t[1]](function(){var e=n&&n.apply(this,arguments);e&&w.isFunction(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this===r?i.promise():this,n?[e]:arguments)})}),a=null}).promise()},promise:function(e){return null!=e?w.extend(e,r):r}},l={};return r.pipe=r.then,w.each(o,function(e,t){var n=t[2],i=t[3];r[t[1]]=n.add,i&&n.add(function(){a=i},o[1^e][2].disable,o[2][2].lock),l[t[0]]=function(){return l[t[0]+"With"](this===l?r:this,arguments),this},l[t[0]+"With"]=n.fireWith}),r.promise(l),e&&e.call(l,l),l},when:function(e){var a,t,n,i=0,o=u.call(arguments),r=o.length,l=1!==r||e&&w.isFunction(e.promise)?r:0,s=1===l?e:w.Deferred(),c=function(t,n,i){return function(e){n[t]=this,i[t]=1<arguments.length?u.call(arguments):e,i===a?s.notifyWith(n,i):--l||s.resolveWith(n,i)}};if(1<r)for(a=new Array(r),t=new Array(r),n=new Array(r);i<r;i++)o[i]&&w.isFunction(o[i].promise)?o[i].promise().progress(c(i,t,a)).done(c(i,n,o)).fail(s.reject):--l;return l||s.resolveWith(n,o),s.promise()}}),w.fn.ready=function(e){return w.ready.promise().done(e),this},w.extend({isReady:!1,readyWait:1,holdReady:function(e){e?w.readyWait++:w.ready(!0)},ready:function(e){(!0===e?--w.readyWait:w.isReady)||(w.isReady=!0)!==e&&0<--w.readyWait||(ie.resolveWith(h,[w]),w.fn.triggerHandler&&(w(h).triggerHandler("ready"),w(h).off("ready")))}}),w.ready.promise=function(e){if(!ie)if(ie=w.Deferred(),"complete"===h.readyState||"loading"!==h.readyState&&!h.documentElement.doScroll)k.setTimeout(w.ready);else if(h.addEventListener)h.addEventListener("DOMContentLoaded",t),k.addEventListener("load",t);else{h.attachEvent("onreadystatechange",t),k.attachEvent("onload",t);var n=!1;try{n=null==k.frameElement&&h.documentElement}catch(e){}n&&n.doScroll&&!function t(){if(!w.isReady){try{n.doScroll("left")}catch(e){return k.setTimeout(t,50)}oe(),w.ready()}}()}return ie.promise(e)},w.ready.promise(),w(m))break;m.ownFirst="0"===ae,m.inlineBlockNeedsLayout=!1,w(function(){var e,t,n=h.getElementsByTagName("body")[0];n&&n.style&&(e=h.createElement("div"),(t=h.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(t).appendChild(e),"undefined"!=typeof e.style.zoom&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",m.inlineBlockNeedsLayout=e=3===e.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(t))});e=h.createElement("div");m.deleteExpando=!0;try{delete e.test}catch(e){m.deleteExpando=!1}var a,g=function(e){var t=w.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)},re=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,le=/([A-Z])/g;function se(e,t,n){if(n===undefined&&1===e.nodeType){var i="data-"+t.replace(le,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:re.test(n)?w.parseJSON(n):n)}catch(e){}w.data(e,t,n)}else n=undefined}return n}function ce(e){for(var t in e)if(("data"!==t||!w.isEmptyObject(e[t]))&&"toJSON"!==t)return;return 1}function ue(e,t,n,i){if(g(e)){var a,o=w.expando,r=e.nodeType,l=r?w.cache:e,s=r?e[o]:e[o]&&o;if(s&&l[s]&&(i||l[s].data)||n!==undefined||"string"!=typeof t)return l[s=s||(r?e[o]=d.pop()||w.guid++:o)]||(l[s]=r?{}:{toJSON:w.noop}),"object"!=typeof t&&"function"!=typeof t||(i?l[s]=w.extend(l[s],t):l[s].data=w.extend(l[s].data,t)),e=l[s],i||(e.data||(e.data={}),e=e.data),n!==undefined&&(e[w.camelCase(t)]=n),"string"==typeof t?null==(a=e[t])&&(a=e[w.camelCase(t)]):a=e,a}}function de(e,t,n){if(g(e)){var i,a,o=e.nodeType,r=o?w.cache:e,l=o?e[w.expando]:w.expando;if(r[l]){if(t&&(i=n?r[l]:r[l].data)){a=(t=w.isArray(t)?t.concat(w.map(t,w.camelCase)):t in i||(t=w.camelCase(t))in i?[t]:t.split(" ")).length;for(;a--;)delete i[t[a]];if(n?!ce(i):!w.isEmptyObject(i))return}(n||(delete r[l].data,ce(r[l])))&&(o?w.cleanData([e],!0):m.deleteExpando||r!=r.window?delete r[l]:r[l]=undefined)}}}w.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){return!!(e=e.nodeType?w.cache[e[w.expando]]:e[w.expando])&&!ce(e)},data:function(e,t,n){return ue(e,t,n)},removeData:function(e,t){return de(e,t)},_data:function(e,t,n){return ue(e,t,n,!0)},_removeData:function(e,t){return de(e,t,!0)}}),w.fn.extend({data:function(e,t){var n,i,a,o=this[0],r=o&&o.attributes;if(e!==undefined)return"object"==typeof e?this.each(function(){w.data(this,e)}):1<arguments.length?this.each(function(){w.data(this,e,t)}):o?se(o,e,w.data(o,e)):undefined;if(this.length&&(a=w.data(o),1===o.nodeType&&!w._data(o,"parsedAttrs"))){for(n=r.length;n--;)r[n]&&0===(i=r[n].name).indexOf("data-")&&se(o,i=w.camelCase(i.slice(5)),a[i]);w._data(o,"parsedAttrs",!0)}return a},removeData:function(e){return this.each(function(){w.removeData(this,e)})}}),w.extend({queue:function(e,t,n){var i;if(e)return i=w._data(e,t=(t||"fx")+"queue"),n&&(!i||w.isArray(n)?i=w._data(e,t,w.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=w.queue(e,t),i=n.length,a=n.shift(),o=w._queueHooks(e,t);"inprogress"===a&&(a=n.shift(),i--),a&&("fx"===t&&n.unshift("inprogress"),delete o.stop,a.call(e,function(){w.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return w._data(e,n)||w._data(e,n,{empty:w.Callbacks("once memory").add(function(){w._removeData(e,t+"queue"),w._removeData(e,n)})})}}),w.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?w.queue(this[0],t):n===undefined?this:this.each(function(){var e=w.queue(this,t,n);w._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&w.dequeue(this,t)})},dequeue:function(e){return this.each(function(){w.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,a=w.Deferred(),o=this,r=this.length,l=function(){--i||a.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=undefined),e=e||"fx";r--;)(n=w._data(o[r],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(l));return l(),a.promise(t)}}),m.shrinkWrapBlocks=function(){return null!=a?a:(a=!1,(t=h.getElementsByTagName("body")[0])&&t.style?(e=h.createElement("div"),(n=h.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",t.appendChild(n).appendChild(e),"undefined"!=typeof e.style.zoom&&(e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",e.appendChild(h.createElement("div")).style.width="5px",a=3!==e.offsetWidth),t.removeChild(n),a):void 0);var e,t,n};var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,fe=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),l=["Top","Right","Bottom","Left"],pe=function(e,t){return"none"===w.css(e=t||e,"display")||!w.contains(e.ownerDocument,e)};function he(e,t,n,i){var a,o=1,r=20,l=i?function(){return i.cur()}:function(){return w.css(e,t,"")},s=l(),c=n&&n[3]||(w.cssNumber[t]?"":"px"),u=(w.cssNumber[t]||"px"!==c&&+s)&&fe.exec(w.css(e,t));if(u&&u[3]!==c)for(c=c||u[3],n=n||[],u=+s||1;u/=o=o||".5",w.style(e,t,u+c),o!==(o=l()/s)&&1!==o&&--r;);return n&&(u=+u||+s||0,a=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=a)),a}var f=function(e,t,n,i,a,o,r){var l=0,s=e.length,c=null==n;if("object"===w.type(n))for(l in a=!0,n)f(e,t,l,n[l],!0,o,r);else if(i!==undefined&&(a=!0,w.isFunction(i)||(r=!0),t=c?r?(t.call(e,i),null):(c=t,function(e,t,n){return c.call(w(e),n)}):t))for(;l<s;l++)t(e[l],n,r?i:i.call(e[l],l,t(e[l],n)));return a?e:c?t.call(e):s?t(e[0],n):o},ye=/^(?:checkbox|radio)$/i,me=/<([\w:-]+)/,ge=/^$|\/(?:java|ecma)script/i,ve=/^\s+/,be="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";function xe(e){var t=be.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}L=h.createElement("div"),D=h.createDocumentFragment(),H=h.createElement("input"),L.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",m.leadingWhitespace=3===L.firstChild.nodeType,m.tbody=!L.getElementsByTagName("tbody").length,m.htmlSerialize=!!L.getElementsByTagName("link").length,m.html5Clone="<:nav></:nav>"!==h.createElement("nav").cloneNode(!0).outerHTML,H.type="checkbox",H.checked=!0,D.appendChild(H),m.appendChecked=H.checked,L.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!L.cloneNode(!0).lastChild.defaultValue,D.appendChild(L),(H=h.createElement("input")).setAttribute("type","radio"),H.setAttribute("checked","checked"),H.setAttribute("name","t"),L.appendChild(H),m.checkClone=L.cloneNode(!0).cloneNode(!0).lastChild.checked,m.noCloneEvent=!!L.addEventListener,L[w.expando]=1,m.attributes=!L.getAttribute(w.expando);var v={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:m.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};function b(e,t){var n,i,a=0,o="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):undefined;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[a]);a++)!t||w.nodeName(i,t)?o.push(i):w.merge(o,b(i,t));return t===undefined||t&&w.nodeName(e,t)?w.merge([e],o):o}function ke(e,t){for(var n,i=0;null!=(n=e[i]);i++)w._data(n,"globalEval",!t||w._data(t[i],"globalEval"))}v.optgroup=v.option,v.tbody=v.tfoot=v.colgroup=v.caption=v.thead,v.th=v.td;var we=/<|&#?\w+;/,Ce=/<tbody/i;function Te(e){ye.test(e.type)&&(e.defaultChecked=e.checked)}function Ee(e,t,n,i,a){for(var o,r,l,s,c,u,d,f=e.length,p=xe(t),h=[],y=0;y<f;y++)if((r=e[y])||0===r)if("object"===w.type(r))w.merge(h,r.nodeType?[r]:r);else if(we.test(r)){for(s=s||p.appendChild(t.createElement("div")),c=(me.exec(r)||["",""])[1].toLowerCase(),d=v[c]||v._default,s.innerHTML=d[1]+w.htmlPrefilter(r)+d[2],o=d[0];o--;)s=s.lastChild;if(!m.leadingWhitespace&&ve.test(r)&&h.push(t.createTextNode(ve.exec(r)[0])),!m.tbody)for(o=(r="table"!==c||Ce.test(r)?"<table>"!==d[1]||Ce.test(r)?0:s:s.firstChild)&&r.childNodes.length;o--;)w.nodeName(u=r.childNodes[o],"tbody")&&!u.childNodes.length&&r.removeChild(u);for(w.merge(h,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=p.lastChild}else h.push(t.createTextNode(r));for(s&&p.removeChild(s),m.appendChecked||w.grep(b(h,"input"),Te),y=0;r=h[y++];)if(i&&-1<w.inArray(r,i))a&&a.push(r);else if(l=w.contains(r.ownerDocument,r),s=b(p.appendChild(r),"script"),l&&ke(s),n)for(o=0;r=s[o++];)ge.test(r.type||"")&&n.push(r);return s=null,p}var De,Le,Se=h.createElement("div");for(De in{submit:!0,change:!0,focusin:!0})(m[De]=(Le="on"+De)in k)||(Se.setAttribute(Le,"t"),m[De]=!1===Se.attributes[Le].expando);var Ae=/^(?:input|select|textarea)$/i,Ne=/^key/,je=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Me=/^(?:focusinfocus|focusoutblur)$/,He=/^([^.]*)(?:\.(.+)|)/;function Ie(){return!0}function s(){return!1}function Fe(){try{return h.activeElement}catch(e){}}function qe(e,t,n,i,a,o){var r,l;if("object"==typeof t){for(l in"string"!=typeof n&&(i=i||n,n=undefined),t)qe(e,l,n,i,t[l],o);return e}if(null==i&&null==a?(a=n,i=n=undefined):null==a&&("string"==typeof n?(a=i,i=undefined):(a=i,i=n,n=undefined)),!1===a)a=s;else if(!a)return e;return 1===o&&(r=a,(a=function(e){return w().off(e),r.apply(this,arguments)}).guid=r.guid||(r.guid=w.guid++)),e.each(function(){w.event.add(this,t,a,i,n)})}w.event={global:{},add:function(e,t,n,i,a){var o,r,l,s,c,u,d,f,p,h=w._data(e);if(h)for(n.handler&&(n=(l=n).handler,a=l.selector),n.guid||(n.guid=w.guid++),(o=h.events)||(o=h.events={}),(c=h.handle)||((c=h.handle=function(e){return void 0===w||e&&w.event.triggered===e.type?undefined:w.event.dispatch.apply(c.elem,arguments)}).elem=e),r=(t=(t||"").match(C)||[""]).length;r--;)d=p=(f=He.exec(t[r])||[])[1],f=(f[2]||"").split(".").sort(),d&&(s=w.event.special[d]||{},d=(a?s.delegateType:s.bindType)||d,s=w.event.special[d]||{},p=w.extend({type:d,origType:p,data:i,handler:n,guid:n.guid,selector:a,needsContext:a&&w.expr.match.needsContext.test(a),namespace:f.join(".")},l),(u=o[d])||((u=o[d]=[]).delegateCount=0,s.setup&&!1!==s.setup.call(e,i,f,c)||(e.addEventListener?e.addEventListener(d,c,!1):e.attachEvent&&e.attachEvent("on"+d,c))),s.add&&(s.add.call(e,p),p.handler.guid||(p.handler.guid=n.guid)),a?u.splice(u.delegateCount++,0,p):u.push(p),w.event.global[d]=!0)},remove:function(e,t,n,i,a){var o,r,l,s,c,u,d,f,p,h,y,m=w.hasData(e)&&w._data(e);if(m&&(u=m.events)){for(c=(t=(t||"").match(C)||[""]).length;c--;)if(p=y=(l=He.exec(t[c])||[])[1],h=(l[2]||"").split(".").sort(),p){for(d=w.event.special[p]||{},f=u[p=(i?d.delegateType:d.bindType)||p]||[],l=l[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)r=f[o],!a&&y!==r.origType||n&&n.guid!==r.guid||l&&!l.test(r.namespace)||i&&i!==r.selector&&("**"!==i||!r.selector)||(f.splice(o,1),r.selector&&f.delegateCount--,d.remove&&d.remove.call(e,r));s&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||w.removeEvent(e,p,m.handle),delete u[p])}else for(p in u)w.event.remove(e,p+t[c],n,i,!0);w.isEmptyObject(u)&&(delete m.handle,w._removeData(e,"events"))}},trigger:function(e,t,n,i){var a,o,r,l,s,c,u=[n||h],d=y.call(e,"type")?e.type:e,f=y.call(e,"namespace")?e.namespace.split("."):[],p=s=n=n||h;if(3!==n.nodeType&&8!==n.nodeType&&!Me.test(d+w.event.triggered)&&(-1<d.indexOf(".")&&(d=(f=d.split(".")).shift(),f.sort()),o=d.indexOf(":")<0&&"on"+d,(e=e[w.expando]?e:new w.Event(d,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=f.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=undefined,e.target||(e.target=n),t=null==t?[e]:w.makeArray(t,[e]),l=w.event.special[d]||{},i||!l.trigger||!1!==l.trigger.apply(n,t))){if(!i&&!l.noBubble&&!w.isWindow(n)){for(r=l.delegateType||d,Me.test(r+d)||(p=p.parentNode);p;p=p.parentNode)u.push(p),s=p;s===(n.ownerDocument||h)&&u.push(s.defaultView||s.parentWindow||k)}for(c=0;(p=u[c++])&&!e.isPropagationStopped();)e.type=1<c?r:l.bindType||d,(a=(w._data(p,"events")||{})[e.type]&&w._data(p,"handle"))&&a.apply(p,t),(a=o&&p[o])&&a.apply&&g(p)&&(e.result=a.apply(p,t),!1===e.result&&e.preventDefault());if(e.type=d,!i&&!e.isDefaultPrevented()&&(!l._default||!1===l._default.apply(u.pop(),t))&&g(n)&&o&&n[d]&&!w.isWindow(n)){(s=n[o])&&(n[o]=null),w.event.triggered=d;try{n[d]()}catch(e){}w.event.triggered=undefined,s&&(n[o]=s)}return e.result}},dispatch:function(e){e=w.event.fix(e);var t,n,i,a,o,r=u.call(arguments),l=(w._data(this,"events")||{})[e.type]||[],s=w.event.special[e.type]||{};if((r[0]=e).delegateTarget=this,!s.preDispatch||!1!==s.preDispatch.call(this,e)){for(o=w.event.handlers.call(this,e,l),t=0;(i=o[t++])&&!e.isPropagationStopped();)for(e.currentTarget=i.elem,n=0;(a=i.handlers[n++])&&!e.isImmediatePropagationStopped();)e.rnamespace&&!e.rnamespace.test(a.namespace)||(e.handleObj=a,e.data=a.data,(a=((w.event.special[a.origType]||{}).handle||a.handler).apply(i.elem,r))!==undefined&&!1===(e.result=a)&&(e.preventDefault(),e.stopPropagation()));return s.postDispatch&&s.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,a,o,r=[],l=t.delegateCount,s=e.target;if(l&&s.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;s!=this;s=s.parentNode||this)if(1===s.nodeType&&(!0!==s.disabled||"click"!==e.type)){for(i=[],n=0;n<l;n++)i[a=(o=t[n]).selector+" "]===undefined&&(i[a]=o.needsContext?-1<w(a,this).index(s):w.find(a,this,null,[s]).length),i[a]&&i.push(o);i.length&&r.push({elem:s,handlers:i})}return l<t.length&&r.push({elem:this,handlers:t.slice(l)}),r},fix:function(e){if(e[w.expando])return e;var t,n,i,a=e.type,o=e,r=this.fixHooks[a];for(r||(this.fixHooks[a]=r=je.test(a)?this.mouseHooks:Ne.test(a)?this.keyHooks:{}),i=r.props?this.props.concat(r.props):this.props,e=new w.Event(o),t=i.length;t--;)e[n=i[t]]=o[n];return e.target||(e.target=o.srcElement||h),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,r.filter?r.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,a=t.button,o=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=(n=e.target.ownerDocument||h).documentElement,n=n.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&o&&(e.relatedTarget=o===e.target?t.toElement:o),e.which||a===undefined||(e.which=1&a?1:2&a?3:4&a?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==Fe()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===Fe()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(w.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return w.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){e.result!==undefined&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n){e=w.extend(new w.Event,n,{type:e,isSimulated:!0});w.event.trigger(e,null,t),e.isDefaultPrevented()&&n.preventDefault()}},w.removeEvent=h.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)}:function(e,t,n){t="on"+t;e.detachEvent&&("undefined"==typeof e[t]&&(e[t]=null),e.detachEvent(t,n))},w.Event=function(e,t){if(!(this instanceof w.Event))return new w.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===undefined&&!1===e.returnValue?Ie:s):this.type=e,t&&w.extend(this,t),this.timeStamp=e&&e.timeStamp||w.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:s,isPropagationStopped:s,isImmediatePropagationStopped:s,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ie,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ie,e&&!this.isSimulated&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ie,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,a){w.event.special[e]={delegateType:a,bindType:a,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||w.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=a),t}}}),m.submit||(w.event.special.submit={setup:function(){if(w.nodeName(this,"form"))return!1;w.event.add(this,"click._submit keypress._submit",function(e){e=e.target,e=w.nodeName(e,"input")||w.nodeName(e,"button")?w.prop(e,"form"):undefined;e&&!w._data(e,"submit")&&(w.event.add(e,"submit._submit",function(e){e._submitBubble=!0}),w._data(e,"submit",!0))})},postDispatch:function(e){e._submitBubble&&(delete e._submitBubble,this.parentNode&&!e.isTrigger&&w.event.simulate("submit",this.parentNode,e))},teardown:function(){if(w.nodeName(this,"form"))return!1;w.event.remove(this,"._submit")}}),m.change||(w.event.special.change={setup:function(){if(Ae.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(w.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._justChanged=!0)}),w.event.add(this,"click._change",function(e){this._justChanged&&!e.isTrigger&&(this._justChanged=!1),w.event.simulate("change",this,e)})),!1;w.event.add(this,"beforeactivate._change",function(e){e=e.target;Ae.test(e.nodeName)&&!w._data(e,"change")&&(w.event.add(e,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||w.event.simulate("change",this.parentNode,e)}),w._data(e,"change",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return w.event.remove(this,"._change"),!Ae.test(this.nodeName)}}),m.focusin||w.each({focus:"focusin",blur:"focusout"},function(n,i){var a=function(e){w.event.simulate(i,e.target,w.event.fix(e))};w.event.special[i]={setup:function(){var e=this.ownerDocument||this,t=w._data(e,i);t||e.addEventListener(n,a,!0),w._data(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this,t=w._data(e,i)-1;t?w._data(e,i,t):(e.removeEventListener(n,a,!0),w._removeData(e,i))}}}),w.fn.extend({on:function(e,t,n,i){return qe(this,e,t,n,i)},one:function(e,t,n,i){return qe(this,e,t,n,i,1)},off:function(e,t,n){var i,a;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,w(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=undefined),!1===n&&(n=s),this.each(function(){w.event.remove(this,e,n,t)});for(a in e)this.off(a,t,e[a]);return this},trigger:function(e,t){return this.each(function(){w.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return w.event.trigger(e,t,n,!0)}});var _e=/ jQuery\d+="(?:null|\d+)"/g,Pe=new RegExp("<(?:"+be+")[\\s/>]","i"),Be=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,Oe=/<script|<style|<link/i,Re=/checked\s*(?:[^=]|=\s*.checked.)/i,ze=/^true\/(.*)/,We=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,$e=xe(h).appendChild(h.createElement("div"));function Ye(e,t){return w.nodeName(e,"table")&&w.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function Xe(e){return e.type=(null!==w.find.attr(e,"type"))+"/"+e.type,e}function Ve(e){var t=ze.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Ue(e,t){if(1===t.nodeType&&w.hasData(e)){var n,i,a,e=w._data(e),o=w._data(t,e),r=e.events;if(r)for(n in delete o.handle,o.events={},r)for(i=0,a=r[n].length;i<a;i++)w.event.add(t,n,r[n][i]);o.data&&(o.data=w.extend({},o.data))}}function x(n,i,a,o){i=_.apply([],i);var e,t,r,l,s,c,u=0,d=n.length,f=d-1,p=i[0],h=w.isFunction(p);if(h||1<d&&"string"==typeof p&&!m.checkClone&&Re.test(p))return n.each(function(e){var t=n.eq(e);h&&(i[0]=p.call(this,e,t.html())),x(t,i,a,o)});if(d&&(e=(c=Ee(i,n[0].ownerDocument,!1,n,o)).firstChild,1===c.childNodes.length&&(c=e),e||o)){for(r=(l=w.map(b(c,"script"),Xe)).length;u<d;u++)t=c,u!==f&&(t=w.clone(t,!0,!0),r&&w.merge(l,b(t,"script"))),a.call(n[u],t,u);if(r)for(s=l[l.length-1].ownerDocument,w.map(l,Ve),u=0;u<r;u++)t=l[u],ge.test(t.type||"")&&!w._data(t,"globalEval")&&w.contains(s,t)&&(t.src?w._evalUrl&&w._evalUrl(t.src):w.globalEval((t.text||t.textContent||t.innerHTML||"").replace(We,"")));c=e=null}return n}function Ke(e,t,n){for(var i,a=t?w.filter(t,e):e,o=0;null!=(i=a[o]);o++)n||1!==i.nodeType||w.cleanData(b(i)),i.parentNode&&(n&&w.contains(i.ownerDocument,i)&&ke(b(i,"script")),i.parentNode.removeChild(i));return e}w.extend({htmlPrefilter:function(e){return e.replace(Be,"<$1></$2>")},clone:function(e,t,n){var i,a,o,r,l,s=w.contains(e.ownerDocument,e);if(m.html5Clone||w.isXMLDoc(e)||!Pe.test("<"+e.nodeName+">")?o=e.cloneNode(!0):($e.innerHTML=e.outerHTML,$e.removeChild(o=$e.firstChild)),!(m.noCloneEvent&&m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||w.isXMLDoc(e)))for(i=b(o),l=b(e),r=0;null!=(a=l[r]);++r)if(i[r]){d=u=c=p=f=void 0;var c,u,d,f=a,p=i[r];if(1===p.nodeType){if(c=p.nodeName.toLowerCase(),!m.noCloneEvent&&p[w.expando]){for(u in(d=w._data(p)).events)w.removeEvent(p,u,d.handle);p.removeAttribute(w.expando)}"script"===c&&p.text!==f.text?(Xe(p).text=f.text,Ve(p)):"object"===c?(p.parentNode&&(p.outerHTML=f.outerHTML),m.html5Clone&&f.innerHTML&&!w.trim(p.innerHTML)&&(p.innerHTML=f.innerHTML)):"input"===c&&ye.test(f.type)?(p.defaultChecked=p.checked=f.checked,p.value!==f.value&&(p.value=f.value)):"option"===c?p.defaultSelected=p.selected=f.defaultSelected:"input"!==c&&"textarea"!==c||(p.defaultValue=f.defaultValue)}}if(t)if(n)for(l=l||b(e),i=i||b(o),r=0;null!=(a=l[r]);r++)Ue(a,i[r]);else Ue(e,o);return 0<(i=b(o,"script")).length&&ke(i,!s&&b(e,"script")),i=l=a=null,o},cleanData:function(e,t){for(var n,i,a,o,r=0,l=w.expando,s=w.cache,c=m.attributes,u=w.event.special;null!=(n=e[r]);r++)if((t||g(n))&&(o=(a=n[l])&&s[a])){if(o.events)for(i in o.events)u[i]?w.event.remove(n,i):w.removeEvent(n,i,o.handle);s[a]&&(delete s[a],c||"undefined"==typeof n.removeAttribute?n[l]=undefined:n.removeAttribute(l),d.push(a))}}}),w.fn.extend({domManip:x,detach:function(e){return Ke(this,e,!0)},remove:function(e){return Ke(this,e)},text:function(e){return f(this,function(e){return e===undefined?w.text(this):this.empty().append((this[0]&&this[0].ownerDocument||h).createTextNode(e))},null,e,arguments.length)},append:function(){return x(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Ye(this,e).appendChild(e)})},prepend:function(){return x(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=Ye(this,e)).insertBefore(e,t.firstChild)})},before:function(){return x(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return x(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&w.cleanData(b(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&w.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return w.clone(this,e,t)})},html:function(e){return f(this,function(e){var t=this[0]||{},n=0,i=this.length;if(e===undefined)return 1===t.nodeType?t.innerHTML.replace(_e,""):undefined;if("string"==typeof e&&!Oe.test(e)&&(m.htmlSerialize||!Pe.test(e))&&(m.leadingWhitespace||!ve.test(e))&&!v[(me.exec(e)||["",""])[1].toLowerCase()]){e=w.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(w.cleanData(b(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return x(this,arguments,function(e){var t=this.parentNode;w.inArray(this,n)<0&&(w.cleanData(b(this)),t&&t.replaceChild(e,this))},n)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,r){w.fn[e]=function(e){for(var t,n=0,i=[],a=w(e),o=a.length-1;n<=o;n++)t=n===o?this:this.clone(!0),w(a[n])[r](t),P.apply(i,t.get());return this.pushStack(i)}});var Ge,Je={HTML:"block",BODY:"block"};function Qe(e,t){e=w(t.createElement(e)).appendTo(t.body),t=w.css(e[0],"display");return e.detach(),t}function Ze(e){var t=h,n=Je[e];return n||("none"!==(n=Qe(e,t))&&n||((t=((Ge=(Ge||w("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement))[0].contentWindow||Ge[0].contentDocument).document).write(),t.close(),n=Qe(e,t),Ge.detach()),Je[e]=n),n}var n,et,tt,nt,it,at,ot,o,rt=/^margin/,lt=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),st=function(e,t,n,i){var a,o={};for(a in t)o[a]=e.style[a],e.style[a]=t[a];for(a in i=n.apply(e,i||[]),t)e.style[a]=o[a];return i},ct=h.documentElement;function r(){var e,t=h.documentElement;t.appendChild(ot),o.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",n=tt=at=!1,et=it=!0,k.getComputedStyle&&(e=k.getComputedStyle(o),n="1%"!==(e||{}).top,at="2px"===(e||{}).marginLeft,tt="4px"===(e||{width:"4px"}).width,o.style.marginRight="50%",et="4px"===(e||{marginRight:"4px"}).marginRight,(e=o.appendChild(h.createElement("div"))).style.cssText=o.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",e.style.marginRight=e.style.width="0",o.style.width="1px",it=!parseFloat((k.getComputedStyle(e)||{}).marginRight),o.removeChild(e)),o.style.display="none",(nt=0===o.getClientRects().length)&&(o.style.display="",o.innerHTML="<table><tr><td></td><td>t</td></tr></table>",o.childNodes[0].style.borderCollapse="separate",(e=o.getElementsByTagName("td"))[0].style.cssText="margin:0;border:0;padding:0;display:none",(nt=0===e[0].offsetHeight)&&(e[0].style.display="",e[1].style.display="none",nt=0===e[0].offsetHeight)),t.removeChild(ot)}ot=h.createElement("div"),(o=h.createElement("div")).style&&(o.style.cssText="float:left;opacity:.5",m.opacity="0.5"===o.style.opacity,m.cssFloat=!!o.style.cssFloat,o.style.backgroundClip="content-box",o.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===o.style.backgroundClip,(ot=h.createElement("div")).style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",o.innerHTML="",ot.appendChild(o),m.boxSizing=""===o.style.boxSizing||""===o.style.MozBoxSizing||""===o.style.WebkitBoxSizing,w.extend(m,{reliableHiddenOffsets:function(){return null==n&&r(),nt},boxSizingReliable:function(){return null==n&&r(),tt},pixelMarginRight:function(){return null==n&&r(),et},pixelPosition:function(){return null==n&&r(),n},reliableMarginRight:function(){return null==n&&r(),it},reliableMarginLeft:function(){return null==n&&r(),at}}));var c,p,ut=/^(top|right|bottom|left)$/;function dt(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}k.getComputedStyle?(c=function(e){var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:k).getComputedStyle(e)},p=function(e,t,n){var i,a,o=e.style;return""!==(a=(n=n||c(e))?n.getPropertyValue(t)||n[t]:undefined)&&a!==undefined||w.contains(e.ownerDocument,e)||(a=w.style(e,t)),n&&!m.pixelMarginRight()&&lt.test(a)&&rt.test(t)&&(e=o.width,t=o.minWidth,i=o.maxWidth,o.minWidth=o.maxWidth=o.width=a,a=n.width,o.width=e,o.minWidth=t,o.maxWidth=i),a===undefined?a:a+""}):ct.currentStyle&&(c=function(e){return e.currentStyle},p=function(e,t,n){var i,a,o,r=e.style;return null==(n=(n=n||c(e))?n[t]:undefined)&&r&&r[t]&&(n=r[t]),lt.test(n)&&!ut.test(t)&&(i=r.left,(o=(a=e.runtimeStyle)&&a.left)&&(a.left=e.currentStyle.left),r.left="fontSize"===t?"1em":n,n=r.pixelLeft+"px",r.left=i,o&&(a.left=o)),n===undefined?n:n+""||"auto"});var ft=/alpha\([^)]*\)/i,pt=/opacity\s*=\s*([^)]*)/i,ht=/^(none|table(?!-c[ea]).+)/,yt=new RegExp("^("+e+")(.*)$","i"),mt={position:"absolute",visibility:"hidden",display:"block"},gt={letterSpacing:"0",fontWeight:"400"},vt=["Webkit","O","Moz","ms"],bt=h.createElement("div").style;function xt(e){if(e in bt)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=vt.length;n--;)if((e=vt[n]+t)in bt)return e}function kt(e,t){for(var n,i,a,o=[],r=0,l=e.length;r<l;r++)(i=e[r]).style&&(o[r]=w._data(i,"olddisplay"),n=i.style.display,t?(o[r]||"none"!==n||(i.style.display=""),""===i.style.display&&pe(i)&&(o[r]=w._data(i,"olddisplay",Ze(i.nodeName)))):(a=pe(i),(n&&"none"!==n||!a)&&w._data(i,"olddisplay",a?n:w.css(i,"display"))));for(r=0;r<l;r++)!(i=e[r]).style||t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[r]||"":"none");return e}function wt(e,t,n){var i=yt.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function Ct(e,t,n,i,a){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,r=0;o<4;o+=2)"margin"===n&&(r+=w.css(e,n+l[o],!0,a)),i?("content"===n&&(r-=w.css(e,"padding"+l[o],!0,a)),"margin"!==n&&(r-=w.css(e,"border"+l[o]+"Width",!0,a))):(r+=w.css(e,"padding"+l[o],!0,a),"padding"!==n&&(r+=w.css(e,"border"+l[o]+"Width",!0,a)));return r}function Tt(e,t,n){var i=!0,a="width"===t?e.offsetWidth:e.offsetHeight,o=c(e),r=m.boxSizing&&"border-box"===w.css(e,"boxSizing",!1,o);if(a<=0||null==a){if(((a=p(e,t,o))<0||null==a)&&(a=e.style[t]),lt.test(a))return a;i=r&&(m.boxSizingReliable()||a===e.style[t]),a=parseFloat(a)||0}return a+Ct(e,t,n||(r?"border":"content"),i,o)+"px"}function T(e,t,n,i,a){return new T.prototype.init(e,t,n,i,a)}w.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=p(e,"opacity"))?"1":t}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:m.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var a,o,r,l=w.camelCase(t),s=e.style;if(t=w.cssProps[l]||(w.cssProps[l]=xt(l)||l),r=w.cssHooks[t]||w.cssHooks[l],n===undefined)return r&&"get"in r&&(a=r.get(e,!1,i))!==undefined?a:s[t];if("string"===(o=typeof n)&&(a=fe.exec(n))&&a[1]&&(n=he(e,t,a),o="number"),null!=n&&n==n&&("number"===o&&(n+=a&&a[3]||(w.cssNumber[l]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(s[t]="inherit"),!(r&&"set"in r&&(n=r.set(e,n,i))===undefined)))try{s[t]=n}catch(e){}}},css:function(e,t,n,i){var a,o=w.camelCase(t);return t=w.cssProps[o]||(w.cssProps[o]=xt(o)||o),"normal"===(a=(a=(o=w.cssHooks[t]||w.cssHooks[o])&&"get"in o?o.get(e,!0,n):a)===undefined?p(e,t,i):a)&&t in gt&&(a=gt[t]),""===n||n?(o=parseFloat(a),!0===n||isFinite(o)?o||0:a):a}}),w.each(["height","width"],function(e,a){w.cssHooks[a]={get:function(e,t,n){if(t)return ht.test(w.css(e,"display"))&&0===e.offsetWidth?st(e,mt,function(){return Tt(e,a,n)}):Tt(e,a,n)},set:function(e,t,n){var i=n&&c(e);return wt(0,t,n?Ct(e,a,n,m.boxSizing&&"border-box"===w.css(e,"boxSizing",!1,i),i):0)}}}),m.opacity||(w.cssHooks.opacity={get:function(e,t){return pt.test((t&&e.currentStyle?e.currentStyle:e.style).filter||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,e=e.currentStyle,i=w.isNumeric(t)?"alpha(opacity="+100*t+")":"",a=e&&e.filter||n.filter||"";((n.zoom=1)<=t||""===t)&&""===w.trim(a.replace(ft,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||e&&!e.filter)||(n.filter=ft.test(a)?a.replace(ft,i):a+" "+i)}}),w.cssHooks.marginRight=dt(m.reliableMarginRight,function(e,t){if(t)return st(e,{display:"inline-block"},p,[e,"marginRight"])}),w.cssHooks.marginLeft=dt(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(p(e,"marginLeft"))||(w.contains(e.ownerDocument,e)?e.getBoundingClientRect().left-st(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}):0))+"px"}),w.each({margin:"",padding:"",border:"Width"},function(a,o){w.cssHooks[a+o]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[a+l[t]+o]=i[t]||i[t-2]||i[0];return n}},rt.test(a)||(w.cssHooks[a+o].set=wt)}),w.fn.extend({css:function(e,t){return f(this,function(e,t,n){var i,a,o={},r=0;if(w.isArray(t)){for(i=c(e),a=t.length;r<a;r++)o[t[r]]=w.css(e,t[r],!1,i);return o}return n!==undefined?w.style(e,t,n):w.css(e,t)},e,t,1<arguments.length)},show:function(){return kt(this,!0)},hide:function(){return kt(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){pe(this)?w(this).show():w(this).hide()})}}),((w.Tween=T).prototype={constructor:T,init:function(e,t,n,i,a,o){this.elem=e,this.prop=n,this.easing=a||w.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(w.cssNumber[n]?"":"px")},cur:function(){var e=T.propHooks[this.prop];return(e&&e.get?e:T.propHooks._default).get(this)},run:function(e){var t,n=T.propHooks[this.prop];return this.options.duration?this.pos=t=w.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:T.propHooks._default).set(this),this}}).init.prototype=T.prototype,(T.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=w.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){w.fx.step[e.prop]?w.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[w.cssProps[e.prop]]&&!w.cssHooks[e.prop]?e.elem[e.prop]=e.now:w.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=T.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},w.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},w.fx=T.prototype.init,w.fx.step={};var E,Et,D,L,Dt=/^(?:toggle|show|hide)$/,Lt=/queueHooks$/;function St(){return k.setTimeout(function(){E=undefined}),E=w.now()}function At(e,t){var n,i={height:e},a=0;for(t=t?1:0;a<4;a+=2-t)i["margin"+(n=l[a])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function Nt(e,t,n){for(var i,a=(S.tweeners[t]||[]).concat(S.tweeners["*"]),o=0,r=a.length;o<r;o++)if(i=a[o].call(n,t,e))return i}function S(a,e,t){var n,o,i,r,l,s,c,u=0,d=S.prefilters.length,f=w.Deferred().always(function(){delete p.elem}),p=function(){if(o)return!1;for(var e=E||St(),e=Math.max(0,h.startTime+h.duration-e),t=1-(e/h.duration||0),n=0,i=h.tweens.length;n<i;n++)h.tweens[n].run(t);return f.notifyWith(a,[h,t,e]),t<1&&i?e:(f.resolveWith(a,[h]),!1)},h=f.promise({elem:a,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},t),originalProperties:e,originalOptions:t,startTime:E||St(),duration:t.duration,tweens:[],createTween:function(e,t){t=w.Tween(a,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(t),t},stop:function(e){var t=0,n=e?h.tweens.length:0;if(o)return this;for(o=!0;t<n;t++)h.tweens[t].run(1);return e?(f.notifyWith(a,[h,1,0]),f.resolveWith(a,[h,e])):f.rejectWith(a,[h,e]),this}}),y=h.props,m=y,g=h.opts.specialEasing;for(i in m)if(l=g[r=w.camelCase(i)],s=m[i],w.isArray(s)&&(l=s[1],s=m[i]=s[0]),i!==r&&(m[r]=s,delete m[i]),(c=w.cssHooks[r])&&"expand"in c)for(i in s=c.expand(s),delete m[r],s)i in m||(m[i]=s[i],g[i]=l);else g[r]=l;for(;u<d;u++)if(n=S.prefilters[u].call(h,a,y,h.opts))return w.isFunction(n.stop)&&(w._queueHooks(h.elem,h.opts.queue).stop=w.proxy(n.stop,n)),n;return w.map(y,Nt,h),w.isFunction(h.opts.start)&&h.opts.start.call(a,h),w.fx.timer(w.extend(p,{elem:a,anim:h,queue:h.opts.queue})),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always)}w.Animation=w.extend(S,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return he(n.elem,e,fe.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,a=(e=w.isFunction(e)?(t=e,["*"]):e.match(C)).length;i<a;i++)n=e[i],S.tweeners[n]=S.tweeners[n]||[],S.tweeners[n].unshift(t)},prefilters:[function(t,e,n){var i,a,o,r,l,s,c,u=this,d={},f=t.style,p=t.nodeType&&pe(t),h=w._data(t,"fxshow");for(i in n.queue||(null==(l=w._queueHooks(t,"fx")).unqueued&&(l.unqueued=0,s=l.empty.fire,l.empty.fire=function(){l.unqueued||s()}),l.unqueued++,u.always(function(){u.always(function(){l.unqueued--,w.queue(t,"fx").length||l.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],"inline"===("none"===(c=w.css(t,"display"))?w._data(t,"olddisplay")||Ze(t.nodeName):c)&&"none"===w.css(t,"float")&&(m.inlineBlockNeedsLayout&&"inline"!==Ze(t.nodeName)?f.zoom=1:f.display="inline-block")),n.overflow&&(f.overflow="hidden",m.shrinkWrapBlocks()||u.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),e)if(a=e[i],Dt.exec(a)){if(delete e[i],o=o||"toggle"===a,a===(p?"hide":"show")){if("show"!==a||!h||h[i]===undefined)continue;p=!0}d[i]=h&&h[i]||w.style(t,i)}else c=undefined;if(w.isEmptyObject(d))"inline"===("none"===c?Ze(t.nodeName):c)&&(f.display=c);else for(i in h?"hidden"in h&&(p=h.hidden):h=w._data(t,"fxshow",{}),o&&(h.hidden=!p),p?w(t).show():u.done(function(){w(t).hide()}),u.done(function(){for(var e in w._removeData(t,"fxshow"),d)w.style(t,e,d[e])}),d)r=Nt(p?h[i]:0,i,u),i in h||(h[i]=r.start,p&&(r.end=r.start,r.start="width"===i||"height"===i?1:0))}],prefilter:function(e,t){t?S.prefilters.unshift(e):S.prefilters.push(e)}}),w.speed=function(e,t,n){var i=e&&"object"==typeof e?w.extend({},e):{complete:n||!n&&t||w.isFunction(e)&&e,duration:e,easing:n&&t||t&&!w.isFunction(t)&&t};return i.duration=w.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in w.fx.speeds?w.fx.speeds[i.duration]:w.fx.speeds._default,null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){w.isFunction(i.old)&&i.old.call(this),i.queue&&w.dequeue(this,i.queue)},i},w.fn.extend({fadeTo:function(e,t,n,i){return this.filter(pe).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){var a=w.isEmptyObject(t),o=w.speed(e,n,i),e=function(){var e=S(this,w.extend({},t),o);(a||w._data(this,"finish"))&&e.stop(!0)};return e.finish=e,a||!1===o.queue?this.each(e):this.queue(o.queue,e)},stop:function(a,e,o){var r=function(e){var t=e.stop;delete e.stop,t(o)};return"string"!=typeof a&&(o=e,e=a,a=undefined),e&&!1!==a&&this.queue(a||"fx",[]),this.each(function(){var e=!0,t=null!=a&&a+"queueHooks",n=w.timers,i=w._data(this);if(t)i[t]&&i[t].stop&&r(i[t]);else for(t in i)i[t]&&i[t].stop&&Lt.test(t)&&r(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=a&&n[t].queue!==a||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||w.dequeue(this,a)})},finish:function(r){return!1!==r&&(r=r||"fx"),this.each(function(){var e,t=w._data(this),n=t[r+"queue"],i=t[r+"queueHooks"],a=w.timers,o=n?n.length:0;for(t.finish=!0,w.queue(this,r,[]),i&&i.stop&&i.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===r&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),w.each(["toggle","show","hide"],function(e,i){var a=w.fn[i];w.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?a.apply(this,arguments):this.animate(At(i,!0),e,t,n)}}),w.each({slideDown:At("show"),slideUp:At("hide"),slideToggle:At("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){w.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),w.timers=[],w.fx.tick=function(){var e,t=w.timers,n=0;for(E=w.now();n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||w.fx.stop(),E=undefined},w.fx.timer=function(e){w.timers.push(e),e()?w.fx.start():w.timers.pop()},w.fx.interval=13,w.fx.start=function(){Et=Et||k.setInterval(w.fx.tick,w.fx.interval)},w.fx.stop=function(){k.clearInterval(Et),Et=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(i,e){return i=w.fx&&w.fx.speeds[i]||i,this.queue(e=e||"fx",function(e,t){var n=k.setTimeout(e,i);t.stop=function(){k.clearTimeout(n)}})},D=h.createElement("input"),H=h.createElement("div"),L=h.createElement("select"),e=L.appendChild(h.createElement("option")),(H=h.createElement("div")).setAttribute("className","t"),H.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",F=H.getElementsByTagName("a")[0],D.setAttribute("type","checkbox"),H.appendChild(D),(F=H.getElementsByTagName("a")[0]).style.cssText="top:1px",m.getSetAttribute="t"!==H.className,m.style=/top/.test(F.getAttribute("style")),m.hrefNormalized="/a"===F.getAttribute("href"),m.checkOn=!!D.value,m.optSelected=e.selected,m.enctype=!!h.createElement("form").enctype,L.disabled=!0,m.optDisabled=!e.disabled,(D=h.createElement("input")).setAttribute("value",""),m.input=""===D.getAttribute("value"),D.value="t",D.setAttribute("type","radio"),m.radioValue="t"===D.value;var jt=/\r/g,Mt=/[\x20\t\r\n\f]+/g;w.fn.extend({val:function(t){var n,e,i,a=this[0];return arguments.length?(i=w.isFunction(t),this.each(function(e){1===this.nodeType&&(null==(e=i?t.call(this,e,w(this).val()):t)?e="":"number"==typeof e?e+="":w.isArray(e)&&(e=w.map(e,function(e){return null==e?"":e+""})),(n=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&n.set(this,e,"value")!==undefined||(this.value=e))})):a?(n=w.valHooks[a.type]||w.valHooks[a.nodeName.toLowerCase()])&&"get"in n&&(e=n.get(a,"value"))!==undefined?e:"string"==typeof(e=a.value)?e.replace(jt,""):null==e?"":e:void 0}}),w.extend({valHooks:{option:{get:function(e){var t=w.find.attr(e,"value");return null!=t?t:w.trim(w.text(e)).replace(Mt," ")}},select:{get:function(e){for(var t,n=e.options,i=e.selectedIndex,a="select-one"===e.type||i<0,o=a?null:[],r=a?i+1:n.length,l=i<0?r:a?i:0;l<r;l++)if(((t=n[l]).selected||l===i)&&(m.optDisabled?!t.disabled:null===t.getAttribute("disabled"))&&(!t.parentNode.disabled||!w.nodeName(t.parentNode,"optgroup"))){if(t=w(t).val(),a)return t;o.push(t)}return o},set:function(e,t){for(var n,i,a=e.options,o=w.makeArray(t),r=a.length;r--;)if(i=a[r],-1<w.inArray(w.valHooks.option.get(i),o))try{i.selected=n=!0}catch(e){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),a}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(e,t){if(w.isArray(t))return e.checked=-1<w.inArray(w(e).val(),t)}},m.checkOn||(w.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var A,Ht,N=w.expr.attrHandle,It=/^(?:checked|selected)$/i,j=m.getSetAttribute,Ft=m.input,qt=(w.fn.extend({attr:function(e,t){return f(this,w.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){w.removeAttr(this,e)})}}),w.extend({attr:function(e,t,n){var i,a,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?w.prop(e,t,n):(1===o&&w.isXMLDoc(e)||(t=t.toLowerCase(),a=w.attrHooks[t]||(w.expr.match.bool.test(t)?Ht:A)),n!==undefined?null===n?void w.removeAttr(e,t):a&&"set"in a&&(i=a.set(e,n,t))!==undefined?i:(e.setAttribute(t,n+""),n):!(a&&"get"in a&&null!==(i=a.get(e,t)))&&null==(i=w.find.attr(e,t))?undefined:i)},attrHooks:{type:{set:function(e,t){var n;if(!m.radioValue&&"radio"===t&&w.nodeName(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,i,a=0,o=t&&t.match(C);if(o&&1===e.nodeType)for(;n=o[a++];)i=w.propFix[n]||n,w.expr.match.bool.test(n)?Ft&&j||!It.test(n)?e[i]=!1:e[w.camelCase("default-"+n)]=e[i]=!1:w.attr(e,n,""),e.removeAttribute(j?n:i)}}),Ht={set:function(e,t,n){return!1===t?w.removeAttr(e,n):Ft&&j||!It.test(n)?e.setAttribute(!j&&w.propFix[n]||n,n):e[w.camelCase("default-"+n)]=e[n]=!0,n}},w.each(w.expr.match.bool.source.match(/\w+/g),function(e,t){var o=N[t]||w.find.attr;Ft&&j||!It.test(t)?N[t]=function(e,t,n){var i,a;return n||(a=N[t],N[t]=i,i=null!=o(e,t,n)?t.toLowerCase():null,N[t]=a),i}:N[t]=function(e,t,n){if(!n)return e[w.camelCase("default-"+t)]?t.toLowerCase():null}}),Ft&&j||(w.attrHooks.value={set:function(e,t,n){if(!w.nodeName(e,"input"))return A&&A.set(e,t,n);e.defaultValue=t}}),j||(A={set:function(e,t,n){var i=e.getAttributeNode(n);if(i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n))return t}},N.id=N.name=N.coords=function(e,t,n){if(!n)return(n=e.getAttributeNode(t))&&""!==n.value?n.value:null},w.valHooks.button={get:function(e,t){t=e.getAttributeNode(t);if(t&&t.specified)return t.value},set:A.set},w.attrHooks.contenteditable={set:function(e,t,n){A.set(e,""!==t&&t,n)}},w.each(["width","height"],function(e,n){w.attrHooks[n]={set:function(e,t){if(""===t)return e.setAttribute(n,"auto"),t}}})),m.style||(w.attrHooks.style={get:function(e){return e.style.cssText||undefined},set:function(e,t){return e.style.cssText=t+""}}),/^(?:input|select|textarea|button|object)$/i),_t=/^(?:a|area)$/i,Pt=(w.fn.extend({prop:function(e,t){return f(this,w.prop,e,t,1<arguments.length)},removeProp:function(e){return e=w.propFix[e]||e,this.each(function(){try{this[e]=undefined,delete this[e]}catch(e){}})}}),w.extend({prop:function(e,t,n){var i,a,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&w.isXMLDoc(e)||(t=w.propFix[t]||t,a=w.propHooks[t]),n!==undefined?a&&"set"in a&&(i=a.set(e,n,t))!==undefined?i:e[t]=n:a&&"get"in a&&null!==(i=a.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=w.find.attr(e,"tabindex");return t?parseInt(t,10):qt.test(e.nodeName)||_t.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.hrefNormalized||w.each(["href","src"],function(e,t){w.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),m.optSelected||(w.propHooks.selected={get:function(e){e=e.parentNode;return e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex),null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this}),m.enctype||(w.propFix.enctype="encoding"),/[\t\r\n\f]/g);function M(e){return w.attr(e,"class")||""}w.fn.extend({addClass:function(t){var e,n,i,a,o,r,l=0;if(w.isFunction(t))return this.each(function(e){w(this).addClass(t.call(this,e,M(this)))});if("string"==typeof t&&t)for(e=t.match(C)||[];n=this[l++];)if(r=M(n),i=1===n.nodeType&&(" "+r+" ").replace(Pt," ")){for(o=0;a=e[o++];)i.indexOf(" "+a+" ")<0&&(i+=a+" ");r!==(r=w.trim(i))&&w.attr(n,"class",r)}return this},removeClass:function(t){var e,n,i,a,o,r,l=0;if(w.isFunction(t))return this.each(function(e){w(this).removeClass(t.call(this,e,M(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof t&&t)for(e=t.match(C)||[];n=this[l++];)if(r=M(n),i=1===n.nodeType&&(" "+r+" ").replace(Pt," ")){for(o=0;a=e[o++];)for(;-1<i.indexOf(" "+a+" ");)i=i.replace(" "+a+" "," ");r!==(r=w.trim(i))&&w.attr(n,"class",r)}return this},toggleClass:function(a,t){var o=typeof a;return"boolean"==typeof t&&"string"==o?t?this.addClass(a):this.removeClass(a):w.isFunction(a)?this.each(function(e){w(this).toggleClass(a.call(this,e,M(this),t),t)}):this.each(function(){var e,t,n,i;if("string"==o)for(t=0,n=w(this),i=a.match(C)||[];e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else a!==undefined&&"boolean"!=o||((e=M(this))&&w._data(this,"__className__",e),w.attr(this,"class",!e&&!1!==a&&w._data(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,i=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+M(t)+" ").replace(Pt," ").indexOf(i))return!0;return!1}}),w.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,n){w.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),w.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var H=k.location,Bt=w.now(),Ot=/\?/,Rt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g,zt=(w.parseJSON=function(e){if(k.JSON&&k.JSON.parse)return k.JSON.parse(e+"");var a,o=null,t=w.trim(e+"");return t&&!w.trim(t.replace(Rt,function(e,t,n,i){return 0===(o=a&&t?0:o)?e:(a=n||t,o+=!i-!n,"")}))?Function("return "+t)():w.error("Invalid JSON: "+e)},w.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{k.DOMParser?t=(new k.DOMParser).parseFromString(e,"text/xml"):((t=new k.ActiveXObject("Microsoft.XMLDOM"))["async"]="false",t.loadXML(e))}catch(e){t=undefined}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||w.error("Invalid XML: "+e),t},/#.*$/),Wt=/([?&])_=[^&]*/,$t=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Yt=/^(?:GET|HEAD)$/,Xt=/^\/\//,Vt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ut={},Kt={},Gt="*/".concat("*"),Jt=H.href,I=Vt.exec(Jt.toLowerCase())||[];function Qt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,a=e.toLowerCase().match(C)||[];if(w.isFunction(t))for(;n=a[i++];)"+"===n.charAt(0)?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Zt(t,i,a,o){var r={},l=t===Kt;function s(e){var n;return r[e]=!0,w.each(t[e]||[],function(e,t){t=t(i,a,o);return"string"!=typeof t||l||r[t]?l?!(n=t):void 0:(i.dataTypes.unshift(t),s(t),!1)}),n}return s(i.dataTypes[0])||!r["*"]&&s("*")}function en(e,t){var n,i,a=w.ajaxSettings.flatOptions||{};for(i in t)t[i]!==undefined&&((a[i]?e:n=n||{})[i]=t[i]);return n&&w.extend(!0,e,n),e}function tn(e,t,n,i){var a,o,r,l,s,c={},u=e.dataTypes.slice();if(u[1])for(r in e.converters)c[r.toLowerCase()]=e.converters[r];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!s&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),s=o,o=u.shift())if("*"===o)o=s;else if("*"!==s&&s!==o){if(!(r=c[s+" "+o]||c["* "+o]))for(a in c)if((l=a.split(" "))[1]===o&&(r=c[s+" "+l[0]]||c["* "+l[0]])){!0===r?r=c[a]:!0!==c[a]&&(o=l[0],u.unshift(l[1]));break}if(!0!==r)if(r&&e["throws"])t=r(t);else try{t=r(t)}catch(e){return{state:"parsererror",error:r?e:"No conversion from "+s+" to "+o}}}return{state:"success",data:t}}function nn(e){if(!w.contains(e.ownerDocument||h,e))return!0;for(;e&&1===e.nodeType;){if("none"===((t=e).style&&t.style.display||w.css(t,"display"))||"hidden"===e.type)return!0;e=e.parentNode}var t;return!1}w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Jt,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(I[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Gt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":w.parseJSON,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?en(en(e,w.ajaxSettings),t):en(w.ajaxSettings,e)},ajaxPrefilter:Qt(Ut),ajaxTransport:Qt(Kt),ajax:function(e,t){"object"==typeof e&&(t=e,e=undefined);var n,s,c,u,d,f,i,p=w.ajaxSetup({},t=t||{}),h=p.context||p,y=p.context&&(h.nodeType||h.jquery)?w(h):w.event,m=w.Deferred(),g=w.Callbacks("once memory"),v=p.statusCode||{},a={},o={},b=0,r="canceled",x={readyState:0,getResponseHeader:function(e){var t;if(2===b){if(!i)for(i={};t=$t.exec(c);)i[t[1].toLowerCase()]=t[2];t=i[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===b?c:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return b||(e=o[n]=o[n]||e,a[e]=t),this},overrideMimeType:function(e){return b||(p.mimeType=e),this},statusCode:function(e){if(e)if(b<2)for(var t in e)v[t]=[v[t],e[t]];else x.always(e[x.status]);return this},abort:function(e){e=e||r;return f&&f.abort(e),l(0,e),this}};if(m.promise(x).complete=g.add,x.success=x.done,x.error=x.fail,p.url=((e||p.url||Jt)+"").replace(zt,"").replace(Xt,I[1]+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=w.trim(p.dataType||"*").toLowerCase().match(C)||[""],null==p.crossDomain&&(e=Vt.exec(p.url.toLowerCase()),p.crossDomain=!(!e||e[1]===I[1]&&e[2]===I[2]&&(e[3]||("http:"===e[1]?"80":"443"))===(I[3]||("http:"===I[1]?"80":"443")))),p.data&&p.processData&&"string"!=typeof p.data&&(p.data=w.param(p.data,p.traditional)),Zt(Ut,p,t,x),2===b)return x;for(n in(d=w.event&&p.global)&&0==w.active++&&w.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Yt.test(p.type),s=p.url,p.hasContent||(p.data&&(s=p.url+=(Ot.test(s)?"&":"?")+p.data,delete p.data),!1===p.cache&&(p.url=Wt.test(s)?s.replace(Wt,"$1_="+Bt++):s+(Ot.test(s)?"&":"?")+"_="+Bt++)),p.ifModified&&(w.lastModified[s]&&x.setRequestHeader("If-Modified-Since",w.lastModified[s]),w.etag[s]&&x.setRequestHeader("If-None-Match",w.etag[s])),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&x.setRequestHeader("Content-Type",p.contentType),x.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Gt+"; q=0.01":""):p.accepts["*"]),p.headers)x.setRequestHeader(n,p.headers[n]);if(p.beforeSend&&(!1===p.beforeSend.call(h,x,p)||2===b))return x.abort();for(n in r="abort",{success:1,error:1,complete:1})x[n](p[n]);if(f=Zt(Kt,p,t,x)){if(x.readyState=1,d&&y.trigger("ajaxSend",[x,p]),2===b)return x;p["async"]&&0<p.timeout&&(u=k.setTimeout(function(){x.abort("timeout")},p.timeout));try{b=1,f.send(a,l)}catch(e){if(!(b<2))throw e;l(-1,e)}}else l(-1,"No Transport");function l(e,t,n,i){var a,o,r,l=t;2!==b&&(b=2,u&&k.clearTimeout(u),f=undefined,c=i||"",x.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(r=function(e,t,n){for(var i,a,o,r,l=e.contents,s=e.dataTypes;"*"===s[0];)s.shift(),a===undefined&&(a=e.mimeType||t.getResponseHeader("Content-Type"));if(a)for(r in l)if(l[r]&&l[r].test(a)){s.unshift(r);break}if(s[0]in n)o=s[0];else{for(r in n){if(!s[0]||e.converters[r+" "+s[0]]){o=r;break}i=i||r}o=o||i}if(o)return o!==s[0]&&s.unshift(o),n[o]}(p,x,n)),r=tn(p,r,x,i),i?(p.ifModified&&((n=x.getResponseHeader("Last-Modified"))&&(w.lastModified[s]=n),(n=x.getResponseHeader("etag"))&&(w.etag[s]=n)),204===e||"HEAD"===p.type?l="nocontent":304===e?l="notmodified":(l=r.state,a=r.data,i=!(o=r.error))):(o=l,!e&&l||(l="error",e<0&&(e=0))),x.status=e,x.statusText=(t||l)+"",i?m.resolveWith(h,[a,l,x]):m.rejectWith(h,[x,l,o]),x.statusCode(v),v=undefined,d&&y.trigger(i?"ajaxSuccess":"ajaxError",[x,p,i?a:o]),g.fireWith(h,[x,l]),d&&(y.trigger("ajaxComplete",[x,p]),--w.active||w.event.trigger("ajaxStop")))}return x},getJSON:function(e,t,n){return w.get(e,t,n,"json")},getScript:function(e,t){return w.get(e,undefined,t,"script")}}),w.each(["get","post"],function(e,a){w[a]=function(e,t,n,i){return w.isFunction(t)&&(i=i||n,n=t,t=undefined),w.ajax(w.extend({url:e,type:a,dataType:i,data:t,success:n},w.isPlainObject(e)&&e))}}),w._evalUrl=function(e){return w.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},w.fn.extend({wrapAll:function(t){return w.isFunction(t)?this.each(function(e){w(this).wrapAll(t.call(this,e))}):(this[0]&&(e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)),this);var e},wrapInner:function(n){return w.isFunction(n)?this.each(function(e){w(this).wrapInner(n.call(this,e))}):this.each(function(){var e=w(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=w.isFunction(t);return this.each(function(e){w(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(){return this.parent().each(function(){w.nodeName(this,"body")||w(this).replaceWith(this.childNodes)}).end()}}),w.expr.filters.hidden=function(e){return m.reliableHiddenOffsets()?e.offsetWidth<=0&&e.offsetHeight<=0&&!e.getClientRects().length:nn(e)},w.expr.filters.visible=function(e){return!w.expr.filters.hidden(e)};var an=/%20/g,on=/\[\]$/,rn=/\r?\n/g,ln=/^(?:submit|button|image|reset|file)$/i,sn=/^(?:input|select|textarea|keygen)/i;w.param=function(e,t){var n,i=[],a=function(e,t){t=w.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(t===undefined&&(t=w.ajaxSettings&&w.ajaxSettings.traditional),w.isArray(e)||e.jquery&&!w.isPlainObject(e))w.each(e,function(){a(this.name,this.value)});else for(n in e)!function n(i,e,a,o){if(w.isArray(e))w.each(e,function(e,t){a||on.test(i)?o(i,t):n(i+"["+("object"==typeof t&&null!=t?e:"")+"]",t,a,o)});else if(a||"object"!==w.type(e))o(i,e);else for(var t in e)n(i+"["+t+"]",e[t],a,o)}(n,e[n],t,a);return i.join("&").replace(an,"+")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=w.prop(this,"elements");return e?w.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!w(this).is(":disabled")&&sn.test(this.nodeName)&&!ln.test(e)&&(this.checked||!ye.test(e))}).map(function(e,t){var n=w(this).val();return null==n?null:w.isArray(n)?w.map(n,function(e){return{name:t.name,value:e.replace(rn,"\r\n")}}):{name:t.name,value:n.replace(rn,"\r\n")}}).get()}}),w.ajaxSettings.xhr=k.ActiveXObject!==undefined?function(){return this.isLocal?fn():8<h.documentMode?dn():/^(get|post|head|put|delete|options)$/i.test(this.type)&&dn()||fn()}:dn;var cn=0,un={},F=w.ajaxSettings.xhr();function dn(){try{return new k.XMLHttpRequest}catch(e){}}function fn(){try{return new k.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}k.attachEvent&&k.attachEvent("onunload",function(){for(var e in un)un[e](undefined,!0)}),m.cors=!!F&&"withCredentials"in F,(F=m.ajax=!!F)&&w.ajaxTransport(function(s){var c;if(!s.crossDomain||m.cors)return{send:function(e,o){var t,r=s.xhr(),l=++cn;if(r.open(s.type,s.url,s["async"],s.username,s.password),s.xhrFields)for(t in s.xhrFields)r[t]=s.xhrFields[t];for(t in s.mimeType&&r.overrideMimeType&&r.overrideMimeType(s.mimeType),s.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)e[t]!==undefined&&r.setRequestHeader(t,e[t]+"");r.send(s.hasContent&&s.data||null),c=function(e,t){var n,i,a;if(c&&(t||4===r.readyState))if(delete un[l],c=undefined,r.onreadystatechange=w.noop,t)4!==r.readyState&&r.abort();else{a={},n=r.status,"string"==typeof r.responseText&&(a.text=r.responseText);try{i=r.statusText}catch(e){i=""}n||!s.isLocal||s.crossDomain?1223===n&&(n=204):n=a.text?200:404}a&&o(n,i,a,r.getAllResponseHeaders())},s["async"]?4===r.readyState?k.setTimeout(c):r.onreadystatechange=un[l]=c:c()},abort:function(){c&&c(undefined,!0)}}}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return w.globalEval(e),e}}}),w.ajaxPrefilter("script",function(e){e.cache===undefined&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),w.ajaxTransport("script",function(t){var i,a;if(t.crossDomain)return a=h.head||w("head")[0]||h.documentElement,{send:function(e,n){(i=h.createElement("script"))["async"]=!0,t.scriptCharset&&(i.charset=t.scriptCharset),i.src=t.url,i.onload=i.onreadystatechange=function(e,t){!t&&i.readyState&&!/loaded|complete/.test(i.readyState)||(i.onload=i.onreadystatechange=null,i.parentNode&&i.parentNode.removeChild(i),i=null,t||n(200,"success"))},a.insertBefore(i,a.firstChild)},abort:function(){i&&i.onload(undefined,!0)}}});var pn=[],hn=/(=)\?(?=&|$)|\?\?/,yn=(w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=pn.pop()||w.expando+"_"+Bt++;return this[e]=!0,e}}),w.ajaxPrefilter("json jsonp",function(e,t,n){var i,a,o,r=!1!==e.jsonp&&(hn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&hn.test(e.data)&&"data");if(r||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=w.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,r?e[r]=e[r].replace(hn,"$1"+i):!1!==e.jsonp&&(e.url+=(Ot.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||w.error(i+" was not called"),o[0]},e.dataTypes[0]="json",a=k[i],k[i]=function(){o=arguments},n.always(function(){a===undefined?w(k).removeProp(i):k[i]=a,e[i]&&(e.jsonpCallback=t.jsonpCallback,pn.push(i)),o&&w.isFunction(a)&&a(o[0]),o=a=undefined}),"script"}),w.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||h;var i=K.exec(e),n=!n&&[];return i?[t.createElement(i[1])]:(i=Ee([e],t,n),n&&n.length&&w(n).remove(),w.merge([],i.childNodes))},w.fn.load);function mn(e){return w.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}return w.fn.load=function(e,t,n){if("string"!=typeof e&&yn)return yn.apply(this,arguments);var i,a,o,r=this,l=e.indexOf(" ");return-1<l&&(i=w.trim(e.slice(l,e.length)),e=e.slice(0,l)),w.isFunction(t)?(n=t,t=undefined):t&&"object"==typeof t&&(a="POST"),0<r.length&&w.ajax({url:e,type:a||"GET",dataType:"html",data:t}).done(function(e){o=arguments,r.html(i?w("<div>").append(w.parseHTML(e)).find(i):e)}).always(n&&function(e,t){r.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){w.fn[t]=function(e){return this.on(t,e)}}),w.expr.filters.animated=function(t){return w.grep(w.timers,function(e){return t===e.elem}).length},w.offset={setOffset:function(e,t,n){var i,a,o,r,l=w.css(e,"position"),s=w(e),c={};"static"===l&&(e.style.position="relative"),o=s.offset(),i=w.css(e,"top"),r=w.css(e,"left"),l=("absolute"===l||"fixed"===l)&&-1<w.inArray("auto",[i,r])?(a=(l=s.position()).top,l.left):(a=parseFloat(i)||0,parseFloat(r)||0),null!=(t=w.isFunction(t)?t.call(e,n,w.extend({},o)):t).top&&(c.top=t.top-o.top+a),null!=t.left&&(c.left=t.left-o.left+l),"using"in t?t.using.call(e,c):s.css(c)}},w.fn.extend({offset:function(t){if(arguments.length)return t===undefined?this:this.each(function(e){w.offset.setOffset(this,t,e)});var e,n={top:0,left:0},i=this[0],a=i&&i.ownerDocument;return a?(e=a.documentElement,w.contains(e,i)?("undefined"!=typeof i.getBoundingClientRect&&(n=i.getBoundingClientRect()),i=mn(a),{top:n.top+(i.pageYOffset||e.scrollTop)-(e.clientTop||0),left:n.left+(i.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):n):void 0},position:function(){var e,t,n,i;if(this[0])return n={top:0,left:0},i=this[0],"fixed"===w.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),(n=w.nodeName(e[0],"html")?n:e.offset()).top+=w.css(e[0],"borderTopWidth",!0),n.left+=w.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-w.css(i,"marginTop",!0),left:t.left-n.left-w.css(i,"marginLeft",!0)}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&!w.nodeName(e,"html")&&"static"===w.css(e,"position");)e=e.offsetParent;return e||ct})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,a){var o=/Y/.test(a);w.fn[t]=function(e){return f(this,function(e,t,n){var i=mn(e);if(n===undefined)return i?a in i?i[a]:i.document.documentElement[t]:e[t];i?i.scrollTo(o?w(i).scrollLeft():n,o?n:w(i).scrollTop()):e[t]=n},t,e,arguments.length,null)}}),w.each(["top","left"],function(e,n){w.cssHooks[n]=dt(m.pixelPosition,function(e,t){if(t)return t=p(e,n),lt.test(t)?w(e).position()[n]+"px":t})}),w.each({Height:"height",Width:"width"},function(o,r){w.each({padding:"inner"+o,content:r,"":"outer"+o},function(i,e){w.fn[e]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),a=i||(!0===e||!0===t?"margin":"border");return f(this,function(e,t,n){var i;return w.isWindow(e)?e.document.documentElement["client"+o]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+o],i["scroll"+o],e.body["offset"+o],i["offset"+o],i["client"+o])):n===undefined?w.css(e,t,a):w.style(e,t,n,a)},r,n?e:undefined,n,null)}})}),w.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),w.fn.size=function(){return this.length},w.fn.andSelf=w.fn.addBack,layui.define(function(e){e("jquery",layui.$=w)}),w});!function(h){"use strict";var y,c,e,i=h.layui&&layui.define,u={getPath:(e=document.currentScript?document.currentScript.src:function(){for(var e,t=document.scripts,n=t.length-1,i=n;0<i;i--)if("interactive"===t[i].readyState){e=t[i].src;break}return e||t[n].src}(),(h.LAYUI_GLOBAL||{}).layer_dir||e.substring(0,e.lastIndexOf("/")+1)),config:{},end:{},minIndex:0,minLeft:[],btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],type:["dialog","page","iframe","loading","tips"],getStyle:function(e,t){e=e.currentStyle||h.getComputedStyle(e,null);return e[e.getPropertyValue?"getPropertyValue":"getAttribute"](t)},link:function(e,i,t){var n,a,o,r,l,s;m.path&&(n=document.getElementsByTagName("head")[0],a=document.createElement("link"),o=((t="string"==typeof i?i:t)||e).replace(/\.|\//g,""),r="layuicss-"+o,l="creating",s=0,a.rel="stylesheet",a.href=m.path+e,a.id=r,document.getElementById(r)||n.appendChild(a),"function"==typeof i&&function e(t){var n=document.getElementById(r);return 100<++s?h.console&&console.error(o+".css: Invalid"):void(1989===parseInt(u.getStyle(n,"width"))?(t===l&&n.removeAttribute("lay-status"),n.getAttribute("lay-status")===l?setTimeout(e,100):i()):(n.setAttribute("lay-status",l),setTimeout(function(){e(l)},100)))}())}},m={v:"3.5.1",ie:(e=navigator.userAgent.toLowerCase(),!!(h.ActiveXObject||"ActiveXObject"in h)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")),index:h.layer&&h.layer.v?1e5:0,path:u.getPath,config:function(e,t){return m.cache=u.config=y.extend({},u.config,e=e||{}),m.path=u.config.path||m.path,"string"==typeof e.extend&&(e.extend=[e.extend]),u.config.path&&m.ready(),e.extend&&(i?layui.addcss("modules/layer/"+e.extend):u.link("theme/"+e.extend)),this},ready:function(e){var t="layer",n=(i?"modules/layer/":"theme/")+"default/layer.css?v="+m.v;return i?layui.addcss(n,e,t):u.link(n,e,t),this},alert:function(e,t,n){var i="function"==typeof t;return m.open(y.extend({content:e,yes:n=i?t:n},i?{}:t))},confirm:function(e,t,n,i){var a="function"==typeof t;return a&&(i=n,n=t),m.open(y.extend({content:e,btn:u.btn,yes:n,btn2:i},a?{}:t))},msg:function(e,t,n){var i="function"==typeof t,a=u.config.skin,a=(a?a+" "+a+"-msg":"")||"layui-layer-msg",o=d.anim.length-1;return i&&(n=t),m.open(y.extend({content:e,time:3e3,shade:!1,skin:a,title:!1,closeBtn:!1,btn:!1,resize:!1,end:n},i&&!u.config.skin?{skin:a+" layui-layer-hui",anim:o}:(-1!==(t=t||{}).icon&&(void 0!==t.icon||u.config.skin)||(t.skin=a+" "+(t.skin||"layui-layer-hui")),t)))},load:function(e,t){return m.open(y.extend({type:3,icon:e||0,resize:!1,shade:.01},t))},tips:function(e,t,n){return m.open(y.extend({type:4,content:[e,t],closeBtn:!1,time:3e3,shade:!1,resize:!1,fixed:!1,maxWidth:260},n))}},t=function(e){var t=this,n=function(){t.creat()};t.index=++m.index,t.config.maxWidth=y(c).width()-30,t.config=y.extend({},t.config,u.config,e),document.body?n():setTimeout(function(){n()},30)},d=(t.pt=t.prototype,["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"]),n=(d.anim=["layer-anim-00","layer-anim-01","layer-anim-02","layer-anim-03","layer-anim-04","layer-anim-05","layer-anim-06"],d.SHADE="layui-layer-shade",d.MOVE="layui-layer-move",t.pt.config={type:0,shade:.3,fixed:!0,move:d[1],title:"&#x4FE1;&#x606F;",offset:"auto",area:"auto",closeBtn:1,time:0,zIndex:19891014,maxWidth:360,anim:0,isOutAnim:!0,minStack:!0,icon:-1,moveType:1,resize:!0,scrollbar:!0,tips:2},t.pt.vessel=function(e,t){var n=this.index,i=this.config,a=i.zIndex+n,o="object"==typeof i.title,r=i.maxmin&&(1===i.type||2===i.type),o=i.title?'<div class="layui-layer-title" style="'+(o?i.title[1]:"")+'">'+(o?i.title[0]:i.title)+"</div>":"";return i.zIndex=a,t([i.shade?'<div class="'+d.SHADE+'" id="'+d.SHADE+n+'" times="'+n+'" style="z-index:'+(a-1)+'; "></div>':"",'<div class="'+d[0]+" layui-layer-"+u.type[i.type]+(0!=i.type&&2!=i.type||i.shade?"":" layui-layer-border")+" "+(i.skin||"")+'" id="'+d[0]+n+'" type="'+u.type[i.type]+'" times="'+n+'" showtime="'+i.time+'" conType="'+(e?"object":"string")+'" style="z-index: '+a+"; width:"+i.area[0]+";height:"+i.area[1]+";position:"+(i.fixed?"fixed;":"absolute;")+'">'+(e&&2!=i.type?"":o)+'<div id="'+(i.id||"")+'" class="layui-layer-content'+(0==i.type&&-1!==i.icon?" layui-layer-padding":"")+(3==i.type?" layui-layer-loading"+i.icon:"")+'">'+(0==i.type&&-1!==i.icon?'<i class="layui-layer-ico layui-layer-ico'+i.icon+'"></i>':"")+((1!=i.type||!e)&&i.content||"")+'</div><span class="layui-layer-setwin">'+(n=r?'<a class="layui-layer-min" href="javascript:;"><cite></cite></a><a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>':"",i.closeBtn&&(n+='<a class="layui-layer-ico '+d[7]+" "+d[7]+(i.title?i.closeBtn:4==i.type?"1":"2")+'" href="javascript:;"></a>'),n)+"</span>"+(i.btn?function(){var e="";"string"==typeof i.btn&&(i.btn=[i.btn]);for(var t=0,n=i.btn.length;t<n;t++)e+='<a class="'+d[6]+t+'">'+i.btn[t]+"</a>";return'<div class="'+d[6]+" layui-layer-btn-"+(i.btnAlign||"")+'">'+e+"</div>"}():"")+(i.resize?'<span class="layui-layer-resize"></span>':"")+"</div>"],o,y('<div class="'+d.MOVE+'" id="'+d.MOVE+'"></div>')),this},t.pt.creat=function(){var e,i=this,a=i.config,o=i.index,r="object"==typeof(s=a.content),l=y("body");if(!a.id||!y("#"+a.id)[0]){switch("string"==typeof a.area&&(a.area="auto"===a.area?["",""]:[a.area,""]),a.shift&&(a.anim=a.shift),6==m.ie&&(a.fixed=!1),a.type){case 0:a.btn="btn"in a?a.btn:u.btn[0],m.closeAll("dialog");break;case 2:var s=a.content=r?a.content:[a.content||"","auto"];a.content='<iframe scrolling="'+(a.content[1]||"auto")+'" allowtransparency="true" id="'+d[4]+o+'" name="'+d[4]+o+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+a.content[0]+'"></iframe>';break;case 3:delete a.title,delete a.closeBtn,-1===a.icon&&a.icon,m.closeAll("loading");break;case 4:r||(a.content=[a.content,"body"]),a.follow=a.content[1],a.content=a.content[0]+'<i class="layui-layer-TipsG"></i>',delete a.title,a.tips="object"==typeof a.tips?a.tips:[a.tips,!0],a.tipsMore||m.closeAll("tips")}i.vessel(r,function(e,t,n){l.append(e[0]),r?2==a.type||4==a.type?y("body").append(e[1]):s.parents("."+d[0])[0]||(s.data("display",s.css("display")).show().addClass("layui-layer-wrap").wrap(e[1]),y("#"+d[0]+o).find("."+d[5]).before(t)):l.append(e[1]),y("#"+d.MOVE)[0]||l.append(u.moveElem=n),i.layero=y("#"+d[0]+o),i.shadeo=y("#"+d.SHADE+o),a.scrollbar||d.html.css("overflow","hidden").attr("layer-full",o)}).auto(o),i.shadeo.css({"background-color":a.shade[1]||"#000",opacity:a.shade[0]||a.shade}),2==a.type&&6==m.ie&&i.layero.find("iframe").attr("src",s[0]),4==a.type?i.tips():(i.offset(),parseInt(u.getStyle(document.getElementById(d.MOVE),"z-index"))||(i.layero.css("visibility","hidden"),m.ready(function(){i.offset(),i.layero.css("visibility","visible")}))),a.fixed&&c.on("resize",function(){i.offset(),(/^\d+%$/.test(a.area[0])||/^\d+%$/.test(a.area[1]))&&i.auto(o),4==a.type&&i.tips()}),a.time<=0||setTimeout(function(){m.close(i.index)},a.time),i.move().callback(),d.anim[a.anim]&&(e="layer-anim "+d.anim[a.anim],i.layero.addClass(e).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){y(this).removeClass(e)})),a.isOutAnim&&i.layero.data("isOutAnim",!0)}},t.pt.auto=function(e){var t=this.config,n=y("#"+d[0]+e),i=(""===t.area[0]&&0<t.maxWidth&&(m.ie&&m.ie<8&&t.btn&&n.width(n.innerWidth()),n.outerWidth()>t.maxWidth&&n.width(t.maxWidth)),[n.innerWidth(),n.innerHeight()]),a=n.find(d[1]).outerHeight()||0,o=n.find("."+d[6]).outerHeight()||0,e=function(e){(e=n.find(e)).height(i[1]-a-o-2*(0|parseFloat(e.css("padding-top"))))};return 2===t.type?e("iframe"):""===t.area[1]?0<t.maxHeight&&n.outerHeight()>t.maxHeight?(i[1]=t.maxHeight,e("."+d[5])):t.fixed&&i[1]>=c.height()&&(i[1]=c.height(),e("."+d[5])):e("."+d[5]),this},t.pt.offset=function(){var e=this,t=e.config,n=e.layero,i=[n.outerWidth(),n.outerHeight()],a="object"==typeof t.offset;e.offsetTop=(c.height()-i[1])/2,e.offsetLeft=(c.width()-i[0])/2,a?(e.offsetTop=t.offset[0],e.offsetLeft=t.offset[1]||e.offsetLeft):"auto"!==t.offset&&("t"===t.offset?e.offsetTop=0:"r"===t.offset?e.offsetLeft=c.width()-i[0]:"b"===t.offset?e.offsetTop=c.height()-i[1]:"l"===t.offset?e.offsetLeft=0:"lt"===t.offset?(e.offsetTop=0,e.offsetLeft=0):"lb"===t.offset?(e.offsetTop=c.height()-i[1],e.offsetLeft=0):"rt"===t.offset?(e.offsetTop=0,e.offsetLeft=c.width()-i[0]):"rb"===t.offset?(e.offsetTop=c.height()-i[1],e.offsetLeft=c.width()-i[0]):e.offsetTop=t.offset),t.fixed||(e.offsetTop=/%$/.test(e.offsetTop)?c.height()*parseFloat(e.offsetTop)/100:parseFloat(e.offsetTop),e.offsetLeft=/%$/.test(e.offsetLeft)?c.width()*parseFloat(e.offsetLeft)/100:parseFloat(e.offsetLeft),e.offsetTop+=c.scrollTop(),e.offsetLeft+=c.scrollLeft()),n.attr("minLeft")&&(e.offsetTop=c.height()-(n.find(d[1]).outerHeight()||0),e.offsetLeft=n.css("left")),n.css({top:e.offsetTop,left:e.offsetLeft})},t.pt.tips=function(){var e=this.config,t=this.layero,n=[t.outerWidth(),t.outerHeight()],i=y(e.follow),a={width:(i=i[0]?i:y("body")).outerWidth(),height:i.outerHeight(),top:i.offset().top,left:i.offset().left},o=t.find(".layui-layer-TipsG"),i=e.tips[0];e.tips[1]||o.remove(),a.autoLeft=function(){0<a.left+n[0]-c.width()?(a.tipLeft=a.left+a.width-n[0],o.css({right:12,left:"auto"})):a.tipLeft=a.left},a.where=[function(){a.autoLeft(),a.tipTop=a.top-n[1]-10,o.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",e.tips[1])},function(){a.tipLeft=a.left+a.width+10,a.tipTop=a.top,o.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",e.tips[1])},function(){a.autoLeft(),a.tipTop=a.top+a.height+10,o.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",e.tips[1])},function(){a.tipLeft=a.left-n[0]-10,a.tipTop=a.top,o.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",e.tips[1])}],a.where[i-1](),1===i?a.top-(c.scrollTop()+n[1]+16)<0&&a.where[2]():2===i?0<c.width()-(a.left+a.width+n[0]+16)||a.where[3]():3===i?0<a.top-c.scrollTop()+a.height+n[1]+16-c.height()&&a.where[0]():4===i&&0<n[0]+16-a.left&&a.where[1](),t.find("."+d[5]).css({"background-color":e.tips[1],"padding-right":e.closeBtn?"30px":""}),t.css({left:a.tipLeft-(e.fixed?c.scrollLeft():0),top:a.tipTop-(e.fixed?c.scrollTop():0)})},t.pt.move=function(){var o=this,r=o.config,e=y(document),l=o.layero,t=l.find(r.move),n=l.find(".layui-layer-resize"),s={};return r.move&&t.css("cursor","move"),t.on("mousedown",function(e){e.preventDefault(),r.move&&(s.moveStart=!0,s.offset=[e.clientX-parseFloat(l.css("left")),e.clientY-parseFloat(l.css("top"))],u.moveElem.css("cursor","move").show())}),n.on("mousedown",function(e){e.preventDefault(),s.resizeStart=!0,s.offset=[e.clientX,e.clientY],s.area=[l.outerWidth(),l.outerHeight()],u.moveElem.css("cursor","se-resize").show()}),e.on("mousemove",function(e){var t,n,i,a;s.moveStart&&(i=e.clientX-s.offset[0],a=e.clientY-s.offset[1],t="fixed"===l.css("position"),e.preventDefault(),s.stX=t?0:c.scrollLeft(),s.stY=t?0:c.scrollTop(),r.moveOut||(t=c.width()-l.outerWidth()+s.stX,n=c.height()-l.outerHeight()+s.stY,t<(i=i<s.stX?s.stX:i)&&(i=t),n<(a=a<s.stY?s.stY:a)&&(a=n)),l.css({left:i,top:a})),r.resize&&s.resizeStart&&(i=e.clientX-s.offset[0],a=e.clientY-s.offset[1],e.preventDefault(),m.style(o.index,{width:s.area[0]+i,height:s.area[1]+a}),s.isResize=!0,r.resizing&&r.resizing(l))}).on("mouseup",function(e){s.moveStart&&(delete s.moveStart,u.moveElem.hide(),r.moveEnd&&r.moveEnd(l)),s.resizeStart&&(delete s.resizeStart,u.moveElem.hide())}),o},t.pt.callback=function(){var t=this,n=t.layero,i=t.config;t.openLayer(),i.success&&(2==i.type?n.find("iframe").on("load",function(){i.success(n,t.index,t)}):i.success(n,t.index,t)),6==m.ie&&t.IE6(n),n.find("."+d[6]).children("a").on("click",function(){var e=y(this).index();0===e?i.yes?i.yes(t.index,n):i.btn1?i.btn1(t.index,n):m.close(t.index):!1!==(i["btn"+(e+1)]&&i["btn"+(e+1)](t.index,n))&&m.close(t.index)}),n.find("."+d[7]).on("click",function(){!1!==(i.cancel&&i.cancel(t.index,n))&&m.close(t.index)}),i.shadeClose&&t.shadeo.on("click",function(){m.close(t.index)}),n.find(".layui-layer-min").on("click",function(){!1!==(i.min&&i.min(n,t.index))&&m.min(t.index,i)}),n.find(".layui-layer-max").on("click",function(){y(this).hasClass("layui-layer-maxmin")?(m.restore(t.index),i.restore&&i.restore(n,t.index)):(m.full(t.index,i),setTimeout(function(){i.full&&i.full(n,t.index)},100))}),i.end&&(u.end[t.index]=i.end)},u.reselect=function(){y.each(y("select"),function(e,t){var n=y(this);n.parents("."+d[0])[0]||1==n.attr("layer")&&y("."+d[0]).length<1&&n.removeAttr("layer").show()})},t.pt.IE6=function(e){y("select").each(function(e,t){var n=y(this);n.parents("."+d[0])[0]||"none"!==n.css("display")&&n.attr({layer:"1"}).hide()})},t.pt.openLayer=function(){m.zIndex=this.config.zIndex,m.setTop=function(e){return m.zIndex=parseInt(e[0].style.zIndex),e.on("mousedown",function(){m.zIndex++,e.css("z-index",m.zIndex+1)}),m.zIndex}},u.record=function(e){var t=[e.width(),e.height(),e.position().top,e.position().left+parseFloat(e.css("margin-left"))];e.find(".layui-layer-max").addClass("layui-layer-maxmin"),e.attr({area:t})},u.rescollbar=function(e){d.html.attr("layer-full")==e&&(d.html[0].style.removeProperty?d.html[0].style.removeProperty("overflow"):d.html[0].style.removeAttribute("overflow"),d.html.removeAttr("layer-full"))},(h.layer=m).getChildFrame=function(e,t){return t=t||y("."+d[4]).attr("times"),y("#"+d[0]+t).find("iframe").contents().find(e)},m.getFrameIndex=function(e){return y("#"+e).parents("."+d[4]).attr("times")},m.iframeAuto=function(e){var t,n,i;e&&(t=m.getChildFrame("html",e).outerHeight(),n=(e=y("#"+d[0]+e)).find(d[1]).outerHeight()||0,i=e.find("."+d[6]).outerHeight()||0,e.css({height:t+n+i}),e.find("iframe").css({height:t}))},m.iframeSrc=function(e,t){y("#"+d[0]+e).find("iframe").attr("src",t)},m.style=function(e,t,n){var e=y("#"+d[0]+e),i=e.find(".layui-layer-content"),a=e.attr("type"),o=e.find(d[1]).outerHeight()||0,r=e.find("."+d[6]).outerHeight()||0;e.attr("minLeft");a!==u.type[3]&&a!==u.type[4]&&(n||(parseFloat(t.width)<=260&&(t.width=260),parseFloat(t.height)-o-r<=64&&(t.height=64+o+r)),e.css(t),r=e.find("."+d[6]).outerHeight()||0,a===u.type[2]?e.find("iframe").css({height:parseFloat(t.height)-o-r}):i.css({height:parseFloat(t.height)-o-r-parseFloat(i.css("padding-top"))-parseFloat(i.css("padding-bottom"))}))},m.min=function(e,t){t=t||{};var n=y("#"+d[0]+e),i=y("#"+d.SHADE+e),a=n.find(d[1]).outerHeight()||0,o=n.attr("minLeft")||181*u.minIndex+"px",r=n.css("position"),l={width:180,height:a,position:"fixed",overflow:"hidden"};u.record(n),u.minLeft[0]&&(o=u.minLeft[0],u.minLeft.shift()),t.minStack&&(l.left=o,l.top=c.height()-a,n.attr("minLeft")||u.minIndex++,n.attr("minLeft",o)),n.attr("position",r),m.style(e,l,!0),n.find(".layui-layer-min").hide(),"page"===n.attr("type")&&n.find(d[4]).hide(),u.rescollbar(e),i.hide()},m.restore=function(e){var t=y("#"+d[0]+e),n=y("#"+d.SHADE+e),i=t.attr("area").split(",");t.attr("type");m.style(e,{width:parseFloat(i[0]),height:parseFloat(i[1]),top:parseFloat(i[2]),left:parseFloat(i[3]),position:t.attr("position"),overflow:"visible"},!0),t.find(".layui-layer-max").removeClass("layui-layer-maxmin"),t.find(".layui-layer-min").show(),"page"===t.attr("type")&&t.find(d[4]).show(),u.rescollbar(e),n.show()},m.full=function(t){var n=y("#"+d[0]+t);u.record(n),d.html.attr("layer-full")||d.html.css("overflow","hidden").attr("layer-full",t),clearTimeout(void 0),setTimeout(function(){var e="fixed"===n.css("position");m.style(t,{top:e?0:c.scrollTop(),left:e?0:c.scrollLeft(),width:c.width(),height:c.height()},!0),n.find(".layui-layer-min").hide()},100)},m.title=function(e,t){y("#"+d[0]+(t||m.index)).find(d[1]).html(e)},m.close=function(i,a){var o,e,r=y("#"+d[0]+i),l=r.attr("type");r[0]&&(o="layui-layer-wrap",e=function(){if(l===u.type[1]&&"object"===r.attr("conType")){r.children(":not(."+d[5]+")").remove();for(var e=r.find("."+o),t=0;t<2;t++)e.unwrap();e.css("display",e.data("display")).removeClass(o)}else{if(l===u.type[2])try{var n=y("#"+d[4]+i)[0];n.contentWindow.document.write(""),n.contentWindow.close(),r.find("."+d[5])[0].removeChild(n)}catch(e){}r[0].innerHTML="",r.remove()}"function"==typeof u.end[i]&&u.end[i](),delete u.end[i],"function"==typeof a&&a()},r.data("isOutAnim")&&r.addClass("layer-anim layer-anim-close"),y("#layui-layer-moves, #"+d.SHADE+i).remove(),6==m.ie&&u.reselect(),u.rescollbar(i),r.attr("minLeft")&&(u.minIndex--,u.minLeft.push(r.attr("minLeft"))),m.ie&&m.ie<10||!r.data("isOutAnim")?e():setTimeout(function(){e()},200))},m.closeAll=function(i,a){"function"==typeof i&&(a=i,i=null);var o=y("."+d[0]);y.each(o,function(e){var t=y(this),n=i?t.attr("type")===i:1;n&&m.close(t.attr("times"),e===o.length-1?a:null)}),0===o.length&&"function"==typeof a&&a()},m.cache||{}),g=function(e){return n.skin?" "+n.skin+" "+n.skin+"-"+e:""};m.prompt=function(n,i){var e="";"function"==typeof(n=n||{})&&(i=n),n.area&&(e='style="width: '+(t=n.area)[0]+"; height: "+t[1]+';"',delete n.area);var a,t=2==n.formType?'<textarea class="layui-layer-input"'+e+"></textarea>":'<input type="'+(1==n.formType?"password":"text")+'" class="layui-layer-input">',o=n.success;return delete n.success,m.open(y.extend({type:1,btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],content:t,skin:"layui-layer-prompt"+g("prompt"),maxWidth:c.width(),success:function(e){(a=e.find(".layui-layer-input")).val(n.value||"").focus(),"function"==typeof o&&o(e)},resize:!1,yes:function(e){var t=a.val();""===t?a.focus():t.length>(n.maxlength||500)?m.tips("&#x6700;&#x591A;&#x8F93;&#x5165;"+(n.maxlength||500)+"&#x4E2A;&#x5B57;&#x6570;",a,{tips:1}):i&&i(t,e,a)}},n))},m.tab=function(i){var a=(i=i||{}).tab||{},o="layui-this",r=i.success;return delete i.success,m.open(y.extend({type:1,skin:"layui-layer-tab"+g("tab"),resize:!1,title:function(){var e=a.length,t=1,n="";if(0<e)for(n='<span class="'+o+'">'+a[0].title+"</span>";t<e;t++)n+="<span>"+a[t].title+"</span>";return n}(),content:'<ul class="layui-layer-tabmain">'+function(){var e=a.length,t=1,n="";if(0<e)for(n='<li class="layui-layer-tabli '+o+'">'+(a[0].content||"no content")+"</li>";t<e;t++)n+='<li class="layui-layer-tabli">'+(a[t].content||"no  content")+"</li>";return n}()+"</ul>",success:function(e){var t=e.find(".layui-layer-title").children(),n=e.find(".layui-layer-tabmain").children();t.on("mousedown",function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0;var e=y(this),t=e.index();e.addClass(o).siblings().removeClass(o),n.eq(t).show().siblings().hide(),"function"==typeof i.change&&i.change(t)}),"function"==typeof r&&r(e)}},i))},m.photos=function(n,e,i){var a={};if((n=n||{}).photos){var t=!("string"==typeof n.photos||n.photos instanceof y),o=t?n.photos:{},r=o.data||[],l=o.start||0,s=(a.imgIndex=1+(0|l),n.img=n.img||"img",n.success);if(delete n.success,t){if(0===r.length)return m.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")}else{var c=y(n.photos),u=function(){r=[],c.find(n.img).each(function(e){var t=y(this);t.attr("layer-index",e),r.push({alt:t.attr("alt"),pid:t.attr("layer-pid"),src:t.attr("layer-src")||t.attr("src"),thumb:t.attr("src")})})};if(u(),0===r.length)return;if(e||c.on("click",n.img,function(){u();var e=y(this).attr("layer-index");m.photos(y.extend(n,{photos:{start:e,data:r,tab:n.tab},full:n.full}),!0)}),!e)return}a.imgprev=function(e){a.imgIndex--,a.imgIndex<1&&(a.imgIndex=r.length),a.tabimg(e)},a.imgnext=function(e,t){a.imgIndex++,a.imgIndex>r.length&&(a.imgIndex=1,t)||a.tabimg(e)},a.keyup=function(e){var t;a.end||(t=e.keyCode,e.preventDefault(),37===t?a.imgprev(!0):39===t?a.imgnext(!0):27===t&&m.close(a.index))},a.tabimg=function(e){if(!(r.length<=1))return o.start=a.imgIndex-1,m.close(a.index),m.photos(n,!0,e)},a.event=function(){a.bigimg.find(".layui-layer-imgprev").on("click",function(e){e.preventDefault(),a.imgprev(!0)}),a.bigimg.find(".layui-layer-imgnext").on("click",function(e){e.preventDefault(),a.imgnext(!0)}),y(document).on("keyup",a.keyup)},a.loadi=m.load(1,{shade:!("shade"in n)&&.9,scrollbar:!1});var t=r[l].src,d=function(e){var t;m.close(a.loadi),i&&(n.anim=-1),a.index=m.open(y.extend({type:1,id:"layui-layer-photos",area:(e=[e.width,e.height],t=[y(h).width()-100,y(h).height()-100],!n.full&&(e[0]>t[0]||e[1]>t[1])&&((t=[e[0]/t[0],e[1]/t[1]])[1]<t[0]?(e[0]=e[0]/t[0],e[1]=e[1]/t[0]):t[0]<t[1]&&(e[0]=e[0]/t[1],e[1]=e[1]/t[1])),[e[0]+"px",e[1]+"px"]),title:!1,shade:.9,shadeClose:!0,closeBtn:!1,move:".layui-layer-phimg img",moveType:1,scrollbar:!1,moveOut:!0,anim:5,isOutAnim:!1,skin:"layui-layer-photos"+g("photos"),content:'<div class="layui-layer-phimg"><img src="'+r[l].src+'" alt="'+(r[l].alt||"")+'" layer-pid="'+r[l].pid+'">'+(1<r.length?'<div class="layui-layer-imgsee"><span class="layui-layer-imguide"><a href="javascript:;" class="layui-layer-iconext layui-layer-imgprev"></a><a href="javascript:;" class="layui-layer-iconext layui-layer-imgnext"></a></span><div class="layui-layer-imgbar" style="display:'+(i?"block":"")+'"><span class="layui-layer-imgtit"><a href="javascript:;">'+(r[l].alt||"")+"</a><em>"+a.imgIndex+" / "+r.length+"</em></span></div></div>":"")+"</div>",success:function(e,t){a.bigimg=e.find(".layui-layer-phimg"),a.imgsee=e.find(".layui-layer-imgbar"),a.event(e),n.tab&&n.tab(r[l],e),"function"==typeof s&&s(e)},end:function(){a.end=!0,y(document).off("keyup",a.keyup)}},n))},f=function(){m.close(a.loadi),m.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;",{time:3e4,btn:["&#x4E0B;&#x4E00;&#x5F20;","&#x4E0D;&#x770B;&#x4E86;"],yes:function(){1<r.length&&a.imgnext(!0,!0)}})},p=new Image;(p.src=t,p.complete)?d(p):(p.onload=function(){p.onload=null,d(p)},p.onerror=function(e){p.onerror=null,f(e)})}},u.run=function(e){c=(y=e)(h),d.html=y("html"),m.open=function(e){return new t(e).index}},h.layui&&layui.define?(m.ready(),layui.define("jquery",function(e){m.path=layui.cache.dir,u.run(layui.$),e("layer",h.layer=m)})):"function"==typeof define&&define.amd?define(["jquery"],function(){return u.run(h.jQuery),m}):(m.ready(),u.run(h.jQuery))}(window);layui.define("jquery",function(e){"use strict";var u=layui.$,o=layui.hint(),a={fixbar:function(t){var e,n,i="layui-fixbar",a="layui-fixbar-top",o=u(document),r=u("body"),l=((t=u.extend({showHeight:200},t)).bar1=!0===t.bar1?"&#xe606;":t.bar1,t.bar2=!0===t.bar2?"&#xe607;":t.bar2,t.bgcolor=t.bgcolor?"background-color:"+t.bgcolor:"",[t.bar1,t.bar2,"&#xe604;"]),l=u(['<ul class="'+i+'">',t.bar1?'<li class="layui-icon" lay-type="bar1" style="'+t.bgcolor+'">'+l[0]+"</li>":"",t.bar2?'<li class="layui-icon" lay-type="bar2" style="'+t.bgcolor+'">'+l[1]+"</li>":"",'<li class="layui-icon '+a+'" lay-type="top" style="'+t.bgcolor+'">'+l[2]+"</li>","</ul>"].join("")),s=l.find("."+a),c=function(){o.scrollTop()>=t.showHeight?e||(s.show(),e=1):e&&(s.hide(),e=0)};u("."+i)[0]||("object"==typeof t.css&&l.css(t.css),r.append(l),c(),l.find("li").on("click",function(){var e=u(this).attr("lay-type");"top"===e&&u("html,body").animate({scrollTop:0},200),t.click&&t.click.call(this,e)}),o.on("scroll",function(){clearTimeout(n),n=setTimeout(function(){c()},100)}))},countdown:function(e,t,n){var i=this,a="function"==typeof t,o=new Date(e).getTime(),r=new Date(!t||a?(new Date).getTime():t).getTime(),o=o-r,l=[Math.floor(o/864e5),Math.floor(o/36e5)%24,Math.floor(o/6e4)%60,Math.floor(o/1e3)%60],a=(a&&(n=t),setTimeout(function(){i.countdown(e,r+1e3,n)},1e3));return n&&n(0<o?l:[0,0,0,0],t,a),o<=0&&clearTimeout(a),a},timeAgo:function(e,t){var n=this,i=[[],[]],a=(new Date).getTime()-new Date(e).getTime();return 26784e5<a?(a=new Date(e),i[0][0]=n.digit(a.getFullYear(),4),i[0][1]=n.digit(a.getMonth()+1),i[0][2]=n.digit(a.getDate()),t||(i[1][0]=n.digit(a.getHours()),i[1][1]=n.digit(a.getMinutes()),i[1][2]=n.digit(a.getSeconds())),i[0].join("-")+" "+i[1].join(":")):864e5<=a?(a/1e3/60/60/24|0)+"天前":36e5<=a?(a/1e3/60/60|0)+"小时前":18e4<=a?(a/1e3/60|0)+"分钟前":a<0?"未来":"刚刚"},digit:function(e,t){var n="";t=t||2;for(var i=(e=String(e)).length;i<t;i++)n+="0";return e<Math.pow(10,t)?n+(0|e):e},toDateString:function(e,t){if(null===e||""===e)return"";var n=this,i=new Date(function(){if(e)return!isNaN(e)&&"string"==typeof e?parseInt(e):e}()||new Date),a=[n.digit(i.getFullYear(),4),n.digit(i.getMonth()+1),n.digit(i.getDate())],n=[n.digit(i.getHours()),n.digit(i.getMinutes()),n.digit(i.getSeconds())];return i.getDate()?(t=t||"yyyy-MM-dd HH:mm:ss").replace(/yyyy/g,a[0]).replace(/MM/g,a[1]).replace(/dd/g,a[2]).replace(/HH/g,n[0]).replace(/mm/g,n[1]).replace(/ss/g,n[2]):(o.error('Invalid Msec for "util.toDateString(Msec)"'),"")},escape:function(e){return e===undefined||null===e?"":/[<"'>]|&(?=#[a-zA-Z0-9]+)/g.test(e+="")?e.replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;"):e},unescape:function(e){return e!==undefined&&null!==e||(e=""),(e+="").replace(/\&amp;/g,"&").replace(/\&lt;/g,"<").replace(/\&gt;/g,">").replace(/\&#39;/g,"'").replace(/\&quot;/g,'"')},toVisibleArea:function(e){var t,n,i,a,o,r,l,s;(e=u.extend({margin:160,duration:200,type:"y"},e)).scrollElem[0]&&e.thisElem[0]&&(t=e.scrollElem,l=e.thisElem,i=(o="y"===e.type)?"top":"left",a=t[n=o?"scrollTop":"scrollLeft"](),o=t[o?"height":"width"](),r=t.offset()[i],s={},((l=l.offset()[i]-r)>o-e.margin||l<e.margin)&&(s[n]=l-o/2+a,t.animate(s,e.duration)))},event:function(n,i,e){var t=u("body");return e=e||"click",i=a.event[n]=u.extend(!0,a.event[n],i)||{},a.event.UTIL_EVENT_CALLBACK=a.event.UTIL_EVENT_CALLBACK||{},t.off(e,"*["+n+"]",a.event.UTIL_EVENT_CALLBACK[n]),a.event.UTIL_EVENT_CALLBACK[n]=function(){var e=u(this),t=e.attr(n);"function"==typeof i[t]&&i[t].call(this,e)},t.on(e,"*["+n+"]",a.event.UTIL_EVENT_CALLBACK[n]),i}};a.on=a.event,e("util",a)});layui.define(["jquery","laytpl","lay"],function(e){"use strict";var i,t,n,u=layui.$,d=layui.laytpl,a=layui.hint(),o=layui.device().mobile?"click":"mousedown",r="dropdown",l="layui_"+r+"_index",f={config:{},index:layui[r]?layui[r].index+1e4:0,set:function(e){var t=this;return t.config=u.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,r,e,t)}},p=function(){var t=this,e=t.config,n=e.id;return p.that[n]=t,{config:e,reload:function(e){t.reload.call(t,e)}}},s="layui-menu-item-up",c="layui-menu-item-down",h="layui-menu-body-title",y="layui-menu-item-group",m="layui-menu-item-parent",g="layui-menu-item-checked",v="layui-menu-item-checked2",b="layui-menu-body-panel",x="layui-menu-body-panel-left",k="."+y+">."+h,w=function(e){var t=this;t.index=++f.index,t.config=u.extend({},t.config,f.config,e),t.init()};w.prototype.config={trigger:"click",content:"",className:"",style:"",show:!1,isAllowSpread:!0,isSpreadItem:!0,data:[],delay:300},w.prototype.reload=function(e){var t=this;t.config=u.extend({},t.config,e),t.init(!0)},w.prototype.init=function(e){var t=this,n=t.config,i=n.elem=u(n.elem);return 1<i.length?(layui.each(i,function(){f.render(u.extend({},n,{elem:this}))}),t):!e&&i[0]&&i.data(l)?(i=p.getThis(i.data(l)))?i.reload(n):void 0:(n.id="id"in n?n.id:t.index,n.show&&t.render(e),void t.events())},w.prototype.render=function(e){var i=this,s=i.config,t=u("body"),c=function(l,e){return layui.each(e,function(e,t){var n,i=t.child&&0<t.child.length,a=("isSpreadItem"in t?t:s).isSpreadItem,o=t.templet?d(t.templet).render(t):s.templet?d(s.templet).render(t):t.title,r=(i&&(t.type=t.type||"parent"),t.type?{group:"group",parent:"parent","-":"-"}[t.type]||"parent":"");("-"===r||t.title||t.id||i)&&((o=u(["<li"+(n={group:"layui-menu-item-group"+(s.isAllowSpread?a?" layui-menu-item-down":" layui-menu-item-up":""),parent:m,"-":"layui-menu-item-divider"},i||r?' class="'+n[r]+'"':"")+">",(n="href"in t?'<a href="'+t.href+'" target="'+(t.target||"_self")+'">'+o+"</a>":o,i?'<div class="'+h+'">'+n+("parent"===r?'<i class="layui-icon layui-icon-right"></i>':"group"===r&&s.isAllowSpread?'<i class="layui-icon layui-icon-'+(a?"up":"down")+'"></i>':"")+"</div>":'<div class="'+h+'">'+n+"</div>"),"</li>"].join(""))).data("item",t),i&&(a=u('<div class="layui-panel layui-menu-body-panel"></div>'),n=u("<ul></ul>"),"parent"===r?(a.append(c(n,t.child)),o.append(a)):o.append(c(n,t.child))),l.append(o))}),l},n=['<div class="layui-dropdown layui-border-box layui-panel layui-anim layui-anim-downbit">',"</div>"].join("");!(e="contextmenu"!==s.trigger&&!lay.isTopElem(s.elem[0])?e:!0)&&s.elem.data(l+"_opened")||(i.elemView=u(n),i.elemView.append(s.content||(e=u('<ul class="layui-menu layui-dropdown-menu"></ul>'),0<s.data.length?c(e,s.data):e.html('<li class="layui-menu-item-none">no menu</li>'),e)),s.className&&i.elemView.addClass(s.className),s.style&&i.elemView.attr("style",s.style),f.thisId=s.id,i.remove(),t.append(i.elemView),s.elem.data(l+"_opened",!0),i.position(),(p.prevElem=i.elemView).data("prevElem",s.elem),i.elemView.find(".layui-menu").on(o,function(e){layui.stope(e)}),i.elemView.find(".layui-menu li").on("click",function(e){var t=u(this),n=t.data("item")||{};n.child&&0<n.child.length||"-"===n.type||(i.remove(),"function"==typeof s.click&&s.click(n,t))}),i.elemView.find(k).on("click",function(e){var t=u(this).parent();"group"===(t.data("item")||{}).type&&s.isAllowSpread&&p.spread(t)}),"mouseenter"===s.trigger&&i.elemView.on("mouseenter",function(){clearTimeout(p.timer)}).on("mouseleave",function(){i.delayRemove()}))},w.prototype.position=function(e){var t=this.config;lay.position(t.elem[0],this.elemView[0],{position:t.position,e:this.e,clickType:"contextmenu"===t.trigger?"right":null,align:t.align||null})},w.prototype.remove=function(){this.config;var e=p.prevElem;e&&(e.data("prevElem")&&e.data("prevElem").data(l+"_opened",!1),e.remove())},w.prototype.delayRemove=function(){var e=this,t=e.config;clearTimeout(p.timer),p.timer=setTimeout(function(){e.remove()},t.delay)},w.prototype.events=function(){var t=this,n=t.config;"hover"===n.trigger&&(n.trigger="mouseenter"),t.prevElem&&t.prevElem.off(n.trigger,t.prevElemCallback),t.prevElem=n.elem,t.prevElemCallback=function(e){clearTimeout(p.timer),t.e=e,t.render(),e.preventDefault(),"function"==typeof n.ready&&n.ready(t.elemView,n.elem,t.e.target)},n.elem.on(n.trigger,t.prevElemCallback),"mouseenter"===n.trigger&&n.elem.on("mouseleave",function(){t.delayRemove()})},p.that={},p.getThis=function(e){var t=p.that[e];return t||a.error(e?r+" instance with ID '"+e+"' not found":"ID argument required"),t},p.spread=function(e){var t=e.children("."+h).find(".layui-icon");e.hasClass(s)?(e.removeClass(s).addClass(c),t.removeClass("layui-icon-down").addClass("layui-icon-up")):(e.removeClass(c).addClass(s),t.removeClass("layui-icon-up").addClass("layui-icon-down"))},i=u(window),t=u(document),i.on("resize",function(){if(f.thisId){var e=p.getThis(f.thisId);if(e){if(!e.elemView[0]||!u(".layui-dropdown")[0])return!1;"contextmenu"===e.config.trigger?e.remove():e.position()}}}),t.on(o,function(e){var t,n;!f.thisId||(t=p.getThis(f.thisId))&&(n=t.config,!lay.isTopElem(n.elem[0])&&"contextmenu"!==n.trigger&&(e.target===n.elem[0]||n.elem.find(e.target)[0]||e.target===t.elemView[0]||t.elemView&&t.elemView.find(e.target)[0])||t.remove())}),n=".layui-menu:not(.layui-dropdown-menu) li",t.on("click",n,function(e){var t=u(this),n=t.parents(".layui-menu").eq(0),i=t.hasClass(y)||t.hasClass(m),a=n.attr("lay-filter")||n.attr("id"),o=lay.options(this);t.hasClass("layui-menu-item-divider")||i||(n.find("."+g).removeClass(g),n.find("."+v).removeClass(v),t.addClass(g),t.parents("."+m).addClass(v),layui.event.call(this,r,"click("+a+")",o))}),t.on("click",n+k,function(e){var t=u(this).parents("."+y+":eq(0)"),n=lay.options(t[0]);"isAllowSpread"in n&&!n.isAllowSpread||p.spread(t)}),n=".layui-menu ."+m,t.on("mouseenter",n,function(e){var t,n=u(this).find("."+b);n[0]&&((t=n[0].getBoundingClientRect()).right>i.width()&&(n.addClass(x),(t=n[0].getBoundingClientRect()).left<0&&n.removeClass(x)),t.bottom>i.height()&&n.eq(0).css("margin-top",-(t.bottom-i.height()+5)))}).on("mouseleave",n,function(e){var t=u(this).children("."+b);t.removeClass(x),t.css("margin-top",0)}),f.reload=function(e,t){e=p.getThis(e);return e?(e.reload(t),p.call(e)):this},f.render=function(e){e=new w(e);return p.call(e)},e(r,f)});layui.define("jquery",function(e){"use strict";var m=layui.$,n={config:{},index:layui.slider?layui.slider.index+1e4:0,set:function(e){var t=this;return t.config=m.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,i,e,t)}},i="slider",d="layui-disabled",g="layui-slider-bar",v="layui-slider-wrap",b="layui-slider-wrap-btn",x="layui-slider-tips",k="layui-slider-input-txt",w="layui-slider-hover",t=function(e){var t=this;t.index=++n.index,t.config=m.extend({},t.config,n.config,e),t.render()};t.prototype.config={type:"default",min:0,max:100,value:0,step:1,showstep:!1,tips:!0,input:!1,range:!1,height:200,disabled:!1,theme:"#009688"},t.prototype.render=function(){var i,a=this,o=a.config,e=(o.step<1&&(o.step=1),o.max<o.min&&(o.max=o.min+o.step),o.range?(o.value="object"==typeof o.value?o.value:[o.min,o.value],t=Math.min(o.value[0],o.value[1]),r=Math.max(o.value[0],o.value[1]),o.value[0]=t>o.min?t:o.min,o.value[1]=r>o.min?r:o.min,o.value[0]=o.value[0]>o.max?o.max:o.value[0],o.value[1]=o.value[1]>o.max?o.max:o.value[1],t=Math.floor((o.value[0]-o.min)/(o.max-o.min)*100),n=(r=Math.floor((o.value[1]-o.min)/(o.max-o.min)*100))-t+"%",t+="%",r+="%"):("object"==typeof o.value&&(o.value=Math.min.apply(null,o.value)),o.value<o.min&&(o.value=o.min),o.value>o.max&&(o.value=o.max),n=Math.floor((o.value-o.min)/(o.max-o.min)*100)+"%"),o.disabled?"#c2c2c2":o.theme),t='<div class="layui-slider '+("vertical"===o.type?"layui-slider-vertical":"")+'">'+(o.tips?'<div class="'+x+'"></div>':"")+'<div class="layui-slider-bar" style="background:'+e+"; "+("vertical"===o.type?"height":"width")+":"+n+";"+("vertical"===o.type?"bottom":"left")+":"+(t||0)+';"></div><div class="layui-slider-wrap" style="'+("vertical"===o.type?"bottom":"left")+":"+(t||n)+';"><div class="layui-slider-wrap-btn" style="border: 2px solid '+e+';"></div></div>'+(o.range?'<div class="layui-slider-wrap" style="'+("vertical"===o.type?"bottom":"left")+":"+r+';"><div class="layui-slider-wrap-btn" style="border: 2px solid '+e+';"></div></div>':"")+"</div>",n=m(o.elem),r=n.next(".layui-slider");if(r[0]&&r.remove(),a.elemTemp=m(t),o.range?(a.elemTemp.find("."+v).eq(0).data("value",o.value[0]),a.elemTemp.find("."+v).eq(1).data("value",o.value[1])):a.elemTemp.find("."+v).data("value",o.value),n.html(a.elemTemp),"vertical"===o.type&&a.elemTemp.height(o.height+"px"),o.showstep){for(var l=(o.max-o.min)/o.step,s="",c=1;c<1+l;c++){var u=100*c/l;u<100&&(s+='<div class="layui-slider-step" style="'+("vertical"===o.type?"bottom":"left")+":"+u+'%"></div>')}a.elemTemp.append(s)}o.input&&!o.range&&(e=m('<div class="layui-slider-input layui-input"><div class="layui-slider-input-txt"><input type="text" class="layui-input"></div><div class="layui-slider-input-btn"><i class="layui-icon layui-icon-up"></i><i class="layui-icon layui-icon-down"></i></div></div>'),n.css("position","relative"),n.append(e),n.find("."+k).children("input").val(o.value),"vertical"===o.type?e.css({left:0,top:-48}):a.elemTemp.css("margin-right",e.outerWidth()+15)),o.disabled?(a.elemTemp.addClass(d),a.elemTemp.find("."+b).addClass(d)):a.slide(),a.elemTemp.find("."+b).on("mouseover",function(){var e="vertical"===o.type?o.height:a.elemTemp[0].offsetWidth,t=a.elemTemp.find("."+v),n=("vertical"===o.type?e-m(this).parent()[0].offsetTop-t.height():m(this).parent()[0].offsetLeft)/e*100,t=m(this).parent().data("value"),e=o.setTips?o.setTips(t):t;a.elemTemp.find("."+x).html(e),clearTimeout(i),i=setTimeout(function(){"vertical"===o.type?a.elemTemp.find("."+x).css({bottom:n+"%","margin-bottom":"20px",display:"inline-block"}):a.elemTemp.find("."+x).css({left:n+"%",display:"inline-block"})},300)}).on("mouseout",function(){clearTimeout(i),a.elemTemp.find("."+x).css("display","none")})},t.prototype.slide=function(e,t,n){var l=this.config,s=this.elemTemp,c=function(){return"vertical"===l.type?l.height:s[0].offsetWidth},u=s.find("."+v),r=s.next(".layui-slider-input"),d=r.children("."+k).children("input").val(),f=100/((l.max-l.min)/Math.ceil(l.step)),p=function(e,t){e=100<(e=100<Math.ceil(e)*f?Math.ceil(e)*f:Math.round(e)*f)?100:e,u.eq(t).css("vertical"===l.type?"bottom":"left",e+"%");var n,i=h(u[0].offsetLeft),a=l.range?h(u[1].offsetLeft):0,o=("vertical"===l.type?(s.find("."+x).css({bottom:e+"%","margin-bottom":"20px"}),i=h(c()-u[0].offsetTop-u.height()),a=l.range?h(c()-u[1].offsetTop-u.height()):0):s.find("."+x).css("left",e+"%"),i=100<i?100:i,a=100<a?100:a,Math.min(i,a)),i=Math.abs(i-a),a=("vertical"===l.type?s.find("."+g).css({height:i+"%",bottom:o+"%"}):s.find("."+g).css({width:i+"%",left:o+"%"}),l.min+Math.round((l.max-l.min)*e/100));d=a,r.children("."+k).children("input").val(d),u.eq(t).data("value",a),s.find("."+x).html(l.setTips?l.setTips(a):a),l.range&&(n=[u.eq(0).data("value"),u.eq(1).data("value")])[0]>n[1]&&n.reverse(),l.change&&l.change(l.range?n:a)},h=function(e){var t=e/c()*100/f,n=Math.round(t)*f;return n=e==c()?Math.ceil(t)*f:n},y=m(['<div class="layui-auxiliar-moving" id="LAY-slider-moving"></div'].join(""));if("set"===e)return p(t,n);s.find("."+b).each(function(o){var r=m(this);r.on("mousedown",function(e){e=e||window.event;var t,n,i=r.parent()[0].offsetLeft,a=e.clientX;"vertical"===l.type&&(i=c()-r.parent()[0].offsetTop-u.height(),a=e.clientY);e=function(e){e=e||window.event;var t=i+("vertical"===l.type?a-e.clientY:e.clientX-a),t=(t=(t=t<0?0:t)>c()?c():t)/c()*100/f;p(t,o),r.addClass(w),s.find("."+x).show(),e.preventDefault()},t=function(){r.removeClass(w),s.find("."+x).hide()},n=function(){t&&t(),y.remove()},m("#LAY-slider-moving")[0]||m("body").append(y),y.on("mousemove",e),y.on("mouseup",n).on("mouseleave",n)})}),s.on("click",function(e){var t=m("."+b),n=m(this);!t.is(event.target)&&0===t.has(event.target).length&&t.length&&(n=(t=(t=(t="vertical"===l.type?c()-e.clientY+n.offset().top-m(window).scrollTop():e.clientX-n.offset().left-m(window).scrollLeft())<0?0:t)>c()?c():t)/c()*100/f,t=l.range?"vertical"===l.type?Math.abs(t-parseInt(m(u[0]).css("bottom")))>Math.abs(t-parseInt(m(u[1]).css("bottom")))?1:0:Math.abs(t-u[0].offsetLeft)>Math.abs(t-u[1].offsetLeft)?1:0:0,p(n,t),e.preventDefault())}),r.children(".layui-slider-input-btn").children("i").each(function(t){m(this).on("click",function(){d=r.children("."+k).children("input").val();var e=((d=1==t?d-l.step<l.min?l.min:Number(d)-l.step:Number(d)+l.step>l.max?l.max:Number(d)+l.step)-l.min)/(l.max-l.min)*100/f;p(e,0)})});var i=function(){var e=this.value,e=(e=(e=(e=isNaN(e)?0:e)<l.min?l.min:e)>l.max?l.max:e,((this.value=e)-l.min)/(l.max-l.min)*100/f);p(e,0)};r.children("."+k).children("input").on("keydown",function(e){13===e.keyCode&&(e.preventDefault(),i.call(this))}).on("change",i)},t.prototype.events=function(){this.config},n.render=function(e){e=new t(e);return function(){var n=this,i=n.config;return{setValue:function(e,t){return i.value=e,n.slide("set",e,t||0)},config:i}}.call(e)},e(i,n)});layui.define(["jquery","lay"],function(e){"use strict";var b=layui.jquery,a=layui.lay,i=layui.device().mobile?"click":"mousedown",o={config:{},index:layui.colorpicker?layui.colorpicker.index+1e4:0,set:function(e){var t=this;return t.config=b.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,"colorpicker",e,t)}},r="layui-colorpicker",l=".layui-colorpicker-main",x="layui-icon-down",k="layui-icon-close",w="layui-colorpicker-trigger-span",C="layui-colorpicker-trigger-i",T="layui-colorpicker-side-slider",E="layui-colorpicker-basis",D="layui-colorpicker-alpha-bgcolor",L="layui-colorpicker-alpha-slider",S="layui-colorpicker-basis-cursor",A="layui-colorpicker-main-input",N=function(e){var t={h:0,s:0,b:0},n=Math.min(e.r,e.g,e.b),i=Math.max(e.r,e.g,e.b),a=i-n;return t.b=i,t.s=0!=i?255*a/i:0,0!=t.s?e.r==i?t.h=(e.g-e.b)/a:e.g==i?t.h=2+(e.b-e.r)/a:t.h=4+(e.r-e.g)/a:t.h=-1,i==n&&(t.h=0),t.h*=60,t.h<0&&(t.h+=360),t.s*=100/255,t.b*=100/255,t},j=function(e){var t,n={},i=e.h,a=255*e.s/100,e=255*e.b/100;return 0==a?n.r=n.g=n.b=e:(e=i%60*((t=e)-(a=(255-a)*e/255))/60,(i=360==i?0:i)<60?(n.r=t,n.b=a,n.g=a+e):i<120?(n.g=t,n.b=a,n.r=t-e):i<180?(n.g=t,n.r=a,n.b=a+e):i<240?(n.b=t,n.r=a,n.g=t-e):i<300?(n.b=t,n.g=a,n.r=a+e):i<360?(n.r=t,n.g=a,n.b=t-e):(n.r=0,n.g=0,n.b=0)),{r:Math.round(n.r),g:Math.round(n.g),b:Math.round(n.b)}},d=function(e){var e=j(e),n=[e.r.toString(16),e.g.toString(16),e.b.toString(16)];return b.each(n,function(e,t){1==t.length&&(n[e]="0"+t)}),n.join("")},M=function(e){e=e.match(/[0-9]{1,3}/g)||[];return{r:e[0],g:e[1],b:e[2]}},H=b(window),s=b(document),c=function(e){this.index=++o.index,this.config=b.extend({},this.config,o.config,e),this.render()};c.prototype.config={color:"",size:null,alpha:!1,format:"hex",predefine:!1,colors:["#009688","#5FB878","#1E9FFF","#FF5722","#FFB800","#01AAED","#999","#c00","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585","rgb(0, 186, 189)","rgb(255, 120, 0)","rgb(250, 212, 0)","#393D49","rgba(0,0,0,.5)","rgba(255, 69, 0, 0.68)","rgba(144, 240, 144, 0.5)","rgba(31, 147, 255, 0.73)"]},c.prototype.render=function(){var e=this,t=e.config,n=b(t.elem);if(1<n.length)return layui.each(n,function(){o.render(b.extend({},t,{elem:this}))}),e;b.extend(t,a.options(n[0]));var n=b(['<div class="layui-unselect layui-colorpicker">',"<span "+("rgb"==t.format&&t.alpha?'class="layui-colorpicker-trigger-bgcolor"':"")+">",'<span class="layui-colorpicker-trigger-span" ','lay-type="'+("rgb"==t.format?t.alpha?"rgba":"torgb":"")+'" ','style="'+(n="",t.color?(n=t.color,3<(t.color.match(/[0-9]{1,3}/g)||[]).length&&(t.alpha&&"rgb"==t.format||(n="#"+d(N(M(t.color))))),"background: "+n):n)+'">','<i class="layui-icon layui-colorpicker-trigger-i '+(t.color?x:k)+'"></i>',"</span>","</span>","</div>"].join("")),i=t.elem=b(t.elem);t.size&&n.addClass("layui-colorpicker-"+t.size),i.addClass("layui-inline").html(e.elemColorBox=n),e.color=e.elemColorBox.find("."+w)[0].style.background,e.events()},c.prototype.renderPicker=function(){var n,e=this,t=e.config,i=e.elemColorBox[0],t=e.elemPicker=b(['<div id="layui-colorpicker'+e.index+'" data-index="'+e.index+'" class="layui-anim layui-anim-downbit layui-colorpicker-main">','<div class="layui-colorpicker-main-wrapper">','<div class="layui-colorpicker-basis">','<div class="layui-colorpicker-basis-white"></div>','<div class="layui-colorpicker-basis-black"></div>','<div class="layui-colorpicker-basis-cursor"></div>',"</div>",'<div class="layui-colorpicker-side">','<div class="layui-colorpicker-side-slider"></div>',"</div>","</div>",'<div class="layui-colorpicker-main-alpha '+(t.alpha?"layui-show":"")+'">','<div class="layui-colorpicker-alpha-bgcolor">','<div class="layui-colorpicker-alpha-slider"></div>',"</div>","</div>",t.predefine?(n=['<div class="layui-colorpicker-main-pre">'],layui.each(t.colors,function(e,t){n.push(['<div class="layui-colorpicker-pre'+(3<(t.match(/[0-9]{1,3}/g)||[]).length?" layui-colorpicker-pre-isalpha":"")+'">','<div style="background:'+t+'"></div>',"</div>"].join(""))}),n.push("</div>"),n.join("")):"",'<div class="layui-colorpicker-main-input">','<div class="layui-inline">','<input type="text" class="layui-input">',"</div>",'<div class="layui-btn-container">','<button class="layui-btn layui-btn-primary layui-btn-sm" colorpicker-events="clear">清空</button>','<button class="layui-btn layui-btn-sm" colorpicker-events="confirm">确定</button>',"</div","</div>","</div>"].join(""));e.elemColorBox.find("."+w)[0];b(l)[0]&&b(l).data("index")==e.index?e.removePicker(c.thisElemInd):(e.removePicker(c.thisElemInd),b("body").append(t)),c.thisElemInd=e.index,c.thisColor=i.style.background,e.position(),e.pickerEvents()},c.prototype.removePicker=function(e){this.config;return b("#layui-colorpicker"+(e||this.index)).remove(),this},c.prototype.position=function(){var e=this,t=e.config;return a.position(e.bindElem||e.elemColorBox[0],e.elemPicker[0],{position:t.position,align:"center"}),e},c.prototype.val=function(){var e,t=this,n=(t.config,t.elemColorBox.find("."+w)),i=t.elemPicker.find("."+A),a=n[0].style.backgroundColor;a?(e=N(M(a)),n=n.attr("lay-type"),t.select(e.h,e.s,e.b),"torgb"===n&&i.find("input").val(a),"rgba"===n&&(e=M(a),3==(a.match(/[0-9]{1,3}/g)||[]).length?(i.find("input").val("rgba("+e.r+", "+e.g+", "+e.b+", 1)"),t.elemPicker.find("."+L).css("left",280)):(i.find("input").val(a),n=280*a.slice(a.lastIndexOf(",")+1,a.length-1),t.elemPicker.find("."+L).css("left",n)),t.elemPicker.find("."+D)[0].style.background="linear-gradient(to right, rgba("+e.r+", "+e.g+", "+e.b+", 0), rgb("+e.r+", "+e.g+", "+e.b+"))")):(t.select(0,100,100),i.find("input").val(""),t.elemPicker.find("."+D)[0].style.background="",t.elemPicker.find("."+L).css("left",280))},c.prototype.side=function(){var a=this,o=a.config,r=a.elemColorBox.find("."+w),l=r.attr("lay-type"),s=a.elemPicker.find(".layui-colorpicker-side"),e=a.elemPicker.find("."+T),c=a.elemPicker.find("."+E),i=a.elemPicker.find("."+S),u=a.elemPicker.find("."+D),d=a.elemPicker.find("."+L),f=e[0].offsetTop/180*360,p=100-(i[0].offsetTop+3)/180*100,h=(i[0].offsetLeft+3)/260*100,y=Math.round(d[0].offsetLeft/280*100)/100,m=a.elemColorBox.find("."+C),t=a.elemPicker.find(".layui-colorpicker-pre").children("div"),g=function(e,t,n,i){a.select(e,t,n);e=j({h:e,s:t,b:n});m.addClass(x).removeClass(k),r[0].style.background="rgb("+e.r+", "+e.g+", "+e.b+")","torgb"===l&&a.elemPicker.find("."+A).find("input").val("rgb("+e.r+", "+e.g+", "+e.b+")"),"rgba"===l&&(d.css("left",280*i),a.elemPicker.find("."+A).find("input").val("rgba("+e.r+", "+e.g+", "+e.b+", "+i+")"),r[0].style.background="rgba("+e.r+", "+e.g+", "+e.b+", "+i+")",u[0].style.background="linear-gradient(to right, rgba("+e.r+", "+e.g+", "+e.b+", 0), rgb("+e.r+", "+e.g+", "+e.b+"))"),o.change&&o.change(a.elemPicker.find("."+A).find("input").val())},n=b(['<div class="layui-auxiliar-moving" id="LAY-colorpicker-moving"></div>'].join("")),v=function(e){b("#LAY-colorpicker-moving")[0]||b("body").append(n),n.on("mousemove",e),n.on("mouseup",function(){n.remove()}).on("mouseleave",function(){n.remove()})};e.on("mousedown",function(e){var i=this.offsetTop,a=e.clientY;v(function(e){var t=i+(e.clientY-a),n=s[0].offsetHeight,n=(t=n<(t=t<0?0:t)?n:t)/180*360;g(f=n,h,p,y),e.preventDefault()}),e.preventDefault()}),s.on("click",function(e){var t=e.clientY-b(this).offset().top,t=(t=(t=t<0?0:t)>this.offsetHeight?this.offsetHeight:t)/180*360;g(f=t,h,p,y),e.preventDefault()}),i.on("mousedown",function(e){var o=this.offsetTop,r=this.offsetLeft,l=e.clientY,s=e.clientX;layui.stope(e),v(function(e){var t=o+(e.clientY-l),n=r+(e.clientX-s),i=c[0].offsetHeight-3,a=c[0].offsetWidth-3,a=((n=a<(n=n<-3?-3:n)?a:n)+3)/260*100,n=100-((t=i<(t=t<-3?-3:t)?i:t)+3)/180*100;g(f,h=a,p=n,y),e.preventDefault()}),e.preventDefault()}),c.on("mousedown",function(e){var t=e.clientY-b(this).offset().top-3+H.scrollTop(),n=e.clientX-b(this).offset().left-3+H.scrollLeft(),n=((t=t<-3?-3:t)>this.offsetHeight-3&&(t=this.offsetHeight-3),((n=(n=n<-3?-3:n)>this.offsetWidth-3?this.offsetWidth-3:n)+3)/260*100),t=100-(t+3)/180*100;g(f,h=n,p=t,y),layui.stope(e),e.preventDefault(),i.trigger(e,"mousedown")}),d.on("mousedown",function(e){var i=this.offsetLeft,a=e.clientX;v(function(e){var t=i+(e.clientX-a),n=u[0].offsetWidth,n=(n<(t=t<0?0:t)&&(t=n),Math.round(t/280*100)/100);g(f,h,p,y=n),e.preventDefault()}),e.preventDefault()}),u.on("click",function(e){var t=e.clientX-b(this).offset().left,t=((t=t<0?0:t)>this.offsetWidth&&(t=this.offsetWidth),Math.round(t/280*100)/100);g(f,h,p,y=t),e.preventDefault()}),t.each(function(){b(this).on("click",function(){b(this).parent(".layui-colorpicker-pre").addClass("selected").siblings().removeClass("selected");var e=this.style.backgroundColor,t=N(M(e)),n=e.slice(e.lastIndexOf(",")+1,e.length-1);f=t.h,h=t.s,p=t.b,3==(e.match(/[0-9]{1,3}/g)||[]).length&&(n=1),y=n,g(t.h,t.s,t.b,n)})})},c.prototype.select=function(e,t,n,i){var a=this,o=(a.config,d({h:e,s:100,b:100})),r=d({h:e,s:t,b:n}),e=e/360*180,n=180-n/100*180-3,t=t/100*260-3;a.elemPicker.find("."+T).css("top",e),a.elemPicker.find("."+E)[0].style.background="#"+o,a.elemPicker.find("."+S).css({top:n,left:t}),"change"!==i&&a.elemPicker.find("."+A).find("input").val("#"+r)},c.prototype.pickerEvents=function(){var l=this,s=l.config,c=l.elemColorBox.find("."+w),u=l.elemPicker.find("."+A+" input"),n={clear:function(e){c[0].style.background="",l.elemColorBox.find("."+C).removeClass(x).addClass(k),l.color="",s.done&&s.done(""),l.removePicker()},confirm:function(e,t){var n,i,a=u.val(),o=a,r={};if(-1<a.indexOf(",")?(r=N(M(a)),l.select(r.h,r.s,r.b),c[0].style.background=o="#"+d(r),3<(a.match(/[0-9]{1,3}/g)||[]).length&&"rgba"===c.attr("lay-type")&&(n=280*a.slice(a.lastIndexOf(",")+1,a.length-1),l.elemPicker.find("."+L).css("left",n),o=c[0].style.background=a)):(3==(n=-1<(n=a).indexOf("#")?n.substring(1):n).length&&(n=(i=n.split(""))[0]+i[0]+i[1]+i[1]+i[2]+i[2]),i={r:(n=parseInt(n,16))>>16,g:(65280&n)>>8,b:255&n},r=N(i),c[0].style.background=o="#"+d(r),l.elemColorBox.find("."+C).removeClass(k).addClass(x)),"change"===t)return l.select(r.h,r.s,r.b,t),void(s.change&&s.change(o));l.color=a,s.done&&s.done(a),l.removePicker()}};l.elemPicker.on("click","*[colorpicker-events]",function(){var e=b(this),t=e.attr("colorpicker-events");n[t]&&n[t].call(this,e)}),u.on("keyup",function(e){var t=b(this);n.confirm.call(this,t,13===e.keyCode?null:"change")})},c.prototype.events=function(){var t=this,e=t.config,n=t.elemColorBox.find("."+w);t.elemColorBox.on("click",function(){t.renderPicker(),b(l)[0]&&(t.val(),t.side())}),e.elem[0]&&!t.elemColorBox[0].eventHandler&&(s.on(i,function(e){b(e.target).hasClass(r)||b(e.target).parents("."+r)[0]||b(e.target).hasClass(l.replace(/\./g,""))||b(e.target).parents(l)[0]||t.elemPicker&&(t.color?(e=N(M(t.color)),t.select(e.h,e.s,e.b)):t.elemColorBox.find("."+C).removeClass(x).addClass(k),n[0].style.background=t.color||"",t.removePicker())}),H.on("resize",function(){if(!t.elemPicker||!b(l)[0])return!1;t.position()}),t.elemColorBox[0].eventHandler=!0)},o.render=function(e){e=new c(e);return function(){return{config:this.config}}.call(e)},e("colorpicker",o)});layui.define("jquery",function(e){"use strict";var u=layui.$,d=(layui.hint(),layui.device()),s="element",c="layui-this",f="layui-show",t=function(){this.config={}},p=(t.prototype.set=function(e){return u.extend(!0,this.config,e),this},t.prototype.on=function(e,t){return layui.onevent.call(this,s,e,t)},t.prototype.tabAdd=function(e,t){var n,e=u(".layui-tab[lay-filter="+e+"]"),i=e.children(".layui-tab-title"),a=i.children(".layui-tab-bar"),e=e.children(".layui-tab-content"),o="<li"+(n=[],layui.each(t,function(e,t){/^(title|content)$/.test(e)||n.push("lay-"+e+'="'+t+'"')}),0<n.length&&n.unshift(""),n.join(" "))+">"+(t.title||"unnaming")+"</li>";return a[0]?a.before(o):i.append(o),e.append('<div class="layui-tab-item">'+(t.content||"")+"</div>"),b.hideTabMore(!0),b.tabAuto(),this},t.prototype.tabDelete=function(e,t){e=u(".layui-tab[lay-filter="+e+"]").children(".layui-tab-title").find('>li[lay-id="'+t+'"]');return b.tabDelete(null,e),this},t.prototype.tabChange=function(e,t){e=u(".layui-tab[lay-filter="+e+"]").children(".layui-tab-title").find('>li[lay-id="'+t+'"]');return b.tabClick.call(e[0],null,null,e),this},t.prototype.tab=function(n){n=n||{},i.on("click",n.headerElem,function(e){var t=u(this).index();b.tabClick.call(this,e,t,null,n)})},t.prototype.progress=function(e,t){var n="layui-progress",e=u("."+n+"[lay-filter="+e+"]").find("."+n+"-bar"),n=e.find("."+n+"-text");return e.css("width",t).attr("lay-percent",t),n.text(t),this},".layui-nav"),h="layui-nav-item",a="layui-nav-bar",y="layui-nav-tree",m="layui-nav-child",g="layui-nav-more",v="layui-anim layui-anim-upbit",b={tabClick:function(e,t,n,i){i=i||{};var n=n||u(this),t=t||n.parent().children("li").index(n),a=i.headerElem?n.parent():n.parents(".layui-tab").eq(0),i=i.bodyElem?u(i.bodyElem):a.children(".layui-tab-content").children(".layui-tab-item"),o=n.find("a"),o="javascript:;"!==o.attr("href")&&"_blank"===o.attr("target"),r="string"==typeof n.attr("lay-unselect"),l=a.attr("lay-filter");o||r||(n.addClass(c).siblings().removeClass(c),i.eq(t).addClass(f).siblings().removeClass(f)),layui.event.call(this,s,"tab("+l+")",{elem:a,index:t})},tabDelete:function(e,t){var t=t||u(this).parent(),n=t.index(),i=t.parents(".layui-tab").eq(0),a=i.children(".layui-tab-content").children(".layui-tab-item"),o=i.attr("lay-filter");t.hasClass(c)&&(t.next()[0]&&t.next().is("li")?b.tabClick.call(t.next()[0],null,n+1):t.prev()[0]&&t.prev().is("li")&&b.tabClick.call(t.prev()[0],null,n-1)),t.remove(),a.eq(n).remove(),setTimeout(function(){b.tabAuto()},50),layui.event.call(this,s,"tabDelete("+o+")",{elem:i,index:n})},tabAuto:function(){var i="layui-tab-bar",a="layui-tab-close",o=this;u(".layui-tab").each(function(){var e=u(this),t=e.children(".layui-tab-title"),n=(e.children(".layui-tab-content").children(".layui-tab-item"),'lay-stope="tabmore"'),n=u('<span class="layui-unselect layui-tab-bar" '+n+"><i "+n+' class="layui-icon">&#xe61a;</i></span>');o===window&&8!=d.ie&&b.hideTabMore(!0),e.attr("lay-allowClose")&&t.find("li").each(function(){var e,t=u(this);t.find("."+a)[0]||((e=u('<i class="layui-icon layui-icon-close layui-unselect '+a+'"></i>')).on("click",b.tabDelete),t.append(e))}),"string"!=typeof e.attr("lay-unauto")&&(t.prop("scrollWidth")>t.outerWidth()+1?t.find("."+i)[0]||(t.append(n),e.attr("overflow",""),n.on("click",function(e){t[this.title?"removeClass":"addClass"]("layui-tab-more"),this.title=this.title?"":"收缩"})):(t.find("."+i).remove(),e.removeAttr("overflow")))})},hideTabMore:function(e){var t=u(".layui-tab-title");!0!==e&&"tabmore"===u(e.target).attr("lay-stope")||(t.removeClass("layui-tab-more"),t.find(".layui-tab-bar").attr("title",""))},clickThis:function(){var e=u(this),t=e.parents(p),n=t.attr("lay-filter"),i=e.parent(),a=e.siblings("."+m),o="string"==typeof i.attr("lay-unselect");"javascript:;"!==e.attr("href")&&"_blank"===e.attr("target")||o||a[0]||(t.find("."+c).removeClass(c),i.addClass(c)),t.hasClass(y)&&(a.removeClass(v),a[0]&&(i["none"===a.css("display")?"addClass":"removeClass"](h+"ed"),"all"===t.attr("lay-shrink")&&i.siblings().removeClass(h+"ed"))),layui.event.call(this,s,"nav("+n+")",e)},collapse:function(){var e=u(this),t=e.find(".layui-colla-icon"),n=e.siblings(".layui-colla-content"),i=e.parents(".layui-collapse").eq(0),a=i.attr("lay-filter"),o="none"===n.css("display");"string"==typeof i.attr("lay-accordion")&&((i=i.children(".layui-colla-item").children("."+f)).siblings(".layui-colla-title").children(".layui-colla-icon").html("&#xe602;"),i.removeClass(f)),n[o?"addClass":"removeClass"](f),t.html(o?"&#xe61a;":"&#xe602;"),layui.event.call(this,s,"collapse("+a+")",{title:e,content:n,show:o})}},n=(t.prototype.render=t.prototype.init=function(e,t){var n=t?'[lay-filter="'+t+'"]':"",t={tab:function(){b.tabAuto.call({})},nav:function(){var r={},l={},s={},c="layui-nav-title";u(p+n).each(function(e){var t=u(this),n=u('<span class="'+a+'"></span>'),i=t.find("."+h);t.find("."+a)[0]||(t.append(n),(t.hasClass(y)?i.find("dd,>."+c):i).on("mouseenter",function(){!function(e,t,n){var i,a=u(this),o=a.find("."+m);t.hasClass(y)?o[0]||(i=a.children("."+c),e.css({top:a.offset().top-t.offset().top,height:(i[0]?i:a).outerHeight(),opacity:1})):(o.addClass(v),o.hasClass("layui-nav-child-c")&&o.css({left:-(o.outerWidth()-a.width())/2}),o[0]?e.css({left:e.position().left+e.width()/2,width:0,opacity:0}):e.css({left:a.position().left+parseFloat(a.css("marginLeft")),top:a.position().top+a.height()-e.height()}),r[n]=setTimeout(function(){e.css({width:o[0]?0:a.width(),opacity:o[0]?0:1})},d.ie&&d.ie<10?0:200),clearTimeout(s[n]),"block"===o.css("display")&&clearTimeout(l[n]),l[n]=setTimeout(function(){o.addClass(f),a.find("."+g).addClass(g+"d")},300))}.call(this,n,t,e)}).on("mouseleave",function(){t.hasClass(y)?n.css({height:0,opacity:0}):(clearTimeout(l[e]),l[e]=setTimeout(function(){t.find("."+m).removeClass(f),t.find("."+g).removeClass(g+"d")},300))}),t.on("mouseleave",function(){clearTimeout(r[e]),s[e]=setTimeout(function(){t.hasClass(y)||n.css({width:0,left:n.position().left+n.width()/2,opacity:0})},200)})),i.find("a").each(function(){var e=u(this);e.parent();e.siblings("."+m)[0]&&!e.children("."+g)[0]&&e.append('<i class="layui-icon layui-icon-down '+g+'"></i>'),e.off("click",b.clickThis).on("click",b.clickThis)})})},breadcrumb:function(){u(".layui-breadcrumb"+n).each(function(){var e=u(this),t="lay-separator",n=e.attr(t)||"/",i=e.find("a");i.next("span["+t+"]")[0]||(i.each(function(e){e!==i.length-1&&u(this).after("<span "+t+">"+n+"</span>")}),e.css("visibility","visible"))})},progress:function(){var i="layui-progress";u("."+i+n).each(function(){var e=u(this),t=e.find(".layui-progress-bar"),n=t.attr("lay-percent");t.css("width",/^.+\/.+$/.test(n)?100*new Function("return "+n)()+"%":n),e.attr("lay-showPercent")&&setTimeout(function(){t.html('<span class="'+i+'-text">'+n+"</span>")},350)})},collapse:function(){u(".layui-collapse"+n).each(function(){u(this).find(".layui-colla-item").each(function(){var e=u(this),t=e.find(".layui-colla-title"),e="none"===e.find(".layui-colla-content").css("display");t.find(".layui-colla-icon").remove(),t.append('<i class="layui-icon layui-colla-icon">'+(e?"&#xe602;":"&#xe61a;")+"</i>"),t.off("click",b.collapse).on("click",b.collapse)})})}};return t[e]?t[e]():layui.each(t,function(e,t){t()})},new t),i=u(document);u(function(){n.render()});i.on("click",".layui-tab-title li",b.tabClick),i.on("click",b.hideTabMore),u(window).on("resize",b.tabAuto),e(s,n)});layui.define("layer",function(e){"use strict";var g=layui.$,t=layui.layer,n=layui.hint(),v=layui.device(),i={config:{},set:function(e){var t=this;return t.config=g.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,a,e,t)}},a="upload",o="layui-upload-file",r="layui-upload-form",b="layui-upload-iframe",x="layui-upload-choose",k=function(e){var t=this;t.config=g.extend({},t.config,i.config,e),t.render()};k.prototype.config={accept:"images",exts:"",auto:!0,bindAction:"",url:"",force:"",field:"file",acceptMime:"",method:"post",data:{},drag:!0,size:0,number:0,multiple:!1},k.prototype.render=function(e){var t=this;(e=t.config).elem=g(e.elem),e.bindAction=g(e.bindAction),t.file(),t.events()},k.prototype.file=function(){var e=this,t=e.config,n=e.elemFile=g(['<input class="'+o+'" type="file" accept="'+t.acceptMime+'" name="'+t.field+'"',t.multiple?" multiple":"",">"].join("")),i=t.elem.next();(i.hasClass(o)||i.hasClass(r))&&i.remove(),v.ie&&v.ie<10&&t.elem.wrap('<div class="layui-upload-wrap"></div>'),e.isFile()?(e.elemFile=t.elem,t.field=t.elem[0].name):t.elem.after(n),v.ie&&v.ie<10&&e.initIE()},k.prototype.initIE=function(){var n,e=this.config,t=g('<iframe id="'+b+'" class="'+b+'" name="'+b+'" frameborder="0"></iframe>'),i=g(['<form target="'+b+'" class="'+r+'" method="post" key="set-mine" enctype="multipart/form-data" action="'+e.url+'">',"</form>"].join(""));g("#"+b)[0]||g("body").append(t),e.elem.next().hasClass(r)||(this.elemFile.wrap(i),e.elem.next("."+r).append((n=[],layui.each(e.data,function(e,t){t="function"==typeof t?t():t,n.push('<input type="hidden" name="'+e+'" value="'+t+'">')}),n.join(""))))},k.prototype.msg=function(e){return t.msg(e,{icon:2,shift:6})},k.prototype.isFile=function(){var e=this.config.elem[0];if(e)return"input"===e.tagName.toLocaleLowerCase()&&"file"===e.type},k.prototype.preview=function(i){window.FileReader&&layui.each(this.chooseFiles,function(e,t){var n=new FileReader;n.readAsDataURL(t),n.onload=function(){i&&i(e,t,this.result)}})},k.prototype.upload=function(n,e){var i,a,t,o,r=this,l=r.config,s=r.elemFile[0],c=function(){var t=0,a=0,e=n||r.files||r.chooseFiles||s.files,o=function(){l.multiple&&t+a===r.fileLength&&"function"==typeof l.allDone&&l.allDone({total:r.fileLength,successful:t,failed:a})};layui.each(e,function(n,e){var i=new FormData,e=(i.append(l.field,e),layui.each(l.data,function(e,t){t="function"==typeof t?t():t,i.append(e,t)}),{url:l.url,type:"post",data:i,contentType:!1,processData:!1,dataType:"json",headers:l.headers||{},success:function(e){t++,d(n,e),o()},error:function(e){a++,r.msg("Request URL is abnormal: "+(e.statusText||"error")),f(n),o()}});"function"==typeof l.progress&&(e.xhr=function(){var e=g.ajaxSettings.xhr();return e.upload.addEventListener("progress",function(e){var t;e.lengthComputable&&(t=Math.floor(e.loaded/e.total*100),l.progress(t,(l.item||l.elem)[0],e,n))}),e}),g.ajax(e)})},u=function(){var n=g("#"+b);r.elemFile.parent().submit(),clearInterval(k.timer),k.timer=setInterval(function(){var e,t=n.contents().find("body");try{e=t.text()}catch(e){r.msg("Cross-domain requests are not supported"),clearInterval(k.timer),f()}e&&(clearInterval(k.timer),t.html(""),d(0,e))},30)},d=function(e,t){if(r.elemFile.next("."+x).remove(),s.value="","json"===l.force&&"object"!=typeof t)try{t=JSON.parse(t)}catch(e){return t={},r.msg("Please return JSON data format")}"function"==typeof l.done&&l.done(t,e||0,function(e){r.upload(e)})},f=function(e){l.auto&&(s.value=""),"function"==typeof l.error&&l.error(e||0,function(e){r.upload(e)})},p=l.exts,h=(a=[],layui.each(n||r.chooseFiles,function(e,t){a.push(t.name)}),a),y={preview:function(e){r.preview(e)},upload:function(e,t){var n={};n[e]=t,r.upload(n)},pushFile:function(){return r.files=r.files||{},layui.each(r.chooseFiles,function(e,t){r.files[e]=t}),r.files},resetFile:function(e,t,n){t=new File([t],n);r.files=r.files||{},r.files[e]=t}},m={file:"文件",images:"图片",video:"视频",audio:"音频"}[l.accept]||"文件",h=0===h.length?s.value.match(/[^\/\\]+\..+/g)||[]||"":h;if(0!==h.length){switch(l.accept){case"file":layui.each(h,function(e,t){if(p&&!RegExp(".\\.("+p+")$","i").test(escape(t)))return i=!0});break;case"video":layui.each(h,function(e,t){if(!RegExp(".\\.("+(p||"avi|mp4|wma|rmvb|rm|flash|3gp|flv")+")$","i").test(escape(t)))return i=!0});break;case"audio":layui.each(h,function(e,t){if(!RegExp(".\\.("+(p||"mp3|wav|mid")+")$","i").test(escape(t)))return i=!0});break;default:layui.each(h,function(e,t){if(!RegExp(".\\.("+(p||"jpg|png|gif|bmp|jpeg")+")$","i").test(escape(t)))return i=!0})}if(i)return r.msg("选择的"+m+"中包含不支持的格式"),s.value="";if("choose"!==e&&!l.auto||(l.choose&&l.choose(y),"choose"!==e)){if(r.fileLength=(t=0,m=n||r.files||r.chooseFiles||s.files,layui.each(m,function(){t++}),t),l.number&&r.fileLength>l.number)return r.msg("同时最多只能上传: "+l.number+" 个文件<br>您当前已经选择了: "+r.fileLength+" 个文件");if(0<l.size&&!(v.ie&&v.ie<10))if(layui.each(r.chooseFiles,function(e,t){t.size>1024*l.size&&(t=1<=(t=l.size/1024)?t.toFixed(2)+"MB":l.size+"KB",s.value="",o=t)}),o)return r.msg("文件大小不能超过 "+o);if(!l.before||!1!==l.before(y))v.ie?(9<v.ie?c:u)():c()}}},k.prototype.reload=function(e){delete(e=e||{}).elem,delete e.bindAction;(e=this.config=g.extend({},this.config,i.config,e)).elem.next().attr({name:e.name,accept:e.acceptMime,multiple:e.multiple})},k.prototype.events=function(){var i=this,a=i.config,o=function(e){i.chooseFiles={},layui.each(e,function(e,t){var n=(new Date).getTime();i.chooseFiles[n+"-"+e]=t})},r=function(e,t){var n=i.elemFile,e=(a.item||a.elem,1<e.length?e.length+"个文件":(e[0]||{}).name||n[0].value.match(/[^\/\\]+\..+/g)||[]||"");n.next().hasClass(x)&&n.next().remove(),i.upload(null,"choose"),i.isFile()||a.choose||n.after('<span class="layui-inline '+x+'">'+e+"</span>")};a.elem.off("upload.start").on("upload.start",function(){var e=g(this),t=e.attr("lay-data");if(t)try{t=new Function("return "+t)(),i.config=g.extend({},a,t)}catch(e){n.error("Upload element property lay-data configuration item has a syntax error: "+t)}i.config.item=e,i.elemFile[0].click()}),v.ie&&v.ie<10||a.elem.off("upload.over").on("upload.over",function(){g(this).attr("lay-over","")}).off("upload.leave").on("upload.leave",function(){g(this).removeAttr("lay-over")}).off("upload.drop").on("upload.drop",function(e,t){var n=g(this),t=t.originalEvent.dataTransfer.files||[];n.removeAttr("lay-over"),o(t),a.auto?i.upload():r(t)}),i.elemFile.off("upload.change").on("upload.change",function(){var e=this.files||[];o(e),a.auto?i.upload():r(e)}),a.bindAction.off("upload.action").on("upload.action",function(){i.upload()}),a.elem.data("haveEvents")||(i.elemFile.on("change",function(){g(this).trigger("upload.change")}),a.elem.on("click",function(){i.isFile()||g(this).trigger("upload.start")}),a.drag&&a.elem.on("dragover",function(e){e.preventDefault(),g(this).trigger("upload.over")}).on("dragleave",function(e){g(this).trigger("upload.leave")}).on("drop",function(e){e.preventDefault(),g(this).trigger("upload.drop",e)}),a.bindAction.on("click",function(){g(this).trigger("upload.action")}),a.elem.data("haveEvents",!0))},i.render=function(e){e=new k(e);return function(){var t=this;return{upload:function(e){t.upload.call(t,e)},reload:function(e){t.reload.call(t,e)},config:t.config}}.call(e)},e(a,i)});layui.define(["layer","util"],function(e){"use strict";var w=layui.$,p=layui.layer,d=layui.util,o=layui.hint(),C=(layui.device(),"form"),s=".layui-form",T="layui-this",E="layui-hide",D="layui-disabled",t=function(){this.config={verify:{required:[/[\S]+/,"Required fields cannot be empty"],phone:[/^1\d{10}$/,"请输入正确的手机号"],email:[/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,"邮箱格式不正确"],url:[/^(#|(http(s?)):\/\/|\/\/)[^\s]+\.[^\s]+$/,"链接格式不正确"],number:function(e){if(!e||isNaN(e))return"只能填写数字"},date:[/^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/,"日期格式不正确"],identity:[/(^\d{15}$)|(^\d{17}(x|X|\d)$)/,"请输入正确的身份证号"]},autocomplete:null}},n=(t.prototype.set=function(e){return w.extend(!0,this.config,e),this},t.prototype.verify=function(e){return w.extend(!0,this.config.verify,e),this},t.prototype.getFormElem=function(e){return w(s+(e?'[lay-filter="'+e+'"]':""))},t.prototype.on=function(e,t){return layui.onevent.call(this,C,e,t)},t.prototype.val=function(e,n){return this.getFormElem(e).each(function(e,t){var i=w(this);layui.each(n,function(e,t){var n,e=i.find('[name="'+e+'"]');e[0]&&("checkbox"===(n=e[0].type)?e[0].checked=t:"radio"===n?e.each(function(){this.value==t&&(this.checked=!0)}):e.val(t))})}),r.render(null,e),this.getValue(e)},t.prototype.getValue=function(e,t){t=t||this.getFormElem(e);var i={},a={},e=t.find("input,select,textarea");return layui.each(e,function(e,t){var n;w(this);t.name=(t.name||"").replace(/^\s*|\s*&/,""),t.name&&(/^.*\[\]$/.test(t.name)&&(n=t.name.match(/^(.*)\[\]$/g)[0],i[n]=0|i[n],n=t.name.replace(/^(.*)\[\]$/,"$1["+i[n]+++"]")),/^checkbox|radio$/.test(t.type)&&!t.checked||(a[n||t.name]=t.value))}),a},t.prototype.render=function(e,t){var n=this.config,i=w(s+(t?'[lay-filter="'+t+'"]':"")),a={input:function(e){e=e||i.find("input,textarea");n.autocomplete&&e.attr("autocomplete",n.autocomplete)},select:function(e){var m,c="请选择",g="layui-form-select",v="layui-select-title",b="layui-select-none",x="",e=e||i.find("select"),k=function(e,t){w(e.target).parent().hasClass(v)&&!t||(w("."+g).removeClass(g+"ed "+g+"up"),m&&x&&m.val(x)),m=null},u=function(i,e,t){var l,r,n,a,s,o,c=w(this),u=i.find("."+v),d=u.find("input"),f=i.find("dl"),p=f.children("dd"),h=f.children("dt"),y=this.selectedIndex;e||(r=c.attr("lay-search"),n=function(){var e=i.offset().top+i.outerHeight()+5-L.scrollTop(),t=f.outerHeight();y=c[0].selectedIndex,i.addClass(g+"ed"),p.removeClass(E),h.removeClass(E),l=null,p.eq(y).addClass(T).siblings().removeClass(T),e+t>L.height()&&t<=e&&i.addClass(g+"up"),s()},a=function(e){i.removeClass(g+"ed "+g+"up"),d.blur(),l=null,e||o(d.val(),function(e){var t=c[0].selectedIndex;e&&(x=w(c[0].options[t]).html(),0===t&&x===d.attr("placeholder")&&(x=""),d.val(x||""))})},s=function(){var e,t,n=f.children("dd."+T);n[0]&&(e=n.position().top,t=f.height(),n=n.height(),t<e&&f.scrollTop(e+f.scrollTop()-t+n-5),e<0&&f.scrollTop(e+f.scrollTop()-5))},u.on("click",function(e){i.hasClass(g+"ed")?a():(k(e,!0),n()),f.find("."+b).remove()}),u.find(".layui-edge").on("click",function(){d.focus()}),d.on("keyup",function(e){9===e.keyCode&&n()}).on("keydown",function(o){var e=o.keyCode,r=(9===e&&a(),function(i,a){o.preventDefault();var e=function(){var e=f.children("dd."+T);if(f.children("dd."+E)[0]&&"next"===i){var t=f.children("dd:not(."+E+",."+D+")"),n=t.eq(0).index();if(0<=n&&n<e.index()&&!t.hasClass(T))return t.eq(0).prev()[0]?t.eq(0).prev():f.children(":last")}return a&&a[0]?a:l&&l[0]?l:e}(),t=e[i](),n=e[i]("dd:not(."+E+")");return t[0]?(l=e[i](),n[0]&&!n.hasClass(D)||!l[0]?(n.addClass(T).siblings().removeClass(T),void s()):r(i,l)):l=null});38===e&&r("prev"),40===e&&r("next"),13===e&&(o.preventDefault(),f.children("dd."+T).trigger("click"))}),o=function(i,e,a){var o=0,t=(layui.each(p,function(){var e=w(this),t=e.text(),n=("cs"!==r&&(t=t.toLowerCase(),i=i.toLowerCase()),-1===t.indexOf(i));(""===i||"blur"===a?i!==t:n)&&o++,"keyup"===a&&e[n?"addClass":"removeClass"](E)}),"keyup"===a&&layui.each(h,function(){var e=w(this),t=e.nextUntil("dt").filter("dd");e[t.length==t.filter("."+E).length?"addClass":"removeClass"](E)}),o===p.length);return e(t),t},t&&d.on("keyup",function(e){var t=this.value,e=e.keyCode;if(9===e||13===e||37===e||38===e||39===e||40===e)return!1;o(t,function(e){e?f.find("."+b)[0]||f.append('<p class="'+b+'">无匹配项</p>'):f.find("."+b).remove()},"keyup"),""===t&&f.find("."+b).remove(),s()}).on("blur",function(e){var t=c[0].selectedIndex;m=d,x=w(c[0].options[t]).html(),0===t&&x===d.attr("placeholder")&&(x=""),setTimeout(function(){o(d.val(),function(e){x||d.val("")},"blur")},200)}),p.on("click",function(){var e=w(this),t=e.attr("lay-value"),n=c.attr("lay-filter");return e.hasClass(D)||(e.hasClass("layui-select-tips")?d.val(""):(d.val(e.text()),e.addClass(T)),e.siblings().removeClass(T),c.val(t).removeClass("layui-form-danger"),layui.event.call(this,C,"select("+n+")",{elem:c[0],value:t,othis:i}),a(!0)),!1}),i.find("dl>dt").on("click",function(e){return!1}),w(document).off("click",k).on("click",k))};e.each(function(e,t){var n=w(this),i=n.next("."+g),a=this.disabled,o=t.value,r=w(t.options[t.selectedIndex]),t=t.options[0];if("string"==typeof n.attr("lay-ignore"))return n.show();var l,s="string"==typeof n.attr("lay-search"),t=t&&!t.value&&t.innerHTML||c,r=w(['<div class="'+(s?"":"layui-unselect ")+g,(a?" layui-select-disabled":"")+'">','<div class="'+v+'">','<input type="text" placeholder="'+d.escape(w.trim(t))+'" value="'+d.escape(w.trim(o?r.html():""))+'"'+(!a&&s?"":" readonly")+' class="layui-input'+(s?"":" layui-unselect")+(a?" "+D:"")+'">','<i class="layui-edge"></i></div>','<dl class="layui-anim layui-anim-upbit'+(n.find("optgroup")[0]?" layui-select-group":"")+'">',(t=n.find("*"),l=[],layui.each(t,function(e,t){0!==e||t.value?"optgroup"===t.tagName.toLowerCase()?l.push("<dt>"+t.label+"</dt>"):l.push('<dd lay-value="'+d.escape(t.value)+'" class="'+(o===t.value?T:"")+(t.disabled?" "+D:"")+'">'+w.trim(t.innerHTML)+"</dd>"):l.push('<dd lay-value="" class="layui-select-tips">'+w.trim(t.innerHTML||c)+"</dd>")}),0===l.length&&l.push('<dd lay-value="" class="'+D+'">没有选项</dd>'),l.join("")+"</dl>"),"</div>"].join(""));i[0]&&i.remove(),n.after(r),u.call(this,r,a,s)})},checkbox:function(e){var s={checkbox:["layui-form-checkbox","layui-form-checked","checkbox"],_switch:["layui-form-switch","layui-form-onswitch","switch"]},e=e||i.find("input[type=checkbox]");e.each(function(e,t){var n=w(this),i=n.attr("lay-skin"),a=(n.attr("lay-text")||"").split("|"),o=this.disabled,r=s[i="switch"===i?"_"+i:i]||s.checkbox;if("string"==typeof n.attr("lay-ignore"))return n.show();var l=n.next("."+r[0]),t=w(['<div class="layui-unselect '+r[0],t.checked?" "+r[1]:"",o?" layui-checkbox-disabled "+D:"",'"',i?' lay-skin="'+i+'"':"",">",(o={checkbox:[t.title.replace(/\s/g,"")?"<span>"+t.title+"</span>":"",'<i class="layui-icon layui-icon-ok"></i>'].join(""),_switch:"<em>"+((t.checked?a[0]:a[1])||"")+"</em><i></i>"})[i]||o.checkbox,"</div>"].join(""));l[0]&&l.remove(),n.after(t),function(n,i){var a=w(this);n.on("click",function(){var e=a.attr("lay-filter"),t=(a.attr("lay-text")||"").split("|");a[0].disabled||(a[0].checked?(a[0].checked=!1,n.removeClass(i[1]).find("em").text(t[1])):(a[0].checked=!0,n.addClass(i[1]).find("em").text(t[0])),layui.event.call(a[0],C,i[2]+"("+e+")",{elem:a[0],value:a[0].value,othis:n}))})}.call(this,t,r)})},radio:function(e){var r="layui-form-radio",l=["&#xe643;","&#xe63f;"],e=e||i.find("input[type=radio]");e.each(function(e,t){var n=w(this),i=n.next("."+r),a=this.disabled;if("string"==typeof n.attr("lay-ignore"))return n.show();i[0]&&i.remove();a=w(['<div class="layui-unselect '+r,t.checked?" "+r+"ed":"",(a?" layui-radio-disabled "+D:"")+'">','<i class="layui-anim layui-icon">'+l[t.checked?0:1]+"</i>","<div>"+(i=t.title||"",i="string"==typeof n.next().attr("lay-radio")?n.next().html():i)+"</div>","</div>"].join(""));n.after(a),function(i){var a=w(this),o="layui-anim-scaleSpring";i.on("click",function(){var e=a[0].name,t=a.parents(s),n=a.attr("lay-filter"),e=t.find("input[name="+e.replace(/(\.|#|\[|\])/g,"\\$1")+"]");a[0].disabled||(layui.each(e,function(){var e=w(this).next("."+r);this.checked=!1,e.removeClass(r+"ed"),e.find(".layui-icon").removeClass(o).html(l[1])}),a[0].checked=!0,i.addClass(r+"ed"),i.find(".layui-icon").addClass(o).html(l[0]),layui.event.call(a[0],C,"radio("+n+")",{elem:a[0],value:a[0].value,othis:i}))})}.call(this,a)})}};return"object"===layui.type(e)?e.each(function(e,t){var n=w(t);n.closest(s).length&&("SELECT"===t.tagName?a.select(n):"INPUT"===t.tagName&&("checkbox"===(t=t.type)||"radio"===t?a[t](n):a.input(n)))}):e?a[e]?a[e]():o.error('不支持的 "'+e+'" 表单渲染'):layui.each(a,function(e,t){t()}),this},t.prototype.validate=function(e){var u=null,d=r.config.verify,f="layui-form-danger";return!(e=w(e))[0]||(e.attr("lay-verify")!==undefined||!1!==this.validate(e.find("*[lay-verify]")))&&(layui.each(e,function(e,r){var l=w(this),t=(l.attr("lay-verify")||"").split("|"),s=l.attr("lay-verType"),c=l.val();if(l.removeClass(f),layui.each(t,function(e,t){var n="",i=d[t];if(i){var a="function"==typeof i?n=i(c,r):!i[0].test(c),o="select"===r.tagName.toLowerCase()||/^checkbox|radio$/.test(r.type),n=n||i[1];if("required"===t&&(n=l.attr("lay-reqText")||n),a)return"tips"===s?p.tips(n,"string"!=typeof l.attr("lay-ignore")&&o?l.next():l,{tips:1}):"alert"===s?p.alert(n,{title:"提示",shadeClose:!0}):/\bstring|number\b/.test(typeof n)&&p.msg(n,{icon:5,shift:6}),setTimeout(function(){(o?l.next().find("input"):r).focus()},7),l.addClass(f),u=!0}}),u)return u}),!u)},t.prototype.submit=function(e,t){var n=w(this),e="string"==typeof e?e:n.attr("lay-filter"),i=this.getFormElem?this.getFormElem(e):n.parents(s).eq(0),a=i.find("*[lay-verify]");if(!r.validate(a))return!1;a=r.getValue(null,i),i={elem:this.getFormElem?window.event&&window.event.target:this,form:(this.getFormElem?i:n.parents("form"))[0],field:a};return"function"==typeof t&&t(i),layui.event.call(this,C,"submit("+e+")",i)}),r=new t,t=w(document),L=w(window);w(function(){r.render()}),t.on("reset",s,function(){var e=w(this).attr("lay-filter");setTimeout(function(){r.render(null,e)},50)}),t.on("submit",s,n).on("click","*[lay-submit]",n),e(C,r)});layui.define(["laytpl","laypage","form","util"],function(e){"use strict";var m=layui.$,g=layui.laytpl,l=layui.laypage,v=layui.layer,f=layui.form,b=layui.util,p=layui.hint(),h=layui.device(),x={config:{checkName:"LAY_CHECKED",indexName:"LAY_TABLE_INDEX",disabledName:"LAY_DISABLED"},cache:{},index:layui.table?layui.table.index+1e4:0,set:function(e){var t=this;return t.config=m.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,w,e,t)}},y=function(){var n=this,e=n.config,i=e.id||e.index;return i&&(y.that[i]=n,y.config[i]=e),{config:e,reload:function(e,t){n.reload.call(n,e,t)},reloadData:function(e,t){x.reloadData(i,e,t)},setColsWidth:function(){n.setColsWidth.call(n)},resize:function(){n.resize.call(n)}}},a=function(e){var t=y.config[e];return t||p.error(e?"The table instance with ID '"+e+"' not found":"ID argument required"),t||null},k=function(e){var t=this.config||{},n=(e=e||{}).item3,i=e.content,t=(("escape"in n?n:t).escape&&(i=b.escape(i)),e.text&&n.exportTemplet||n.templet||n.toolbar);return t&&(i="function"==typeof t?t.call(n,e.tplData,e.obj):g(m(t).html()||String(i)).render(m.extend({LAY_COL:n},e.tplData))),e.text?m("<div>"+i+"</div>").text():i},w="table",C="layui-hide",s="layui-hide-v",c="layui-none",u="layui-table-view",d=".layui-table-header",T=".layui-table-body",E=".layui-table-pageview",D=".layui-table-sort",L="layui-table-edit",S="layui-table-hover",A="layui-table-col-special",N="LAY_TABLE_MOVE_DICT",t=function(e){return['<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{=d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{=d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>',"<thead>","{{# layui.each(d.data.cols, function(i1, item1){ }}","<tr>","{{# layui.each(item1, function(i2, item2){ }}",'{{# if(item2.fixed && item2.fixed !== "right"){ left = true; } }}','{{# if(item2.fixed === "right"){ right = true; } }}',(e=e||{}).fixed&&"right"!==e.fixed?'{{# if(item2.fixed && item2.fixed !== "right"){ }}':"right"===e.fixed?'{{# if(item2.fixed === "right"){ }}':"","{{# var isSort = !(item2.colGroup) && item2.sort; }}",'<th data-field="{{= item2.field||i2 }}" data-key="{{=d.index}}-{{=i1}}-{{=i2}}" {{# if( item2.parentKey){ }}data-parentkey="{{= item2.parentKey }}"{{# } }} {{# if(item2.minWidth){ }}data-minwidth="{{=item2.minWidth}}"{{# } }} {{#if(item2.colspan){}} colspan="{{=item2.colspan}}"{{#} if(item2.rowspan){}} rowspan="{{=item2.rowspan}}"{{#}}} {{# if(item2.unresize || item2.colGroup){ }}data-unresize="true"{{# } }} class="{{# if(item2.hide){ }}layui-hide{{# } }}{{# if(isSort){ }} layui-unselect{{# } }}{{# if(!item2.field){ }} layui-table-col-special{{# } }}">','<div class="layui-table-cell laytable-cell-',"{{# if(item2.colGroup){ }}","group","{{# } else { }}","{{=d.index}}-{{=i1}}-{{=i2}}",'{{# if(item2.type !== "normal"){ }}'," laytable-cell-{{= item2.type }}","{{# } }}","{{# } }}",'" {{#if(item2.align){}}align="{{=item2.align}}"{{#}}}>','{{# if(item2.type === "checkbox"){ }}','<input type="checkbox" name="layTableCheckbox" lay-skin="primary" lay-filter="layTableAllChoose" {{# if(item2[d.data.checkName]){ }}checked{{# }; }}>',"{{# } else { }}",'<span>{{-item2.title||""}}</span>',"{{# if(isSort){ }}",'<span class="layui-table-sort layui-inline"><i class="layui-edge layui-table-sort-asc" title="升序"></i><i class="layui-edge layui-table-sort-desc" title="降序"></i></span>',"{{# } }}","{{# } }}","</div>","</th>",e.fixed?"{{# }; }}":"","{{# }); }}","</tr>","{{# }); }}","</thead>","</table>"].join("")},n=['<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{=d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{=d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>',"<tbody></tbody>","</table>"].join(""),j=[,"{{# if(d.data.toolbar){ }}",'<div class="layui-table-tool">','<div class="layui-table-tool-temp"></div>','<div class="layui-table-tool-self"></div>',"</div>","{{# } }}",'<div class="layui-table-box">',"{{# if(d.data.loading){ }}",'<div class="layui-table-init" style="background-color: #fff;">','<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>',"</div>","{{# } }}","{{# var left, right; }}",'<div class="layui-table-header">',t(),"</div>",'<div class="layui-table-body layui-table-main">',n,"</div>","{{# if(left){ }}",'<div class="layui-table-fixed layui-table-fixed-l">','<div class="layui-table-header">',t({fixed:!0}),"</div>",'<div class="layui-table-body">',n,"</div>","</div>","{{# }; }}","{{# if(right){ }}",'<div class="layui-table-fixed layui-table-fixed-r layui-hide">','<div class="layui-table-header">',t({fixed:"right"}),'<div class="layui-table-mend"></div>',"</div>",'<div class="layui-table-body">',n,"</div>","</div>","{{# }; }}","</div>","{{# if(d.data.totalRow){ }}",'<div class="layui-table-total">','<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{=d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{=d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>','<tbody><tr><td><div class="layui-table-cell" style="visibility: hidden;">Total</div></td></tr></tbody>',"</table>","</div>","{{# } }}",'<div class="layui-table-column layui-table-page layui-hide">','<div class="layui-inline layui-table-pageview" id="layui-table-page{{=d.index}}"></div>',"</div>","<style>","{{# layui.each(d.data.cols, function(i1, item1){","layui.each(item1, function(i2, item2){ }}",".laytable-cell-{{=d.index}}-{{=i1}}-{{=i2}}{ ","{{# if(item2.width){ }}","width: {{=item2.width}}px;","{{# } }}"," }","{{# });","}); }}","{{# if(d.data.lineStyle){",'var cellClassName = ".layui-table-view-"+ d.index +" .layui-table-body .layui-table .layui-table-cell";',"}}","{{= cellClassName }}{","display: -webkit-box; -webkit-box-align: center; white-space: normal; {{- d.data.lineStyle }} ","}","{{= cellClassName }}:hover{overflow: auto;}","{{# } }}","{{# if(d.data.css){ }}","{{- d.data.css }}","{{# } }}","</style>"].join(""),M=m(window),H=m(document),i=function(e){this.index=++x.index,this.config=m.extend({},this.config,x.config,e),this.render()},I=(i.prototype.config={limit:10,loading:!0,escape:!0,cellMinWidth:60,editTrigger:"click",defaultToolbar:["filter","exports","print"],autoSort:!0,text:{none:"No data"}},i.prototype.render=function(e){var t=this,n=t.config;if(n.elem=m(n.elem),n.where=n.where||{},n.id=n.id||n.elem.attr("id")||t.index,n.request=m.extend({pageName:"page",limitName:"limit"},n.request),n.response=m.extend({statusName:"code",statusCode:0,msgName:"msg",dataName:"data",totalRowName:"totalRow",countName:"count"},n.response),"object"==typeof n.page&&(n.limit=n.page.limit||n.limit,n.limits=n.page.limits||n.limits,t.page=n.page.curr=n.page.curr||1,delete n.page.elem,delete n.page.jump),!n.elem[0])return t;if("reloadData"===e)return t.pullData(t.page,{type:"reloadData"});n.height&&/^full-\d+$/.test(n.height)&&(t.fullHeightGap=n.height.split("-")[1],n.height=M.height()-t.fullHeightGap),t.setInit();var i,a,e=n.elem,o=e.next("."+u),r=t.elem=m("<div></div>");r.addClass((i=[u,u+"-"+t.index,"layui-form","layui-border-box"],n.className&&i.push(n.className),i.join(" "))).attr({"lay-filter":"LAY-TABLE-FORM-DF-"+t.index,"lay-id":n.id,style:(i=[],n.width&&i.push("width:"+n.width+"px;"),n.height&&i.push("height:"+n.height+"px;"),i.join(""))}).html(g(j).render({data:n,index:t.index})),n.index=t.index,t.key=n.id||n.index,o[0]&&o.remove(),e.after(r),t.layTool=r.find(".layui-table-tool"),t.layBox=r.find(".layui-table-box"),t.layHeader=r.find(d),t.layMain=r.find(".layui-table-main"),t.layBody=r.find(T),t.layFixed=r.find(".layui-table-fixed"),t.layFixLeft=r.find(".layui-table-fixed-l"),t.layFixRight=r.find(".layui-table-fixed-r"),t.layTotal=r.find(".layui-table-total"),t.layPage=r.find(".layui-table-page"),t.renderToolbar(),t.renderPagebar(),t.fullSize(),1<n.cols.length&&(i=t.layFixed.find(d).find("th"),a=t.layHeader.first(),layui.each(i,function(e,t){(t=m(t)).height(a.find('th[data-key="'+t.attr("data-key")+'"]').height()+"px")})),t.pullData(t.page),t.events()},i.prototype.initOpts=function(e){this.config;e.checkbox&&(e.type="checkbox"),e.space&&(e.type="space"),e.type||(e.type="normal"),"normal"!==e.type&&(e.unresize=!0,e.width=e.width||{checkbox:50,radio:50,space:30,numbers:60}[e.type])},i.prototype.setInit=function(e){var i,n,l=this,s=l.config;if(s.clientWidth=s.width||(i=function(e){var t,n=(e=e||s.elem.parent()).width();try{t="none"===e.css("display")}catch(e){}return!e[0]||n&&!t?n:i(e.parent())})(),"width"===e)return s.clientWidth;s.css&&-1===s.css.indexOf(u)&&(n=s.css.split("}"),layui.each(n,function(e,t){t&&(n[e]="."+u+"-"+l.index+" "+t)}),s.css=n.join("}"));var c=function(n,e,i,a){var o,r;a?(a.key=n+"-"+i,a.hide=a.hide||!1,a.colspan=a.colspan||1,a.rowspan=a.rowspan||1,l.initOpts(a),(o=n+(parseInt(a.rowspan)||1))<s.cols.length?(a.colGroup=!0,r=0,layui.each(s.cols[o],function(e,t){t.HAS_PARENT||1<=r&&r==(a.colspan||1)||(t.HAS_PARENT=!0,t.parentKey=n+"-"+i,r+=parseInt(1<t.colspan?t.colspan:1),c(o,s.cols[o],e,t))})):a.colGroup=!1):e.splice(i,1)};layui.each(s.cols,function(n,i){if(n)return!0;layui.each(i,function(e,t){c(n,i,e,t)})})},i.prototype.renderToolbar=function(){var e=this.config,t=['<div class="layui-inline" lay-event="add"><i class="layui-icon layui-icon-add-1"></i></div>','<div class="layui-inline" lay-event="update"><i class="layui-icon layui-icon-edit"></i></div>','<div class="layui-inline" lay-event="delete"><i class="layui-icon layui-icon-delete"></i></div>'].join(""),n=this.layTool.find(".layui-table-tool-temp"),i=("default"===e.toolbar?n.html(t):"string"==typeof e.toolbar&&(t=m(e.toolbar).html()||"")&&n.html(g(t).render(e)),{filter:{title:"筛选列",layEvent:"LAYTABLE_COLS",icon:"layui-icon-cols"},exports:{title:"导出",layEvent:"LAYTABLE_EXPORT",icon:"layui-icon-export"},print:{title:"打印",layEvent:"LAYTABLE_PRINT",icon:"layui-icon-print"}}),a=[];"object"==typeof e.defaultToolbar&&layui.each(e.defaultToolbar,function(e,t){t="string"==typeof t?i[t]:t;t&&a.push('<div class="layui-inline" title="'+t.title+'" lay-event="'+t.layEvent+'"><i class="layui-icon '+t.icon+'"></i></div>')}),this.layTool.find(".layui-table-tool-self").html(a.join(""))},i.prototype.renderPagebar=function(){var e,t=this.config,n=this.layPagebar=m('<div class="layui-inline layui-table-pagebar"></div>');t.pagebar&&((e=m(t.pagebar).html()||"")&&n.append(g(e).render(t)),this.layPage.append(n))},i.prototype.setParentCol=function(e,t){var n=this.config,i=this.layHeader.find('th[data-key="'+n.index+"-"+t+'"]'),a=parseInt(i.attr("colspan"))||0;i[0]&&(t=t.split("-"),t=n.cols[t[0]][t[1]],e?a--:a++,i.attr("colspan",a),i[a<1?"addClass":"removeClass"](C),t.colspan=a,t.hide=a<1,(n=i.data("parentkey"))&&this.setParentCol(e,n))},i.prototype.setColsPatch=function(){var n=this,e=n.config;layui.each(e.cols,function(e,t){layui.each(t,function(e,t){t.hide&&n.setParentCol(t.hide,t.parentKey)})})},i.prototype.setColsWidth=function(){var t,n,i=this,r=i.config,a=0,l=0,s=0,c=0,u=i.setInit("width"),e=(i.eachCols(function(e,t){t.hide||a++}),u=u-("line"===r.skin||"nob"===r.skin?2:a+1)-i.getScrollWidth(i.layMain[0])-1,function(o){layui.each(r.cols,function(e,a){layui.each(a,function(e,t){var n=0,i=t.minWidth||r.cellMinWidth;t?t.colGroup||t.hide||(o?s&&s<i&&(l--,n=i):(n=t.width||0,/\d+%$/.test(n)?(n=Math.floor(parseFloat(n)/100*u))<i&&(n=i):n||(t.width=n=0,l++)),t.hide&&(n=0),c+=n):a.splice(e,1)})}),c<u&&l&&(s=(u-c)/l)}),o=(e(),e(!0),i.autoColNums=l,i.eachCols(function(e,t){var n=t.minWidth||r.cellMinWidth;t.colGroup||t.hide||(0===t.width?i.getCssRule(r.index+"-"+t.key,function(e){e.style.width=Math.floor(n<=s?s:n)+"px"}):/\d+%$/.test(t.width)&&i.getCssRule(r.index+"-"+t.key,function(e){e.style.width=Math.floor(parseFloat(t.width)/100*u)+"px"}))}),i.layMain.width()-i.getScrollWidth(i.layMain[0])-i.layMain.children("table").outerWidth());i.autoColNums&&-a<=o&&o<=a&&(e=(n=(t=function(e){return!(e=e||i.layHeader.eq(0).find("thead th:last-child")).data("field")&&e.prev()[0]?t(e.prev()):e})()).data("key"),i.getCssRule(e,function(e){var t=e.style.width||n.outerWidth();e.style.width=parseFloat(t)+o+"px",0<i.layMain.height()-i.layMain.prop("clientHeight")&&(e.style.width=parseFloat(e.style.width)-1+"px")})),i.loading(!0)},i.prototype.resize=function(){this.fullSize(),this.setColsWidth(),this.scrollPatch()},i.prototype.reload=function(e,t,n){var i=this;e=e||{},delete i.haveInit,layui.each(e,function(e,t){"array"===layui.type(t)&&delete i.config[e]}),i.config=m.extend(t,{},i.config,e),i.render(n)},i.prototype.errorView=function(e){var t=this,n=t.layMain.find("."+c),e=m('<div class="'+c+'">'+(e||"Error")+"</div>");n[0]&&(t.layNone.remove(),n.remove()),t.layFixed.addClass(C),t.layMain.find("tbody").html(""),t.layMain.append(t.layNone=e),t.layTotal.addClass(s),t.layPage.find(E).addClass(s),x.cache[t.key]=[],t.syncCheckAll()},i.prototype.page=1,i.prototype.pullData=function(t,n){var e,i=this,a=i.config,o=a.request,r=a.response,l=function(){"object"==typeof a.initSort&&i.sort(a.initSort.field,a.initSort.type)};n=n||{},"function"==typeof a.before&&a.before(a),i.startTime=(new Date).getTime(),a.url?((e={})[o.pageName]=t,e[o.limitName]=a.limit,o=m.extend(e,a.where),a.contentType&&0==a.contentType.indexOf("application/json")&&(o=JSON.stringify(o)),i.loading(),m.ajax({type:a.method||"get",url:a.url,contentType:a.contentType,data:o,dataType:a.dataType||"json",jsonpCallback:a.jsonpCallback,headers:a.headers||{},success:function(e){(e="function"==typeof a.parseData?a.parseData(e)||e:e)[r.statusName]!=r.statusCode?(i.renderForm(),i.errorView(e[r.msgName]||'返回的数据不符合规范，正确的成功状态码应为："'+r.statusName+'": '+r.statusCode)):(i.renderData({res:e,curr:t,count:e[r.countName],type:n.type}),l(),a.time=(new Date).getTime()-i.startTime+" ms"),i.setColsWidth(),"function"==typeof a.done&&a.done(e,t,e[r.countName])},error:function(e,t){i.errorView("请求异常，错误提示："+t),i.renderForm(),i.setColsWidth(),"function"==typeof a.error&&a.error(e,t)}})):"array"===layui.type(a.data)&&(e=t*a.limit-a.limit,(o={})[r.dataName]=a.data.concat().splice(e,a.limit),o[r.countName]=a.data.length,"object"==typeof a.totalRow&&(o[r.totalRowName]=m.extend({},a.totalRow)),i.renderData({res:o,curr:t,count:o[r.countName],type:n.type}),l(),i.setColsWidth(),"function"==typeof a.done&&a.done(o,t,o[r.countName]))},i.prototype.eachCols=function(e){return x.eachCols(null,e,this.config.cols),this},i.prototype.col=function(e){try{return e=e.split("-"),this.config.cols[e[1]][e[2]]}catch(e){return p.error(e),{}}},i.prototype.renderData=function(e){var d=this,f=d.config,t=e.res,a=e.curr,n=e.count,o=e.sort,i=t[f.response.dataName]||[],t=t[f.response.totalRowName],p=[],h=[],y=[],r=function(){var u;if(f.HAS_SET_COLS_PATCH||d.setColsPatch(),f.HAS_SET_COLS_PATCH=!0,!o&&d.sortKey)return d.sort(d.sortKey.field,d.sortKey.sort,!0);layui.each(i,function(r,l){var n=[],i=[],s=[],c=r+f.limit*(a-1)+1;"array"===layui.type(l)&&0===l.length||(o||(l[x.config.indexName]=r),d.eachCols(function(e,a){var e=a.field||e,t=f.index+"-"+a.key,o=l[e];o!==undefined&&null!==o||(o=""),a.colGroup||(t=['<td data-field="'+e+'" data-key="'+t+'" '+(e=[],a.templet&&e.push('data-content="'+b.escape(o)+'"'),a.toolbar&&e.push('data-off="true"'),a.event&&e.push('lay-event="'+a.event+'"'),a.minWidth&&e.push('data-minwidth="'+a.minWidth+'"'),e.join(" "))+' class="'+(e=[],a.hide&&e.push(C),a.field||e.push(A),e.join(" "))+'">','<div class="layui-table-cell laytable-cell-'+("normal"===a.type?t:t+" laytable-cell-"+a.type)+'"'+(a.align?' align="'+a.align+'"':"")+(e=[],a.style&&e.push('style="'+a.style+'"'),e.join(" "))+">"+function(){var e,t=m.extend(!0,{LAY_INDEX:c,LAY_COL:a},l),n=x.config.checkName,i=x.config.disabledName;switch(a.type){case"checkbox":return'<input type="checkbox" name="layTableCheckbox" lay-skin="primary" '+(e=[],a[n]&&(l[n]=a[n],a[n]&&(e[0]="checked")),t[n]&&(e[0]="checked"),t[i]&&e.push("disabled"),e.join(" "))+">";case"radio":return t[n]&&(u=r),'<input type="radio" name="layTableRadio_'+f.index+'" '+(e=[],t[n]&&(e[0]="checked"),t[i]&&e.push("disabled"),e.join(" "))+' lay-type="layTableRadio">';case"numbers":return c}return a.toolbar?g(m(a.toolbar).html()||"").render(t):k.call(d,{item3:a,content:o,tplData:t})}(),"</div></td>"].join(""),n.push(t),a.fixed&&"right"!==a.fixed&&i.push(t),"right"===a.fixed&&s.push(t))}),p.push('<tr data-index="'+r+'">'+n.join("")+"</tr>"),h.push('<tr data-index="'+r+'">'+i.join("")+"</tr>"),y.push('<tr data-index="'+r+'">'+s.join("")+"</tr>"))}),"fixed"===f.scrollPos&&"reloadData"===e.type||d.layBody.scrollTop(0),"reset"===f.scrollPos&&d.layBody.scrollLeft(0),d.layMain.find("."+c).remove(),d.layMain.find("tbody").html(p.join("")),d.layFixLeft.find("tbody").html(h.join("")),d.layFixRight.find("tbody").html(y.join("")),d.renderForm(),"number"==typeof u&&d.setThisRowChecked(u),d.syncCheckAll(),d.fullSize(),d.haveInit?d.scrollPatch():setTimeout(function(){d.scrollPatch()},50),d.haveInit=!0,v.close(d.tipsIndex)};return x.cache[d.key]=i,d.layTotal[0==i.length?"addClass":"removeClass"](s),d.layPage[f.page||f.pagebar?"removeClass":"addClass"](C),d.layPage.find(E)[!f.page||0==n||0===i.length&&1==a?"addClass":"removeClass"](s),0===i.length?(d.renderForm(),d.errorView(f.text.none)):(d.layFixLeft.removeClass(C),o?r():(r(),d.renderTotal(i,t),d.layTotal&&d.layTotal.removeClass(C),void(f.page&&(f.page=m.extend({elem:"layui-table-page"+f.index,count:n,limit:f.limit,limits:f.limits||[10,20,30,40,50,60,70,80,90],groups:3,layout:["prev","page","next","skip","count","limit"],prev:'<i class="layui-icon">&#xe603;</i>',next:'<i class="layui-icon">&#xe602;</i>',jump:function(e,t){t||(d.page=e.curr,f.limit=e.limit,d.pullData(e.curr))}},f.page),f.page.count=n,l.render(f.page)))))},i.prototype.renderTotal=function(e,r){var l,s=this,c=s.config,u={};c.totalRow&&(layui.each(e,function(e,i){"array"===layui.type(i)&&0===i.length||s.eachCols(function(e,t){var e=t.field||e,n=i[e];t.totalRow&&(u[e]=(u[e]||0)+(parseFloat(n)||0))})}),s.dataTotal={},l=[],s.eachCols(function(e,t){var n,e=t.field||e,i=r&&r[t.field],a=(n=t.totalRowText||"",o="totalRowDecimals"in t?t.totalRowDecimals:2,o=parseFloat(u[e]).toFixed(o),(a={LAY_COL:t})[e]=o,o=t.totalRow&&k.call(s,{item3:t,content:o,tplData:a})||n,i||o),o=['<td data-field="'+e+'" data-key="'+c.index+"-"+t.key+'" '+(n=[],t.align&&n.push('align="'+t.align+'"'),t.minWidth&&n.push('data-minwidth="'+t.minWidth+'"'),n.join(" "))+' class="'+(o=[],t.hide&&o.push(C),t.field||o.push(A),o.join(" "))+'">','<div class="layui-table-cell laytable-cell-'+(n=c.index+"-"+t.key,"normal"===t.type?n:n+" laytable-cell-"+t.type)+'"'+(o=[],t.style&&o.push('style="'+t.style+'"'),o.join(" "))+">"+("string"==typeof(n=t.totalRow||c.totalRow)?g(n).render(m.extend({TOTAL_NUMS:i||u[e],LAY_COL:t},t)):a),"</div></td>"].join("");t.field&&(s.dataTotal[e]=a),l.push(o)}),s.layTotal.find("tbody").html("<tr>"+l.join("")+"</tr>"))},i.prototype.getColElem=function(e,t){var n=this.config;return e.eq(0).find(".laytable-cell-"+n.index+"-"+t+":eq(0)")},i.prototype.renderForm=function(e){this.config;var t=this.elem.attr("lay-filter");f.render(e,t)},i.prototype.setThisRowChecked=function(e){this.config;var t="layui-table-click";this.layBody.find('tr[data-index="'+e+'"]').addClass(t).siblings("tr").removeClass(t)},i.prototype.sort=function(a,e,t,n){var i,o=this,r={},l=o.config,s=l.elem.attr("lay-filter"),c=x.cache[o.key];"string"==typeof a&&(u=a,o.layHeader.find("th").each(function(e,t){var n=m(this),i=n.data("field");if(i===a)return a=n,u=i,!1}));try{var u=u||a.data("field"),d=a.data("key");if(o.sortKey&&!t&&u===o.sortKey.field&&e===o.sortKey.sort)return;var f=o.layHeader.find("th .laytable-cell-"+d).find(D);o.layHeader.find("th").find(D).removeAttr("lay-sort"),f.attr("lay-sort",e||null),o.layFixed.find("th")}catch(e){p.error("Table modules: sort field '"+u+"' not matched")}o.sortKey={field:u,sort:e},l.autoSort&&("asc"===e?i=layui.sort(c,u):"desc"===e?i=layui.sort(c,u,!0):(i=layui.sort(c,x.config.indexName),delete o.sortKey,delete l.initSort)),r[l.response.dataName]=i||c,o.renderData({res:r,curr:o.page,count:o.count,sort:!0}),n&&(l.initSort={field:u,type:e},layui.event.call(a,w,"sort("+s+")",l.initSort))},i.prototype.loading=function(e){var t=this;t.config.loading&&(e?(t.layInit&&t.layInit.remove(),delete t.layInit,t.layBox.find(".layui-table-init").remove()):(t.layInit=m(['<div class="layui-table-init">','<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>',"</div>"].join("")),t.layBox.append(t.layInit)))},i.prototype.setCheckData=function(e,t){var n=this.config,i=x.cache[this.key];i[e]&&"array"!==layui.type(i[e])&&(i[e][n.checkName]=t)},i.prototype.syncCheckAll=function(){var e=this,i=e.config,t=e.layHeader.find('input[name="layTableCheckbox"]'),n=function(n){return e.eachCols(function(e,t){"checkbox"===t.type&&(t[i.checkName]=n)}),n};t[0]&&(x.checkStatus(e.key).isAll?(t[0].checked||(t.prop("checked",!0),e.renderForm("checkbox")),n(!0)):(t[0].checked&&(t.prop("checked",!1),e.renderForm("checkbox")),n(!1)))},i.prototype.getCssRule=function(n,i){var e=this.elem.find("style")[0],e=e.sheet||e.styleSheet||{},e=e.cssRules||e.rules;layui.each(e,function(e,t){if(t.selectorText===".laytable-cell-"+n)return i(t),!0})},i.prototype.fullSize=function(){var e=this,t=e.config,n=t.height;e.fullHeightGap&&(n=M.height()-e.fullHeightGap,e.elem.css("height",n=n<135?135:n)),n&&(n=parseFloat(n)-(e.layHeader.outerHeight()||38),t.toolbar&&(n-=e.layTool.outerHeight()||50),t.totalRow&&(n-=e.layTotal.outerHeight()||40),(t.page||t.pagebar)&&(n-=e.layPage.outerHeight()||43),e.layMain.outerHeight(n))},i.prototype.getScrollWidth=function(e){var t=0;return e?t=e.offsetWidth-e.clientWidth:((e=document.createElement("div")).style.width="100px",e.style.height="100px",e.style.overflowY="scroll",document.body.appendChild(e),t=e.offsetWidth-e.clientWidth,document.body.removeChild(e)),t},i.prototype.scrollPatch=function(){var e=this,t=e.layMain.children("table"),n=e.layMain.width()-e.layMain.prop("clientWidth"),i=e.layMain.height()-e.layMain.prop("clientHeight"),a=(e.getScrollWidth(e.layMain[0]),t.outerWidth()-e.layMain.width()),o=function(e){var t;n&&i?(e=e.eq(0)).find(".layui-table-patch")[0]||((t=m('<th class="layui-table-patch"><div class="layui-table-cell"></div></th>')).find("div").css({width:n}),e.find("tr").append(t)):e.find(".layui-table-patch").remove()};o(e.layHeader),o(e.layTotal);o=e.layMain.height()-i;e.layFixed.find(T).css("height",t.height()>=o?o:"auto"),e.layFixRight[0<a?"removeClass":"addClass"](C),e.layFixRight.css("right",n-1)},i.prototype.events=function(){var u=this,s=u.config,c=s.elem.attr("lay-filter"),e=u.layHeader.find("th"),d=".layui-table-cell",i=m("body"),a={},o=(u.layTool.on("click","*[lay-event]",function(e){var n,i=m(this),t=i.attr("lay-event"),a=function(e){var t=m(e.list),n=m('<ul class="layui-table-tool-panel"></ul>');n.html(t),s.height&&n.css("max-height",s.height-(u.layTool.outerHeight()||50)),i.find(".layui-table-tool-panel")[0]||i.append(n),u.renderForm(),n.on("click",function(e){layui.stope(e)}),e.done&&e.done(n,t)};switch(layui.stope(e),H.trigger("table.tool.panel.remove"),v.close(u.tipsIndex),t){case"LAYTABLE_COLS":a({list:(n=[],u.eachCols(function(e,t){t.field&&"normal"==t.type&&n.push('<li><input type="checkbox" name="'+t.field+'" data-key="'+t.key+'" data-parentkey="'+(t.parentKey||"")+'" lay-skin="primary" '+(t.hide?"":"checked")+' title="'+b.escape(t.title||t.field)+'" lay-filter="LAY_TABLE_TOOL_COLS"></li>')}),n.join("")),done:function(){f.on("checkbox(LAY_TABLE_TOOL_COLS)",function(e){var e=m(e.elem),i=this.checked,a=e.data("key"),o=e.data("parentkey");layui.each(s.cols,function(n,e){layui.each(e,function(e,t){n+"-"+e===a&&(e=t.hide,t.hide=!i,u.elem.find('*[data-key="'+s.index+"-"+a+'"]')[i?"removeClass":"addClass"](C),e!=t.hide&&u.setParentCol(!i,o),u.resize())})})})}});break;case"LAYTABLE_EXPORT":h.ie?v.tips("导出功能不支持 IE，请用 Chrome 等高级浏览器导出",this,{tips:3}):a({list:['<li data-type="csv">导出 csv 格式文件</li>','<li data-type="xls">导出 xls 格式文件</li>'].join(""),done:function(e,t){t.on("click",function(){var e=m(this).data("type");x.exportFile.call(u,s.id,null,e)})}});break;case"LAYTABLE_PRINT":var o=window.open("about:blank","_blank"),r=["<style>","body{font-size: 12px; color: #5F5F5F;}","table{width: 100%; border-collapse: collapse; border-spacing: 0;}","th,td{line-height: 20px; padding: 9px 15px; border: 1px solid #ccc; text-align: left; font-size: 12px; color: #5F5F5F;}","a{color: #5F5F5F; text-decoration:none;}","*.layui-hide{display: none}","</style>"].join(""),l=m(u.layHeader.html());l.append(u.layMain.find("table").html()),l.append(u.layTotal.find("table").html()),l.find("th.layui-table-patch").remove(),l.find("thead>tr>th."+A).filter(function(e,t){return!m(t).children(".laytable-cell-group").length}).remove(),l.find("tbody>tr>td."+A).remove(),o.document.write(r+l.prop("outerHTML")),o.document.close(),o.print(),o.close()}layui.event.call(this,w,"toolbar("+c+")",m.extend({event:t,config:s},{}))}),u.layPagebar.on("click","*[lay-event]",function(e){var t=m(this).attr("lay-event");layui.event.call(this,w,"pagebar("+c+")",m.extend({event:t,config:s},{}))}),e.on("mousemove",function(e){var t=m(this),n=t.offset().left,e=e.clientX-n;t.data("unresize")||y.eventMoveElem||(a.allowResize=t.width()-e<=10,i.css("cursor",a.allowResize?"col-resize":""))}).on("mouseleave",function(){m(this);y.eventMoveElem||i.css("cursor","")}).on("mousedown",function(e){var t,n=m(this);a.allowResize&&(t=n.data("key"),e.preventDefault(),a.offset=[e.clientX,e.clientY],u.getCssRule(t,function(e){var t=e.style.width||n.outerWidth();a.rule=e,a.ruleWidth=parseFloat(t),a.minWidth=n.data("minwidth")||s.cellMinWidth}),n.data(N,a),y.eventMoveElem=n)}),y.docEvent||H.on("mousemove",function(e){var t;y.eventMoveElem&&(t=y.eventMoveElem.data(N)||{},y.eventMoveElem.data("resizing",1),e.preventDefault(),t.rule&&((e=t.ruleWidth+e.clientX-t.offset[0])<t.minWidth&&(e=t.minWidth),t.rule.style.width=e+"px",v.close(u.tipsIndex)))}).on("mouseup",function(e){y.eventMoveElem&&(a={},i.css("cursor",""),u.scrollPatch(),y.eventMoveElem.removeData(N),delete y.eventMoveElem)}),y.docEvent=!0,e.on("click",function(e){var t=m(this),n=t.find(D),i=n.attr("lay-sort");if(!n[0]||1===t.data("resizing"))return t.removeData("resizing");u.sort(t,"asc"===i?"desc":"desc"===i?null:"asc",null,!0)}).find(D+" .layui-edge ").on("click",function(e){var t=m(this),n=t.index(),t=t.parents("th").eq(0).data("field");layui.stope(e),0===n?u.sort(t,"asc",null,!0):u.sort(t,"desc",null,!0)}),u.commonMember=function(e){var t=m(this).parents("tr").eq(0).data("index"),s=u.layBody.find('tr[data-index="'+t+'"]'),c=(c=x.cache[u.key]||[])[t]||{};return m.extend({tr:s,data:x.clearCacheKey(c),del:function(){x.cache[u.key][t]=[],s.remove(),u.scrollPatch()},update:function(e,l){e=e||{},layui.each(e,function(i,a){var o=s.children('td[data-field="'+i+'"]'),r=o.children(d);i in c&&(c[i]=a),u.eachCols(function(e,t){var n;t.field==i?(r.html(k.call(u,{item3:t,content:a,tplData:c})),o.data("content",a)):l&&(t.templet||t.toolbar)&&(e=s.children('td[data-field="'+(t.field||e)+'"]'),n=c[t.field],e.children(d).html(k.call(u,{item3:t,content:n,tplData:c})),e.data("content",n))})}),u.renderForm()}},e)}),t=(u.elem.on("click",'input[name="layTableCheckbox"]+',function(){var e=m(this).prev(),t=u.layBody.find('input[name="layTableCheckbox"]'),n=e.parents("tr,.tr").eq(0).data("index"),i=e[0].checked,a="layTableAllChoose"===e.attr("lay-filter");e[0].disabled||(a?(t.each(function(e,t){t.checked=i,u.setCheckData(e,i)}),u.syncCheckAll(),u.renderForm("checkbox")):(u.setCheckData(n,i),u.syncCheckAll()),layui.event.call(e[0],w,"checkbox("+c+")",o.call(e[0],{checked:i,type:a?"all":"one"})))}),u.elem.on("click",'input[lay-type="layTableRadio"]+',function(){var e=m(this).prev(),t=e[0].checked,n=x.cache[u.key],i=e.parents("tr").eq(0).data("index");layui.each(n,function(e,t){i===e?t[s.checkName]=!0:delete t[s.checkName]}),u.setThisRowChecked(i),layui.event.call(this,w,"radio("+c+")",o.call(this,{checked:t}))}),u.layBody.on("mouseenter","tr",function(){var e=m(this),t=e.index();e.data("off")||u.layBody.find("tr:eq("+t+")").addClass(S)}).on("mouseleave","tr",function(){var e=m(this),t=e.index();e.data("off")||u.layBody.find("tr:eq("+t+")").removeClass(S)}).on("click","tr",function(){t.call(this,"row")}).on("dblclick","tr",function(){t.call(this,"rowDouble")}),function(e){var t=m(this);t.data("off")||layui.event.call(this,w,e+"("+c+")",o.call(t.children("td")[0]))}),r=(u.layBody.on("change","."+L,function(){var e=m(this),t=this.value,n=e.parent().data("field"),e=e.parents("tr").eq(0).data("index");x.cache[u.key][e][n]=t,layui.event.call(this,w,"edit("+c+")",o.call(this,{value:t,field:n}))}).on("blur","."+L,function(){var e,t=m(this),n=t.parent(),i=n.data("key"),a=t.closest("tr").data("index"),a=x.cache[u.key][a];t.siblings(d).html((e=t[0].value,k.call(u,{item3:u.col(i),content:e,tplData:a}))),n.data("content",t[0].value),t.remove()}),u.layBody.on(s.editTrigger,"td",function(e){var t,n,i,a,o=m(this);o.data("off")||(t=o.data("field"),a=o.data("key"),a=u.col(a),n=o.closest("tr").data("index"),n=x.cache[u.key][n],i=o.children(d),(a="function"==typeof a.edit?a.edit(n):a.edit)&&((a=m("textarea"===a?'<textarea class="layui-input '+L+'"></textarea>':'<input class="layui-input '+L+'">'))[0].value=o.data("content")||n[t]||i.text(),o.find("."+L)[0]||o.append(a),a.focus(),layui.stope(e)))}).on("mouseenter","td",function(){n.call(this)}).on("mouseleave","td",function(){n.call(this,"hide")}),"layui-table-grid-down"),n=function(e){var t=m(this),n=t.children(d);t.data("off")||(e?t.find(".layui-table-grid-down").remove():!(n.prop("scrollWidth")>n.outerWidth()||0<n.find("br").length)||s.lineStyle||n.find("."+r)[0]||t.append('<div class="'+r+'"><i class="layui-icon layui-icon-down"></i></div>'))},l=(u.layBody.on("click","."+r,function(e){var t=m(this).parent().children(d);u.tipsIndex=v.tips(['<div class="layui-table-tips-main" style="margin-top: -'+(t.height()+23)+"px;"+("sm"===s.size?"padding: 4px 15px; font-size: 12px;":"lg"===s.size?"padding: 14px 15px;":"")+'">',t.html(),"</div>",'<i class="layui-icon layui-table-tips-c layui-icon-close"></i>'].join(""),t[0],{tips:[3,""],time:-1,anim:-1,maxWidth:h.ios||h.android?300:u.elem.width()/2,isOutAnim:!1,skin:"layui-table-tips",success:function(e,t){e.find(".layui-table-tips-c").on("click",function(){v.close(t)})}}),layui.stope(e)}),function(e){var t=m(this),n=t.parents("tr").eq(0).data("index");layui.event.call(this,w,(e||"tool")+"("+c+")",o.call(this,{event:t.attr("lay-event")})),u.setThisRowChecked(n)});u.layBody.on("click","*[lay-event]",function(e){l.call(this),layui.stope(e)}).on("dblclick","*[lay-event]",function(e){l.call(this,"toolDouble"),layui.stope(e)}),u.layMain.on("scroll",function(){var e=m(this),t=e.scrollLeft(),e=e.scrollTop();u.layHeader.scrollLeft(t),u.layTotal.scrollLeft(t),u.layFixed.find(T).scrollTop(e),v.close(u.tipsIndex)}),M.on("resize",function(){u.resize()})},H.on("click",function(){H.trigger("table.remove.tool.panel")}),H.on("table.remove.tool.panel",function(){m(".layui-table-tool-panel").remove()}),x.init=function(n,i){i=i||{};var e=m(n?'table[lay-filter="'+n+'"]':".layui-table[lay-data]"),a="Table element property lay-data configuration item has a syntax error: ";return e.each(function(){var e=m(this),t=e.attr("lay-data");try{t=new Function("return "+t)()}catch(e){p.error(a+t,"error")}var o=[],r=m.extend({elem:this,cols:[],data:[],skin:e.attr("lay-skin"),size:e.attr("lay-size"),even:"string"==typeof e.attr("lay-even")},x.config,i,t);n&&e.hide(),e.find("thead>tr").each(function(i){r.cols[i]=[],m(this).children().each(function(e){var t=m(this),n=t.attr("lay-data");try{n=new Function("return "+n)()}catch(e){return p.error(a+n)}t=m.extend({title:t.text(),colspan:t.attr("colspan")||1,rowspan:t.attr("rowspan")||1},n);t.colspan<2&&o.push(t),r.cols[i].push(t)})}),e.find("tbody>tr").each(function(e){var n=m(this),a={};n.children("td").each(function(e,t){var n=m(this),i=n.data("field");if(i)return a[i]=n.html()}),layui.each(o,function(e,t){e=n.children("td").eq(e);a[t.field]=e.html()}),r.data[e]=a}),x.render(r)}),this},y.that={},y.config={},function(n,i,e,a){var o,r;a.colGroup&&(o=0,n++,a.CHILD_COLS=[],r=e+(parseInt(a.rowspan)||1),layui.each(i[r],function(e,t){t.parentKey?t.parentKey===a.key&&(t.PARENT_COL_INDEX=n,a.CHILD_COLS.push(t),I(n,i,r,t)):t.PARENT_COL_INDEX||1<=o&&o==(a.colspan||1)||(t.PARENT_COL_INDEX=n,a.CHILD_COLS.push(t),o+=t.hide?0:parseInt(1<t.colspan?t.colspan:1),I(n,i,r,t))}))});x.eachCols=function(e,n,i){var e=y.config[e]||{},a=[],o=(i=m.extend(!0,[],i||e.cols),layui.each(i,function(n,e){if(n)return!0;layui.each(e,function(e,t){I(0,i,n,t),t.PARENT_COL_INDEX||a.push(t)})}),function(e){layui.each(e||a,function(e,t){if(t.CHILD_COLS)return o(t.CHILD_COLS);"function"==typeof n&&n(e,t)})});o()},x.checkStatus=function(e){var n=0,i=0,a=[],e=x.cache[e]||[];return layui.each(e,function(e,t){"array"===layui.type(t)?i++:t[x.config.checkName]&&(n++,t[x.config.disabledName]||a.push(x.clearCacheKey(t)))}),{data:a,isAll:!!e.length&&n===e.length-i}},x.getData=function(e){var n=[],e=x.cache[e]||[];return layui.each(e,function(e,t){"array"!==layui.type(t)&&n.push(x.clearCacheKey(t))}),n},x.exportFile=function(e,t,n){t=t||x.clearCacheKey(x.cache[e]);var l,i,a,s,o=(n="object"==typeof n?n:(o={},n&&(o.type=n),o)).type||"csv",c=y.that[e],r=y.config[e]||{},u={csv:"text/csv",xls:"application/vnd.ms-excel"}[o],d=document.createElement("a");if(h.ie)return p.error("IE_NOT_SUPPORT_EXPORTS");d.href="data:"+u+";charset=utf-8,\ufeff"+encodeURIComponent((l=[],i=[],a=[],s={},layui.each(t,function(a,o){var r=[];"object"==typeof e?(layui.each(e,function(e,t){0==a&&l.push(t||"")}),layui.each(x.clearCacheKey(o),function(e,t){r.push('"'+(t||"")+'"')})):x.eachCols(e,function(e,t){var n,i;t.field&&"normal"==t.type&&(t.hide?0==a&&(s[t.field]=!0):(n=o[t.field],i=c.layBody.find('tr[data-index="'+a+'"]>td'),n!==undefined&&null!==n||(n=""),0==a&&l.push(t.title||""),r.push('"'+k.call(c,{item3:t,content:n,tplData:o,text:"text",obj:c.commonMember.call(i.eq(0),{td:function(e){return i.filter('[data-field="'+e+'"]')}})})+'"')))}),i.push(r.join(","))}),c&&layui.each(c.dataTotal,function(e,t){s[e]||a.push(t)}),l.join(",")+"\r\n"+i.join("\r\n")+"\r\n"+a.join(","))),d.download=(n.title||r.title||"table_"+(r.index||""))+"."+o,document.body.appendChild(d),d.click(),document.body.removeChild(d)},x.resize=function(e){e?a(e)&&y.that[e].resize():layui.each(y.that,function(){this.resize()})},x.reload=function(e,t,n,i){if(a(e))return e=y.that[e],e.reload(t,n,i),y.call(e)},x.reloadData=function(){var n=m.extend([],arguments),i=(n[3]="reloadData",new RegExp("^("+["data","url","method","contentType","dataType","jsonpCallback","headers","where","page","limit","request","response","parseData","scrollPos"].join("|")+")$"));return layui.each(n[1],function(e,t){i.test(e)||delete n[1][e]}),x.reload.apply(null,n)},x.render=function(e){e=new i(e);return y.call(e)},x.clearCacheKey=function(e){return delete(e=m.extend({},e))[x.config.checkName],delete e[x.config.indexName],delete e[x.config.disabledName],e},m(function(){x.init()}),e(w,x)});layui.define("form",function(e){"use strict";var f=layui.$,t=layui.form,p=layui.layer,n="tree",i={config:{},index:layui[n]?layui[n].index+1e4:0,set:function(e){var t=this;return t.config=f.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,n,e,t)}},a=function(){var t=this,e=t.config,n=e.id||t.index;return a.that[n]=t,{config:a.config[n]=e,reload:function(e){t.reload.call(t,e)},getChecked:function(){return t.getChecked.call(t)},setChecked:function(e){return t.setChecked.call(t,e)}}},h="layui-hide",s="layui-disabled",y="layui-tree-set",m="layui-tree-iconClick",g="layui-icon-addition",v="layui-icon-subtraction",b="layui-tree-entry",x="layui-tree-main",k="layui-tree-txt",w="layui-tree-pack",C="layui-tree-spread",T="layui-tree-setLineShort",E="layui-tree-showLine",D="layui-tree-lineExtend",o=function(e){var t=this;t.index=++i.index,t.config=f.extend({},t.config,i.config,e),t.render()};o.prototype.config={data:[],showCheckbox:!1,showLine:!0,accordion:!1,onlyIconControl:!1,isJump:!1,edit:!1,text:{defaultNodeName:"未命名",none:"No data"}},o.prototype.reload=function(e){var n=this;layui.each(e,function(e,t){"array"===layui.type(t)&&delete n.config[e]}),n.config=f.extend(!0,{},n.config,e),n.render()},o.prototype.render=function(){var e=this,t=e.config,n=(e.checkids=[],f('<div class="layui-tree'+(t.showCheckbox?" layui-form":"")+(t.showLine?" layui-tree-line":"")+'" lay-filter="LAY-tree-'+e.index+'"></div>')),i=(e.tree(n),t.elem=f(t.elem));if(i[0]){if(e.key=t.id||e.index,e.elem=n,e.elemNone=f('<div class="layui-tree-emptyText">'+t.text.none+"</div>"),i.html(e.elem),0==e.elem.find(".layui-tree-set").length)return e.elem.append(e.elemNone);t.showCheckbox&&e.renderForm("checkbox"),e.elem.find(".layui-tree-set").each(function(){var e=f(this);e.parent(".layui-tree-pack")[0]||e.addClass("layui-tree-setHide"),!e.next()[0]&&e.parents(".layui-tree-pack").eq(1).hasClass("layui-tree-lineExtend")&&e.addClass(T),e.next()[0]||e.parents(".layui-tree-set").eq(0).next()[0]||e.addClass(T)}),e.events()}},o.prototype.renderForm=function(e){t.render(e,"LAY-tree-"+this.index)},o.prototype.tree=function(o,e){var r=this,l=r.config,e=e||l.data;layui.each(e,function(e,t){var n=t.children&&0<t.children.length,i=f('<div class="layui-tree-pack" '+(t.spread?'style="display: block;"':"")+"></div>"),a=f(['<div data-id="'+t.id+'" class="layui-tree-set'+(t.spread?" layui-tree-spread":"")+(t.checked?" layui-tree-checkedFirst":"")+'">','<div class="layui-tree-entry">','<div class="layui-tree-main">',l.showLine?n?'<span class="layui-tree-iconClick layui-tree-icon"><i class="layui-icon '+(t.spread?"layui-icon-subtraction":"layui-icon-addition")+'"></i></span>':'<span class="layui-tree-iconClick"><i class="layui-icon layui-icon-file"></i></span>':'<span class="layui-tree-iconClick"><i class="layui-tree-iconArrow '+(n?"":h)+'"></i></span>',l.showCheckbox?'<input type="checkbox" name="'+(t.field||"layuiTreeCheck_"+t.id)+'" same="layuiTreeCheck" lay-skin="primary" '+(t.disabled?"disabled":"")+' value="'+t.id+'">':"",l.isJump&&t.href?'<a href="'+t.href+'" target="_blank" class="'+k+'">'+(t.title||t.label||l.text.defaultNodeName)+"</a>":'<span class="'+k+(t.disabled?" "+s:"")+'">'+(t.title||t.label||l.text.defaultNodeName)+"</span>","</div>",function(){if(!l.edit)return"";var n={add:'<i class="layui-icon layui-icon-add-1"  data-type="add"></i>',update:'<i class="layui-icon layui-icon-edit" data-type="update"></i>',del:'<i class="layui-icon layui-icon-delete" data-type="del"></i>'},i=['<div class="layui-btn-group layui-tree-btnGroup">'];return!0===l.edit&&(l.edit=["update","del"]),"object"==typeof l.edit?(layui.each(l.edit,function(e,t){i.push(n[t]||"")}),i.join("")+"</div>"):void 0}(),"</div></div>"].join(""));n&&(a.append(i),r.tree(i,t.children)),o.append(a),a.prev("."+y)[0]&&a.prev().children(".layui-tree-pack").addClass("layui-tree-showLine"),n||a.parent(".layui-tree-pack").addClass("layui-tree-lineExtend"),r.spread(a,t),l.showCheckbox&&(t.checked&&r.checkids.push(t.id),r.checkClick(a,t)),l.edit&&r.operate(a,t)})},o.prototype.spread=function(i,e){var a=this.config,t=i.children("."+b),n=t.children("."+x),o=t.find("."+m),t=t.find("."+k),r=a.onlyIconControl?o:n,l="";r.on("click",function(e){var t=i.children("."+w),n=(r.children(".layui-icon")[0]?r:r.find(".layui-tree-icon")).children(".layui-icon");t[0]?i.hasClass(C)?(i.removeClass(C),t.slideUp(200),n.removeClass(v).addClass(g)):(i.addClass(C),t.slideDown(200),n.addClass(v).removeClass(g),a.accordion&&((t=i.siblings("."+y)).removeClass(C),t.children("."+w).slideUp(200),t.find(".layui-tree-icon").children(".layui-icon").removeClass(v).addClass(g))):l="normal"}),t.on("click",function(){f(this).hasClass(s)||(l=i.hasClass(C)?a.onlyIconControl?"open":"close":a.onlyIconControl?"close":"open",a.click&&a.click({elem:i,state:l,data:e}))})},o.prototype.setCheckbox=function(e,t,n){this.config;var a,o=n.prop("checked");n.prop("disabled")||("object"!=typeof t.children&&!e.find("."+w)[0]||e.find("."+w).find('input[same="layuiTreeCheck"]').each(function(){this.disabled||(this.checked=o)}),(a=function(e){var t,n,i;e.parents("."+y)[0]&&(n=(e=e.parent("."+w)).parent(),i=e.prev().find('input[same="layuiTreeCheck"]'),o?i.prop("checked",o):(e.find('input[same="layuiTreeCheck"]').each(function(){this.checked&&(t=!0)}),t||i.prop("checked",!1)),a(n))})(e),this.renderForm("checkbox"))},o.prototype.checkClick=function(n,i){var a=this,o=a.config;n.children("."+b).children("."+x).on("click",'input[same="layuiTreeCheck"]+',function(e){layui.stope(e);var e=f(this).prev(),t=e.prop("checked");e.prop("disabled")||(a.setCheckbox(n,i,e),o.oncheck&&o.oncheck({elem:n,checked:t,data:i}))})},o.prototype.operate=function(l,s){var c=this,u=c.config,e=l.children("."+b),d=e.children("."+x);e.children(".layui-tree-btnGroup").on("click",".layui-icon",function(e){layui.stope(e);var t,e=f(this).data("type"),i=l.children("."+w),a={data:s,type:e,elem:l};if("add"==e){i[0]||(u.showLine?(d.find("."+m).addClass("layui-tree-icon"),d.find("."+m).children(".layui-icon").addClass(g).removeClass("layui-icon-file")):d.find(".layui-tree-iconArrow").removeClass(h),l.append('<div class="layui-tree-pack"></div>'));var n,o=u.operate&&u.operate(a),r={};if(r.title=u.text.defaultNodeName,r.id=o,c.tree(l.children("."+w),[r]),u.showLine&&(i[0]?(i.hasClass(D)||i.addClass(D),l.find("."+w).each(function(){f(this).children("."+y).last().addClass(T)}),(i.children("."+y).last().prev().hasClass(T)?i.children("."+y).last().prev():i.children("."+y).last()).removeClass(T),!l.parent("."+w)[0]&&l.next()[0]&&i.children("."+y).last().removeClass(T)):(o=l.siblings("."+y),n=1,r=l.parent("."+w),layui.each(o,function(e,t){f(t).children("."+w)[0]||(n=0)}),1==n?(o.children("."+w).addClass(E),o.children("."+w).children("."+y).removeClass(T),l.children("."+w).addClass(E),r.removeClass(D),r.children("."+y).last().children("."+w).children("."+y).last().addClass(T)):l.children("."+w).children("."+y).addClass(T))),!u.showCheckbox)return;d.find('input[same="layuiTreeCheck"]')[0].checked&&(l.children("."+w).children("."+y).last().find('input[same="layuiTreeCheck"]')[0].checked=!0),c.renderForm("checkbox")}else"update"==e?(o=d.children("."+k).html(),d.children("."+k).html(""),d.append('<input type="text" class="layui-tree-editInput">'),d.children(".layui-tree-editInput").val(o).focus(),t=function(e){var t=(t=e.val().trim())||u.text.defaultNodeName;e.remove(),d.children("."+k).html(t),a.data.title=t,u.operate&&u.operate(a)},d.children(".layui-tree-editInput").blur(function(){t(f(this))}),d.children(".layui-tree-editInput").on("keydown",function(e){13===e.keyCode&&(e.preventDefault(),t(f(this)))})):p.confirm('确认删除该节点 "<span style="color: #999;">'+(s.title||"")+'</span>" 吗？',function(e){if(u.operate&&u.operate(a),a.status="remove",p.close(e),!l.prev("."+y)[0]&&!l.next("."+y)[0]&&!l.parent("."+w)[0])return l.remove(),void c.elem.append(c.elemNone);var o,n,t;l.siblings("."+y).children("."+b)[0]?(u.showCheckbox&&(o=function(e){var t,n,i,a;e.parents("."+y)[0]&&(t=e.siblings("."+y).children("."+b),n=(e=e.parent("."+w).prev()).find('input[same="layuiTreeCheck"]')[0],i=1,(a=0)==n.checked&&(t.each(function(e,t){t=f(t).find('input[same="layuiTreeCheck"]')[0];0!=t.checked||t.disabled||(i=0),t.disabled||(a=1)}),1==i&&1==a&&(n.checked=!0,c.renderForm("checkbox"),o(e.parent("."+y)))))})(l),u.showLine&&(e=l.siblings("."+y),n=1,t=l.parent("."+w),layui.each(e,function(e,t){f(t).children("."+w)[0]||(n=0)}),1==n?(i[0]||(t.removeClass(D),e.children("."+w).addClass(E),e.children("."+w).children("."+y).removeClass(T)),(l.next()[0]?t.children("."+y).last():l.prev()).children("."+w).children("."+y).last().addClass(T),l.next()[0]||l.parents("."+y)[1]||l.parents("."+y).eq(0).next()[0]||l.prev("."+y).addClass(T)):!l.next()[0]&&l.hasClass(T)&&l.prev().addClass(T))):(e=l.parent("."+w).prev(),u.showLine?(e.find("."+m).removeClass("layui-tree-icon"),e.find("."+m).children(".layui-icon").removeClass(v).addClass("layui-icon-file"),(t=e.parents("."+w).eq(0)).addClass(D),t.children("."+y).each(function(){f(this).children("."+w).children("."+y).last().addClass(T)})):e.find(".layui-tree-iconArrow").addClass(h),l.parents("."+y).eq(0).removeClass(C),l.parent("."+w).remove()),l.remove()})})},o.prototype.events=function(){var t=this,a=t.config;t.elem.find(".layui-tree-checkedFirst");t.setChecked(t.checkids),t.elem.find(".layui-tree-search").on("keyup",function(){var e=f(this),n=e.val(),e=e.nextAll(),i=[];e.find("."+k).each(function(){var t,e=f(this).parents("."+b);-1!=f(this).html().indexOf(n)&&(i.push(f(this).parent()),(t=function(e){e.addClass("layui-tree-searchShow"),e.parent("."+w)[0]&&t(e.parent("."+w).parent("."+y))})(e.parent("."+y)))}),e.find("."+b).each(function(){var e=f(this).parent("."+y);e.hasClass("layui-tree-searchShow")||e.addClass(h)}),0==e.find(".layui-tree-searchShow").length&&t.elem.append(t.elemNone),a.onsearch&&a.onsearch({elem:i})}),t.elem.find(".layui-tree-search").on("keydown",function(){f(this).nextAll().find("."+b).each(function(){f(this).parent("."+y).removeClass("layui-tree-searchShow "+h)}),f(".layui-tree-emptyText")[0]&&f(".layui-tree-emptyText").remove()})},o.prototype.getChecked=function(){var e=this.config,t=[],n=[],a=(this.elem.find(".layui-form-checked").each(function(){t.push(f(this).prev()[0].value)}),function(e,i){layui.each(e,function(e,n){layui.each(t,function(e,t){if(n.id==t)return delete(t=f.extend({},n)).children,i.push(t),n.children&&(t.children=[],a(n.children,t.children)),!0})})});return a(f.extend({},e.data),n),n},o.prototype.setChecked=function(o){this.config;this.elem.find("."+y).each(function(e,t){var n=f(this).data("id"),i=f(t).children("."+b).find('input[same="layuiTreeCheck"]'),a=i.next();if("number"==typeof o){if(n==o)return i[0].checked||a.click(),!1}else"object"==typeof o&&layui.each(o,function(e,t){if(t==n&&!i[0].checked)return a.click(),!0})})},a.that={},a.config={},i.reload=function(e,t){e=a.that[e];return e.reload(t),a.call(e)},i.getChecked=function(e){return a.that[e].getChecked()},i.setChecked=function(e,t){return a.that[e].setChecked(t)},i.render=function(e){e=new o(e);return a.call(e)},e(n,i)});layui.define(["laytpl","form"],function(e){"use strict";var c=layui.$,a=layui.laytpl,t=layui.form,n="transfer",i={config:{},index:layui[n]?layui[n].index+1e4:0,set:function(e){var t=this;return t.config=c.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,n,e,t)}},o=function(){var t=this,e=t.config,n=e.id||t.index;return o.that[n]=t,{config:o.config[n]=e,reload:function(e){t.reload.call(t,e)},getData:function(){return t.getData.call(t)}}},u="layui-hide",d="layui-btn-disabled",r="layui-none",l="layui-transfer-box",f="layui-transfer-header",s="layui-transfer-search",p="layui-transfer-data",h=function(e){return['<div class="layui-transfer-box" data-index="'+(e=e||{}).index+'">','<div class="layui-transfer-header">','<input type="checkbox" name="'+e.checkAllName+'" lay-filter="layTransferCheckbox" lay-type="all" lay-skin="primary" title="{{ d.data.title['+e.index+"] || 'list"+(e.index+1)+"' }}\">","</div>","{{# if(d.data.showSearch){ }}",'<div class="layui-transfer-search">','<i class="layui-icon layui-icon-search"></i>','<input type="input" class="layui-input" placeholder="关键词搜索">',"</div>","{{# } }}",'<ul class="layui-transfer-data"></ul>',"</div>"].join("")},y=['<div class="layui-transfer layui-form layui-border-box" lay-filter="LAY-transfer-{{ d.index }}">',h({index:0,checkAllName:"layTransferLeftCheckAll"}),'<div class="layui-transfer-active">','<button type="button" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0">','<i class="layui-icon layui-icon-next"></i>',"</button>",'<button type="button" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1">','<i class="layui-icon layui-icon-prev"></i>',"</button>","</div>",h({index:1,checkAllName:"layTransferRightCheckAll"}),"</div>"].join(""),m=function(e){var t=this;t.index=++i.index,t.config=c.extend({},t.config,i.config,e),t.render()};m.prototype.config={title:["列表一","列表二"],width:200,height:360,data:[],value:[],showSearch:!1,id:"",text:{none:"No data",searchNone:"无匹配数据"}},m.prototype.reload=function(e){var t=this;t.config=c.extend({},t.config,e),t.render()},m.prototype.render=function(){var e=this,t=e.config,n=e.elem=c(a(y).render({data:t,index:e.index})),i=t.elem=c(t.elem);i[0]&&(t.data=t.data||[],t.value=t.value||[],e.key=t.id||e.index,i.html(e.elem),e.layBox=e.elem.find("."+l),e.layHeader=e.elem.find("."+f),e.laySearch=e.elem.find("."+s),e.layData=n.find("."+p),e.layBtn=n.find(".layui-transfer-active .layui-btn"),e.layBox.css({width:t.width,height:t.height}),e.layData.css({height:(i=t.height-e.layHeader.outerHeight(),t.showSearch&&(i-=e.laySearch.outerHeight()),i-2)}),e.renderData(),e.events())},m.prototype.renderData=function(){var e=this,i=(e.config,[{checkName:"layTransferLeftCheck",views:[]},{checkName:"layTransferRightCheck",views:[]}]);e.parseData(function(e){var t=e.selected?1:0,n=["<li>",'<input type="checkbox" name="'+i[t].checkName+'" lay-skin="primary" lay-filter="layTransferCheckbox" title="'+e.title+'"'+(e.disabled?" disabled":"")+(e.checked?" checked":"")+' value="'+e.value+'">',"</li>"].join("");i[t].views.push(n),delete e.selected}),e.layData.eq(0).html(i[0].views.join("")),e.layData.eq(1).html(i[1].views.join("")),e.renderCheckBtn()},m.prototype.renderForm=function(e){t.render(e,"LAY-transfer-"+this.index)},m.prototype.renderCheckBtn=function(r){var l=this,s=l.config;r=r||{},l.layBox.each(function(e){var t=c(this),n=t.find("."+p),t=t.find("."+f).find('input[type="checkbox"]'),i=n.find('input[type="checkbox"]'),a=0,o=!1;i.each(function(){var e=c(this).data("hide");(this.checked||this.disabled||e)&&a++,this.checked&&!e&&(o=!0)}),t.prop("checked",o&&a===i.length),l.layBtn.eq(e)[o?"removeClass":"addClass"](d),r.stopNone||(i=n.children("li:not(."+u+")").length,l.noneView(n,i?"":s.text.none))}),l.renderForm("checkbox")},m.prototype.noneView=function(e,t){var n=c('<p class="layui-none">'+(t||"")+"</p>");e.find("."+r)[0]&&e.find("."+r).remove(),t.replace(/\s/g,"")&&e.append(n)},m.prototype.setValue=function(){var e=this.config,t=[];return this.layBox.eq(1).find("."+p+' input[type="checkbox"]').each(function(){c(this).data("hide")||t.push(this.value)}),e.value=t,this},m.prototype.parseData=function(t){var i=this.config,a=[];return layui.each(i.data,function(e,n){n=("function"==typeof i.parseData?i.parseData(n):n)||n,a.push(n=c.extend({},n)),layui.each(i.value,function(e,t){t==n.value&&(n.selected=!0)}),t&&t(n)}),i.data=a,this},m.prototype.getData=function(e){var t=this.config,i=[];return this.setValue(),layui.each(e||t.value,function(e,n){layui.each(t.data,function(e,t){delete t.selected,n==t.value&&i.push(t)})}),i},m.prototype.transfer=function(e,t){var n,i=this,a=i.config,o=i.layBox.eq(e),r=[],t=(t?((n=(t=t).find('input[type="checkbox"]'))[0].checked=!1,o.siblings("."+l).find("."+p).append(t.clone()),t.remove(),r.push(n[0].value),i.setValue()):o.each(function(e){c(this).find("."+p).children("li").each(function(){var e=c(this),t=e.find('input[type="checkbox"]'),n=t.data("hide");t[0].checked&&!n&&(t[0].checked=!1,o.siblings("."+l).find("."+p).append(e.clone()),e.remove(),r.push(t[0].value)),i.setValue()})}),i.renderCheckBtn(),o.siblings("."+l).find("."+s+" input"));""!==t.val()&&t.trigger("keyup"),a.onchange&&a.onchange(i.getData(r),e)},m.prototype.events=function(){var a=this,o=a.config;a.elem.on("click",'input[lay-filter="layTransferCheckbox"]+',function(){var e=c(this).prev(),t=e[0].checked,n=e.parents("."+l).eq(0).find("."+p);e[0].disabled||("all"===e.attr("lay-type")&&n.find('input[type="checkbox"]').each(function(){this.disabled||(this.checked=t)}),setTimeout(function(){a.renderCheckBtn({stopNone:!0})},0))}),a.elem.on("dblclick","."+p+">li",function(e){var t=c(this),n=t.children('input[type="checkbox"]'),i=t.parent().parent();n[0].disabled||a.transfer(i.data("index"),t)}),a.layBtn.on("click",function(){var e=c(this),t=e.data("index");e.hasClass(d)||a.transfer(t)}),a.laySearch.find("input").on("keyup",function(){var i=this.value,e=c(this).parents("."+s).eq(0).siblings("."+p),t=e.children("li"),t=(t.each(function(){var e=c(this),t=e.find('input[type="checkbox"]'),n=t[0].title,n=("cs"!==o.showSearch&&(n=n.toLowerCase(),i=i.toLowerCase()),-1!==n.indexOf(i));e[n?"removeClass":"addClass"](u),t.data("hide",!n)}),a.renderCheckBtn(),t.length===e.children("li."+u).length);a.noneView(e,t?o.text.searchNone:"")})},o.that={},o.config={},i.reload=function(e,t){e=o.that[e];return e.reload(t),o.call(e)},i.getData=function(e){return o.that[e].getData()},i.render=function(e){e=new m(e);return o.call(e)},e(n,i)});layui.define("jquery",function(e){"use strict";var a=layui.$,n=(layui.hint(),layui.device(),{config:{},set:function(e){var t=this;return t.config=a.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,l,e,t)}}),l="carousel",s="layui-this",c="layui-carousel-left",u="layui-carousel-right",d="layui-carousel-prev",f="layui-carousel-next",i="layui-carousel-arrow",o="layui-carousel-ind",t=function(e){var t=this;t.config=a.extend({},t.config,n.config,e),t.render()};t.prototype.config={width:"600px",height:"280px",full:!1,arrow:"hover",indicator:"inside",autoplay:!0,interval:3e3,anim:"",trigger:"click",index:0},t.prototype.render=function(){var e=this,t=e.config;t.elem=a(t.elem),t.elem[0]&&(e.elemItem=t.elem.find(">*[carousel-item]>*"),t.index<0&&(t.index=0),t.index>=e.elemItem.length&&(t.index=e.elemItem.length-1),t.interval<800&&(t.interval=800),t.full?t.elem.css({position:"fixed",width:"100%",height:"100%",zIndex:9999}):t.elem.css({width:t.width,height:t.height}),t.elem.attr("lay-anim",t.anim),e.elemItem.eq(t.index).addClass(s),e.elemItem.length<=1||(e.indicator(),e.arrow(),e.autoplay(),e.events()))},t.prototype.reload=function(e){var t=this;clearInterval(t.timer),t.config=a.extend({},t.config,e),t.render()},t.prototype.prevIndex=function(){var e=this.config.index-1;return e=e<0?this.elemItem.length-1:e},t.prototype.nextIndex=function(){var e=this.config.index+1;return e=e>=this.elemItem.length?0:e},t.prototype.addIndex=function(e){var t=this.config;t.index=t.index+(e=e||1),t.index>=this.elemItem.length&&(t.index=0)},t.prototype.subIndex=function(e){var t=this.config;t.index=t.index-(e=e||1),t.index<0&&(t.index=this.elemItem.length-1)},t.prototype.autoplay=function(){var e=this,t=e.config;t.autoplay&&(clearInterval(e.timer),e.timer=setInterval(function(){e.slide()},t.interval))},t.prototype.arrow=function(){var t=this,e=t.config,n=a(['<button class="layui-icon '+i+'" lay-type="sub">'+("updown"===e.anim?"&#xe619;":"&#xe603;")+"</button>",'<button class="layui-icon '+i+'" lay-type="add">'+("updown"===e.anim?"&#xe61a;":"&#xe602;")+"</button>"].join(""));e.elem.attr("lay-arrow",e.arrow),e.elem.find("."+i)[0]&&e.elem.find("."+i).remove(),e.elem.append(n),n.on("click",function(){var e=a(this).attr("lay-type");t.slide(e)})},t.prototype.indicator=function(){var t,n=this,i=n.config,e=n.elemInd=a(['<div class="'+o+'"><ul>',(t=[],layui.each(n.elemItem,function(e){t.push("<li"+(i.index===e?' class="layui-this"':"")+"></li>")}),t.join("")),"</ul></div>"].join(""));i.elem.attr("lay-indicator",i.indicator),i.elem.find("."+o)[0]&&i.elem.find("."+o).remove(),i.elem.append(e),"updown"===i.anim&&e.css("margin-top",-e.height()/2),e.find("li").on("hover"===i.trigger?"mouseover":i.trigger,function(){var e=a(this).index();e>i.index?n.slide("add",e-i.index):e<i.index&&n.slide("sub",i.index-e)})},t.prototype.slide=function(e,t){var n=this,i=n.elemItem,a=n.config,o=a.index,r=a.elem.attr("lay-filter");n.haveSlide||("sub"===e?(n.subIndex(t),i.eq(a.index).addClass(d),setTimeout(function(){i.eq(o).addClass(u),i.eq(a.index).addClass(u)},50)):(n.addIndex(t),i.eq(a.index).addClass(f),setTimeout(function(){i.eq(o).addClass(c),i.eq(a.index).addClass(c)},50)),setTimeout(function(){i.removeClass(s+" "+d+" "+f+" "+c+" "+u),i.eq(a.index).addClass(s),n.haveSlide=!1},300),n.elemInd.find("li").eq(a.index).addClass(s).siblings().removeClass(s),n.haveSlide=!0,e={index:a.index,prevIndex:o,item:i.eq(a.index)},"function"==typeof a.change&&a.change(e),layui.event.call(this,l,"change("+r+")",e))},t.prototype.events=function(){var e=this,t=e.config;t.elem.data("haveEvents")||(t.elem.on("mouseenter",function(){"always"!==e.config.autoplay&&clearInterval(e.timer)}).on("mouseleave",function(){"always"!==e.config.autoplay&&e.autoplay()}),t.elem.data("haveEvents",!0))},n.render=function(e){return new t(e)},e(l,n)});layui.define("jquery",function(e){"use strict";var s=layui.jquery,n={config:{},index:layui.rate?layui.rate.index+1e4:0,set:function(e){var t=this;return t.config=s.extend({},t.config,e),t},on:function(e,t){return layui.onevent.call(this,i,e,t)}},i="rate",c="layui-icon-rate",u="layui-icon-rate-solid",r="layui-icon-rate-half",l="layui-icon-rate-solid layui-icon-rate-half",d="layui-icon-rate layui-icon-rate-half",t=function(e){var t=this;t.index=++n.index,t.config=s.extend({},t.config,n.config,e),t.render()};t.prototype.config={length:5,text:!1,readonly:!1,half:!1,value:0,theme:""},t.prototype.render=function(){for(var e=this,t=e.config,n=t.theme?'style="color: '+t.theme+';"':"",i=(t.elem=s(t.elem),t.value>t.length&&(t.value=t.length),parseInt(t.value)===t.value||t.half||(t.value=Math.ceil(t.value)-t.value<.5?Math.ceil(t.value):Math.floor(t.value)),'<ul class="layui-rate" '+(t.readonly?"readonly":"")+">"),a=1;a<=t.length;a++){var o='<li class="layui-inline"><i class="layui-icon '+(a>Math.floor(t.value)?c:u)+'" '+n+"></i></li>";t.half&&parseInt(t.value)!==t.value&&a==Math.ceil(t.value)?i=i+'<li><i class="layui-icon layui-icon-rate-half" '+n+"></i></li>":i+=o}i+="</ul>"+(t.text?'<span class="layui-inline">'+t.value+"星":"")+"</span>";var r=t.elem,l=r.next(".layui-rate");l[0]&&l.remove(),e.elemTemp=s(i),t.span=e.elemTemp.next("span"),t.setText&&t.setText(t.value),r.html(e.elemTemp),r.addClass("layui-inline"),t.readonly||e.action()},t.prototype.setvalue=function(e){this.config.value=e,this.render()},t.prototype.action=function(){var i=this.config,a=this.elemTemp,o=a.find("i").width();a.children("li").each(function(e){var t=e+1,n=s(this);n.on("click",function(e){i.value=t,i.half&&e.pageX-s(this).offset().left<=o/2&&(i.value=i.value-.5),i.text&&a.next("span").text(i.value+"星"),i.choose&&i.choose(i.value),i.setText&&i.setText(i.value)}),n.on("mousemove",function(e){a.find("i").each(function(){s(this).addClass(c).removeClass(l)}),a.find("i:lt("+t+")").each(function(){s(this).addClass(u).removeClass(d)}),i.half&&e.pageX-s(this).offset().left<=o/2&&n.children("i").addClass(r).removeClass(u)}),n.on("mouseleave",function(){a.find("i").each(function(){s(this).addClass(c).removeClass(l)}),a.find("i:lt("+Math.floor(i.value)+")").each(function(){s(this).addClass(u).removeClass(d)}),i.half&&parseInt(i.value)!==i.value&&a.children("li:eq("+Math.floor(i.value)+")").children("i").addClass(r).removeClass("layui-icon-rate-solid layui-icon-rate")})})},t.prototype.events=function(){this.config},n.render=function(e){e=new t(e);return function(){var t=this;return{setvalue:function(e){t.setvalue.call(t,e)},config:t.config}}.call(e)},e(i,n)});layui.define("jquery",function(e){"use strict";var g=layui.$,t=function(e){};t.prototype.load=function(e){var i,a,o,t,r,n,l,s,c,u,d,f,p,h=this,y=0,m=g((e=e||{}).elem);if(m[0])return t=g(e.scrollElem||document),r=e.mb||50,n=!("isAuto"in e)||e.isAuto,l=e.end||"没有更多了",s=e.scrollElem&&e.scrollElem!==document,c="<cite>加载更多</cite>",u=g('<div class="layui-flow-more"><a href="javascript:;">'+c+"</a></div>"),m.find(".layui-flow-more")[0]||m.append(u),d=function(e,t){e=g(e),u.before(e),(t=0==t||null)?u.html(l):u.find("a").html(c),a=t,i=null,p&&p()},f=function(){i=!0,u.find("a").html('<i class="layui-anim layui-anim-rotate layui-anim-loop layui-icon ">&#xe63e;</i>'),"function"==typeof e.done&&e.done(++y,d)},f(),u.find("a").on("click",function(){g(this);a||i||f()}),e.isLazyimg&&(p=h.lazyimg({elem:e.elem+" img",scrollElem:e.scrollElem})),n&&t.on("scroll",function(){var t=g(this),n=t.scrollTop();o&&clearTimeout(o),!a&&m.width()&&(o=setTimeout(function(){var e=(s?t:g(window)).height();(s?t.prop("scrollHeight"):document.documentElement.scrollHeight)-n-e<=r&&(i||f())},100))}),h},t.prototype.lazyimg=function(e){var t,s=this,c=0,u=g((e=e||{}).scrollElem||document),d=e.elem||"img",f=e.scrollElem&&e.scrollElem!==document,p=function(t,e){var n,i=u.scrollTop(),e=i+e,a=f?t.offset().top-u.offset().top+i:t.offset().top;i<=a&&a<=e&&t.attr("lay-src")&&(n=t.attr("lay-src"),layui.img(n,function(){var e=s.lazyimg.elem.eq(c);t.attr("src",n).removeAttr("lay-src"),e[0]&&o(e),c++},function(){s.lazyimg.elem.eq(c);t.removeAttr("lay-src")}))},o=function(e,t){var n=(f?t||u:g(window)).height(),i=u.scrollTop(),a=i+n;if(s.lazyimg.elem=g(d),e)p(e,n);else for(var o=0;o<s.lazyimg.elem.length;o++){var r=s.lazyimg.elem.eq(o),l=f?r.offset().top-u.offset().top+i:r.offset().top;if(p(r,n),c=o,a<l)break}};return o(),u.on("scroll",function(){var e=g(this);t&&clearTimeout(t),t=setTimeout(function(){o(null,e)},50)}),o},e("flow",new t)});layui.define(["layer","form"],function(e){"use strict";var u=layui.$,c=layui.layer,a=layui.form,d=(layui.hint(),layui.device()),n="layedit",f="layui-disabled",t=function(){this.index=0,this.config={tool:["strong","italic","underline","del","|","left","center","right","|","link","unlink"],hideTool:[],height:280}},p=(t.prototype.set=function(e){return u.extend(!0,this.config,e),this},t.prototype.on=function(e,t){return layui.onevent(n,e,t)},t.prototype.build=function(e,t){t=t||{};var n,i,a=this,o=a.config,r="layui-layedit",l=u("string"==typeof e?"#"+e:e),s="LAY_layedit_"+ ++a.index,c=l.next("."+r),o=u.extend({},o,t),t=(n=[],i={},layui.each(o.hideTool,function(e,t){i[t]=!0}),layui.each(o.tool,function(e,t){w[t]&&!i[t]&&n.push(w[t])}),n.join("")),r=u(['<div class="'+r+'">','<div class="layui-unselect layui-layedit-tool">'+t+"</div>",'<div class="layui-layedit-iframe">','<iframe id="'+s+'" name="'+s+'" textarea="'+e+'" frameborder="0"></iframe>',"</div>","</div>"].join(""));return d.ie&&d.ie<8?l.removeClass("layui-hide").addClass("layui-show"):(c[0]&&c.remove(),p.call(a,r,l[0],o),l.addClass("layui-hide").after(r),a.index)},t.prototype.getContent=function(e){e=o(e);if(e[0])return i(e[0].document.body.innerHTML)},t.prototype.getText=function(e){e=o(e);if(e[0])return u(e[0].document.body).text()},t.prototype.setContent=function(e,t,n){var i=o(e);i[0]&&(n?u(i[0].document.body).append(t):u(i[0].document.body).html(t),layedit.sync(e))},t.prototype.sync=function(e){e=o(e);e[0]&&u("#"+e[1].attr("textarea")).val(i(e[0].document.body.innerHTML))},t.prototype.getSelection=function(e){var e=o(e);if(e[0])return e=y(e[0].document),document.selection?e.text:e.toString()},function(a,o,r){var l=this,s=a.find("iframe");s.css({height:r.height}).on("load",function(){var e=s.contents(),t=s.prop("contentWindow"),n=e.find("head"),i=u(["<style>","*{margin: 0; padding: 0;}","body{padding: 10px; line-height: 20px; overflow-x: hidden; word-wrap: break-word; font: 14px Helvetica Neue,Helvetica,PingFang SC,Microsoft YaHei,Tahoma,Arial,sans-serif; -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}","a{color:#01AAED; text-decoration:none;}a:hover{color:#c00}","p{margin-bottom: 10px;}","img{display: inline-block; border: none; vertical-align: middle;}","pre{margin: 10px 0; padding: 10px; line-height: 20px; border: 1px solid #ddd; border-left-width: 6px; background-color: #F2F2F2; color: #333; font-family: Courier New; font-size: 12px;}","</style>"].join("")),e=e.find("body");n.append(i),e.attr("contenteditable","true").css({"min-height":r.height}).html(o.value||""),h.apply(l,[t,s,o,r]),b.call(l,t,a,r)})}),o=function(e){e=u("#LAY_layedit_"+e);return[e.prop("contentWindow"),e]},i=function(e){return e=8==d.ie?e.replace(/<.+>/g,function(e){return e.toLowerCase()}):e},h=function(t,e,n,i){var a=t.document,o=u(a.body);o.on("keydown",function(e){if(13===e.keyCode){var t=y(a);if("pre"===m(t).parentNode.tagName.toLowerCase())return e.shiftKey?void 0:(c.msg("请暂时用shift+enter"),!1);a.execCommand("formatBlock",!1,"<p>")}}),u(n).parents("form").on("submit",function(){var e=o.html();8==d.ie&&(e=e.replace(/<.+>/g,function(e){return e.toLowerCase()})),n.value=e}),o.on("paste",function(e){a.execCommand("formatBlock",!1,"<p>"),setTimeout(function(){r.call(t,o),n.value=o.html()},100)})},r=function(e){this.document;e.find("*[style]").each(function(){var e=this.style.textAlign;this.removeAttribute("style"),u(this).css({"text-align":e||""})}),e.find("table").addClass("layui-table"),e.find("script,link").remove()},y=function(e){return e.selection?e.selection.createRange():e.getSelection().getRangeAt(0)},m=function(e){return e.endContainer||e.parentElement().childNodes[0]},g=function(e,t,n){var i,a,o=this.document,r=document.createElement(e);for(i in t)r.setAttribute(i,t[i]);r.removeAttribute("text"),o.selection?(a=n.text||t.text,"a"===e&&!a||(a&&(r.innerHTML=a),n.pasteHTML(u(r).prop("outerHTML")),n.select())):(a=n.toString()||t.text,"a"===e&&!a||(a&&(r.innerHTML=a),n.deleteContents(),n.insertNode(r)))},v=function(t,e){var n=this.document,i="layedit-tool-active",n=m(y(n)),a=function(e){return t.find(".layedit-tool-"+e)};e&&e[e.hasClass(i)?"removeClass":"addClass"](i),t.find(">i").removeClass(i),a("unlink").addClass(f),u(n).parents().each(function(){var e=this.tagName.toLowerCase(),t=this.style.textAlign;"b"!==e&&"strong"!==e||a("b").addClass(i),"i"!==e&&"em"!==e||a("i").addClass(i),"u"===e&&a("u").addClass(i),"strike"===e&&a("d").addClass(i),"p"===e&&a("center"===t?"center":"right"===t?"right":"left").addClass(i),"a"===e&&(a("link").addClass(i),a("unlink").removeClass(f))})},b=function(a,e,t){var o=a.document,r=u(o.body),l={link:function(n){var e=m(n),i=u(e).parent();x.call(r,{href:i.attr("href"),target:i.attr("target")},function(e){var t=i[0];"A"===t.tagName?t.href=e.url:g.call(a,"a",{target:e.target,href:e.url,text:e.url},n)})},unlink:function(e){o.execCommand("unlink")},code:function(t){k.call(r,function(e){g.call(a,"pre",{text:e.code,"lay-lang":e.lang},t)})},help:function(){c.open({type:2,title:"帮助",area:["600px","380px"],shadeClose:!0,shade:.1,skin:"layui-layer-msg",content:["","no"]})}},s=e.find(".layui-layedit-tool"),n=function(){var e,t=u(this),n=t.attr("layedit-event"),i=t.attr("lay-command");t.hasClass(f)||(r.focus(),(e=y(o)).commonAncestorContainer,i?(o.execCommand(i),/justifyLeft|justifyCenter|justifyRight/.test(i)&&o.execCommand("formatBlock",!1,"<p>"),setTimeout(function(){r.focus()},10)):l[n]&&l[n].call(this,e),v.call(a,s,t))},i=/image/;s.find(">i").on("mousedown",function(){var e=u(this).attr("layedit-event");i.test(e)||n.call(this)}).on("click",function(){var e=u(this).attr("layedit-event");i.test(e)&&n.call(this)}),r.on("click",function(){v.call(a,s)})},x=function(e,n){var i=this,e=c.open({type:1,id:"LAY_layedit_link",area:"350px",shade:.05,shadeClose:!0,moveType:1,title:"超链接",skin:"layui-layer-msg",content:['<ul class="layui-form" style="margin: 15px;">','<li class="layui-form-item">','<label class="layui-form-label" style="width: 60px;">URL</label>','<div class="layui-input-block" style="margin-left: 90px">','<input name="url" lay-verify="url" value="'+(e.href||"")+'" autofocus="true" autocomplete="off" class="layui-input">',"</div>","</li>",'<li class="layui-form-item">','<label class="layui-form-label" style="width: 60px;">打开方式</label>','<div class="layui-input-block" style="margin-left: 90px">','<input type="radio" name="target" value="_self" class="layui-input" title="当前窗口"'+("_self"!==e.target&&e.target?"":"checked")+">",'<input type="radio" name="target" value="_blank" class="layui-input" title="新窗口" '+("_blank"===e.target?"checked":"")+">","</div>","</li>",'<li class="layui-form-item" style="text-align: center;">','<button type="button" lay-submit lay-filter="layedit-link-yes" class="layui-btn"> 确定 </button>','<button style="margin-left: 20px;" type="button" class="layui-btn layui-btn-primary"> 取消 </button>',"</li>","</ul>"].join(""),success:function(e,t){a.render("radio"),e.find(".layui-btn-primary").on("click",function(){c.close(t),i.focus()}),a.on("submit(layedit-link-yes)",function(e){c.close(x.index),n&&n(e.field)})}});x.index=e},k=function(n){var i=this,e=c.open({type:1,id:"LAY_layedit_code",area:"550px",shade:.05,shadeClose:!0,moveType:1,title:"插入代码",skin:"layui-layer-msg",content:['<ul class="layui-form layui-form-pane" style="margin: 15px;">','<li class="layui-form-item">','<label class="layui-form-label">请选择语言</label>','<div class="layui-input-block">','<select name="lang">','<option value="JavaScript">JavaScript</option>','<option value="HTML">HTML</option>','<option value="CSS">CSS</option>','<option value="Java">Java</option>','<option value="PHP">PHP</option>','<option value="C#">C#</option>','<option value="Python">Python</option>','<option value="Ruby">Ruby</option>','<option value="Go">Go</option>',"</select>","</div>","</li>",'<li class="layui-form-item layui-form-text">','<label class="layui-form-label">代码</label>','<div class="layui-input-block">','<textarea name="code" lay-verify="required" autofocus="true" class="layui-textarea" style="height: 200px;"></textarea>',"</div>","</li>",'<li class="layui-form-item" style="text-align: center;">','<button type="button" lay-submit lay-filter="layedit-code-yes" class="layui-btn"> 确定 </button>','<button style="margin-left: 20px;" type="button" class="layui-btn layui-btn-primary"> 取消 </button>',"</li>","</ul>"].join(""),success:function(e,t){a.render("select"),e.find(".layui-btn-primary").on("click",function(){c.close(t),i.focus()}),a.on("submit(layedit-code-yes)",function(e){c.close(k.index),n&&n(e.field)})}});k.index=e},w={html:'<i class="layui-icon layedit-tool-html" title="HTML源代码" lay-command="html" layedit-event="html"">&#xe64b;</i><span class="layedit-tool-mid"></span>',strong:'<i class="layui-icon layedit-tool-b" title="加粗" lay-command="Bold" layedit-event="b"">&#xe62b;</i>',italic:'<i class="layui-icon layedit-tool-i" title="斜体" lay-command="italic" layedit-event="i"">&#xe644;</i>',underline:'<i class="layui-icon layedit-tool-u" title="下划线" lay-command="underline" layedit-event="u"">&#xe646;</i>',del:'<i class="layui-icon layedit-tool-d" title="删除线" lay-command="strikeThrough" layedit-event="d"">&#xe64f;</i>',"|":'<span class="layedit-tool-mid"></span>',left:'<i class="layui-icon layedit-tool-left" title="左对齐" lay-command="justifyLeft" layedit-event="left"">&#xe649;</i>',center:'<i class="layui-icon layedit-tool-center" title="居中对齐" lay-command="justifyCenter" layedit-event="center"">&#xe647;</i>',right:'<i class="layui-icon layedit-tool-right" title="右对齐" lay-command="justifyRight" layedit-event="right"">&#xe648;</i>',link:'<i class="layui-icon layedit-tool-link" title="插入链接" layedit-event="link"">&#xe64c;</i>',unlink:'<i class="layui-icon layedit-tool-unlink layui-disabled" title="清除链接" lay-command="unlink" layedit-event="unlink"">&#xe64d;</i>',face:'<i class="layui-icon layedit-tool-face" title="表情" layedit-event="face"">&#xe650;</i>',image:'<i class="layui-icon layedit-tool-image" title="图片" layedit-event="image">&#xe64a;<input type="file" name="file"></i>',code:'<i class="layui-icon layedit-tool-code" title="插入代码" layedit-event="code">&#xe64e;</i>',help:'<i class="layui-icon layedit-tool-help" title="帮助" layedit-event="help">&#xe607;</i>'},t=new t;e(n,t)});layui.define(["lay","util"],function(e){"use strict";var s=layui.$,c=layui.util,u="layui-code-title",t={elem:".layui-code",title:"&lt;/&gt;",about:"",ln:!0};e("code",function(e){var l=e=s.extend({},t,e);e.elem=s(e.elem),e.elem[0]&&layui.each(e.elem.get().reverse(),function(e,t){var i,a=s(t),n=(n=a.html(),s.trim(n).replace(/^\n|\n$/,"")),t=s.extend({},l,lay.options(t),(i={},layui.each(["title","height","encode","skin","about"],function(e,t){var n=a.attr("lay-"+t);"string"==typeof n&&(i[t]=n)}),i)),o=t.ln?"ol":"ul",o=s("<"+o+' class="layui-code-'+o+'">'),r=s('<div class="'+u+'">');a.addClass("layui-code-view layui-box"),t.skin&&("notepad"===t.skin&&(t.skin="dark"),a.addClass("layui-code-"+t.skin)),n=(n=t.encode?c.escape(n):n).replace(/[\r\t\n]+/g,"</li><li>"),a.html(o.html("<li>"+n+"</li>")),a.children("."+u)[0]||(r.html(t.title+(t.about?'<div class="layui-code-about">'+t.about+"</div>":"")),a.prepend(r)),0<(n=Math.floor(o.find("li").length/100))&&o.css("margin-left",n+"px"),t.height&&o.css("max-height",t.height)})})}).addcss("modules/code.css?v=3","skincodecss");