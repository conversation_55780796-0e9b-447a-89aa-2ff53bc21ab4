<?php /*a:2:{s:51:"C:\wwwroot\127.0.0.1\app\admin\view\index\main.html";i:1687490468;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css" />
<style>
    .layuimini-notice:hover {background:#f6f6f6;}
    .layuimini-notice {clear:both;font-size:12px !important;cursor:pointer;position:relative;transition:background 0.2s ease-in-out;}
    .layuimini-notice-title,.layuimini-notice-label {
        padding-right: 70px !important;text-overflow:ellipsis!important;overflow:hidden!important;white-space:nowrap!important;}
    .layuimini-notice-title {line-height:28px;font-size:14px;}
    .layuimini-notice-extra {position:absolute;top:50%;margin-top:-8px;right:16px;display:inline-block;height:16px;color:#999;}
    #jxphb tbody tr:nth-child(2){background:#ffcc99;}
    #jxphb tbody tr:nth-child(3){background:#ccccff;}
    #jxphb tbody tr:nth-child(4){background:#ffcccc;}
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md8">
            <div class="layui-row layui-col-space15">
               <!-- <div class="layui-col-xs12 layui-col-sm6 layui-col-md6 layui-col-lg6">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header">
                            我的线索数
                            &lt;!&ndash; <span class="layui-badge layui-bg-blue layuiadmin-badge">周</span> &ndash;&gt;
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">
                            <p class="layuiadmin-big-font"><?php echo htmlentities($cluesCount_week); ?></p>
                            <p>
                                总线索
                                <span class="layuiadmin-span-color"><?php echo htmlentities($cluesCount); ?> 条</span>
                            </p>
                        </div>
                    </div>
                </div>-->

                <div class="layui-col-xs12 layui-col-sm4 layui-col-md4 layui-col-lg4">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header">
                            <?php echo fy('client pool'); ?>
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">

                            <p class="layuiadmin-big-font"><?php echo htmlentities($liberumCount); ?></p>
                            <p>
                                <?php echo fy('Total data'); ?>
                                <span class="layuiadmin-span-color"><?php echo htmlentities($liberumCount); ?></span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="layui-col-xs12 layui-col-sm4 layui-col-md4 layui-col-lg4">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header">
                            <?php echo fy('My clients'); ?>
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">
                            <p class="layuiadmin-big-font"><?php echo htmlentities($clientCount_month); ?></p>
                            <p>
                                <?php echo fy('Total clients'); ?>
                                <span class="layuiadmin-span-color"><?php echo htmlentities($clientCount); ?></span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs12 layui-col-sm4 layui-col-md4 layui-col-lg4">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header">
                            <?php echo fy('My Business Opportunities'); ?>
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">
                            <?php


                            $business_total=\think\facade\Db::name('business')->count();
                            $business_my_total=\think\facade\Db::name('business')->where('owner_admin_id','=',$admin['admin_id'])->count();
                            ?>
                            <p class="layuiadmin-big-font"><?php echo htmlentities($business_my_total); ?></p>
                            <p>
                                <?php echo fy('Total opportunities'); ?>
                                <span class="layuiadmin-span-color"><?php echo htmlentities($business_total); ?></span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header"><?php echo fy('Performance Monthly Ranking'); ?> <a href="javascript:;" style="float: right;" layuimini-content-href="<?php echo myurl('order/analytics'); ?>" data-title="<?php echo fy('Performance statistics'); ?>" data-icon="fa fa-university"><?php echo fy('More'); ?>>></a></div>
                        <div class="layui-card-body" style="overflow-y: scroll;">
                            <table class="layui-table" id="jxphb">
                                <tbody>
                                <tr>
                                    <td><?php echo fy('Monthly ranking'); ?></td>
                                    <td><?php echo fy('Salesman'); ?></td>
                                    <td><?php echo fy('Month target'); ?></td>
                                    <td><?php echo fy('Dealed'); ?>（<?php echo fy('amount'); ?>）</td>
                                                <td><?php echo fy('Freight'); ?></td>
                                    <td><?php echo fy('Completion rate'); ?>（%）</td>
                                    <td><?php echo fy('Dealed'); ?>（<?php echo fy('Order volume'); ?>）</td>
                                    <td><?php echo fy('Commission'); ?>（%）</td>
                                </tr>
                                <?php if(is_array($userlist) || $userlist instanceof \think\Collection || $userlist instanceof \think\Paginator): $k = 0; $__LIST__ = $userlist;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                                <tr >
                                    <?php
if($vo['money_month']){
if(!empty($vo['mubiao'])){
$vo['wanchenglv']=round($vo['money_month']/$vo['mubiao']*100,2);
}else{
$vo['wanchenglv']='';
}

}else{
$vo['wanchenglv']=$vo['money_month']='';
}
?>
                                    <td><?php echo htmlentities($k); ?></td>
                                    <td><?php echo htmlentities($vo['username']); ?></td>
                                    <td><?php echo htmlentities($vo['mubiao']); ?></td>
                                    <td><?php echo htmlentities($vo['money_month']); ?></td>
                                                <td><?php echo htmlentities((isset($vo['freight_month']) && ($vo['freight_month'] !== '')?$vo['freight_month']:0)); ?></td>
                                    <td><?php echo htmlentities($vo['wanchenglv']); ?></td>
                                    <td><?php echo htmlentities((isset($vo['number_month']) && ($vo['number_month'] !== '')?$vo['number_month']:0)); ?></td>
                                    <td><?php echo empty($vo['ticheng'])?'':$vo['ticheng'].'%'; ?></td>
                                </tr>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header"><?php echo fy('Reminder information'); ?> <a href="javascript:;" style="float: right;" layuimini-content-href="<?php echo myurl('process.todo/index'); ?>" data-title="<?php echo fy('Reminder information'); ?>" data-icon="fa fa-commenting"><?php echo fy('More'); ?>>></a></div>
                        <div class="layui-card-body">
                            <?php
$aid=$admin['admin_id'];

$group_id=\think\facade\Db::name('admin')->where('admin_id',$aid)->cache('group_id_'.$aid)->value('group_id');
$todoLst=\think\facade\Db::name('todo')->alias('t')->field('t.mess,t.event,t.todo_no,t.show_auth_group_id,t.show_auth_admin_id,a.username,t.is_finish,t.url,t.id,t.title,t.result,t.createtime')->join('admin a','t.admin_id=a.admin_id','left')->whereRaw('t.`admin_id` = :aid OR FIND_IN_SET(:group_id,t.`show_auth_group_id`) OR FIND_IN_SET(:admin_id,t.`show_auth_admin_id`)',['aid'=>$aid,'group_id'=>$group_id,'admin_id'=>$aid])->order(\think\facade\Db::raw("FIELD(t.`result`,'0','-1','1') ASC,t.createtime DESC"))->limit(10)->select();
                            foreach($todoLst as $v){
                            $v['title']=fy($v['title']);
                                if(empty($v['mess'])){
                                     $v['mess']=$v['username'].' '.fy($v['event']).' '.$v['todo_no'];
                                }

                            ?>
                            <div class="layuimini-notice" <?php if((strpos(','.$v['show_auth_group_id'].',',','.$group_id.',')!==false || strpos(','.$v['show_auth_admin_id'].',',','.$aid.',')!==false) && $v['is_finish']==0){ ?>data-open="<?php echo htmlentities($v['url']); ?>&tudo_id=<?php echo htmlentities($v['id']); ?>" <?php } ?> data-title="<?php echo htmlentities($v['title']); ?>">
                            <div class="layuimini-notice-title">【<?php echo htmlentities($v['title']); ?>】<?php echo htmlentities($v['mess']); ?> <span style="color: blue;"><?php echo fy($v['result']); ?></span></div>
                            <div class="layuimini-notice-extra"><?php echo date('Y-m-d H:i',$v['createtime']); ?></div>
                        </div>
                        <?php } ?>

                    </div>
                </div>


            </div>

            <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
                <div class="layui-card layui-panel">
                    <div class="layui-card-header"><?php echo fy('Dynamic'); ?></div>
                    <div class="layui-card-body">
                        <dl class="layuiadmin-card-status">
                            <?php if(is_array($result) || $result instanceof \think\Collection || $result instanceof \think\Paginator): $i = 0; $__LIST__ = $result;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <dd>
                                <div class="layui-status-img"><a href="javascript:;">
                                    <img src="<?php echo htmlentities($vo['avatar']); ?>"></a>
                                </div>
                                <div>
                                    <p><?php echo htmlentities($vo['username']); ?> <em style="color: red;margin-right: 5px"><?php echo fy('follow up'); ?></em>  <a href="javascript:;" data-open="crm.record/dialogue?id=<?php echo htmlentities($vo['id']); ?>" data-title="<?php echo fy('Recent follow-up records'); ?>" data-full="true" > <?php echo htmlentities($vo['name']); ?></a> </p>
                                    <p><strong style="color: burlywood;margin-right: 5px"><?php echo fy('Follow up on records'); ?>: </strong><?php echo htmlentities($vo['content']); ?></p>
                                    <span><?php echo fy('Follow-up time'); ?>：<?php echo htmlentities(date('Y-m-d H:i:s',!is_numeric($vo['create_time'])? strtotime($vo['create_time']) : $vo['create_time'])); ?></span>
                                </div>
                            </dd>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                        </dl>
                    </div>
                </div>
            </div>


            </div>

        </div>

        <div class="layui-col-md4">
            <div class="layui-row layui-col-space15">

                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header">
                            <?php echo fy('Notice'); ?>
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">

                            <p>
                                <?php echo $system['notice']; ?>
                            </p>
                        </div>
                    </div>
                </div>


                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header"><?php echo fy('Need to be dealt with'); ?></div>
                        <div class="layui-card-body layui-text">
                            <table class="layui-table">
                                <tbody>
                                <tr>
                                    <td><?php echo fy('Followed up with customers today'); ?></td>
                                    <td>
                                        <?php echo htmlentities($today_followed_count); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><?php echo fy('Number of customers not followed up'); ?></td>
                                    <td>
                                        <?php echo htmlentities($not_followed_count); ?>
                                    </td>
                                </tr>
                                <?php
 $today_business_record_count = \think\facade\Db::name('business_record')->where('create_admin_id','=',$admin['admin_id'])->whereTime('create_time','today')->count('DISTINCT business_id');

                                //        没有跟进的列表
                                $today_business_tiixng = \think\facade\Db::name('business')->field('`id`,`owner_username`,`customer_id`,`name`,`next_time`')->where('is_end','=',0)->where('owner_admin_id','=',$admin['admin_id'])->whereNotNull('next_time')->where('next_time','<>',0)->whereTime('next_time','<=','tomorrow -1second')->order('next_time ASC')->limit(10)->select()->toArray();
                                //        待跟进数
                                $not_followed_business_count=\think\facade\Db::name('business')->where('is_end','=',0)->where('owner_admin_id','=',$admin['admin_id'])->whereNotNull('next_time')->where('next_time','<>',0)->whereTime('next_time','<=','tomorrow -1second')->count();
?>
                                <tr>
                                    <td><?php echo fy('Opportunities have been followed today'); ?></td>
                                    <td>
                                        <?php echo htmlentities($today_business_record_count); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><?php echo fy('Opportunities are not followed up'); ?></td>
                                    <td>
                                        <?php echo htmlentities($not_followed_business_count); ?>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
                    <div class="layui-card layui-panel">
                        <div class="layui-card-header"><?php echo fy('Clients to be followed'); ?></div>
                        <div class="layui-card-body">
                            <dl class="layuiadmin-card-status">
                                <?php if(is_array($today_tiixng) || $today_tiixng instanceof \think\Collection || $today_tiixng instanceof \think\Paginator): $i = 0; $__LIST__ = $today_tiixng;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <dd>
                                    <div>
                                        <p><?php echo htmlentities($vo['pr_user']); ?> <em style="color: red;margin-right: 5px"><?php echo fy('Follow up'); ?></em>  <a href="javascript:;" data-open="crm.record/dialogue?id=<?php echo htmlentities($vo['id']); ?>" data-title="<?php echo fy('Clients to be followed'); ?>" data-full="true" > <?php echo htmlentities($vo['name']); ?></a> </p>
                                        <span><?php echo fy('Follow-up time'); ?>：<?php echo htmlentities(date('Y-m-d H:i:s',!is_numeric($vo['next_time'])? strtotime($vo['next_time']) : $vo['next_time'])); ?></span>
                                    </div>
                                </dd>
                                <?php endforeach; endif; else: echo "" ;endif; ?>

                            </dl>
                        </div>
                    </div>

                    <div class="layui-card layui-panel">
                        <div class="layui-card-header"><?php echo fy('Wait for the opportunity to be followed up'); ?></div>
                        <div class="layui-card-body">
                            <dl class="layuiadmin-card-status">
                                <?php if(is_array($today_business_tiixng) || $today_business_tiixng instanceof \think\Collection || $today_business_tiixng instanceof \think\Paginator): $i = 0; $__LIST__ = $today_business_tiixng;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <dd>
                                    <div>
                                        <p><?php echo htmlentities($vo['owner_username']); ?> <em style="color: red;margin-right: 5px"><?php echo fy('Follow up'); ?></em>  <a href="javascript:;" data-open="business.record/add?business_id=<?php echo htmlentities($vo['id']); ?>&customer_id=<?php echo htmlentities($vo['customer_id']); ?>" data-title="<?php echo fy('Wait for the opportunity to be followed up'); ?>" data-full="true" > <?php echo htmlentities($vo['name']); ?></a> </p>
                                        <span><?php echo fy('Follow-up time'); ?>：<?php echo htmlentities(date('Y-m-d H:i:s',!is_numeric($vo['next_time'])? strtotime($vo['next_time']) : $vo['next_time'])); ?></span>
                                    </div>
                                </dd>
                                <?php endforeach; endif; else: echo "" ;endif; ?>

                            </dl>
                        </div>
                    </div>
                </div>

            </div>
        </div>

</div>
</div>

</body>
</html>