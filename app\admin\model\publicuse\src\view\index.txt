<link rel="stylesheet" href="/static/plugs/lay-module/treetable-lay/treetable.css?v={:time()}" media="all">
<style>
    .layui-btn:not(.layui-btn-lg ):not(.layui-btn-sm):not(.layui-btn-xs) {
        height: 34px;
        line-height: 34px;
        padding: 0 8px;
    }
    /*.layui-table-cell{*/
    /*display:table-cell;*/
    /*vertical-align: middle;*/
    /*}*/
    able{table-layout: fixed;}
    td{word-break: break-all; word-wrap:break-word;}
    .layui-fluid {
        height:auto;
    }
    {{STYLE}}
</style>
<script>
    var parameter = '{$parameter|raw|default=""}';
</script>
<div class="layuimini-container">
    <div class="layuimini-main">
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="{:auth('{{class}}/add')}"
               data-auth-edit="{:auth('{{class}}/edit')}"
               data-auth-delete="{:auth('{{class}}/delete')}"
{{toolBar}}
               lay-filter="currentTable">
        </table>
    </div>
</div>
<script type="text/html" id="toolbar">
    <button class="layui-btn layui-btn-sm layuimini-btn-primary" data-treetable-refresh><i class="fa fa-refresh"></i> </button>
    <button class="layui-btn layui-btn-normal layui-btn-sm {if !auth('{{class}}/add')}layui-hide{/if}" data-open="{{class}}/add" data-title="添加"><i class="fa fa-plus"></i> 添加</button>
    <button class="layui-btn layui-btn-sm layui-btn-danger {if !auth('{{class}}/del')}layui-hide{/if}" data-url="{{class}}/delete" data-treetable-delete="currentTableRenderId"><i class="fa fa-trash-o"></i> 删除</button>
</script>
<script type="text/html" id="right_button">
    {{DefaultRightButton}}
    {{RightButton}}
</script>