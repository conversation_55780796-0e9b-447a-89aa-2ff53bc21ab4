<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Parent')}</label>
            <div class="layui-input-block">
                <select name="pid" lay-verify="required" lay-filter="pid" >
                    <option value="0">{:fy('Default Top Level')}</option>
                    <?php
 $typeLst = \think\facade\Db::name('product_type')->order('pid asc,sort asc,id asc')->select();
                    $nav = new \clt\Leftnav();
                    $typeLst = $nav->menu($typeLst);
                    $id=isset($_GET['id'])?$_GET['id']:0;
                    ?>
                    {volist name="typeLst" id="vo"}
                    <option value="{$vo.id}" <?php if($vo['id']==$id)echo 'selected'; ?> >{$vo.lefthtml}{:fy($vo['title'])}</option>
                    {/volist}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Category Name')}</label>
            <div class="layui-input-block">
                <input type="text" name="title" class="layui-input" lay-verify="required" placeholder="{:fy('Please enter')}{:fy('Category Name')}" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Sort')}</label>
            <div class="layui-input-block">
                <input type="text" name="sort" class="layui-input"  placeholder="{:fy('Please enter')}{:fy('Sort')}" value="0">
            </div>
        </div>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>{:fy('Confirm')}</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">{:fy('Reset')}</button>
        </div>

    </form>
</div>