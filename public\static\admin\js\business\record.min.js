define(["jquery","easy-admin","vue"],function(e,s,i){var l={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"business.record/index",add_url:"business.record/add",edit_url:"business.record/edit",delete_url:"business.record/delete",export_url:"business.record/export",modify_url:"business.record/modify"};var t={index:function(){s.table.render({toolbar:["refresh","delete"],init:l,limit:CONFIG.ADMINPAGESIZE,cols:[[{type:"checkbox"},{field:"id",title:"ID"},{field:"create_username",title:fy("Follower"),search:true},{field:"create_time",title:fy("Follow up time"),search:"range",templet:s.table.date},{field:"content",title:fy("Follow up content"),search:true},{field:"next_time",title:fy("Next follow-up time"),search:"range",templet:s.table.date},{field:"record_type",title:fy("Follow up type")},{field:"attachs",title:fy("Attachment"),templet:function(e,t){if(e.attachs){return'<a class="layui-btn layui-btn-success layui-btn-xs" data-open="attachs/index?attachs='+e.attachs+'" data-title="'+fy("Follow up")+" "+fy("Attachment")+'">'+fy("Attachment")+"</a>"}else{return""}}},{width:250,title:fy("Operate"),templet:s.table.tool,operat:["delete"]}]],done:function(e,t,i){if(i===undefined&&e.msg&&e.url){s.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}},where:{scope:1}});s.listen()},add:function(){this.renderPro();s.listen("",function(e){s.msg.success(e.msg,function(){layui.table.reload(l.table_render_id,{page:{curr:1}})})},function(e){s.msg.error(e.msg,function(){})})},edit:function(){s.listen()},renderPro:function(){business_id=e("#table-pro").data("business_id");s.table.render({toolbar:["refresh"],init:l,limit:CONFIG.ADMINPAGESIZE,cols:[[{field:"id",title:"ID"},{field:"create_username",title:fy("Follower"),search:true},{field:"create_time",title:fy("Follow up time"),search:"range",templet:s.table.date},{field:"content",title:fy("Follow up content"),search:true},{field:"next_time",title:fy("Next follow-up time"),search:"range",templet:s.table.date},{field:"record_type",title:fy("Follow up type")},{field:"attachs",title:fy("Attachment"),templet:function(e,t){if(e.attachs){return'<a class="layui-btn layui-btn-success layui-btn-xs" data-open="attachs/index?attachs='+e.attachs+'" data-title="'+fy("Follow up")+" "+fy("Attachment")+'">'+fy("Attachment")+"</a>"}else{return""}}}]],done:function(e,t,i){if(i===undefined&&e.msg&&e.url){s.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}},where:{scope:1,business_id:business_id}});var t=new i({el:"#app",data:{pro_list:[],cost_sum:0,sale_sum:0},methods:{entryTime(e){if(!this.pro_list[e]["create_time"]){this.pro_list[e]["create_time"]=Date.parse(new Date)/1e3}return layui.util.toDateString(this.pro_list[e]["create_time"]*1e3,"yyyy-MM-dd HH:mm")},removePro(t,e){that=this;if(e){s.request.ajax("get",{url:s.url("business/delproduct_by_business"),data:{business_product_id:e}},function(e){if(e.code){that.pro_list.splice(t,1)}else{s.msg.error(fy("Delete failed"))}})}else{that.pro_list.splice(t,1)}}},computed:{getTotal(){var t=this.pro_list;var i=0,s=0,l=0,r=0;for(let e=0;e<t.length;e++){i+=t[e].cost_price*t[e].nums;l+=t[e].sale_price*t[e].nums;s+=parseFloat(t[e].discount);r+=parseInt(t[e].nums)}if(l){real_sale_sum=l-s}else{real_sale_sum=0}return{cost_sum:i.toFixed(2),nums_sum:r,discount_sum:s.toFixed(2),sale_sum:l.toFixed(2),real_sale_sum:real_sale_sum.toFixed(2)}}}});if(business_id){s.request.ajax("get",{url:s.url("business/product_by_business"),data:{business_id:business_id}},function(e){t.pro_list=e.data})}}};return t});