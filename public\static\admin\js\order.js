define(["jquery", "easy-admin"], function ($, ea) {
    table = layui.table;form = layui.form;
    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        add_url: 'order/add',
        index_url: 'order/index',
        delete_url: 'order/del',
        personindex_url: 'order/personindex',
        export_url: 'order/export',
    };

    var Controller = {
        index: function () {
            cols_fields=[];
            if(CONFIG.cols_fields){
                cols_fields=[].concat({'type': 'checkbox'}, {field: 'id', title: 'ID'},
                    {'width': 200, 'title': fy('Operate'), 'templet': ea.table.tool,operat: [
                            [{
                                class: 'layui-btn layui-btn-xs layui-btn-primary',
                                method: 'open',
                                text: fy('审核订单'),
                                auth: 'editAudit',
                                url: 'order/editAudit?id={id}&tudo_id={tudo_id}',
                                extend: 'data-full="true"', icon: 'fa fa-edit ',
                                hidden:function (data) {
                                    if (data.status==0) return false;
                                    return  true;
                                }
                            }], [{
                                class: 'layui-btn layui-btn-xs',
                                method: 'open',
                                text: fy('详情'),
                                auth: 'desc',
                                url: 'order/desc',
                                extend: 'data-full="true"', icon: ''
                            }], 'delete']},
                    {field: 'cname', title: fy('Client name'),'width': 150,search:true},
                    {field: 'orderno', title: fy('Order number'),'width': 150,search:true},
                    {field: 'cphone', title: fy('Contact number') ,'width': 150,search:true},
                    {field: 'money', title: fy('amount'),'width': 150},
                    {field: 'freight', title: fy('Freight'),'width': 150},
                    {field: 'pr_user', title: fy('Responsible Person'),'width': 150,hide:false,search:true },
                    {field: 'status', title: fy('Audit status'),'width': 150,templet:function (res) {
                            if(res.status==0){
                                return CONFIG.statusList[res.status];
                            }else if(res.status==1){
                                return '<span class="green">'+CONFIG.statusList[res.status]+'</span>';
                            }else{
                                return '<span class="red">'+CONFIG.statusList[res.status]+'</span>';
                            }

                        }
                    ,search: 'select', selectList: CONFIG.statusList  },
                    {field: 'audit_feedback', title: '审核反馈','width': 150},
                    {field: 'update_time', title: fy('Update time'),'width': 150},CONFIG.cols_fields)
            }
            ea.table.render({
                toolbar: ['refresh','add','export',[{
                    text: fy('Custom fields'),
                    url: 'system.fields/index?table=crm_client_order',
                    method: 'open',
                    auth: 'fields',
                    class: 'layui-btn layui-btn-warm layui-btn-sm',
                    icon: 'fa fa-cogs ',
                    extend: 'data-full="true"',
                }]],
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols:[ cols_fields],
                done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                }
            });
            ea.listen();
        }
        ,
        personindex: function () {
            init = {
                table_elem: '#currentTable',
                table_render_id: 'currentTableRenderId',
                add_url: 'order/add',
                index_url: 'order/personindex',
                delete_url: 'order/mydel',
                export_url: 'device/export',
            };
            cols_fields=[];
            if(CONFIG.cols_fields){
                cols_fields=[].concat({'type': 'checkbox'}, {field: 'id', title: 'ID'},
                    {'width': 178, 'title': fy('Operate'), 'templet': ea.table.tool,operat: [
                            [{
                                class: 'layui-btn layui-btn-xs layui-btn-primary',
                                method: 'open',
                                text: fy('编辑'),
                                auth: 'edit',
                                url: 'order/myedit',
                                extend: 'data-full="true"', icon: 'fa fa-edit ',
                                hidden:function (data) {
                                    if (data.status==-1) return false;
                                    return  true;
                                }
                            }], [{
                                class: 'layui-btn layui-btn-xs',
                                method: 'open',
                                text: fy('详情'),
                                auth: 'desc',
                                url: 'order/desc',
                                extend: 'data-full="true"', icon: ''
                            }], 'delete']},
                    {field: 'cname', title: fy('Client name'),'width': 150,search:true},
                    {field: 'orderno', title: fy('Order number'),'width': 150,search:true},
                    {field: 'cphone', title: fy('Contact number') ,'width': 150,search:true},
                    {field: 'money', title: fy('amount'),'width': 150},
                    {field: 'freight', title: fy('Freight'),'width': 150},
                    {field: 'pr_user', title: fy('Responsible Person'),'width': 150,hide:false,search:true },
                    {field: 'status', title: fy('Audit status'),'width': 150,templet:function (res) {

                            if(res.status==0){
                                return CONFIG.statusList[res.status];
                            }else if(res.status==1){
                                return '<span class="green">'+CONFIG.statusList[res.status]+'</span>';
                            }else{
                                return '<span class="red">'+CONFIG.statusList[res.status]+'</span>';
                            }

                        },search: 'select', selectList: CONFIG.statusList  },
                    {field: 'audit_feedback', title: '审核反馈','width': 150},
                    {field: 'update_time', title: fy('Update time'),'width': 150},CONFIG.cols_fields)
            }
            ea.table.render({
                toolbar: ['refresh','add'],
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols:[ cols_fields],
                done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                }
            });
            ea.listen();
        },desc: function () {
            ea.listen();
        },changeyewu:function (custphone){
            if(ea.admin.empty(custphone)){
                return false;
            }
            $.ajax({
                //几个参数需要注意一下
                type: "POST",//方法类型
                dataType: "json",//预期服务器返回的数据类型
                data: {'cphone':custphone},
                url:ea.url("order/changeyewu") ,
                success: function (result) {
                    console.log(result);
                    //console.log(result);//打印服务端返回的数据(调试用)
                    if (result['msg']['code'] == 0) {
                        $('#checklabel').css('color','red');
                    }else{
                        $('#checklabel').css('color','green');
                        $('#cname').val(result['msg']['custname']);
                        $('#pr_user').val(result['msg']['pr_user']);
                        $('#kh_username').val(result['msg']['kh_username']);
                        $('#kh_name').val(result['msg']['contact']);
                    }
                    $('#checklabel').html(result['msg']['msg']);


                },
                error : function() {
                    alert(fy('abnormal')+"！");
                }
            });
        },
        add: function () {
            var cphoneObj=$("#cphone");
            var custphone = cphoneObj.val();
            $(document).on('change','#cphone',function (){
                //重新查找业务员的客户信息
                custphone = cphoneObj.val();
                $('#cname').val('');
                Controller.changeyewu(custphone);
            });

            form.verify({
                orderno_unqiue : function(value, item) {
                    var checkResult=false;
                    $.ajax({
                        url: ea.url("order/verify_orderno"),
                        type: "POST",dataType:"json",
                        data: {orderno:value},
                        async: false,
                        success: function(res) {
                            if (res.code < 0){
                                checkResult=res.msg;
                            }
                        },
                        error: function() {
                        }
                    });
                    if(checkResult){
                        return checkResult;
                    }
                }
            });
            ea.listen(null,function (res) {
                if (res.code == 1){
                    ea.msg.success(res.msg, function () {
                        window.parent.location.reload();
                    });
                }else {
                    ea.msg.error(res.msg);
                }
            });
            var custphone = cphoneObj.val();
            if(custphone){
                Controller.changeyewu(custphone);
            }
        },
        myedit: function () {

            $(document).on('change','#cphone',function (){
                //重新查找业务员的客户信息
                custphone = $("#cphone").val();
                Controller.changeyewu(custphone);
            });
            form.verify({
                orderno_unqiue : function(value, item) {
                    var checkResult=false;
                    $.ajax({
                        url: ea.url("order/verify_orderno"),
                        type: "POST",dataType:"json",
                        data: {orderno:value,id:$('input[name="id"]').val()},
                        async: false,
                        success: function(res) {
                            if (res.code < 0){
                                checkResult=res.msg;
                            }
                        },
                        error: function() {
                        }
                    });
                    if(checkResult){
                        return checkResult;
                    }
                }
            });
            ea.listen(null,function (res) {
                if (res.code == 1){
                    ea.msg.success(res.msg, function () {
                        window.parent.location.reload();
                    });
                }else {
                    ea.msg.error(res.msg);
                }
            });
        },
        sale: function () {
            ea.listen();
        },
        editAudit: function () {
            ea.listen(null,function (res) {
                if (res.code == 1){
                    ea.msg.success(res.msg,function (){
                        window.parent.location.reload();
                    });
                }else {
                    ea.msg.error(res.msg);
                }
            });
        },
        analytics:function (){
            init = {
                table_elem: '#currentTable',
                table_render_id: 'currentTableRenderId',
                add_url: 'order/add',
                index_url: 'order/analytics',
                export_url: 'device/export',
            };
            var myDate = new Date();
            var year=myDate.getFullYear(); //获取完整的年份(4位,1970-????)
            var month=myDate.getMonth()+1; //获取当前月份(0-11,0代表1月)
            var cur_date=year+'年'+month+'日'
            ea.table.render({
                toolbar: ['refresh'],
                init: init, limit: Number.MAX_VALUE,page: false,
            defaultToolbar:['filter',{
                    title: fy('Custom printing'),
                    layEvent: 'DIY_PRINT',
                    icon: 'layui-icon-print',
                }],
                cols: [[
                    {search:'<div class="layui-form-item layui-inline"><label class="layui-form-label">'+fy('month')+'</label><div class="layui-input-inline"><input id="c-month" name="month" data-date data-date-type="month" class="layui-input" data-search-op="=" value="'+year+'-'+month+'" /></div>',hide:true},
                    {field: 'username', title: fy('salesman'),search:true},

                    {field: 'mubiao', title: fy('Month target')},
                    {field: 'money_month', title: fy('Dealed'),templet: function (res,option){
                            if(res.money_month){
                                return res.money_month;
                            }
                            return 0;
                        }},
                    {field: 'freight_month', title: fy('Freight'),templet: function (res,option){
                            if(res.freight_month){
                                return res.freight_month;
                            }
                            return 0;
                        }},
                    {field: 'wanchenglv', title: fy('Completion rate')+'（%）',templet: function (res,option){
                            if(res.money_month){
                                if(res.mubiao){
                                    wclv=res.money_month/res.mubiao*100;
                                    return wclv.toFixed(2);
                                }else{
                                    return '月目标未设置'
                                }
                            }
                            return 0;
                        }},
                    {field: 'number_month', title: fy('Dealed')+'（'+fy('Order volume')+'）',templet: function (res,option){
                            if(res.number_month){
                                return res.number_month;
                            }
                            return 0;
                        }},
                    {field: 'ticheng', title: fy('Commission')+'（%）',templet: function (res,option){
                            if(res.ticheng){
                                return res.ticheng+'%';
                            }
                            return '';
                        }}
                ]],

                done: function(res, curr, count){
                    if(res.cur_date){
                        cur_date=res.cur_date;
                    }
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                }
            });

            ea.listen();
            table.on('toolbar(currentTableRenderId_LayFilter)', function (obj) {
                // 搜索表单的显示
                switch (obj.event) {
                    case 'DIY_PRINT':
                        //自定义打印处理
                        var f = ["<style>", "body{font-size: 12px; color: #666;}", "table{width: 100%; border-collapse: collapse; border-spacing: 0;}", "th,td{line-height: 20px; padding: 9px 15px; border: 1px solid #ccc; text-align: left; font-size: 12px; color: #666;text-align: center;}", "a{color: #666; text-decoration:none;}", "*.layui-hide{display: none}", "</style>"].join("");
                        v=$($(".layui-table-header").html());
                        v.append($(".layui-table-main table").html());
                        v.find("th.layui-table-patch").remove(), v.find(".layui-table-col-special").remove();
                        var h = window.open("Print_window", "_blank");
                        h.document.write(f +'<h1 style="text-align: center;">'+cur_date+'员工业绩表<h1>'+ $(v).prop("outerHTML"));
                        h.document.close();
                        h.print();
                        h.close();
                }
            });
            $('.table-search-fieldset').removeClass('layui-hide');
        }
    };
    return Controller;
});
