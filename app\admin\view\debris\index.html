{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>碎片管理</legend>
    </fieldset>
    <div class="demoTable">
        <div class="layui-inline">
            <input class="layui-input" name="key" id="key" placeholder="{:lang('Please enter')}关键字">
        </div>
        <button class="layui-btn" id="search" data-type="reload">{:lang('search')}</button>
        <a href="{:url('index')}" class="layui-btn">显示全部</a>
        <button type="button" class="layui-btn layui-btn-danger" id="delAll">批量删除</button>
        <a href="{:url('add')}" class="layui-btn" style="float:right;"><i class="fa fa-plus" aria-hidden="true"></i>{:lang('add')}碎片</a>
        <div style="clear: both;"></div>
    </div>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="order">
    <input name="{{d.id}}" data-id="{{d.id}}" class="list_order layui-input" value=" {{d.sort}}" size="10"/>
</script>
<script type="text/html" id="action">
    <a href="{:url('edit')}?id={{d.id}}" class="layui-btn layui-btn-xs">{:fy('Edit')}</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">{:fy('Delete')}</a>
</script>
<script>
    layui.use('table', function() {
        var table = layui.table, $ = layui.jquery;
        var tableIn = table.render({
            id: 'debris',
            elem: '#list',
            url: '{:url("index")}',
            method: 'post',
            page:true,
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'id', title: '{:lang("id")}', width: 80, fixed: true},
                {field: 'title', title: '碎片标题', width: 400},
                {field: 'typename', title: '碎片位置', width: 300},
                {field: 'addtime', title: '{:lang("add")}{:lang("time")}',width: 150},
                {field: 'sort', align: 'center', title: '{:lang("order")}', width: 120, templet: '#order'},
                {width: 160, align: 'center', toolbar: '#action'}
            ]],
            limit:{$system['admin_pagesize']}
        });
        //搜索
        $('#search').on('click', function () {
            var key = $('#key').val();
            if ($.trim(key) === '') {
                layer.msg('{:lang("pleaseEnter")}关键字！', {icon: 0});
                return;
            }
            tableIn.reload({ page: {page: 1}, where: {key: key}});
        });
        table.on('tool(list)', function(obj) {
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('您确定要删除该碎片吗？', function(index){
                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                    $.post("{:url('del')}",{id:data.id},function(res){
                        layer.close(loading);
                        if(res.code===1){
                            layer.msg(res.msg,{time:2000,icon:1});
                            tableIn.reload();
                        }else{
                            layer.msg('{:fy("Operation failed")}'+'！',{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
        //{:fy('Sort')}
        $('body').on('blur','.list_order',function() {
            var id = $(this).attr('data-id');
            var sort = $(this).val();
            var loading = layer.load(1, {shade: [0.1, '#fff']});
            $.post('{:url("debrisOrder")}',{id:id,sort:sort},function(res){
                layer.close(loading);
                if(res.code === 1){
                    layer.msg(res.msg, {time: 1000, icon: 1});
                    tableIn.reload();
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                }
            })
        });
        $('#delAll').click(function(){
            layer.confirm('确认要删除选中的信息吗？', {icon: 3}, function(index) {
                layer.close(index);
                var checkStatus = table.checkStatus('debris'); //test即为参数id设定的值
                var ids = [];
                $(checkStatus.data).each(function (i, o) {
                    ids.push(o.id);
                });
                var loading = layer.load(1, {shade: [0.1, '#fff']});
                $.post("{:url('delall')}", {ids: ids}, function (data) {
                    layer.close(loading);
                    if (data.code === 1) {
                        layer.msg(data.msg, {time: 1000, icon: 1});
                        tableIn.reload();
                    } else {
                        layer.msg(data.msg, {time: 1000, icon: 2});
                    }
                });
            });
        })
    })
</script>