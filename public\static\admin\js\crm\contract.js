define(["jquery", "easy-admin","vue"], function ($, ea,Vue) {
    var tableSelect = layui.tableSelect;
    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'crm.contract/index',
        add_url: 'crm.contract/add',
        edit_url: 'crm.contract/edit',
        delete_url: 'crm.contract/delete',
        export_url: 'crm.contract/export',
        modify_url: 'crm.contract/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,limit:CONFIG.ADMINPAGESIZE,
                toolbar: ['refresh',
                    [{
                        text: '添加',
                        url: init.add_url,
                        method: 'open',
                        auth: 'add',
                        class: 'layui-btn layui-btn-normal layui-btn-sm',
                        icon: 'fa fa-plus ',
                        extend: 'data-full="true"',
                    }],
                    'delete'],
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'id'},
                    {field: 'customer_id', title: '客户ID'},
                    {field: 'business_id', title: '商机ID'},
                    {field: 'contacts_id', title: '客户签约人（联系人ID）'},
                    {field: 'source_id', title: '来源ID'},
                    {field: 'name', title: '合同名称'},
                    {field: 'number', title: '合同编号'},
                    {field: 'order_time', title: '下单时间'},
                    {field: 'money', title: '合同金额'},
                    {field: 'total_price', title: '产品总金额'},
                    {field: 'return_money', title: '已收到款项'},
                    {field: 'discount_rate', title: '整单折扣'},
                    {field: 'check_status', title: '0待审核、1审核中、2审核通过、3审核未通过'},
                    {field: 'flow_id', title: '审核流程ID'},
                    {field: 'step_id', title: '审核步骤ID'},
                    {field: 'check_admin_id', title: '已经审批人IDs'},
                    {field: 'flow_admin_id', title: '当前需要审批的人'},
                    {field: 'start_time', title: '开始时间'},
                    {field: 'end_time', title: '结束时间'},
                    {field: 'order_admin_id', title: '公司签约人'},
                    {field: 'remark', title: fy("Remark"), templet: ea.table.text},
                    {field: 'create_user_id', title: '创建人ID'},
                    {field: 'owner_user_id', title: '负责人ID'},
                    {field: 'ro_user_id', title: '只读权限'},
                    {field: 'rw_user_id', title: '读写权限'},
                    {field: 'next_time', title: fy("Next contact time")},
                    {field: 'create_time', title: fy('Creation time')},
                    {field: 'expire_handle', title: '0合同过期未处理1已续签2不再合作'},
                    {field: 'invoice_money', title: '已开票金额'},
                    {width: 250, title: fy('Operate'), templet: ea.table.tool},
                ]],
                done: function(res, curr, count){
                       if(count===undefined && res.msg && res.url){
                           ea.msg.tips(res.msg,1,function (){
                               window.top.location.href=res.url;
                           })
                       }
                }
            });

            ea.listen();
        },
        add: function () {
            $(document).on('click','#create_numbering',function (){
                ea.request.post({
                    url: ea.url('crm.contract/create_numbering'),
                }, function (res) {
                    if(res.code){
                        $('input[name="numbering"]').val(res.data.numbering);
                    }else{
                        ea.msg.error(res.msg, function () {
                        });
                    }

                });
            });
            this.renderPro();
            ea.listen();
        },
        edit: function () {
            this.renderPro();
            ea.listen();
        },renderPro:function (){

        var app = new Vue({
            el: '#app',
            data: {
                pro_list: [],
                cost_sum:0.00,sale_sum:0.00
            },
            methods:{
                entryTime(index){
                    if(!this.pro_list[index]['create_time']){
                        this.pro_list[index]['create_time']=(Date.parse(new Date()))/1000;
                    }
                    return layui.util.toDateString(this.pro_list[index]['create_time']*1000, 'yyyy-MM-dd HH:mm');
                },removePro(index,product_id){
                    //删除数据库对应的商机产品
                    that =this;
                    if(product_id){
                        ea.request.ajax('get',{url:ea.url('contract/delproduct'),data:{'product_id':product_id}},function (res){
                            if(res.code){
                                that.pro_list.splice(index, 1)
                            }else{
                                ea.msg.error(fy('Delete failed'));
                            }
                        });
                    }else{
                        that.pro_list.splice(index, 1)
                    }


                }
            },computed: {
                getTotal() {
                    // 获取productList中select为true的数据
                    var proList = this.pro_list
                    // 设置一个值用来存储总价
                    var cost_sum=0,discount_sum=0,sale_sum=0,nums_sum=0;
                    for (let i = 0; i < proList.length; i++) {

                        cost_sum += proList[i].cost_price * proList[i].nums;
                        sale_sum += proList[i].sale_price * proList[i].nums;
                        discount_sum+=parseFloat(proList[i].discount);
                        nums_sum += parseInt(proList[i].nums);
                    }
                    if(sale_sum){
                        real_sale_sum=sale_sum-discount_sum;
                    }else{
                        real_sale_sum=0;
                    }

                    return {

                        cost_sum: cost_sum.toFixed(2),
                        nums_sum: nums_sum,
                        discount_sum: discount_sum.toFixed(2),
                        sale_sum: sale_sum.toFixed(2),
                        real_sale_sum: real_sale_sum.toFixed(2),
                    }
                },
            }
        });
        id=$('#table-pro').data('id');
        if(id){
            //获取合同对应的产品
            ea.request.ajax('get',{url:ea.url('contract/product'),data:{'id':id}},function (res){
                app.pro_list=res.data;

            });
        }

        tableSelect.render({
            elem: "#select-pro",
            searchType: 'more',
            checkedKey: 'id',
            searchList: [
                {searchKey: 'name', searchPlaceholder: '请输入产品名'},
            ],
            table: {
                url: ea.url('Product/index'),
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'ID'},
                    {field: 'type.title', width:90,title: '产品分类',templet: function (d){
                            return '<span>'+d.type.title+'</span>'
                        }},
                    {field: 'name', title: '产品名称',width:200 },
                    {field: 'thumb', width:90,title: '产品图片', templet: ea.table.image},
                    {field: 'specification', title: '规格'},

                    {field: 'inventory', title: '产品库存',width:90,templet: function (d){
                            if( d.inventory<=d.min_warning){
                                return '<span class="layui-font-red">'+d.inventory+'</span>'
                            }else  if( d.inventory>=d.max_warning){
                                return '<span class="layui-font-orange">'+d.inventory+'</span>'
                            }
                        }},
                    {field: 'cost_price', width:90,title: '成本价格'},
                    {field: 'sale_price', width:90,title: '出售价格'},
                ]]
            },
            done: function (e, data) {
                for (let i = 0; i < data.data.length; i++) {
                    data.data[i].product_id=data.data[i].id;
                    data.data[i].id=0;
                    data.data[i].remark='';
                    data.data[i].nums=data.data[i].nums?data.data[i].nums:1;
                    data.data[i].discount=data.data[i].discount?data.data[i].discount:0;
                }
                app.pro_list=app.pro_list.concat(data.data);


            },where: {status: 1}
        })
    }
    };
    return Controller;
});
