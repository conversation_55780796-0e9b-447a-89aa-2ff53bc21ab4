<?php /*a:2:{s:64:"/www/wwwroot/ceshi71.cn/app/admin/view/analysis/admin/index.html";i:1685769400;s:58:"/www/wwwroot/ceshi71.cn/app/admin/view/layout/default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/public/static/js/html5.min.js"></script>
    <script src="/public/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/public/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/public/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
  <div class="layuimini-main">
    <div class="layui-card">
      <div class="layui-card-header">客户量趋势</div>
      <div class="layui-card-body">
        <div  class="layui-form-item">
          <div class="layui-input-inline">
            <input type="text" name="head_admin_id" data-toggle="selectPage" class="layui-input" data-source="<?php echo myurl('admin/selectpage'); ?>"  data-field="username"  data-format-item="{username}"  data-primary-key="admin_id"  placeholder="查看指定业务员" data-params='{"custom[is_open]":"1"}'  lay-verify="required" id="head_admin_id">
          </div>
          <a href="javascript:;" class="layui-btn  layui-btn-success btn-filter" data-value="last7Days">最近7天</a>
          <a href="javascript:;" class="layui-btn  layui-btn-success btn-filter" data-value="last30Days">最近30天</a>
          <a href="javascript:;" class="layui-btn  layui-btn-success btn-filter" data-value="lastMonth">上月</a>
          <a href="javascript:;" class="layui-btn  layui-btn-success btn-filter" data-value="thisMonth">本月</a>

          <div class="layui-input-inline" style="width: 300px;">
            <input value="" placeholder="指定日期" class="layui-input form-control input-inline datetimerange" autocomplete="off" id="datetimerange" data-url="<?php echo myurl('analysis.admin/index',['ajax'=>1]); ?>" data-admin_id="0" data-charttype="order">
          </div>
        </div>

        <div id="order-echart" class="btn-refresh" style="height:200px;width:100%;">

        </div>
      </div>
    </div>

    <table id="currentTable" class="layui-table layui-hide"
           lay-filter="currentTable">
    </table>
  </div>
</div>
</body>
</html>