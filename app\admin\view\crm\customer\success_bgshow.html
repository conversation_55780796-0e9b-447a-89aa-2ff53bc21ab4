<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>成交客户</title>
    <link rel="stylesheet" href="__MY_PUBLIC__/static/bigdata/css/bootstrap.min.css?t=1" />
    <link rel="stylesheet" href="__MY_PUBLIC__/static/bigdata/css/big.css?t=1" />
    <link rel="stylesheet" href="__MY_PUBLIC__/static/plugs/layui-v2.7.6/css/modules/layer/default/layer.css" />
    <style>

        .statusList .outlineBorder ul li,.statusList .seTable li{
            width: <?php echo 100/count($fields); ?>%;
        }
        .web-name {color: #fff;position: absolute;top: 0;left: 0;width: 35%;text-align: center;margin-top: 16px;line-height: 80px;}

        #orderItems ul li input{
            background: transparent;height: 40px;width: 100%;border: none;text-align: center;color: #fff;
        }
        .empty-data{line-height: 2em;font-size: 18px;color: #fff;text-align: center;}
    </style>
</head>
<body style="background-color: #081832;">
<h1 class="web-name">{$system['name']}</h1>
<div class="header_center" style="position:relative">

    <h2><strong>成交客户表</strong></h2>

</div>
<div class="todayTimeBox">
    <div class="todayTime" id="todayTime">
    </div>
</div>
<div class="background-img" id="mainbody">

    <div class="mainbody" id="">
        <div class="leftContent">
            <div class="serviceForm">
                <div class="topss" style="display: flex; align-items: center;">

                    <form action="">
                                <input type="text" placeholder="请输入客户名称" style="width: 180px;height: 28px;border-radius: 3px;text-indent: 1em;border: 1px solid#4b8df8;color: #000;" name="kwd" value="{$kwd}">
                            <button class="btn btn-primary btn-sm submit" style="margin-left:20px" type="submit"><span class="glyphicon glyphicon-search"></span>查询</button>
                            <button class="btn btn-default btn-sm reset" style="margin-left:20px" type="reset"><span class="glyphicon glyphicon-remove"></span>重置</button>
                    </form>



                    </div>
                <div class="statusList">
                    <ul class="seTable">
                        <?php

                        foreach($fields as $field){
                         $name=$field['xsname']?$field['xsname']:$field['name'];
?>
                        <li>{$name}</li>
                        <?php } ?>
                    </ul>
                    <div id="orderItems" class="outlineBorder">

                    <?php
$c=0;
foreach($list as $item){
$c=$c+1;
?>
                        <ul data-id="{$item['id']}">
                            <?php

                        foreach($fields as $field){
                          $value=$item[$field['field']];
                $value=real_field_val($field,$value);
                if($field['field']=='phone' && $isphone==0){
                    $value=mb_substr($value, 0, 3).'****'. mb_substr($value, 7, 11);
                }
                        echo  '<li>'.$value.' </li>';
?>

                            <?php } ?>
                        </ul>
                        <?php
}
if($c<1){

    echo '<div class="empty-data">暂无数据</div>';
                    }
?>

                    </div>
                </div>
            </div>
        </div>


    </div>

</div>


<script src="__MY_PUBLIC__/static/common/js/jquery.min.js"></script>
<script src="__MY_PUBLIC__/static/plugs/layui-v2.7.6/modules/layer.js"></script>
<script src="__MY_PUBLIC__/static/js/jquery.SuperSlide.2.1.3.js"></script>
<script>
    //重新刷新页面，使用location.reload()有可能导致重新提交
    function reloadPage(win) {
        if (win) {

        } else {
            win = window;
        }
        var location  = win.location;
        location.href = location.pathname + location.search;
    }
    //获取浏览器高度
    //http://crm.jdzfalv.com.cn/admin.php/crm.customer/index?page=1&limit=10&scope=1&sort_by=next_time&sort_order=asc
    var bodyHeight = $(window).height();
    // 获取statusList类到顶部的距离
    var offsetTop = $(".statusList").offset().top;
    var orderItemsObj= $("#orderItems");

    var statusListObj=$('.statusList');
    var slideObj=statusListObj.slide( {
        mainCell: ".outlineBorder",
        effect: "topLoop",
        autoPage: true,
        //function( i, c, slider, titCell, mainCell, targetCell, prevCell, nextCell ){}，
        autoPlay: true,vis:parseInt((bodyHeight-offsetTop-50)/40),endFun:function(i,c, slider, titCell, mainCell, targetCell, prevCell, nextCell){
            console.log('i=,c=',i,c, slider, titCell, mainCell, targetCell, prevCell, nextCell)
            if(i==c-1){
               // 刷新当前页面
                reloadPage();
            }

        }
    });
    var changed = false; // 标志位，表示输入框的值是否改变
    var kwdObj=$('input[name="kwd"]');

    // 当输入框的值改变时设置标志位为true
    $(document).on('input', '#orderItems input', function() {
        changed = true;
    });

    $(document).on('click','.reset',  function() {

        window.location.href="{:myurl('success_bgshow')}";
    });
    //监听input失去焦点事件

    $(document).on('blur','#orderItems input',function(){
        if (changed) { // 只有在输入框的值真正改变后才触发 AJAX 请求
            var value = $(this).val();
            var name = $(this).attr('name');
            var id = $(this).parents('ul[data-id]').attr('data-id');
            var loading = layer.load(1, {shade: [0.1, '#fff']});
            $.ajax({
                url: "{:myurl('big_modify')}",    //请求的url地址
                dataType: "json",   //返回格式为json
                async: true,//请求是否异步，默认为异步，这也是ajax重要特性
                data: {'id': id, 'field': name, 'value': value},
                type: "POST",   //请求方式
                success: function (req) {

                    //请求成功时处理
                    if (req.code == 1) {
                        layer.msg(req.msg, {time: 2000, icon: 1});

                    } else {
                        layer.msg(req.msg, {time: 2000, icon: 2});
                    }
                }, complete: function () {
                    layer.close(loading);
                }
            });
        }
        changed = false; // 重置标志位

    });
    function updateCurrentTime() {
        // 获取当前时间
        var now = new Date();

        // 格式化当前时间
        var formattedTime = "<span>"+now.getFullYear() + "年" + (now.getMonth() + 1) + "月" + now.getDate() + "日 " +"</span><span>"+ padZero(now.getHours()) + ":" + padZero(now.getMinutes()) + ":" + padZero(now.getSeconds())+"</span>";

        $('#todayTime').html(formattedTime);
    }

    // 每秒更新一次时间
    setInterval(updateCurrentTime, 1000);

    // 初始化页面显示
    updateCurrentTime();

    // 在数字前补零（例如：9 -> '09'）
    function padZero(num) {
        return num < 10 ? "0" + num : num;
    }
    var lazyFun;
    function init(el, width, height) {
        var _el = document.getElementById(el);
        var hScale = window.innerHeight / height;
        var wScale = window.innerWidth / width;
        _el.style.transform = 'scale(' + wScale + ',' + hScale + ')'
    }
    // init('mainbody', 1920, 1080);
    window.onresize = function() {
        clearTimeout(lazyFun);
        lazyFun = setTimeout(function() {
            // init('mainbody', 1920, 1080)
        }, 600);
    };
</script>

</body>
</html>
