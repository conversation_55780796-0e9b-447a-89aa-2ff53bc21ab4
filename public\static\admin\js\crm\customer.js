define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'crm.customer/index',
        add_url: 'crm.customer/add',
        edit_url: 'crm.customer/edit',
        delete_url: 'crm.customer/delete',
        import_url: 'crm.customer/import',
        export_url: 'crm.customer/export',
        modify_url: 'crm.customer/modify',
    };

    cols_fields=[];
    var Controller = {
        index: function () {
            ea.init.where['scope']=1;
            console.log(CONFIG.cols_fields)
            if(CONFIG.cols_fields){
                cols_fields=[].concat({'type': 'checkbox'},CONFIG.cols_fields,{'width': 310, 'title': fy('Operate'),'fixed':'right', 'templet': ea.table.tool,operat: [
                       [{
                            text: fy('Follow up'),
                            url: 'crm.record/dialogue',
                            method: 'none',
                            auth: 'dialogue',
                            class: 'layui-btn layui-btn-xs layui-btn-primary',
                            icon: 'fa fa-commenting-o',
                            extend: 'ymwl-event="dialogue" ',
                        }],
                        [{
                            text: '拨号',
                            url: 'crm.customer/dial?id={id}&phone={phone}&name={name}',
                            method: 'none',
                            auth: 'dial',
                            class: 'layui-btn layui-btn-xs layui-btn-normal',
                            icon: 'fa fa-phone',
                            extend: 'lay-event="dial" ymwl-event="dial"',
                        }],
                        [{
                            class: 'layui-btn layui-btn-xs layui-btn-success',
                            method: 'open',
                            text: fy('Submit the order'),
                            auth: 'addOrder',
                            url: 'Order/add?customer_id={id}',
                            field:'',
                            extend: 'data-full="true"',
                            hidden:function (data) {
                                //我的1下才展示提交订单
                                if (ea.init.where['scope']==1) return false;
                                return  true;
                            }
                        }], [{
                            class: 'layui-btn layui-btn-xs layui-btn-warm',
                            method: 'open',
                            text: fy("Sharing"),
                            auth: 'share',
                            url: 'crm.customer/share',
                            field:'',
                            extend: '',
                            hidden:function (data) {
                                //我的1下才展示分享
                                if (ea.init.where['scope']==1) return false;
                                return  true;
                            }
                        }], [{
                            class: 'layui-btn layui-btn-xs layui-btn-warm',
                            method: 'request',
                            text: fy("Cancel sharing"),
                            title:fy("Are you sure you want to unshare"),
                            auth: 'del_share',
                            url: 'crm.customer/del_share',
                            field:'',
                            extend: '',
                            hidden:function (data) {
                                //已经分享给他人20的展示取消
                                if (ea.init.where['scope']==20) return false;
                                return  true;
                            }
                        }],[{
                            class: 'layui-btn layui-btn-success layui-btn-xs',
                            method: 'open',
                            text: fy('Edit'),
                            auth: 'edit',
                            url: 'crm.customer/edit',
                            field:'',
                            extend: '',
                            hidden:function (data) {
                                //分享给我的21也没有编辑权限
                                if (ea.init.where['scope']==21) return true;
                                return  false;
                            }
                        }],
                        [{
                            class: 'layui-btn layui-btn-danger layui-btn-xs',
                            method: 'request',
                            text: fy('Delete'),
                            auth: 'delete',
                            url: 'crm.customer/delete',
                            field:'',
                            extend: '',
                            hidden:function (data) {
                                //分享给我的21没有删除权限
                                if (ea.init.where['scope']==21) return true;
                                return  false;
                            }
                        }]]})
            }
            var page=1,limit=CONFIG.ADMINPAGESIZE;
            var tableIn=ea.table.render({
                autoSort: false,
                toolbar: ['refresh','add','delete','export',[{
                    text: fy('Import'),
                    url: init.import_url,
                    method: 'open',
                    auth: 'import',
                    class: 'layui-btn layui-btn-success layui-btn-sm',
                    icon: 'fa fa-upload ',
                    extend: '',
                }],[{
                    text: fy('Move into the client pool'),url:'crm.customer/to_move_gh',
                    method: 'url',
                    auth: 'to_move_gh',
                    class: 'layui-btn layui-btn-danger layui-btn-sm',title:fy('Are you sure you want to move the checked customers into the client pool')+'？',
                    icon: 'fa fa-industry',extend:'data-checkbox="true"'
                }],[{
                    text: fy('Transfer customers'),url:'crm.customer/alter_pr_user',
                    method: 'open',
                    auth: 'alter_pr_user',
                    class: 'layui-btn layui-btn-primary layui-btn-sm',title:fy('Are you sure you want to transfer the checked customers')+'？',
                    icon: 'fa fa-clock-o',extend:'data-checkbox="true" data-height="350px"'
                }],[{
                    text: fy('Custom fields'),
                    url: 'system.fields/index?table=crm_customer',
                    method: 'open',
                    auth: 'fields',
                    class: 'layui-btn layui-btn-warm layui-btn-sm',
                    icon: 'fa fa-cogs ',
                    extend: 'data-full="true"',
                }],[{
                    class: 'layui-btn layui-btn-normal layui-btn-sm',
                    method: 'open',
                    text: fy('客户查重'),
                    auth: 'reduplicate',
                    url: 'crm.customer/reduplicate',
                    extend: '', icon: 'fa fa-search',
                },{
                    class: 'layui-btn layui-btn-danger layui-btn-sm',
                    method: 'none',
                    text: '挂断',
                    auth: 'hangup',
                    url: 'crm.customer/hangup',
                    extend: 'ymwl-event="hangup"', icon: 'fa fa-phone-slash',
                }]],
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols:[ cols_fields],
                done: function(res, curr, count){
                    page=curr;limit=this.limit;
                    console.log('表格数据加载完成:', res);
                    if(res && res.data && res.data.length > 0) {
                        console.log('第一行数据:', res.data[0]);
                    }
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                },where: {scope: 1}
            });

           layui.table.on('sort('+init.table_render_id+'_LayFilter)', function(obj){
               ea.init.where['sort_by']=obj.field;
               ea.init.where['sort_order']=obj.type;
               layui.table.reload(init.table_render_id, {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                    ,where: ea.init.where
                });
            });
          layui.table.on('page(test)', function(obj){
                console.log(obj.curr); // 当前页码
            });
            $('body').on('click', '[ymwl-event="dialogue"]', function () {
                var url = $(this).attr('data-url');

                var where= ea.init.where;
                where['page']=page;
                where['limit']=limit;
                ea.open(
                    '写跟进',
                    ea.url(url+'&'+ea.parseParams(where)),
                    '100%',
                    '100%'
                );
            });
            
            // 挂断按钮事件
            $('body').on('click', '[ymwl-event="hangup"]', function () {
                var loading = layer.load(1, {shade: [0.1, '#fff']});

                $.post(ea.url('websocket_api/hangup'), {}, function(res) {
                    layer.close(loading);
                    if (res.code === 1) {
                        ea.msg.success(res.msg || '挂断指令发送成功');
                    } else {
                        ea.msg.error(res.msg || '挂断指令发送失败');
                    }
                }).fail(function() {
                    layer.close(loading);
                    ea.msg.error('网络请求失败');
                });
            });

            // 监听表格工具栏事件（包括拨号按钮）
            layui.table.on('tool(' + init.table_render_id + '_LayFilter)', function(obj) {
                var data = obj.data;
                var layEvent = obj.event;

                console.log('表格工具栏事件:', {event: layEvent, data: data});

                if (layEvent === 'dial') {
                    var customerId = data.id;
                    var phone = data.phone;
                    var customerName = data.name;

                    console.log('表格行数据:', data);
                    console.log('拨号参数:', {customerId: customerId, phone: phone, customerName: customerName});

                    if (!customerId) {
                        ea.msg.error('无法获取客户ID');
                        return;
                    }

                    if (!phone) {
                        ea.msg.error('无法获取客户电话号码');
                        return;
                    }

                    var loading = layer.load(1, {shade: [0.1, '#fff']});

                    $.post(ea.url('websocket_api/dial'), {
                        customer_id: customerId,
                        phone: phone
                    }, function(res) {
                        layer.close(loading);
                        if (res.code === 1) {
                            ea.msg.success(res.msg || '拨号指令发送成功');
                        } else {
                            ea.msg.error(res.msg || '拨号指令发送失败');
                        }
                    }).fail(function() {
                        layer.close(loading);
                        ea.msg.error('网络请求失败');
                    });
                }
            });

            // 拨号按钮事件
            $('body').on('click', '[ymwl-event="dial"]', function () {
                var $this = $(this);
                var url = $this.attr('data-url') || '';

                console.log('拨号按钮URL:', url);
                console.log('拨号按钮所有属性:', $this[0].attributes);

                // 尝试从表格缓存中获取数据
                var $tr = $this.closest('tr');
                var index = $tr.data('index');
                var tableCache = layui.table.cache[init.table_render_id];

                console.log('表格行索引:', index);
                console.log('表格缓存:', tableCache);

                var customerId = '';
                var phone = '';
                var customerName = '';

                // 优先从表格缓存获取数据
                if (tableCache && index !== undefined && tableCache[index]) {
                    var rowData = tableCache[index];
                    customerId = rowData.id;
                    phone = rowData.phone;
                    customerName = rowData.name;
                    console.log('从表格缓存获取的数据:', rowData);
                } else {
                    // 从URL中解析参数
                    if (url) {
                        var urlParts = url.split('?');
                        if (urlParts.length > 1) {
                            var params = urlParts[1].split('&');
                            params.forEach(function(param) {
                                var keyValue = param.split('=');
                                if (keyValue.length === 2) {
                                    var key = keyValue[0];
                                    var value = decodeURIComponent(keyValue[1]);

                                    if (key === 'id') {
                                        customerId = value;
                                    } else if (key === 'phone') {
                                        phone = value;
                                    } else if (key === 'name') {
                                        customerName = value;
                                    }
                                }
                            });
                        }
                    }
                }

                console.log('最终拨号参数:', {
                    customerId: customerId,
                    phone: phone,
                    customerName: customerName
                });

                if (!customerId || customerId === '{id}') {
                    ea.msg.error('无法获取客户ID，URL: ' + url);
                    return;
                }

                if (!phone || phone === '{phone}') {
                    ea.msg.error('无法获取客户电话号码，URL: ' + url);
                    return;
                }

                var loading = layer.load(1, {shade: [0.1, '#fff']});

                $.post(ea.url('websocket_api/dial'), {
                    customer_id: customerId,
                    phone: phone
                }, function(res) {
                    layer.close(loading);
                    if (res.code === 1) {
                        ea.msg.success(res.msg || '拨号指令发送成功');
                    } else {
                        ea.msg.error(res.msg || '拨号指令发送失败');
                    }
                }).fail(function() {
                    layer.close(loading);
                    ea.msg.error('网络请求失败');
                });
            });

            // 检查设备连接状态
            Controller.checkDeviceStatus();

            // 定时检查设备状态（每30秒）
            setInterval(function() {
                Controller.checkDeviceStatus();
            }, 30000);

            ea.listen();

        },

        // 检查设备连接状态
        checkDeviceStatus: function() {
            $.get(ea.url('websocket_api/getDeviceStatus'), function(res) {
                if (res.code === 1 && res.data) {
                    var status = res.data.online ? '在线' : '离线';
                    var statusClass = res.data.online ? 'layui-badge-rim' : 'layui-badge';
                    var deviceInfo = res.data.online ?
                        '设备ID: ' + res.data.device_id + ' | 最后活跃: ' + res.data.last_active :
                        '设备未连接，请先在手机端登录';

                    // 更新页面上的设备状态显示
                    if ($('#device-status').length === 0) {
                        $('.layuimini-main').prepend(
                            '<div id="device-status" class="layui-elem-quote layui-quote-nm" style="margin-bottom: 10px;">' +
                            '<span class="layui-badge ' + statusClass + '">设备状态: ' + status + '</span> ' +
                            '<span style="margin-left: 10px; color: #666;">' + deviceInfo + '</span>' +
                            '</div>'
                        );
                    } else {
                        $('#device-status').html(
                            '<span class="layui-badge ' + statusClass + '">设备状态: ' + status + '</span> ' +
                            '<span style="margin-left: 10px; color: #666;">' + deviceInfo + '</span>'
                        );
                    }
                }
            });
        },

        reduplicate: function () {
            ea.table.render({url:'crm.customer/reduplicate',
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols: [[
                    {field: 'id', title: 'id'},
                    {field: 'name', title: fy('客户名称'),"width":"150","search":true},
                    {field: 'phone', title: fy('联系电话'),"width":"120","search":true},
                    {field: 'contact', title: fy('客户联系人'),"width":"100","search":true},
                    {field: 'at_user', title: fy('创建人'),"width":"100"},
                    {field: 'pr_user', title: fy('负责人'),"width":"100"},
                    {field:"last_up_time","title":fy('跟进时间'),"search":false,"width":160,"templet":ea.table.dateTime},
                    {field:"issuccess","title":fy("是否成交"),"search":false,"width":"100","selectList":["未成交","已成交"],"templet":ea.table.select},
                    {field: 'create_time', title: fy('Creation time'),"width":"180"},
                    {field: 'update_time', title: fy('Update time'),"width":"180"},
                    {width: 250, title: fy('Operate'), templet: ea.table.tool},
                ]],done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                }
            });
            $('.table-search-fieldset').removeClass('layui-hide');
            ea.listen();

        },
        seas: function () {
            if(CONFIG.cols_fields){
                cols_fields=[].concat({'type': 'checkbox'},CONFIG.cols_fields,{'width': 100, 'title': fy('Operate'),toolbar: '#action',fixed: 'right'})
            }
            ea.table.render({
                autoSort: false,
                url:'crm.customer/seas',
                toolbar: ['refresh',[{
                    text: fy('Receive'),url:'crm.seas/robclient',
                    method: 'url',
                    auth: 'robclient',
                    class: 'layui-btn layui-btn-success layui-btn-sm',title:fy('Are you sure you want to pick up the currently checked customer')+'？',
                    icon: 'fa fa-user-o',extend:'data-checkbox="true"'
                }],'delete'],
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols:[ cols_fields],
                done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                }
            });
            layui.table.on('sort('+init.table_render_id+'_LayFilter)', function(obj){
                ea.init.where['sort_by']=obj.field;
                ea.init.where['sort_order']=obj.type;
                layui.table.reload(init.table_render_id, {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                    ,where: ea.init.where
                });
            });

            ea.listen();

        },
        issuccess: function () {
            if(CONFIG.cols_fields){
                cols_fields=[].concat(CONFIG.cols_fields)
            }
            ea.table.render({
                autoSort: false,
                url:'crm.customer/issuccess',
                toolbar: ['refresh',[{
                    class: 'layui-btn layui-btn-normal layui-btn-sm',
                    method: 'none',
                    text: '数据大屏',
                    auth: 'success_bgshow',
                    url: 'crm.customer/success_bgshow',
                    extend: 'ymwl-event="success_bgshow"', icon: '',
                }]],
                init: init,limit:CONFIG.ADMINPAGESIZE
                ,
                cols:[ cols_fields],
                done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                }
            });
            $('body').on('click', '[ymwl-event="success_bgshow"]', function () {
                var url = $(this).attr('data-url');
                var where= ea.init.where;
                window.open(ea.url(url+'?'+ea.parseParams(where)));
            });
            layui.table.on('sort('+init.table_render_id+'_LayFilter)', function(obj){
                ea.init.where['sort_by']=obj.field;
                ea.init.where['sort_order']=obj.type;
                layui.table.reload(init.table_render_id, {
                    initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                    ,where: ea.init.where
                });
            });
            ea.listen();

            // 本地许可证验证（已移除远程验证）
            try {
                // 本地验证逻辑 - 检查基本配置
                if (window.CONFIG) {
                    console.log('System initialized successfully');
                    // 可以在这里添加本地验证逻辑
                    // 例如：检查配置完整性、版本信息等
                }
            } catch (e) {
                console.warn('Local verification failed:', e);
            }

        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        }, test: function () {
            ea.listen();
        },
        import: function () {
            ea.listen();
        },
        alter_pr_user: function () {
            ea.listen();
        }, share: function () {
            ea.listen();
        },
    };
    return Controller;
});
