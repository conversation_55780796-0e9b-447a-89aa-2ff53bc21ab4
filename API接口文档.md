# ThinkPHP 6.1 CRM系统移动端API接口文档

## 1. 接口概述

### 1.1 基本信息
- **接口域名**: `http://your-domain.com`
- **接口版本**: v1.0
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: GET/POST
- **认证方式**: JWT Token

### 1.2 通用响应格式
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {},
    "time": 1640995200
}
```

### 1.3 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或Token失效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 405 | 请求方法不允许 |
| 500 | 服务器内部错误 |

## 2. 认证接口

### 2.1 用户登录
**接口地址**: `POST /api/auth/login`

**请求参数**:
```json
{
    "username": "admin",
    "password": "123456",
    "device_id": "device_123456",
    "device_type": "mobile"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名或手机号 |
| password | string | 是 | 密码 |
| device_id | string | 否 | 设备ID |
| device_type | string | 否 | 设备类型(mobile/tablet) |

**响应示例**:
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expire_time": 1641081600,
        "user_info": {
            "admin_id": 1,
            "username": "admin",
            "realname": "管理员",
            "group_id": 1,
            "avatar": "",
            "isphone": 0,
            "role_id": 1
        },
        "group_info": {
            "id": 1,
            "title": "超级管理员",
            "max_customers_num": 0
        },
        "device_id": "device_123456"
    },
    "time": 1640995200
}
```

### 2.2 刷新Token
**接口地址**: `POST /api/auth/refresh`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
    "device_id": "device_123456"
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "Token刷新成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expire_time": 1641081600
    },
    "time": 1640995200
}
```

### 2.3 用户登出
**接口地址**: `POST /api/auth/logout`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "登出成功",
    "data": {},
    "time": 1640995200
}
```

### 2.4 获取用户信息
**接口地址**: `GET /api/auth/userinfo`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "user_info": {
            "admin_id": 1,
            "username": "admin",
            "realname": "管理员",
            "group_id": 1,
            "avatar": "",
            "isphone": 0,
            "role_id": 1
        },
        "group_info": {
            "id": 1,
            "title": "超级管理员",
            "max_customers_num": 0
        }
    },
    "time": 1640995200
}
```

### 2.5 修改密码
**接口地址**: `POST /api/auth/changePassword`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
    "old_password": "123456",
    "new_password": "654321"
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "密码修改成功，请重新登录",
    "data": {},
    "time": 1640995200
}
```

## 3. 客户管理接口

### 3.1 获取客户列表
**接口地址**: `GET /api/customer/list`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认15 |
| follow_status | string | 否 | 跟进状态：never(从未跟进)、today(今日已跟进)、pending(待跟进)、range(指定范围) |
| start_date | string | 否 | 开始日期(YYYY-MM-DD) |
| end_date | string | 否 | 结束日期(YYYY-MM-DD) |
| keyword | string | 否 | 搜索关键词 |
| status | int | 否 | 客户状态：0线索,1客户,2公海 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "name": "张三",
                "phone": "13800138000",
                "company": "测试公司",
                "status": 1,
                "status_text": "客户",
                "pr_user": "admin",
                "at_time": 1640995200,
                "at_time_text": "2022-01-01 12:00",
                "to_kh_time": 1640995200,
                "to_kh_time_text": "2022-01-01 12:00",
                "kh_rank": 1,
                "source_id": 1,
                "hangye_id": 1,
                "address": "北京市朝阳区",
                "remark": "重要客户",
                "last_record_time": 1640995200,
                "last_record_time_text": "2022-01-01 12:00",
                "next_record_time": 1641081600,
                "next_record_time_text": "2022-01-02 12:00",
                "need_follow": false
            }
        ],
        "total": 100,
        "page": 1,
        "limit": 15,
        "pages": 7
    },
    "time": 1640995200
}
```

### 3.2 获取客户详情
**接口地址**: `GET /api/customer/detail/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "customer": {
            "id": 1,
            "name": "张三",
            "phone": "13800138000",
            "company": "测试公司",
            "status": 1,
            "pr_user": "admin",
            "address": "北京市朝阳区",
            "remark": "重要客户"
        },
        "records": [
            {
                "id": 1,
                "content": "电话沟通，客户有意向",
                "create_time": 1640995200,
                "next_time": 1641081600
            }
        ],
        "contracts": [
            {
                "id": 1,
                "contract_no": "HT202201001",
                "amount": 10000,
                "status": 1,
                "sign_date": "2022-01-01"
            }
        ]
    },
    "time": 1640995200
}
```

### 3.3 获取客户统计
**接口地址**: `GET /api/customer/statistics`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "total": 100,
        "leads": 30,
        "customers": 60,
        "seas": 10,
        "today_new": 5,
        "pending_follow": 15,
        "today_followed": 8
    },
    "time": 1640995200
}
```

## 4. 通话统计接口

### 4.1 上传通话统计数据
**接口地址**: `POST /api/call/statistics`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
    "stat_date": "2022-01-01",
    "call_count": 25,
    "avg_duration": 180,
    "total_duration": 4500,
    "connect_rate": 85.5
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stat_date | string | 是 | 统计日期(YYYY-MM-DD) |
| call_count | int | 是 | 通话量 |
| avg_duration | int | 是 | 平均时长(秒) |
| total_duration | int | 是 | 总通话时长(秒) |
| connect_rate | float | 是 | 接通率(0-100) |

**响应示例**:
```json
{
    "code": 200,
    "msg": "统计数据上传成功",
    "data": {},
    "time": 1640995200
}
```

### 4.2 获取通话统计数据
**接口地址**: `GET /api/call/statistics`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_date | string | 否 | 开始日期(YYYY-MM-DD) |
| end_date | string | 否 | 结束日期(YYYY-MM-DD) |
| days | int | 否 | 查询最近N天，默认7天 |

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "admin_id": 1,
                "username": "admin",
                "stat_date": "2022-01-01",
                "call_count": 25,
                "avg_duration": 180,
                "total_duration": 4500,
                "connect_rate": 85.5,
                "create_time": "2022-01-01 12:00:00",
                "update_time": "2022-01-01 12:00:00"
            }
        ],
        "summary": {
            "total_days": 7,
            "total_calls": 175,
            "total_duration": 31500,
            "avg_calls_per_day": 25,
            "avg_duration_per_call": 180,
            "avg_connect_rate": 85.5
        },
        "query_params": {
            "start_date": "2021-12-25",
            "end_date": "2022-01-01",
            "days": 7
        }
    },
    "time": 1640995200
}
```

### 4.3 获取统计汇总
**接口地址**: `GET /api/call/statistics/summary`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| period | string | 否 | 统计周期：week(周)、month(月)、year(年)，默认week |

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "total_days": 7,
        "total_calls": 175,
        "total_duration": 31500,
        "avg_calls_per_day": 25,
        "avg_duration_per_call": 180,
        "avg_connect_rate": 85.5,
        "period": "week",
        "date_range": {
            "start_date": "2021-12-25",
            "end_date": "2022-01-01"
        }
    },
    "time": 1640995200
}
```

### 4.4 删除统计数据
**接口地址**: `DELETE /api/call/statistics/{date}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| date | string | 是 | 统计日期(YYYY-MM-DD) |

**响应示例**:
```json
{
    "code": 200,
    "msg": "删除成功",
    "data": {},
    "time": 1640995200
}
```

### 4.5 批量上传统计数据
**接口地址**: `POST /api/call/statistics/batch`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
    "data": [
        {
            "stat_date": "2022-01-01",
            "call_count": 25,
            "avg_duration": 180,
            "total_duration": 4500,
            "connect_rate": 85.5
        },
        {
            "stat_date": "2022-01-02",
            "call_count": 30,
            "avg_duration": 165,
            "total_duration": 4950,
            "connect_rate": 88.2
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "批量上传完成",
    "data": {
        "total": 2,
        "success_count": 2,
        "failed_count": 0,
        "failed_list": []
    },
    "time": 1640995200
}
```

## 5. 错误码说明

### 5.1 通用错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权或Token失效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 405 | 请求方法不允许 |
| 500 | 服务器内部错误 |

### 5.2 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 用户名或密码错误 |
| 1002 | 用户已被禁用 |
| 1003 | Token已失效，请重新登录 |
| 1004 | 原密码错误 |
| 1005 | 新密码长度不能少于6位 |
| 2001 | 客户不存在或无权限访问 |
| 2002 | 客户数据不存在 |
| 3001 | 统计日期格式错误 |
| 3002 | 不能上传未来日期的统计数据 |
| 3003 | 通话量不能为负数 |
| 3004 | 接通率应在0-100之间 |

## 6. 数据库表结构

### 6.1 通话统计表 (ymwl_crm_call_statistics)
```sql
CREATE TABLE `ymwl_crm_call_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `call_count` int(11) NOT NULL DEFAULT '0' COMMENT '通话量',
  `avg_duration` int(11) NOT NULL DEFAULT '0' COMMENT '平均时长(秒)',
  `total_duration` int(11) NOT NULL DEFAULT '0' COMMENT '总通话时长(秒)',
  `connect_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '接通率(%)',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_admin_date` (`admin_id`,`stat_date`),
  KEY `idx_username` (`username`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CRM通话统计表';
```
