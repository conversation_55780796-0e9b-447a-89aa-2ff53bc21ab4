# ThinkPHP 6.1 CRM系统移动端API部署说明

## 1. 系统要求

### 1.1 服务器环境
- **PHP版本**: >= 7.2.0
- **MySQL版本**: >= 5.6
- **Web服务器**: Apache/Nginx
- **PHP扩展**: 
  - PDO
  - PDO_MySQL
  - OpenSSL
  - JSON
  - Curl
  - Mbstring

### 1.2 依赖库
- ThinkPHP 6.1
- Firebase JWT (已包含在vendor中)

## 2. 安装步骤

### 2.1 文件部署
1. 将所有API相关文件上传到服务器
2. 确保以下目录结构存在：
```
app/
├── api/
│   ├── controller/
│   │   ├── Common.php
│   │   ├── Auth.php
│   │   ├── Customer.php
│   │   └── CallStatistics.php
│   ├── model/
│   │   ├── CallStatistics.php
│   │   └── CrmCustomer.php
│   └── traits/
│       └── Curd.php
├── admin/
│   ├── controller/
│   │   └── analysis/
│   │       └── CallStatistics.php
│   └── model/
│       └── CrmCallStatistics.php
route/
└── api.php
view/
└── admin/
    └── analysis/
        └── call_statistics/
            └── index.html
```

### 2.2 数据库配置
1. 执行SQL脚本创建通话统计表：
```sql
-- 执行 database_create_call_statistics.sql
```

2. 添加菜单权限：
```sql
-- 执行 database_insert_menu.sql
```

### 2.3 路由配置
确保 `route/app.php` 文件包含以下内容：
```php
// 加载API路由
require_once __DIR__ . '/api.php';
```

### 2.4 权限配置
1. 设置目录权限：
```bash
chmod -R 755 runtime/
chmod -R 755 public/upload/
```

2. 确保Web服务器对以下目录有写权限：
- runtime/
- public/upload/

## 3. 配置说明

### 3.1 JWT配置
在 `app/api/controller/Common.php` 中可以修改以下配置：
```php
// JWT密钥（建议修改为更复杂的密钥）
protected $jwtKey = 'crm_api_jwt_secret_key_2024';

// Token过期时间（秒）
protected $tokenExpire = 86400 * 7; // 7天
```

### 3.2 CORS配置
API已配置跨域访问，如需修改可在 `route/api.php` 中调整：
```php
->allowCrossDomain([
    'Access-Control-Allow-Origin'        => '*',
    'Access-Control-Allow-Methods'       => 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers'       => 'Content-Type,Authorization,X-Requested-With',
    'Access-Control-Allow-Credentials'   => 'true'
]);
```

## 4. 功能验证

### 4.1 API接口测试
1. 测试登录接口：
```bash
curl -X POST http://your-domain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

2. 测试客户列表接口：
```bash
curl -X GET http://your-domain.com/api/customer/list \
  -H "Authorization: Bearer YOUR_TOKEN"
```

3. 测试通话统计上传：
```bash
curl -X POST http://your-domain.com/api/call/statistics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"stat_date":"2024-01-01","call_count":25,"avg_duration":180,"total_duration":4500,"connect_rate":85.5}'
```

### 4.2 后台管理验证
1. 登录后台管理系统
2. 访问 "数据分析" -> "通话统计" 菜单
3. 查看统计数据和图表展示

## 5. 安全配置

### 5.1 生产环境配置
1. 关闭调试模式：
```php
// .env文件
APP_DEBUG = false
```

2. 修改JWT密钥：
```php
// 使用更复杂的密钥
protected $jwtKey = 'your_complex_secret_key_here';
```

3. 配置HTTPS：
- 确保生产环境使用HTTPS协议
- 更新API基础URL为HTTPS

### 5.2 访问控制
1. 限制API访问频率（可选）
2. 配置防火墙规则
3. 定期更新系统和依赖库

## 6. 监控和维护

### 6.1 日志监控
- 监控API访问日志
- 关注错误日志
- 定期清理过期日志

### 6.2 数据备份
- 定期备份数据库
- 备份重要配置文件
- 制定数据恢复计划

### 6.3 性能优化
- 启用OPcache
- 配置Redis缓存（可选）
- 优化数据库查询

## 7. 常见问题

### 7.1 API无法访问
**问题**: 404错误，API接口无法访问
**解决方案**:
1. 检查路由配置是否正确
2. 确认Web服务器重写规则
3. 验证文件权限设置

### 7.2 Token验证失败
**问题**: 401错误，Token验证失败
**解决方案**:
1. 检查JWT密钥配置
2. 确认Token格式正确
3. 验证Token是否过期

### 7.3 数据库连接失败
**问题**: 500错误，数据库连接失败
**解决方案**:
1. 检查数据库配置
2. 确认数据库服务状态
3. 验证用户权限

### 7.4 跨域问题
**问题**: CORS错误，跨域请求被阻止
**解决方案**:
1. 检查CORS配置
2. 确认请求头设置
3. 验证预检请求处理

## 8. 技术支持

### 8.1 联系方式
- **开发团队**: ＥＴ资源网络
- **技术支持**: QQ 2062665023
- **文档版本**: v1.0
- **更新日期**: 2024-01-01

### 8.2 更新说明
- 定期检查系统更新
- 关注安全补丁发布
- 及时升级依赖库版本

## 9. 附录

### 9.1 完整的API接口列表
| 接口 | 方法 | 说明 |
|------|------|------|
| /api/auth/login | POST | 用户登录 |
| /api/auth/refresh | POST | 刷新Token |
| /api/auth/logout | POST | 用户登出 |
| /api/auth/userinfo | GET | 获取用户信息 |
| /api/auth/changePassword | POST | 修改密码 |
| /api/customer/list | GET | 获取客户列表 |
| /api/customer/detail/{id} | GET | 获取客户详情 |
| /api/customer/statistics | GET | 获取客户统计 |
| /api/call/statistics | POST | 上传通话统计 |
| /api/call/statistics | GET | 获取通话统计 |
| /api/call/statistics/summary | GET | 获取统计汇总 |
| /api/call/statistics/{date} | DELETE | 删除统计数据 |
| /api/call/statistics/batch | POST | 批量上传统计 |

### 9.2 数据库表说明
- `ymwl_crm_call_statistics`: 通话统计数据表
- `ymwl_admin`: 管理员表
- `ymwl_crm_customer`: 客户表
- `ymwl_auth_rule`: 权限规则表

### 9.3 配置文件说明
- `config/database.php`: 数据库配置
- `config/app.php`: 应用配置
- `route/api.php`: API路由配置
- `.env`: 环境变量配置

---

**注意**: 本文档基于ThinkPHP 6.1 CRM系统开发，请根据实际环境调整配置参数。
