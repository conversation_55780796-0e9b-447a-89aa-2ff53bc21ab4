<?php
namespace app\admin\model\publicuse;
use app\admin\model\SystemNode;
use app\admin\model\traits\ModelHandle;
use app\admin\model\traits\TablHandle;
use app\admin\service\TriggerService;
use EasyAdmin\auth\Node as NodeService;
use maowenke\mysql\Backup;
use maowenke\mysql\Restore;
use think\facade\Cache;
use think\facade\Db;

class AssetsHand
{
    protected $message = '';
    public function getMessage(){
        return $this->message;
    }

    /**安装插件
     * @param string $assets
     * @param $shuju
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function installAsset($assets,$shuju,$cover=true){
        //安装数据库
        $bool = $this->installSql($assets,$cover);
        if(!$bool){
            return false;
        }
        if(isset($shuju['menu'])&&!empty($shuju['menu'])){
            $bool = $this->installMenu($shuju['menu']);
            if(!$bool){
                $this->message = '安装菜单失败';
                return false;
            }
        }
        //安装文件
        $bool = $this->installFile($assets);
        if(!$bool){
            $this->message = '安装文件失败';
            return false;
        }
        //更新表
        if(isset($shuju['table'])&&$shuju['table']&&is_array($shuju['table'])){
            foreach ($shuju['table'] as $value){
                $bool = TablHandle::updatetable($value);
                if(!$bool){
                    $this->message = '更新表失败';
                    return true;
                }
            }
        }
//        //安装配置文件
//        $bool = $this->installConfig($assets);
//        if(!$bool){
//            $this->message = '安装配置文件失败';
//            return false;
//        }
        $bool = $this->installCache();
        if(!$bool){
            $this->message = '清理缓存失败';
            return false;
        }
        return true;
    }
    public static function installConfig($assets){
        $config = base_path().'addons'.DIRECTORY_SEPARATOR.$assets.'config';
        if(is_dir($config)){
            $a_config = config_path().'addons'.DIRECTORY_SEPARATOR.$assets;
            if(!is_dir($a_config)){
                chmod(base_path().'addons',0777);
                mkdir($a_config,0777,true);
            }
            $bool = self::moveFile($config,$a_config);
            if(!$bool){
                return [false,'移动配置文件失败'];
            }
        }
        return [true,'移动成功'];
    }
    protected function installCache(){
        $force = 1;
        $nodeList = (new NodeService())->getNodelist();
        empty($nodeList) && $this->error('暂无需要更新的系统节点');
        $model = new SystemNode();
        try {
            if ($force == 1) {
                $updateNodeList = $model->whereIn('node', array_column($nodeList, 'node'))->select();
                $formatNodeList = array_format_key($nodeList, 'node');
                foreach ($updateNodeList as $vo) {
                    isset($formatNodeList[$vo['node']]) && $model->where('id', $vo['id'])->update([
                        'title'   => $formatNodeList[$vo['node']]['title'],
                        'is_auth' => $formatNodeList[$vo['node']]['is_auth'],
                    ]);
                }
            }
            $existNodeList = $model->field('node,title,type,is_auth')->select();
            foreach ($nodeList as $key => $vo) {
                foreach ($existNodeList as $v) {
                    if ($vo['node'] == $v->node) {
                        unset($nodeList[$key]);
                        break;
                    }
                }
            }
            $model->saveAll($nodeList);
            TriggerService::updateNode();
        } catch (\Exception $e) {
            $this->message = '更新节点失败';
            return false;
        }
        Cache::clear();
        return true;
    }

    /**安装文件
     * @param $assets
     * @return bool
     */
    protected function installFile($assets){
        //安装控制器
        $controller = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'controller';
        if(is_dir($controller)){
            $a_controller = base_path().'admin'.DIRECTORY_SEPARATOR.'controller'.DIRECTORY_SEPARATOR.$assets;
            if(!is_dir($a_controller)){
                chmod(base_path().'admin',0777);
                mkdir($a_controller);
            }
            $bool = $this->moveFile($controller,$a_controller);
            if(!$bool){
                $this->message = '安装控制器失败';
                return false;
            }
        }
        //安装模型
        $model = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'model';
        if(is_dir($model)){
            $a_model = base_path().'admin'.DIRECTORY_SEPARATOR.'model';
            $bool = $this->moveFile($model,$a_model);
            if(!$bool){
                $this->message = '安装模型失败';
                return false;
            }
        }
        //安装视图
        $view = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'view';
        if(is_dir($view)){
            $a_view = base_path().'admin'.DIRECTORY_SEPARATOR.'view'.DIRECTORY_SEPARATOR.$assets;
            if(!is_dir($a_controller)){
                chmod(base_path().'admin',0777);
                mkdir($a_view);
            }
            $bool = $this->moveFile($view,$a_view);
            if(!$bool){
                $this->message = '安装模型失败';
                return false;
            }
        }
        //安装js
        $js = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'js';
        if(is_dir($js)){
            $a_js = public_path().'static'.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'js'.DIRECTORY_SEPARATOR.$assets;
            if(!is_dir($a_controller)){
                chmod(public_path().'static'.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'js',0777);
                mkdir($a_js);
            }
            $bool = $this->moveFile($js,$a_js);
            if(!$bool){
                $this->message = '安装模型失败';
                return false;
            }
        }
        //安装前端
        $index = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'index';
        if(is_dir($index)){
            $a_index = base_path().$assets;
            if(!is_dir($a_index)){
                chmod(base_path(),0777);
                mkdir($a_index);
            }
            $bool = $this->moveFile($index,$a_index);
            if(!$bool){
                $this->message = '安装模型失败';
                return false;
            }
        }
        //安装静态文件
        $addons = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'static';
        if(is_dir($addons)){
            $a_addons = public_path().'static'.DIRECTORY_SEPARATOR.'addons'.DIRECTORY_SEPARATOR.$assets;
            if(!is_dir($a_addons)){
                chmod(public_path().'static',0777);
                mkdir($a_addons);
            }
            $bool = $this->moveFile($addons,$a_addons);
            if(!$bool){
                $this->message = '安装静态文件失败';
                return false;
            }
        }
        return true;
    }

    /**安装菜单
     * @param array $menu
     * @param int $pid
     * @return bool
     */
    protected function installMenu($menu=[],$pid=0){
        $menu['pid'] = $pid;
//        dump($menu);die();
        $child = empty($menu['child'])?[]:$menu['child'];
//        if(!isset($menu['title'])){
//            dump($menu);die();
//        }
        $me = Db::name('system_menu')
            ->where('title',$menu['title']);
        if(isset($menu['href'])&&$menu['href']){
            $me = $me->where('href',$menu['href']);
        }
        if(isset($menu['table'])&&$menu['table']){
            $me = $me->where('table',$menu['table']);
        }
        $me = $me->findOrEmpty();
        if(empty($me)){
            $menu['id'] = Db::name('system_menu')
                ->strict(false)
                ->insertGetId($menu);
        }else{
            $menu['id'] = $me['id'];
        }
//        dump($child);die();
        foreach ($child as $value){
            $bool = $this->installMenu($value,$menu['id']);
            if(!$bool){
                $this->message = '安装菜单失败';
                return false;
            }
        }
        return true;
    }

    /**安装数据库
     * @param $assets
     * @return bool
     */
    protected function installSql($assets,$cover=true){
        $sql_path = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'sql';
        if(!is_dir($sql_path)){
            return true;
        }
        $config = getDataBaseConfig();
        $database = [
            'username' => $config['username'],
            'password' =>$config['password'],
            'host' => $config['hostname'].':'.$config['hostport'],
            'database' => $config['database'],
            'prefix' => $config['prefix'],
        ];
        $back_tool = new Restore($database);
        if(is_dir($sql_path)){
            $array = scandir($sql_path);
//            echo '<pre>';
//            print_r($array);
//            exit;
            foreach ($array as $values){
                if($values!='.'&&$values!='..'&&$values!='uninstall.sql'&&$values!='sql.lock'){
                    $bool = $back_tool->restore($sql_path.DIRECTORY_SEPARATOR.$values);
                    if(!$bool){
                        $this->message = 'sql安装失败';
                        return false;
                    }
                }
            }
            $mysql = getDataBaseConfig('database');
            $qianzui = getDataBaseConfig('prefix');
            $sql = "select TABLE_NAME  AS 'table',TABLE_COMMENT as 'name',engine as 'engine' from information_schema.tables where table_schema='".$mysql."'";
            $array = Db::query($sql);
//        dump($array);die();
            foreach ($array as $value){
                if(strpos($value['table'],'system_log')!==false){
                    continue;
                }
                $table = $value['table'];
                $tables = str_replace($qianzui,'',$table);
                if(in_array($tables,[
                    'system_admin','system_auth','system_auth_node','system_config',
                    'system_field','system_menu','system_model','system_node',
                    'system_quick','system_uploadfile'
                ])){
                    continue;
                }
                $bool = TablHandle::updatetable($tables);
                if(!$bool){
                    return $this->error(fy("Update failed"));
                }
            }
        }
//        die();
        return true;
    }

    /**导出
     * @param string $assets
     * @return array|false|int|\think\Model|null
     */
    public function exportAsset($assets='',$is_data=false){
        $dir = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'config'.DIRECTORY_SEPARATOR.'config.json';
        if(!is_file($dir)){
            return $this->error('插件不存在');
        }
        $shuju = json_decode(file_get_contents($dir),true);
        //导出菜单
        if(isset($shuju['menu'])&&$shuju['menu']){
            $menu = $this->exportMenu($shuju['menu']);
            if(!$menu){
                return $menu;
            }
            $shuju['menu'] = $menu;
        }
        //导出数据库结构
        $bool = $this->exportSql($shuju,$is_data);
        if(!$bool){
            return false;
        }
        $bool = $this->exportFile($shuju);
        if(!$bool){
            return false;
        }
        $str = json_encode($shuju,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT|JSON_UNESCAPED_SLASHES);

//        echo $str;exit;
        $bool = file_put_contents($dir,$str);
        if(!$bool){
            $this->message = '配置文件导出失败';
        }
        $config = config_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'config.json';
//        dump($config);die();
        if(is_file($config)){
            $newshuju = json_decode(file_get_contents($config),true);
            $newshuju = array_merge($shuju,$newshuju);
            $newstr = json_encode($newshuju,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT|JSON_UNESCAPED_SLASHES);
            $bool = file_put_contents($config,$newstr);
            if(!$bool){
                $this->message = '重置配置文件失败';
            }
        }
        $bool = $this->exportConfig($assets);
//        if(!$bool){
//            $this->message = '重置配置文件失败';
//        }
        return $bool;
    }
    protected function exportConfig($assets){
        $config = config_path().'addons'.DIRECTORY_SEPARATOR.$assets;
        if(is_dir($config)){
            $a_config = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'config';
            if(!is_dir($a_config)){
                chmod(root_path().'addons',0777);
                mkdir($a_config);
            }
            $bool = $this->moveFile($config,$a_config);
            if(!$bool){
                $this->message = '移动配置文件失败';
                return false;
            }
        }
        return true;
    }

    /**卸载插件
     * @param $assets
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function unInstall($assets){
        $dir = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'config'.DIRECTORY_SEPARATOR.'config.json';
        if(!is_file($dir)){
            return $this->error('插件不存在');
        }
        $shuju = json_decode(file_get_contents($dir),true);
        //删除文件
        $bool = $this->delFile($assets,$shuju['table']??[]);
        if(!$bool){
            return false;
        }
        if(isset($shuju['table'])&&$shuju['table']&&is_array($shuju['table'])){
            //删除数据表
            $bool = $this->delTable($shuju['table']);
            if(!$bool){
                return false;
            }
        }
        if(isset($shuju['menu'])&&$shuju['menu']&&is_array($shuju['menu'])){
            $bool = $this->delMenu($shuju['menu']);
            if(!$bool){
                $this->message = '删除菜单失败';
                return false;
            }
        }
        $bool = $this->delSql($assets);
        if(!$bool){
//            $this->message = '更新缓存失败';
            return false;
        }
        $bool = $this->clearCache();
        if(!$bool){
            $this->message = '更新缓存失败';
            return false;
        }
        $shuju['install'] = 0;
        $str = json_encode($shuju,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT|JSON_UNESCAPED_SLASHES);
        $bool = file_put_contents($dir,$str);
        if(!$bool){
            $this->message = '配置文件修改失败';
        }
        return true;
    }
    protected function delSql($assets){
        $sql = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'sql'.DIRECTORY_SEPARATOR.'uninstall.sql';
        if(is_file($sql)){
            $config = getDataBaseConfig();
            $database = [
                'username' => $config['username'],
                'password' =>$config['password'],
                'host' => $config['hostname'].':'.$config['hostport'],
                'database' => $config['database'],
                'prefix' => $config['prefix'],
            ];
            $back_tool = new Restore($database);
            $bool = $back_tool->restore($sql);
            if(!$bool){
                $this->message = '删除变更字段失败';
                return false;
            }
            $mysql = getDataBaseConfig('database');
            $qianzui = getDataBaseConfig('prefix');
            $sql = "select TABLE_NAME  AS 'table',TABLE_COMMENT as 'name',engine as 'engine' from information_schema.tables where table_schema='".$mysql."'";
            $array = Db::query($sql);
//        dump($array);die();
            foreach ($array as $value){
                if(strpos($value['table'],'system_log')!==false){
                    continue;
                }
                $table = $value['table'];
                $tables = str_replace($qianzui,'',$table);
                if(in_array($tables,[
                    'system_admin','system_auth','system_auth_node','system_config',
                    'system_field','system_menu','system_model','system_node',
                    'system_quick','system_uploadfile'
                ])){
                    continue;
                }
                $bool = TablHandle::updatetable($tables);
                if(!$bool){
                    return $this->error(fy("Update failed"));
                }
            }
        }

        return true;
    }

    /**清除缓存
     * @return bool
     * @throws \Doctrine\Common\Annotations\AnnotationException
     * @throws \ReflectionException
     */
    protected function clearCache(){
        $nodeList = (new NodeService())->getNodelist();
        $model = new SystemNode();
        try {
            $existNodeList = $model->field('id,node,title,type,is_auth')->select()->toArray();
            $formatNodeList = array_format_key($nodeList, 'node');
            foreach ($existNodeList as $vo) {
                !isset($formatNodeList[$vo['node']]) && $model->where('id', $vo['id'])->delete();
            }
            TriggerService::updateNode();
        } catch (\Exception $e) {
            $this->message = '更新缓存失败';
            return false;
        }
        Cache::clear();
        return true;
    }

    /**删除菜单
     * @param $menu
     * @return bool
     * @throws \think\db\exception\DbException
     */
    protected function delMenu($menu){
        if(isset($menu['child'])){
            foreach ($menu['child'] as $value){
                $bool = $this->delMenu($value);
                if(!$bool){
                    $this->message = '删除菜单失败';
                    return false;
                }
            }
        }
        $me = Db::name('system_menu')
            ->where('title',$menu['title']);
        if(isset($menu['href'])&&$menu['href']){
            $me = $me->where('href',$menu['href']);
        }
        if(isset($menu['table'])&&$menu['table']){
            $me = $me->where('table',$menu['table']);
        }
        $me = $me->findOrEmpty();
        if(empty($me)){
            return true;
        }
        $child_count = Db::name('system_menu')
            ->where('pid',$me['id'])
            ->count();
        if($child_count==0){
            $bool = Db::name('system_menu')
                ->where('id',$me['id'])
                ->delete();
        }
        return true;
    }

    /**删除数据表
     * @param $tables
     * @return false
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function delTable($tables){
        $thistables = Db::name('system_model')
            ->where('table','in',$tables)
            ->select()->toArray();
        $bool = ModelHandle::deleteTable($thistables);
        if(!$bool){
            $this->message = '删除数据表失败';
            return false;
        }
        $bool = Db::name('system_model')
            ->where('table','in',$tables)
            ->delete();
        if(!$bool){
            $this->message = '删除模型表失败';
            return false;
        }
        return true;
    }
    /**删除文件
     * @param $assets
     * @param $tables
     * @return bool
     */
    protected function delFile($assets,$tables){
        //删除控制器
        $controller = base_path().'admin'.DIRECTORY_SEPARATOR.'controller'.DIRECTORY_SEPARATOR.$assets;
        if(is_dir($controller)){
            $bool = $this->deleteFile($controller);
            if(!$bool){
                $this->message = '删除控制器失败';
                return false;
            }
        }
        //删除模型
        if($tables&&is_array($tables)){
            $model = base_path().'admin'.DIRECTORY_SEPARATOR.'model'.DIRECTORY_SEPARATOR;
            foreach ($tables as $value){
                $value = ucfirst(PublicUse::UnderlineToHump($value)).'.php';
                if(is_file($model.$value)){
                    chmod($model.$value,0777);
                    unlink($model.$value);
                }
            }
        }
        //删除视图
        $view = base_path().'admin'.DIRECTORY_SEPARATOR.'view'.DIRECTORY_SEPARATOR.$assets;
        if(is_dir($view)){
            $bool = $this->deleteFile($view);
            if(!$bool){
                $this->message = '删除视图失败';
                return false;
            }
        }
        //删除js
        $js = public_path().'static'.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'js'.DIRECTORY_SEPARATOR.$assets;
        if(is_dir($js)){
            $bool = $this->deleteFile($js);
            if(!$bool){
                $this->message = '删除js失败';
                return false;
            }
        }
        //删除前端
        $index = base_path().$assets;
        if(is_dir($index)){
            $bool = $this->deleteFile($index);
            if(!$bool){
                $this->message = '删除前端失败';
                return false;
            }
        }
        $config = config_path().$assets;
        if(is_dir($config)){
            $bool = $this->deleteFile($config);
            if(!$bool){
                $this->message = '删除配置失败';
                return false;
            }
        }
        //删除静态文件
        $addons = public_path().'static'.DIRECTORY_SEPARATOR.'addons'.DIRECTORY_SEPARATOR.$assets;
        if(is_dir($addons)){
            $bool = $this->deleteFile($addons);
            if(!$bool){
                $this->message = '删除静态文件失败';
                return false;
            }
        }
        return true;
    }

    /**导出菜单
     * @param array $menu
     * @return array|\think\Model
     */
    protected function exportMenu($menu=[]){
        if(empty($menu)){
            return [];
        }
        $newvalue = Db::name('system_menu')
            ->where('title',$menu['title']);
        if(isset($menu['href'])&&$menu['href']){
            $newvalue = $newvalue->where('href',$menu['href']);
        }
        if(isset($menu['table'])&&$menu['table']){
            $newvalue = $newvalue->where('table',$menu['table']);
        }
        $newvalue = $newvalue->findOrEmpty();
        unset($newvalue['id']);
        if(isset($newvalue['default_tablebar'])&&$newvalue['default_tablebar']){
            $newvalue['default_tablebar'] = str_replace('"','\"',$newvalue['default_tablebar']);
        }
        if(isset($newvalue['default_right_button'])&&$newvalue['default_right_button']){
            $newvalue['default_right_button'] = str_replace('"','\"',$newvalue['default_right_button']);
        }
        if(isset($menu['child'])){
            $child = [];
            foreach ($menu['child'] as $key=>$value){
                $child[$key] = $this->exportMenu($value);
            }
            $newvalue['child'] = $child;
        }
        return $newvalue;
    }

    /**导出数据库
     * @param $shuju
     * @return false|int
     */
    protected function exportSql($shuju,$getdata=false){
        $assets = $shuju['assets'];
        $tables = $shuju['table']??[];
        if(empty($tables)){
            return true;
        }
        $dir = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'sql';
        if(!is_dir($dir)){
            chmod(root_path().'addons'.DIRECTORY_SEPARATOR.$assets,0777);
            mkdir($dir,0777,true);
        }
        $config = getDataBaseConfig();
//        echo '<pre>';
//        print_r($config);
//        exit;
        $database = [
            'username' => $config['username'],
            'password' =>$config['password'],
            'host' => $config['hostname'].':'.$config['hostport'],
            'database' => $config['database'],
        ];

        $back_tool = new Backup($database);
        foreach ($tables as $key=>$value){
            $value = $config['prefix'].$value;
            $tables[$key] = $value;
        }
//        echo '<pre>';
//        print_r($tables);
//        exit;
        $filepath = $back_tool->setTables($tables)->setFilename($dir.DIRECTORY_SEPARATOR.'install')->setWithData($getdata)->dump();
        $file_name = $filepath[0];
        $str = file_get_contents($file_name);
        $str = str_replace($config['prefix'],'__PREFIX__',$str);
        $bool = file_put_contents($file_name,$str);
        if(!$bool){
            $this->message = fy('Save failed');
        }
        return $bool;
    }

    /**导出文件
     * @param $shuju
     * @return bool|null
     */
    protected function exportFile($shuju){
        $assets = $shuju['assets'];
        $controller = base_path().'admin'.DIRECTORY_SEPARATOR.'controller'.DIRECTORY_SEPARATOR.$assets;
        $admin = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR;
        //复制控制器
        if(is_dir($controller)){
            $bool = $this->moveFile($controller,$admin.'controller');
            if(!$bool){
                return false;
            }
        }
        $view = base_path().'admin'.DIRECTORY_SEPARATOR.'view'.DIRECTORY_SEPARATOR.$assets;
        //复制视图
        if(is_dir($view)){
//            echo 1;exit;
            $bool = $this->moveFile($view,$admin.'view');
            if(!$bool){
                return false;
            }
        }
        $tables = $shuju['table']??[];
        //复制模型
        if(!empty($tables)){
            if(!is_dir($admin.'model')){
                chmod(root_path().'addons',0777);
                mkdir($admin.'model',0777,true);
            }
            $model = base_path().'admin'.DIRECTORY_SEPARATOR.'model'.DIRECTORY_SEPARATOR;
            foreach ($tables as $value){
                $class_name = ucfirst(PublicUse::UnderlineToHump($value));
                if(is_file($model.$class_name.'.php')){
                    $bool = copy($model.$class_name.'.php',$admin.'model'.DIRECTORY_SEPARATOR.$class_name.'.php');
                    if(!$bool){
                        $this->message = '移动模型失败';
                        return false;
                    }
                }
            }
        }
        $js = public_path().'static'.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'js'.DIRECTORY_SEPARATOR.$assets;
        //复制js
        if(is_dir($js)){
            $a_js = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'admin'.DIRECTORY_SEPARATOR.'js';
            if(!is_dir($a_js)){
                chmod(root_path().'addons',0777);
                mkdir($a_js);
            }
            $bool = $this->moveFile($js,$a_js);
            if(!$bool){
                return $bool;
            }
        }
        //复制前端
        $index = base_path().$assets;
        if(is_dir($index)){
            $a_index = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'index';
            if(!is_dir($a_index)){
                chmod(root_path().'addons',0777);
                mkdir($a_index);
            }
            $bool = $this->moveFile($index,$a_index);
            if(!$bool){
                $this->message = '移动应用失败';
                return false;
            }
        }
        //复制插件其余静态文件
        $addons = public_path().'static'.DIRECTORY_SEPARATOR.'addons'.DIRECTORY_SEPARATOR.$assets;
        if(is_dir($addons)){
            $a_addone = root_path().'addons'.DIRECTORY_SEPARATOR.$assets.DIRECTORY_SEPARATOR.'static';
            if(!is_dir($a_addone)){
                chmod(root_path().'addons',0777);
                mkdir($a_addone);
            }
            $bool = $this->moveFile($addons,$a_addone);
            if(!$bool){
                $this->message = '移动静态文件失败';
                return false;
            }
        }
        return true;
    }

    /**移动文件
     * @param $file_path
     * @param $to_path
     * @return bool
     */
    public function moveFile($file_path,$to_path,$exclude=[]){
        if(empty($file_path)||!is_dir($file_path)){
            return true;
        }
//        echo $file_path;exit;
        if(!is_dir($to_path)){
            chmod(root_path(),0777);
            mkdir($to_path,0777,true);
        }
        $array = scandir($file_path);
        foreach ($array as $value){
            if($value!='.'&&$value!='..'){
                if(in_array($value,$exclude)){
                    continue;
                }
                if(is_dir(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value)){
                    $bool = $this->moveFile(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value,rtrim($to_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value);
                    if(!$bool){
//                        $this->message = '移动文件失败';
                        return false;
                    }
                }
                if(is_file(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value)){
//                    $str = file_get_contents(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value);
//                    $bool = file_put_contents(rtrim($to_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value,$str);
                    $bool = copy(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value,rtrim($to_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value);
                    if(!$bool){
                        $this->message = '移动文件失败';
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**删除文件夹
     * @param $file_path
     * @return bool|null
     */
    protected function deleteFile($file_path){
        if(!is_dir($file_path)){
            return true;
        }
        $array = scandir($file_path);
        foreach ($array as $value){
            if($value!='.'&&$value!='..'){
                if(is_file(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value)){
                    chmod(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value,0777);
                    unlink(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value);
                }
                if(is_dir(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value)){
                    chmod(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value,0777);
                    $bool = $this->deleteFile(rtrim($file_path,DIRECTORY_SEPARATOR).DIRECTORY_SEPARATOR.$value);
                    if(!$bool){
                        return $bool;
                    }
                }
            }
        }
        rmdir($file_path);
        return true;
    }
}