<?php
namespace app\plugins\common\admin\controller;
use app\common\controller\AdminController;
use EasyAdmin\upload\Uploadfile;
use think\db\Query;
use think\facade\Db;
use think\facade\Filesystem;
use think\facade\Lang;
use app\common\controller\PluginsController;

class Ajax extends PluginsController{


    /**
     * 加载语言包
     */
    public function lang()
    {

        $header = ['Content-Type' => 'application/javascript'];
        if (!env('APP_DEBUG', false)) {
            $offset = 30 * 60 * 60 * 24; // 缓存一个月
            $header['Cache-Control'] = 'public';
            $header['Pragma'] = 'cache';
            $header['Expires'] = gmdate("D, d M Y H:i:s", time() + $offset) . " GMT";
        }

        $controller = $this->request->get('controller');

        //默认只加载了控制器对应的语言名，你还根据控制器名来加载额外的语言包
        $this->loadlang($controller);
        return jsonp(Lang::get(), 200, $header, ['json_encode_param' => JSON_FORCE_OBJECT | JSON_UNESCAPED_UNICODE]);
    }



}