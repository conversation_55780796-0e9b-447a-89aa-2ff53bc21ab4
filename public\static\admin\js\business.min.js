define(["jquery","easy-admin","vue"],function(e,a,t){var s=layui.tableSelect;var i={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"business/index",add_url:"business/add",edit_url:"business/edit",delete_url:"business/delete",export_url:"business/export",modify_url:"business/modify"};var l={index:function(){a.table.render({init:i,limit:CONFIG.ADMINPAGESIZE,toolbar:["refresh",[{text:fy("Add"),url:i.add_url,method:"open",auth:"add",class:"layui-btn layui-btn-normal layui-btn-sm",icon:"fa fa-plus ",extend:'data-full="true"'}],"delete"],cols:[[{type:"checkbox"},{field:"id",title:"id"},{field:"crmCustomer.name",title:fy("Client name"),search:true},{field:"name",title:fy("Opportunity Name")},{field:"money",title:fy("Budget amount")},{field:"total_price",title:fy("Total sales amount")},{field:"next_time",title:fy("Next contact time"),width:142,templet:function(e){return layui.util.toDateString(e.next_time*1e3,"yyyy-MM-dd HH:mm")},search:"range"},{field:"is_end",search:"select",selectList:CONFIG.getIsEndList,title:fy("Status")},{field:"deal_time",title:fy("Estimated transaction date"),templet:function(e){return layui.util.toDateString(e.deal_time*1e3,"yyyy-MM-dd HH:mm")}},{field:"remark",title:fy("Remark"),templet:a.table.text},{field:"createAdmin.username",title:fy("Created by"),search:true},{field:"ownerAdmin.username",title:fy("Responsible Person"),search:true},{field:"create_time",title:fy("Creation time")},{width:250,title:fy("Operate"),templet:a.table.tool,operat:[[{class:"layui-btn layui-btn-xs layui-btn-primary",method:"open",text:fy("Write follow-up"),auth:"record_add",url:"business.record/add?business_id={id}&customer_id={customer_id}",extend:'data-full="true"',icon:"fa fa-plus"}],[{text:fy("Edit"),url:i.edit_url,method:"open",auth:"edit",class:"layui-btn layui-btn-xs layui-btn-success",extend:'data-full="true"'}],"delete"]}]],done:function(e,t,i){if(i===undefined&&e.msg&&e.url){a.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}},where:{scope:1}});a.listen()},add:function(){this.renderPro();a.listen()},edit:function(){this.renderPro();a.listen()},renderPro:function(){var i=new t({el:"#app",data:{pro_list:[],cost_sum:0,sale_sum:0},methods:{entryTime(e){if(!this.pro_list[e]["create_time"]){this.pro_list[e]["create_time"]=Date.parse(new Date)/1e3}return layui.util.toDateString(this.pro_list[e]["create_time"]*1e3,"yyyy-MM-dd HH:mm")},removePro(t,e){that=this;if(e){a.request.ajax("get",{url:a.url("business/delproduct_by_business"),data:{business_product_id:e}},function(e){if(e.code){that.pro_list.splice(t,1)}else{a.msg.error(fy("Delete failed"))}})}else{that.pro_list.splice(t,1)}}},computed:{getTotal(){var t=this.pro_list;var i=0,a=0,s=0,l=0;for(let e=0;e<t.length;e++){i+=t[e].cost_price*t[e].nums;s+=t[e].sale_price*t[e].nums;a+=parseFloat(t[e].discount);l+=parseInt(t[e].nums)}if(s){real_sale_sum=s-a}else{real_sale_sum=0}return{cost_sum:i.toFixed(2),nums_sum:l,discount_sum:a.toFixed(2),sale_sum:s.toFixed(2),real_sale_sum:real_sale_sum.toFixed(2)}}}});business_id=e("#table-pro").data("business_id");if(business_id){a.request.ajax("get",{url:a.url("business/product_by_business"),data:{business_id:business_id}},function(e){i.pro_list=e.data})}s.render({elem:"#select-pro",checkedKey:"",searchType:"more",searchList:[{searchKey:"name",searchPlaceholder:fy("Please enter")+fy("Product name")}],table:{url:a.url("Product/index"),cols:[[{type:"checkbox"},{field:"type.title",width:90,title:fy("Product Classification"),templet:function(e){return"<span>"+e.type.title+"</span>"}},{field:"name",title:fy("Product name"),width:200},{field:"thumb",width:90,title:fy("Product images"),templet:a.table.image},{field:"specification",title:fy("Specifications")},{field:"model",title:fy("Model")},{field:"inventory",title:fy("Inventory"),width:90,templet:function(e){if(e.inventory<=e.min_warning){return'<span class="layui-font-red">'+e.inventory+"</span>"}else if(e.inventory>=e.max_warning){return'<span class="layui-font-orange">'+e.inventory+"</span>"}}},{field:"cost_price",width:90,title:fy("Cost price")},{field:"sale_price",width:90,title:fy("Sale price")}]]},done:function(e,t){for(let e=0;e<t.data.length;e++){t.data[e].product_id=t.data[e].id;t.data[e].id=0;t.data[e].remark="";t.data[e].nums=t.data[e].nums?t.data[e].nums:1;t.data[e].discount=t.data[e].discount?t.data[e].discount:0}i.pro_list=i.pro_list.concat(t.data)},where:{status:1}})}};return l});