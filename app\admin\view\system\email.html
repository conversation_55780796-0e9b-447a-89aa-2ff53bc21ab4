{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{:fy("邮箱配置")}</legend>
    </fieldset>
    <form class="layui-form layui-form-pane" lay-filter="form-email">
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy("Server")}</label>
            <div class="layui-input-block">
                <input type="text" lay-verify="required" name="smtp_server" placeholder="SMTP {:fy('Server')}" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">SMTP {:fy('Port')}</label>
            <div class="layui-input-block">
                <input type="text" lay-verify="required" name="smtp_port" placeholder="SMTP {:fy('Port')}" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Sender')}</label>
            <div class="layui-input-block">
                <input type="text" name="email_id" lay-verify="required" placeholder="{:fy('Sender')}" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Sending email address')}</label>
            <div class="layui-input-block">
                <input type="text" name="smtp_user" lay-verify="required" placeholder="{:fy("Sender's email address")}" value="" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Authentication code')}</label>
            <div class="layui-input-3">
                <input type="password" name="smtp_pwd" lay-verify="required" placeholder="SMTP {:fy('Authentication code')}" value="" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Test mailbox')}</label>
            <div class="layui-input-3">
                <input type="text" name="test_eamil" id="test_eamil" placeholder="{:fy('Test receiving email address')}" value="" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">{:fy('Mail test content')}</label>
            <div class="layui-input-block">
                <textarea name="test_eamil_info" id="test_eamil_info" placeholder="{:lang('Please enter')} {:fy('Mail test content')}" class="layui-textarea"></textarea>
            </div>
        </div>




        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">{:lang('submit')}</button>
                <button type="reset" class="layui-btn layui-btn-primary">{:lang('Reset')}</button>
                <button type="button" class="layui-btn layui-btn-normal" id="trySend">{:fy('Test sending')}</button>
            </div>
        </div>
    </form>
</div>
{include file="common/foot"/}
<script>
    layui.use(['form', 'layer'], function () {
        var form = layui.form,layer = layui.layer,$= layui.jquery;
        //发送测试邮件
        form.val("form-email", {$info|raw})
        $('#trySend').click(function(){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var email = $('#test_eamil').val();
            $.post("{:url('trySend')}",{email:email},function(res){
                layer.close(loading);
                if(res.code > 0){
                    layer.msg(res.msg,{time:1800});
                }else{
                    layer.msg(res.msg,{time:1800});
                }
            });
        });
        //提交监听
        form.on('submit(submit)', function (data) {
            loading =layer.load(1, {shade: [0.1,'#fff']});
            $.post("{:url('system/email')}",data.field,function(res){
                layer.close(loading);
                if(res.code > 0){
                    layer.msg(res.msg,{icon: 1, time: 1000},function(){
                        location.href = res.url;
                    });
                }else{
                    layer.msg(res.msg,{icon: 2, time: 1000});
                }
            });
        })
    })
</script>
</body>
</html>