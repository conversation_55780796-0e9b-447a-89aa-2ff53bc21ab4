<?php

return [
    'Field Name'       => '字段名称',
    'width'       => '宽度',
    'List display'       => '列表展示',
    'Presentation name'       => '展示名称',
    'Presentation field'       => '展示字段',
    'Storage field'       => '存储字段',
    'Association Table'       => '关联表',
    'Field type'       => '字段类型',
    'Field length'       => '字段长度',
    'Drop down the request address'       => '下拉请求地址',
    'Validation rules'       => '验证规则',
    'form types'       => '表单类型',
    'Options'       => '选项',
    'The form type is a single choice box, a multiple choice box, or a drop-down box. Example: 0: close, 1: open'       => '表单类型为单选框，多选框，下拉框的选项。例：保存内容1:展示内容1,保存内容2:展示内容2,保存内容3:展示内容3',
    'Added edit data will be validated'       => '新增编辑数据将验证',
    'Enter the maximum number of characters that need to be saved in this field'       => '填写该字段最大可容纳的字符数',
    'Must start with an English letter, followed by supported numbers and _'       => '必须是以英文字母开头，后面支持数字和_',
];
