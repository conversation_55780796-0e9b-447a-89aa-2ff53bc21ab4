[2025-07-26T07:21:44+08:00][sql] CONNECT:[ UseTime:0.001234s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T07:21:44+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.001321s ]
[2025-07-26T07:21:44+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000798s ]
[2025-07-26T07:21:44+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.001345s ]
[2025-07-26T07:21:44+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000546s ]
[2025-07-26T07:21:44+08:00][sql] INSERT INTO `ymwl_admin_device` SET `admin_id` = 1 , `device_id` = '设备唯一标识' , `socket_id` = 'socket_68841188c1b944.17908401' , `status` = 1 , `created_at` = '2025-07-26 07:21:44' , `updated_at` = '2025-07-26 07:21:44' [ RunTime:0.010390s ]
[2025-07-26T07:22:48+08:00][sql] UPDATE `ymwl_admin_device`  SET `last_active` = '2025-07-26 07:22:48'  WHERE  `socket_id` = 'socket_68841188c1b944.17908401' [ RunTime:0.007282s ]
[2025-07-26T07:25:37+08:00][sql] UPDATE `ymwl_admin_device`  SET `last_active` = '2025-07-26 07:25:37'  WHERE  `socket_id` = 'socket_68841188c1b944.17908401' [ RunTime:0.006387s ]
[2025-07-26T07:49:49+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_68841188c1b944.17908401' [ RunTime:0.009003s ]
[2025-07-26T07:49:49+08:00][error] [0]socket_close(): Argument #1 ($socket) has already been closed
[2025-07-26T07:59:36+08:00][sql] CONNECT:[ UseTime:0.000945s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T07:59:36+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.000613s ]
[2025-07-26T07:59:36+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000400s ]
[2025-07-26T07:59:36+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000553s ]
[2025-07-26T07:59:36+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000245s ]
[2025-07-26T07:59:36+08:00][error] [10501]SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1-设备唯一标识' for key 'unique_admin_device'
[2025-07-26T08:01:30+08:00][sql] CONNECT:[ UseTime:0.001306s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T08:01:30+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.000655s ]
[2025-07-26T08:01:30+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000252s ]
[2025-07-26T08:01:30+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000356s ]
[2025-07-26T08:01:30+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1 [ RunTime:0.000281s ]
[2025-07-26T08:01:30+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = 'test_device_1753488090' LIMIT 1 [ RunTime:0.000230s ]
[2025-07-26T08:01:30+08:00][sql] INSERT INTO `ymwl_admin_device` SET `admin_id` = 1 , `device_id` = 'test_device_1753488090' , `socket_id` = 'socket_68841adaa79230.54481538' , `status` = 1 , `last_active` = '2025-07-26 08:01:30' , `created_at` = '2025-07-26 08:01:30' , `updated_at` = '2025-07-26 08:01:30' [ RunTime:0.008111s ]
[2025-07-26T08:01:30+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_crm_customer` [ RunTime:0.000838s ]
[2025-07-26T08:01:30+08:00][sql] SELECT * FROM `ymwl_crm_customer` LIMIT 3 [ RunTime:0.000331s ]
[2025-07-26T08:01:30+08:00][sql] DELETE FROM `ymwl_admin_device` WHERE  `device_id` = 'test_device_1753488090' [ RunTime:0.005807s ]
[2025-07-26T08:08:28+08:00][sql] CONNECT:[ UseTime:0.001027s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T08:08:28+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.001033s ]
[2025-07-26T08:08:28+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000360s ]
[2025-07-26T08:08:28+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000601s ]
[2025-07-26T08:08:28+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000189s ]
[2025-07-26T08:08:28+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = '设备唯一标识' LIMIT 1 [ RunTime:0.000250s ]
[2025-07-26T08:08:28+08:00][sql] UPDATE `ymwl_admin_device`  SET `socket_id` = 'socket_68841c7c5d6a33.49940440' , `status` = 1 , `last_active` = '2025-07-26 08:08:28' , `updated_at` = '2025-07-26 08:08:28'  WHERE  `id` = 1 [ RunTime:0.009501s ]
[2025-07-26T08:08:29+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000744s ]
[2025-07-26T08:08:29+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000961s ]
[2025-07-26T08:08:29+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_68841c7c5d6a33.49940440' [ RunTime:0.007347s ]
[2025-07-26T08:08:29+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = '设备唯一标识' LIMIT 1 [ RunTime:0.000357s ]
[2025-07-26T08:08:29+08:00][sql] UPDATE `ymwl_admin_device`  SET `socket_id` = 'socket_68841c7ddc1482.47951895' , `status` = 1 , `last_active` = '2025-07-26 08:08:29' , `updated_at` = '2025-07-26 08:08:29'  WHERE  `id` = 1 [ RunTime:0.005594s ]
[2025-07-26T08:08:29+08:00][error] [0]socket_write(): Argument #1 ($socket) has already been closed
[2025-07-26T08:21:59+08:00][sql] CONNECT:[ UseTime:0.000997s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T08:21:59+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.000972s ]
[2025-07-26T08:21:59+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000369s ]
[2025-07-26T08:21:59+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000530s ]
[2025-07-26T08:21:59+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000239s ]
[2025-07-26T08:21:59+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_68841c7ddc1482.47951895' [ RunTime:0.009352s ]
[2025-07-26T08:21:59+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = '设备唯一标识' LIMIT 1 [ RunTime:0.000599s ]
[2025-07-26T08:21:59+08:00][sql] UPDATE `ymwl_admin_device`  SET `socket_id` = 'socket_68841fa74744c8.17715627' , `status` = 1 , `last_active` = '2025-07-26 08:21:59' , `updated_at` = '2025-07-26 08:21:59'  WHERE  `id` = 1 [ RunTime:0.008677s ]
[2025-07-26T08:22:18+08:00][sql] UPDATE `ymwl_admin_device`  SET `last_active` = '2025-07-26 08:22:18'  WHERE  `socket_id` = 'socket_68841fa74744c8.17715627' [ RunTime:0.008553s ]
[2025-07-26T08:45:30+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_68841fa74744c8.17715627' [ RunTime:0.006060s ]
[2025-07-26T08:45:30+08:00][error] [0]socket_close(): Argument #1 ($socket) has already been closed
[2025-07-26T20:36:10+08:00][error] [2]socket_read(): unable to read from socket [10054]: 远程主机强迫关闭了一个现有的连接。
[2025-07-26T22:04:18+08:00][sql] CONNECT:[ UseTime:0.000974s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T22:04:18+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.001150s ]
[2025-07-26T22:04:18+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000408s ]
[2025-07-26T22:04:18+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000411s ]
[2025-07-26T22:04:18+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000458s ]
[2025-07-26T22:04:18+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = '65168418616' LIMIT 1 [ RunTime:0.000359s ]
[2025-07-26T22:04:18+08:00][sql] UPDATE `ymwl_admin_device`  SET `socket_id` = 'socket_6884e0625733b7.65052518' , `status` = 1 , `last_active` = '2025-07-26 22:04:18' , `updated_at` = '2025-07-26 22:04:18'  WHERE  `id` = 4 [ RunTime:0.010399s ]
[2025-07-26T22:05:17+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_6884e0625733b7.65052518' [ RunTime:0.008763s ]
[2025-07-26T22:05:17+08:00][error] [0]socket_close(): Argument #1 ($socket) has already been closed
[2025-07-26T23:24:17+08:00][sql] CONNECT:[ UseTime:0.001120s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T23:24:17+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.000706s ]
[2025-07-26T23:24:17+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000232s ]
[2025-07-26T23:24:17+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000380s ]
[2025-07-26T23:24:17+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000284s ]
[2025-07-26T23:24:17+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = '65168418616' LIMIT 1 [ RunTime:0.000278s ]
[2025-07-26T23:24:17+08:00][sql] UPDATE `ymwl_admin_device`  SET `socket_id` = 'socket_6884f32186d284.26249434' , `status` = 1 , `last_active` = '2025-07-26 23:24:17' , `updated_at` = '2025-07-26 23:24:17'  WHERE  `id` = 4 [ RunTime:0.004311s ]
[2025-07-26T23:24:27+08:00][sql] UPDATE `ymwl_admin_device`  SET `last_active` = '2025-07-26 23:24:27'  WHERE  `socket_id` = 'socket_6884f32186d284.26249434' [ RunTime:0.018608s ]
[2025-07-26T23:25:28+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_6884f32186d284.26249434' [ RunTime:0.009371s ]
[2025-07-26T23:25:28+08:00][error] [0]socket_close(): Argument #1 ($socket) has already been closed
[2025-07-26T23:27:53+08:00][sql] CONNECT:[ UseTime:0.001277s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T23:27:53+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000695s ]
[2025-07-26T23:27:53+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `status` = 1  AND `last_active` < '2025-07-26 23:22:53' [ RunTime:0.000604s ]
[2025-07-26T23:27:53+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0 , `updated_at` = '2025-07-26 23:27:53'  WHERE  `status` = 1  AND `last_active` < '2025-07-26 23:22:53' [ RunTime:0.014139s ]
[2025-07-26T23:28:23+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `status` = 1  AND `last_active` < '2025-07-26 23:23:23' [ RunTime:0.002007s ]
[2025-07-26T23:28:23+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0 , `updated_at` = '2025-07-26 23:28:23'  WHERE  `status` = 1  AND `last_active` < '2025-07-26 23:23:23' [ RunTime:0.018383s ]
[2025-07-26T23:28:53+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `status` = 1  AND `last_active` < '2025-07-26 23:23:53' [ RunTime:0.000884s ]
[2025-07-26T23:28:53+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0 , `updated_at` = '2025-07-26 23:28:53'  WHERE  `status` = 1  AND `last_active` < '2025-07-26 23:23:53' [ RunTime:0.004749s ]
[2025-07-26T23:31:37+08:00][sql] CONNECT:[ UseTime:0.000728s ] mysql:host=127.0.0.1;port=3306;dbname=127;charset=utf8mb4
[2025-07-26T23:31:37+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin` [ RunTime:0.000561s ]
[2025-07-26T23:31:37+08:00][sql] SELECT * FROM `ymwl_admin` WHERE  `username` = 'admin'  AND `is_open` = 1 LIMIT 1 [ RunTime:0.000208s ]
[2025-07-26T23:31:37+08:00][sql] SHOW FULL COLUMNS FROM `ymwl_admin_device` [ RunTime:0.000376s ]
[2025-07-26T23:31:37+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `status` = 1 LIMIT 1 [ RunTime:0.000334s ]
[2025-07-26T23:31:37+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_6884f4532e7d25.62118569' [ RunTime:0.012803s ]
[2025-07-26T23:31:37+08:00][sql] SELECT * FROM `ymwl_admin_device` WHERE  `admin_id` = 1  AND `device_id` = '65168418616' LIMIT 1 [ RunTime:0.000369s ]
[2025-07-26T23:31:37+08:00][sql] UPDATE `ymwl_admin_device`  SET `socket_id` = 'socket_6884f4d98d8e01.99972288' , `status` = 1 , `last_active` = '2025-07-26 23:31:37' , `updated_at` = '2025-07-26 23:31:37'  WHERE  `id` = 4 [ RunTime:0.006512s ]
[2025-07-26T23:31:43+08:00][sql] UPDATE `ymwl_admin_device`  SET `last_active` = '2025-07-26 23:31:43'  WHERE  `socket_id` = 'socket_6884f4d98d8e01.99972288' [ RunTime:0.005782s ]
[2025-07-26T23:32:43+08:00][sql] UPDATE `ymwl_admin_device`  SET `status` = 0  WHERE  `socket_id` = 'socket_6884f4d98d8e01.99972288' [ RunTime:0.009332s ]
[2025-07-26T23:32:43+08:00][error] [0]socket_close(): Argument #1 ($socket) has already been closed
