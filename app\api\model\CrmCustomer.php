<?php

namespace app\api\model;

use think\Model;

/**
 * CRM客户模型
 */
class CrmCustomer extends Model
{
    protected $name = 'crm_customer';
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'name'              => 'string',
        'phone'             => 'string',
        'company'           => 'string',
        'status'            => 'int',
        'pr_user'           => 'string',
        'at_user'           => 'string',
        'head_admin_id'     => 'int',
        'at_time'           => 'int',
        'to_kh_time'        => 'int',
        'to_gh_time'        => 'int',
        'kh_rank'           => 'int',
        'source_id'         => 'int',
        'hangye_id'         => 'int',
        'address'           => 'string',
        'remark'            => 'string',
        'last_record_time'  => 'int',
        'next_record_time'  => 'int',
        'create_time'       => 'int',
        'update_time'       => 'int',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    /**
     * 获取用户的客户列表
     */
    public function getUserCustomers($username, $status = null, $page = 1, $limit = 15)
    {
        $query = $this->where('pr_user', $username);
        
        if ($status !== null) {
            $query->where('status', $status);
        }
        
        return $query->order('id', 'desc')
            ->page($page, $limit)
            ->select();
    }
    
    /**
     * 获取客户统计信息
     */
    public function getCustomerStats($username)
    {
        return [
            'total' => $this->where('pr_user', $username)->count(),
            'leads' => $this->where('pr_user', $username)->where('status', 0)->count(),
            'customers' => $this->where('pr_user', $username)->where('status', 1)->count(),
            'seas' => $this->where('pr_user', $username)->where('status', 2)->count(),
        ];
    }
    
    /**
     * 关联客户等级
     */
    public function rank()
    {
        return $this->belongsTo('CrmRank', 'kh_rank', 'id');
    }
    
    /**
     * 关联客户来源
     */
    public function source()
    {
        return $this->belongsTo('CrmSource', 'source_id', 'id');
    }
    
    /**
     * 关联行业分类
     */
    public function hangye()
    {
        return $this->belongsTo('CrmHangye', 'hangye_id', 'id');
    }
    
    /**
     * 关联跟进记录
     */
    public function records()
    {
        return $this->hasMany('CrmRecord', 'customer_id', 'id');
    }
    
    /**
     * 关联合同
     */
    public function contracts()
    {
        return $this->hasMany('CrmContract', 'customer_id', 'id');
    }
}
