{"name": "box/spout", "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "type": "library", "keywords": ["php", "read", "write", "csv", "xlsx", "ods", "odf", "open", "office", "excel", "spreadsheet", "scale", "memory", "stream", "ooxml"], "license": "Apache-2.0", "homepage": "https://www.github.com/box/spout", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2.0", "ext-zip": "*", "ext-xmlreader": "*", "ext-dom": "*"}, "require-dev": {"phpunit/phpunit": "^8", "friendsofphp/php-cs-fixer": "^2"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-intl\" is not already installed or is too limited)", "ext-intl": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "autoload": {"psr-4": {"Box\\Spout\\": "src/Spout"}}, "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "config": {"platform": {"php": "7.2"}}}