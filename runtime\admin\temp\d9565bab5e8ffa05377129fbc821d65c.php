<?php /*a:2:{s:65:"C:\wwwroot\127.0.0.1\app\admin\view\system\config_group\edit.html";i:1680785460;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Group Name'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="name" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Group Name'); ?>" value="<?php echo htmlentities((isset($row['name']) && ($row['name'] !== '')?$row['name']:'')); ?>">
            </div>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux"><?php echo fy('Group Name'); ?><?php echo fy('Must be unique'); ?></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Sort'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="sort" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Sort'); ?>" value="<?php echo htmlentities((isset($row['sort']) && ($row['sort'] !== '')?$row['sort']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Status'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="status" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Status'); ?>" value="<?php echo htmlentities((isset($row['status']) && ($row['status'] !== '')?$row['status']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Identification'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="identification" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Identification'); ?>" value="<?php echo htmlentities((isset($row['identification']) && ($row['identification'] !== '')?$row['identification']:'')); ?>">
            </div>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux"><?php echo fy('Please use English letters to identify and guarantee uniqueness'); ?></div>
            </div>
        </div>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit><?php echo fy('Confirm'); ?></button>
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm"><?php echo fy('Reset'); ?></button>
        </div>

    </form>
</div>
</body>
</html>