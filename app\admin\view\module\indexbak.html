
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{:lang('module')}{:lang('list')}</legend>
    </fieldset>
    <table class="layui-table" id="list" lay-filter="list"></table>
</div>
{include file="common/foot"/}
<script type="text/html" id="action">
    <a data-open="{:url('field')}?id={{d.id}}" data-title="{{d.title}}字段" class="layui-btn layui-btn-normal layui-btn-xs">模型字段</a>
    <!--<a href="{:url('edit')}?id={{d.id}}" class="layui-btn layui-btn-xs" lay-event="edit">{:lang('Edit')}</a>-->
    <a class="layui-btn layui-btn-xs" lay-event="edit">{:lang('Edit')}</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">{:lang('del')}</a>
</script>
<script type="text/html" id="status">
    <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="{:fy('Open')}|{:fy('Close')}" lay-filter="status" {{ d.status == 1 ? 'checked' : '' }}>
</script>
<script type="text/html" id="topBtn">
    <button class="layui-btn layui-btn-sm" lay-event="add">{:lang('add')}{:lang('module')}</button>
</script>
<script>
    layui.use(['table','form','easyadmin'], function() {
        var table = layui.table, form=layui.form,$ = layui.jquery;
        ea=layui.easyadmin;
        ea.listen();
        var tableIn = table.render({
            elem: '#list',
            url: '{:url("index")}',
            method: 'post',
            toolbar: '#topBtn',
            autoSort: false,
            page:true,
            cols: [[
                {field: 'id', title: '{:lang("id")}', width:90, fixed: true,sort:true},
                {field: 'title', title: '{:lang("module")}{:lang("name")}', width: 120},
                {field: 'name', title: '{:lang("table")}', width: 200},
                {field: 'description', title: '{:lang("detail")}', width: 180},
                {field: 'status', align: 'center',title: '{:lang("status")}', width: 120,toolbar: '#status',sort:true},
                {width: 200, align: 'center', toolbar: '#action'}
            ]]
        });
        //监听{:fy('Sort')}事件
        table.on('sort(list)', function(obj){
            table.reload('list', {
                initSort: obj
                ,where: {sort_by: obj.field,sort_order: obj.type}
            });
        });
        //头工具栏事件
        table.on('toolbar(list)', function(obj){
            switch(obj.event){
                case 'add':
                    var index = layer.open({
                        type: 2,
                        content: '{:url("add")}',
                        area: ['300px', '300px'],
                        maxmin: true
                    });
                    layer.full(index);
                    break;
            }
        });
        form.on('switch(status)', function(obj){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var id = this.value;
            var status = obj.elem.checked===true?1:0;
            $.post('{:url("moduleState")}',{'id':id,'status':status},function (res) {
                layer.close(loading);
                if (res.status==1) {
                    tableIn.reload();
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    return false;
                }
            })
        });
        table.on('tool(list)', function(obj) {
            var data = obj.data;
            var id = data.id;
            if(obj.event === 'del'){
                layer.confirm('你确定要删除该模型吗？', {icon: 3}, function (index) {
                    $.post("{:url('del')}",{id:data.id},function(res){
                        if(res.code==1){
                            layer.msg(res.msg,{time:2000,icon:1});
                            tableIn.reload();
                        }else{
                            layer.msg(res.msg,{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event === 'edit'){
                var index = layer.open({
                    type: 2,
                    content: '{:url("edit")}?id='+id,
                    area: ['300px', '300px'],
                    maxmin: true
                });
                layer.full(index);
            }
        });
    })
</script>