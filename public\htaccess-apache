<IfModule mod_rewrite.c>
    Options +FollowSymlinks -Multiviews
    RewriteEngine On

   # 禁止访问 upload 目录下的 PHP 文件，并返回 404 Not Found
   RewriteCond %{REQUEST_URI} ^/upload/.*\.php$
   RewriteRule ^ - [R=404,L]
   RewriteCond %{REQUEST_URI} ^/static/.*\.php$
   RewriteRule ^ - [R=404,L]

    # 如果请求不是指向现有文件或目录，则重写到 index.php
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php?s=$1 [QSA,PT,L]
</IfModule>
