<?php /*a:2:{s:57:"C:\wwwroot\127.0.0.1\app\admin\view\admin\log\detail.html";i:1674463334;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
    
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Operator'); ?></label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" lay-verify="required"  value="<?php echo htmlentities((isset($row['realname']) && ($row['realname'] !== '')?$row['realname']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Action page"); ?></label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" lay-verify="required"  value="<?php echo htmlentities((isset($row['url']) && ($row['url'] !== '')?$row['url']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Log title"); ?></label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" lay-verify="required"  value="<?php echo htmlentities((isset($row['title']) && ($row['title'] !== '')?$row['title']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Content"); ?></label>
            <div class="layui-input-block">
                <textarea rows="5" class="layui-textarea editor" ><?php echo (isset($row['content']) && ($row['content'] !== '')?$row['content']:''); ?></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">IP</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input"   value="<?php echo htmlentities((isset($row['ip']) && ($row['ip'] !== '')?$row['ip']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">User-Agent</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" lay-verify="required"  value="<?php echo htmlentities((isset($row['useragent']) && ($row['useragent'] !== '')?$row['useragent']:'')); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Operation time"); ?></label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" lay-verify="required"  value="<?php echo htmlentities((isset($row['create_time']) && ($row['create_time'] !== '')?$row['create_time']:'')); ?>">
            </div>
        </div>
</div>
</body>
</html>