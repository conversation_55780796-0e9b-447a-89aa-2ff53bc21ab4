<?php /*a:1:{s:59:"C:\wwwroot\127.0.0.1\app\admin\view\process\todo\index.html";i:1676980960;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>消息提醒-<?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/static/plugs/layui-v2.7.6/css/layui.css" media="all" />
    <link rel="stylesheet" href="/static/admin/css/global.css" media="all">

    <script src="/static/common/js/jquery.min.js"></script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/js/date/date.format.js"></script>
    <style>

        .filtrate-warp{
            margin-bottom: 10px;
        }

        .filtrate-warp .title{
            padding: 5px 12px 7px 12px;
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            cursor: pointer;
            width: 90px;
            background-color: transparent!important;
            color: black!important;
        }

        .filtrate-warp .title:hover{
            color: black;
        }

        .filtrate-warp .flag{
            padding: 6px 12px 6px 12px;
            cursor: pointer;
        }

        .filtrate-warp .flag:hover{
            color: black;
        }

        .filtrate-warp .layui-badge:hover{
            color: white!important;
        }
        .layui-form-label{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;
        }
    </style>
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
</head>
<body class="skin-<?php if(!empty($_COOKIE['skin'])){echo $_COOKIE['skin'];}else{echo '0';setcookie('skin','0');}?>">

<div class="admin-main layui-anim layui-anim-upbit">


    <fieldset class="layui-elem-field " >
        <legend><?php echo fy('Advanced Query'); ?></legend>

        <div class="layui-card" style="box-shadow: none;margin-top: 10px">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto" style="border-bottom: transparent;height: auto" >

                <div class="layui-form-item" style="text-align: left;padding-left: 20px;">

                    <div class="layui-inline">
                        <label class="layui-form-label"><?php echo fy('Type'); ?>：</label>
                        <div class="layui-input-inline">
                            <select name="title">
                                <option value=""><?php echo fy('Please select'); ?><?php echo fy('Type'); ?></option>
<?php
$titleLst=\think\facade\Db::name('todo')->field('title')->group('title')->select();
                                if(is_array($titleLst) || $titleLst instanceof \think\Collection || $titleLst instanceof \think\Paginator): $i = 0; $__LIST__ = $titleLst;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <option value="<?php echo htmlentities($vo['title']); ?>"><?php echo fy($vo['title']); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>

                            </select>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label"><?php echo fy('Processing status'); ?>：</label>
                        <div class="layui-input-inline">
                            <select name="result">
                                <option value=""><?php echo fy('Please select the operation status'); ?></option>
                                <?php
$titleLst=\think\facade\Db::name('todo')->field('result')->group('result')->select();

                                if(is_array($titleLst) || $titleLst instanceof \think\Collection || $titleLst instanceof \think\Paginator): $i = 0; $__LIST__ = $titleLst;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                <option value="<?php echo htmlentities($vo['result']); ?>"><?php echo fy($vo['result']); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>

                            </select>
                        </div>
                    </div>




                </div>



                <div class="layui-inline" style="float: right;padding-bottom: 15px">
                    <button class="layui-btn layuiadmin-btn-list"  lay-submit lay-filter="LAY-app-contlist-search">
                        <i class="layui-icon layui-icon-search layuiadmin-button-btn"  style="vertical-align: sub;"></i><?php echo fy('Search'); ?>
                    </button>
                </div>

            </div>
        </div>



    </fieldset>



    <table class="layui-table" id="table-list" lay-filter="table-list"></table>
</div>


<?php
$aid=$admin['admin_id'];
$group_id=\think\facade\Db::name('admin')->where('admin_id',$aid)->cache('group_id_'.$aid)->value('group_id');
?>

<script type="text/html" id="action">
    {{# if(d.is_finish==0 && (d.show_auth_group_id.indexOf(",<?php echo htmlentities($group_id); ?>,")>=0 || d.show_auth_admin_id.indexOf(",<?php echo htmlentities($aid); ?>,")>=0 ) ){ }}
    <a class="layui-btn  layui-btn-xs" lay-event="audit"><i class="layui-icon">&#xe642;</i><?php echo fy('Review'); ?></a>
    {{# } }}
    <?php  if(auth('Process.todo/del')){ ?>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon">&#xe640;</i><?php echo fy('Delete'); ?></a>
    <?php  } ?>

</script>


<script type="text/html" id="topBtn">
    <?php  if(auth('Process.todo/del')){ ?>
    <button type="button" class="layui-btn layui-btn-danger layui-btn-radius layui-btn-sm" id="selectDel" lay-event="selectDel"><i class="layui-icon">&#xe640;</i><?php echo fy('Delete'); ?></button>
    <?php  } ?>
</script>
<script>

    layui.use(['table','form','upload','util','laydate'], function() {
        var table = layui.table,form = layui.form, $ = layui.jquery;
        var tableIn = table.render({
            elem: '#table-list',
            url: '<?php echo url("index"); ?>',
            method: 'post',
            toolbar: '#topBtn',
            defaultToolbar:['filter','print','exports'],
            cols: [[
                {checkbox:true,fixed: true},
                {field: 'id', title: 'ID', fixed: true},
                {field: 'username', title: "<?php echo fy('Originator'); ?>"},
                {field: 'title', title: "<?php echo fy('Type'); ?>"},
                {field: 'mess', title: "<?php echo fy('Content'); ?>",width: 200 },
                {field: 'createtime', title: "<?php echo fy('Creation time'); ?>",templet:function (res) {
                        return new Date(res.createtime*1000).format('Y-m-d H:i:s');
                    } },
                {field: 'result', title: "<?php echo fy('Operation status'); ?>" },
                {field: 'result_mess', title: "<?php echo fy('Operating instructions'); ?>",width: 200 },
                {field: 'reviewer', title: "<?php echo fy('operator'); ?>" },
                {field: 'audittime', title: "<?php echo fy('Operation time'); ?>" ,templet:function (res) {
                    if(res.audittime<1)return '';
                        return new Date(res.audittime*1000).format('Y-m-d H:i:s');

                    }},
                {title: "<?php echo fy('Operate'); ?>",align: 'center', toolbar: '#action'}
            ]],
            limit: <?php echo htmlentities($system['admin_pagesize']); ?>, //每页默认显示的数量

            page: {limits:[10, 20, 30, 40, 50,100,1000]},
        });


        table.on('tool(table-list)', function(obj) {
            var data = obj.data;

            if (obj.event === 'del') {
                layer.confirm('<?php echo fy("Are you sure you want to delete"); ?>'+'？', function(index){
                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                    $.post("<?php echo url('del'); ?>",{id:data.id},function(res){
                        layer.close(loading);
                        if(res.code===0){
                            layer.msg(res.msg,{time:2000,icon:1});
                            tableIn.reload();
                        }else{
                            layer.msg('<?php echo fy("Operation failed"); ?>'+'！',{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
            else if(obj.event === 'audit'){
                layer.open({
                    type: 2,
                    title: data.title,
                    shadeClose: true,
                    shade: false,
                    area: ['80%', '100%'],
                    content: CONFIG.MODULEURL + '/' +data.url+'&tudo_id='+data.id
                });
            }
        });


        //监听事件
        table.on('toolbar(table-list)', function(obj){

            var checkStatus = table.checkStatus(obj.config.id)
                ,data = checkStatus.data; //获取选中的数据
            switch(obj.event){
                case 'selectDel':
                    if(data.length > 0){
                        alterList = [];  //定义需要转移的数组
                        data.forEach(function (obj,index) {
                            alterList.push(obj.id);
                        });
                        currConfirm = layer.confirm('<?php echo fy("Are you sure you want to delete the checked information"); ?>'+'？',function (index) {
                            var loading = layer.load(1, {shade: [0.1, '#fff']});
                            $.post("<?php echo url('del'); ?>",{id:alterList},function(res){
                                layer.close(loading);
                                if(res.code===0){
                                    layer.msg(res.msg,{time:2000,icon:1});
                                    tableIn.reload();
                                }else{
                                    layer.msg('<?php echo fy("Operation failed"); ?>'+'！',{time:2000,icon:2});
                                }
                            });
                            layer.close(index);
                        });
                    }else {
                        layer.msg("<?php echo fy('Please check the required information first'); ?>");
                    }
                    break;
            };
        });


//监听搜索
        form.on('submit(LAY-app-contlist-search)', function(data){
            var field = data.field;
            table.reload('table-list', {
                url:"<?php echo url('index'); ?>",
                where: {
                    keyword: data.field
                },
                page:{
                    curr:1
                }
            });


        });
    });
</script>


<script>

    /*添加*/
    function layer_add(title,url){
        
        var indexOpen = layer.open({
            type: 2,
            title: title,
            closeBtn: 1, //是否显示关闭按钮
            area: ['100%','80%'],
            anim: 2,  //动画
            shadeClose: false,  //点击空白处是否关闭
            maxmin: true, //开启最大化最小化按钮
            content:[url],
        });
    }
</script>

</body>
</html>