<?php /*a:2:{s:64:"/www/wwwroot/ceshi71.cn/app/admin/view/system/config/config.html";i:1680794172;s:58:"/www/wwwroot/ceshi71.cn/app/admin/view/layout/default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/public/static/js/html5.min.js"></script>
    <script src="/public/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/public/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/public/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
  <div class="layuimini-main" id="app">

    <div class="layui-tab" lay-filter="configGroup">
      <ul class="layui-tab-title">
        <?php $groupList=\think\facade\Db::name('system_config_group')->where('status','=',1)->order('sort ASC,id ASC')->column('name','identification');
          $n=0;
        foreach($groupList as $k=>$v){
        ?>
        <li class="<?php if($n==0)echo 'layui-this'; ?>" data-identification="<?php echo htmlentities($k); ?>"><?php echo fy($v); ?></li>
<?php
$n++;
} ?>
      </ul>
      <div class="layui-tab-content">

                <form id="app-form" class="layui-form layuimini-form">
<div class="input-list">
    <?php echo $fields_str; ?>
</div>

                <div class="hr-line"></div>
                <div class="layui-form-item text-center">
                    <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit><?php echo fy('Confirm'); ?></button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm"><?php echo fy('Reset'); ?></button>
                </div>
            </form>
      </div>
    </div>
  </div>
</div>
</body>
</html>