SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `ymwl_admin`;
CREATE TABLE `ymwl_admin` (
                              `admin_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
                              `username` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员用户名',
                              `pwd` varchar(70) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员密码',
                              `salt` varchar(12) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                              `group_id` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT '分组ID',
                              `role_id` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
                              `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '直属上级ID',
                              `email` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
                              `realname` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
                              `tel` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话号码',
                              `ip` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
                              `add_time` bigint(16) unsigned DEFAULT NULL COMMENT '添加时间',
                              `is_open` tinyint(2) DEFAULT '1' COMMENT '审核状态',
                              `avatar` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像',
                              `mubiao` float(10,2) DEFAULT NULL COMMENT '当月目标业绩',
                              `ticheng` float(10,2) DEFAULT NULL COMMENT '提成点%',
                              `curgetnum` int(11) DEFAULT '0' COMMENT '当前用户使用抢的次数',
                              `curmaxnum` int(11) DEFAULT NULL COMMENT '用户当前抢的最大值',
                              `isphone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '手机号查看权限',
                              PRIMARY KEY (`admin_id`) USING BTREE,
                              KEY `username` (`username`) USING BTREE,
                              KEY `is_open` (`is_open`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='后台管理员';

INSERT INTO `ymwl_admin` (`admin_id`, `username`, `pwd`, `salt`, `group_id`, `role_id`, `parent_id`, `email`, `realname`, `tel`, `ip`, `add_time`, `is_open`, `avatar`, `mubiao`, `ticheng`, `curgetnum`, `curmaxnum`, `isphone`) VALUES
    (1,	'admin',	'106385edc7c6b7905db96de571e2c365',	'nqPjNRJx6d2G',	1,	1,	0,	'<EMAIL>',	'大C',	'13412341239',	'127.0.0.1',	1697722725,	1,	'/upload/images/20221228/bfaadea6d553f33fa215a22c6d20c7e3.jpg',	100000.00,	10.00,	7,	NULL,	'1');

DROP TABLE IF EXISTS `ymwl_admin_log`;
CREATE TABLE `ymwl_admin_log` (
                                  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员ID',
                                  `realname` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作人',
                                  `url` varchar(1500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作页面',
                                  `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '日志标题',
                                  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
                                  `ip` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'IP',
                                  `useragent` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'User-Agent',
                                  `create_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '操作时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `name` (`realname`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='操作日志表';


DROP TABLE IF EXISTS `ymwl_auth_group`;
CREATE TABLE `ymwl_auth_group` (
                                   `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '组ID',
                                   `pid` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT '父级',
                                   `title` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户组名',
                                   `rules` longtext COLLATE utf8mb4_unicode_ci COMMENT '规则',
                                   `max_customers_num` int(11) NOT NULL DEFAULT '0' COMMENT '最大客户数',
                                   `status` tinyint(1) DEFAULT '1' COMMENT '状态',
                                   `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '添加时间',
                                   `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='组织架构';

INSERT INTO `ymwl_auth_group` (`id`, `pid`, `title`, `rules`, `max_customers_num`, `status`, `create_time`, `update_time`) VALUES
                                                                                                                               (1,	0,	'超级管理组',	'*',	0,	1,	1650209082,	1654501227),
                                                                                                                               (2,	1,	'总经办',	'0,281,284,354,326,368,319,315,309,295,330,355,311,329,307,313,321,322,323,1,356,16,120,121,145,357,370,374,339,',	88,	1,	1652063340,	1679492273),
                                                                                                                               (3,	2,	'销售一部',	'0,281,284,354,326,368,319,315,309,295,330,355,311,329,307,313,321,322,323,1,356,16,120,121,145,357,370,374,339,',	60,	1,	1654441412,	1679194586),
                                                                                                                               (4,	3,	'销售一部一组',	'0,281,284,354,326,368,319,315,309,295,330,355,311,329,307,313,321,322,323,1,356,16,120,121,145,357,370,374,339,',	0,	1,	1654441535,	1679194523),
                                                                                                                               (5,	3,	'销售一部二组',	'0,281,284,354,326,368,319,315,309,295,330,355,311,329,307,313,321,322,323,1,356,16,120,121,145,357,370,374,339,',	0,	1,	1654490291,	1654490291),
                                                                                                                               (6,	1,	'财务部',	'0,281,284,354,326,368,319,315,309,295,330,355,311,329,307,313,321,322,323,1,356,16,120,121,145,357,370,374,339,',	0,	1,	1677040531,	1677040531);

DROP TABLE IF EXISTS `ymwl_auth_role`;
CREATE TABLE `ymwl_auth_role` (
                                  `id` tinyint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                  `name` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '角色名称',
                                  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '角色权限 {radio} (0:本人,1:下属, 2:本部门, 3:仅下属部门,4:本部门及下属部门,5:全部)',
                                  `rules` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '规则',
                                  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                  `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                  `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='员工角色';

INSERT INTO `ymwl_auth_role` (`id`, `name`, `type`, `rules`, `status`, `sort`, `create_time`, `update_time`) VALUES
                                                                                                                 (1,	'管理员',	5,	NULL,	1,	0,	1677229788,	1698467697),
                                                                                                                 (2,	'经理',	3,	NULL,	1,	0,	1677229813,	1679157834),
                                                                                                                 (3,	'员工',	0,	NULL,	1,	0,	1677230014,	1677230014);

DROP TABLE IF EXISTS `ymwl_auth_rule`;
CREATE TABLE `ymwl_auth_rule` (
                                  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
                                  `href` char(80) NOT NULL DEFAULT '',
                                  `title` char(20) NOT NULL DEFAULT '',
                                  `type` tinyint(1) NOT NULL DEFAULT '1',
                                  `status` tinyint(1) NOT NULL DEFAULT '1',
                                  `authopen` tinyint(2) NOT NULL DEFAULT '1' COMMENT '是否验证权限1是0否',
                                  `icon` varchar(50) DEFAULT NULL COMMENT '样式',
                                  `condition` char(100) DEFAULT '',
                                  `pid` int(5) NOT NULL DEFAULT '0' COMMENT '父栏目ID',
                                  `sort` int(11) DEFAULT '0' COMMENT '排序',
                                  `addtime` bigint(16) NOT NULL DEFAULT '0' COMMENT '添加时间',
                                  `menustatus` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否作为菜单显示1显示0不显示',
                                  `target` varchar(20) NOT NULL DEFAULT '_self' COMMENT '链接打开方式',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='权限节点';

INSERT INTO `ymwl_auth_rule` (`id`, `href`, `title`, `type`, `status`, `authopen`, `icon`, `condition`, `pid`, `sort`, `addtime`, `menustatus`, `target`) VALUES
                                                                                                                                                              (1,	'System',	'系统管理',	1,	1,	1,	'fa fa-cogs',	'',	0,	10,	1446535750,	1,	'_self'),
                                                                                                                                                              (379,	'auth.role/index',	'角色管理',	1,	1,	1,	'fa fa-address-card-o',	'',	339,	0,	0,	1,	'_self'),
                                                                                                                                                              (16,	'Auth/adminList',	'员工管理',	1,	1,	1,	'fa fa-user',	'',	356,	0,	1446535750,	1,	'_self'),
                                                                                                                                                              (17,	'auth.group/index',	'部门权限',	1,	1,	1,	'fa fa-users',	'',	339,	1,	1446535750,	1,	'_self'),
                                                                                                                                                              (18,	'Auth/adminRule',	'菜单规则',	1,	1,	1,	'fa fa-align-justify',	'',	339,	2,	1446535750,	1,	'_self'),
                                                                                                                                                              (108,	'Auth/ruleAdd',	'添加',	1,	1,	1,	'',	'',	18,	0,	1461550835,	0,	'_self'),
                                                                                                                                                              (109,	'Auth/ruleState',	'状态',	1,	1,	1,	'',	'',	18,	5,	1461550949,	0,	'_self'),
                                                                                                                                                              (110,	'Auth/ruleTz',	'验证',	1,	1,	1,	'',	'',	18,	6,	1461551129,	0,	'_self'),
                                                                                                                                                              (111,	'Auth/ruleorder',	'排序',	1,	1,	1,	'',	'',	18,	7,	1461551263,	0,	'_self'),
                                                                                                                                                              (112,	'Auth/ruleDel',	'删除',	1,	1,	1,	'',	'',	18,	4,	1461551536,	0,	'_self'),
                                                                                                                                                              (114,	'Auth/ruleEdit',	'修改',	1,	1,	1,	'',	'',	18,	2,	1461551913,	0,	'_self'),
                                                                                                                                                              (116,	'auth.group/edit',	'角色编辑',	1,	1,	1,	'',	'',	17,	3,	1461552326,	0,	'_self'),
                                                                                                                                                              (117,	'auth.group/delete',	'角色删除',	1,	1,	1,	'',	'',	17,	30,	1461552349,	0,	'_self'),
                                                                                                                                                              (118,	'Auth/groupAccess',	'角色授权',	1,	1,	1,	'',	'',	17,	40,	1461552404,	0,	'_self'),
                                                                                                                                                              (119,	'Auth/adminAdd',	'添加',	1,	1,	1,	'',	'',	16,	0,	1461553162,	0,	'_self'),
                                                                                                                                                              (120,	'Auth/adminEdit',	'修改',	1,	1,	1,	'',	'',	16,	2,	1461554130,	0,	'_self'),
                                                                                                                                                              (121,	'Auth/adminDel',	'删除',	1,	1,	1,	'',	'',	16,	4,	1461554152,	0,	'_self'),
                                                                                                                                                              (145,	'Auth/adminState',	'状态',	1,	1,	1,	'',	'',	16,	5,	1461550835,	0,	'_self'),
                                                                                                                                                              (149,	'auth.group/add',	'添加角色',	1,	1,	1,	'',	'',	17,	1,	1461550835,	0,	'_self'),
                                                                                                                                                              (151,	'Auth/groupRunaccess',	'权存',	1,	1,	1,	'',	'',	17,	50,	1461550835,	0,	'_self'),
                                                                                                                                                              (181,	'Auth/groupState',	'状态',	1,	1,	1,	'',	'',	17,	50,	1461834340,	0,	'_self'),
                                                                                                                                                              (270,	'System/email',	'邮箱配置',	1,	1,	1,	'fa fa-envelope-square',	'',	1,	20,	**********,	1,	'_self'),
                                                                                                                                                              (281,	'crm',	'CRM',	1,	1,	1,	'fa fa-address-card',	'',	0,	0,	**********,	1,	'_self'),
                                                                                                                                                              (284,	'',	'客户管理',	1,	1,	1,	'fa fa-drivers-license-o',	'',	281,	2,	**********,	1,	'_self'),
                                                                                                                                                              (286,	'crm.rank/index',	'客户等级',	1,	1,	1,	'fa fa-grav',	'',	284,	50,	**********,	1,	'_self'),
                                                                                                                                                              (287,	'crm.status/index',	'客户状态',	1,	1,	1,	'fa fa-newspaper-o',	'',	284,	50,	**********,	1,	'_self'),
                                                                                                                                                              (326,	'crm.customer/export',	'导出客户',	1,	1,	1,	'',	'',	354,	50,	**********,	0,	'_self'),
                                                                                                                                                              (289,	'crm.source/index',	'客户来源',	1,	1,	1,	'fa fa-blind',	'',	284,	50,	1554704322,	1,	'_self'),
                                                                                                                                                              (295,	'crm.customer/alter_pr_user',	'转移客户',	1,	1,	1,	'fa fa-clock-o',	'',	284,	50,	1563244558,	0,	'_self'),
                                                                                                                                                              (307,	'Order',	'业绩订单',	1,	1,	1,	'fa fa-shopping-cart',	'',	0,	4,	1585592104,	1,	'_self'),
                                                                                                                                                              (308,	'Order/index',	'订单列表',	1,	1,	1,	'fa fa-shopping-cart',	'',	307,	50,	1585593076,	1,	'_self'),
                                                                                                                                                              (309,	'crm.hangye/index',	'客户行业',	1,	1,	1,	'fa fa-align-center',	'',	284,	49,	**********,	1,	'_self'),
                                                                                                                                                              (311,	'crm.customer/issuccess',	'成交客户',	1,	1,	1,	'fa fa-drivers-license',	'',	281,	3,	**********,	1,	'_self'),
                                                                                                                                                              (313,	'order/personindex',	'我的订单',	1,	1,	1,	'fa fa-shopping-basket',	'',	307,	50,	**********,	1,	'_self'),
                                                                                                                                                              (315,	'crm.customer/import',	'导入客户',	1,	1,	1,	'',	'',	354,	50,	**********,	0,	'_self'),
                                                                                                                                                              (319,	'crm.customer/delete',	'删除',	1,	1,	1,	'',	'',	354,	50,	**********,	0,	'_self'),
                                                                                                                                                              (320,	'Process.todo/del',	'消息删除',	1,	1,	1,	'',	'',	323,	50,	1652281948,	0,	'_self'),
                                                                                                                                                              (321,	'Order/mydel',	'删除',	1,	1,	1,	'',	'',	313,	50,	1652348493,	0,	'_self'),
                                                                                                                                                              (322,	'process',	'流程信息',	1,	1,	1,	'fa fa-send',	'',	0,	9,	1652280509,	1,	'_self'),
                                                                                                                                                              (323,	'process.todo/index',	'消息提醒',	1,	1,	1,	'fa fa-comments',	'',	322,	49,	1652281851,	1,	'_self'),
                                                                                                                                                              (324,	'Order/editAudit',	'订单审核',	1,	1,	1,	'',	'',	308,	50,	1652500847,	0,	'_self'),
                                                                                                                                                              (325,	'Order/del',	'订单删除',	1,	1,	1,	'',	'',	308,	50,	1652519532,	0,	'_self'),
                                                                                                                                                              (327,	'system.models/index',	'模型管理',	1,	1,	1,	'fa fa-cogs',	'',	1,	50,	1654864156,	0,	'_self'),
                                                                                                                                                              (328,	'system.fields/index',	'字段自定义设置',	1,	1,	1,	'fa fa-certificate',	'',	327,	50,	1654866815,	0,	'_self'),
                                                                                                                                                              (329,	'crm.customer/seas',	'公海客户',	1,	1,	1,	'fa fa-industry',	'',	281,	5,	1655142083,	1,	'_self'),
                                                                                                                                                              (330,	'',	'跟进管理',	1,	1,	1,	'fa fa-comments-o',	'',	284,	50,	1656402926,	1,	'_self'),
                                                                                                                                                              (331,	'crm.RecordType/index',	'跟进类型',	1,	1,	1,	'fa fa-amazon',	'',	330,	50,	1656403098,	1,	'_self'),
                                                                                                                                                              (332,	'crm.record/delete',	'跟进记录删除',	1,	1,	1,	'',	'',	355,	30,	1656431970,	0,	'_self'),
                                                                                                                                                              (333,	'crm.RecordType/add',	'跟进类型添加',	1,	1,	1,	'',	'',	331,	50,	1656476581,	0,	'_self'),
                                                                                                                                                              (334,	'crm.RecordType/delete',	'跟进类型删除',	1,	1,	1,	'',	'',	331,	50,	1656476759,	0,	'_self'),
                                                                                                                                                              (335,	'crm.RecordType/edit',	'跟进类型编辑',	1,	1,	1,	'',	'',	331,	50,	1656476800,	0,	'_self'),
                                                                                                                                                              (336,	'order/analytics',	'业绩统计',	1,	1,	1,	'fa fa-university',	'',	307,	50,	1656488258,	1,	'_self'),
                                                                                                                                                              (337,	'system.uploadfile/index',	'上传管理',	1,	1,	1,	'fa fa-arrow-up',	'',	1,	50,	1658029929,	1,	'_self'),
                                                                                                                                                              (338,	'system.uploadfile/delete',	'附件删除',	1,	1,	1,	'',	'',	337,	50,	1658030016,	0,	'_self'),
                                                                                                                                                              (339,	'',	'权限管理',	1,	1,	1,	'fa fa-anchor',	'',	1,	10,	1659541621,	1,	'_self'),
                                                                                                                                                              (340,	'Product/index',	'产品列表',	1,	1,	1,	'fa fa-bars',	'',	344,	50,	1659710978,	1,	'_self'),
                                                                                                                                                              (341,	'product.type/index',	'产品分类',	1,	1,	1,	'fa fa-bars',	'',	344,	50,	1659711095,	1,	'_self'),
                                                                                                                                                              (342,	'product.type/add',	'添加',	1,	1,	1,	'',	'',	341,	50,	1659754378,	0,	'_self'),
                                                                                                                                                              (343,	'product.type/edit',	'编辑',	1,	1,	1,	'',	'',	341,	50,	1659754411,	0,	'_self'),
                                                                                                                                                              (344,	'',	'产品管理',	1,	1,	1,	'fa fa-paint-brush',	'',	281,	39,	1659754525,	1,	'_self'),
                                                                                                                                                              (345,	'product.type/delete',	'删除',	1,	1,	1,	'',	'',	341,	50,	1659754629,	0,	'_self'),
                                                                                                                                                              (346,	'product/add',	'添加',	1,	1,	1,	'',	'',	340,	50,	1659754822,	0,	'_self'),
                                                                                                                                                              (347,	'product/edit',	'编辑',	1,	1,	1,	'',	'',	340,	50,	1659754851,	0,	'_self'),
                                                                                                                                                              (349,	'product/delete',	'删除',	1,	1,	1,	'',	'',	340,	50,	1659754914,	0,	'_self'),
                                                                                                                                                              (350,	'',	'商业机会',	1,	1,	1,	'fa fa-btc',	'',	281,	50,	1660057859,	1,	'_self'),
                                                                                                                                                              (351,	'business/index',	'商机列表',	1,	1,	1,	'fa fa-eye',	'',	350,	50,	1660058026,	1,	'_self'),
                                                                                                                                                              (352,	'business.record/index',	'跟进记录',	1,	1,	1,	'fa fa-comments-o',	'',	350,	50,	1663210573,	1,	'_self'),
                                                                                                                                                              (353,	'business.record/add',	'添加',	1,	1,	1,	'fa fa-plus',	'',	352,	50,	1663244355,	0,	'_self'),
                                                                                                                                                              (354,	'crm.customer/index',	'客户列表',	1,	1,	1,	'fa fa-list-ul',	'',	284,	1,	1663575903,	1,	'_self'),
                                                                                                                                                              (355,	'crm.record/index',	'跟进记录',	1,	1,	1,	'fa fa-commenting-o',	'',	330,	1,	1663575959,	1,	'_self'),
                                                                                                                                                              (356,	'',	'管理员管理',	1,	1,	1,	'fa fa-address-book',	'',	1,	5,	1666001409,	1,	'_self'),
                                                                                                                                                              (357,	'login.log/index',	'登录日志',	1,	1,	1,	'fa fa-paper-plane',	'',	356,	50,	1666001966,	1,	'_self'),
                                                                                                                                                              (358,	'login.log/delete',	'删除',	1,	1,	1,	'',	'',	357,	50,	**********,	0,	'_self'),
                                                                                                                                                              (359,	'weixin',	'微信',	1,	1,	1,	'fa fa-weixin',	'',	0,	50,	**********,	1,	'_self'),
                                                                                                                                                              (360,	'/plugins/wechat/admin/account/index',	'公众号配置',	1,	1,	1,	'fa fa-weixin',	'',	362,	30,	**********,	1,	'_self'),
                                                                                                                                                              (361,	'/plugins/wechat/admin/menu/index',	'公众号菜单',	1,	1,	1,	'fa fa-map-o',	'',	362,	50,	**********,	1,	'_self'),
                                                                                                                                                              (362,	'',	'公众号',	1,	1,	1,	'fa fa-wechat',	'',	359,	30,	**********,	1,	'_self'),
                                                                                                                                                              (369,	'crm.hangye/delete',	'删除',	1,	1,	1,	'',	'',	309,	50,	**********,	0,	'_self'),
                                                                                                                                                              (368,	'crm.customer/edit',	'编辑',	1,	1,	1,	'',	'',	354,	50,	1674453997,	0,	'_self'),
                                                                                                                                                              (367,	'crm.hangye/edit',	'编辑',	1,	1,	1,	'',	'',	309,	50,	1674402033,	0,	'_self'),
                                                                                                                                                              (366,	'crm.hangye/add',	'添加',	1,	1,	1,	'',	'',	309,	50,	1674402002,	0,	'_self'),
                                                                                                                                                              (370,	'admin.log/index',	'操作日志',	1,	1,	1,	'fa fa-list-alt',	'',	356,	50,	0,	1,	'_self'),
                                                                                                                                                              (374,	'admin.log/detail',	'详情',	1,	1,	1,	'',	'',	370,	50,	1674461409,	0,	'_self'),
                                                                                                                                                              (373,	'admin.log/delete',	'删除',	1,	1,	1,	NULL,	'',	370,	0,	0,	0,	'_self'),
                                                                                                                                                              (3,	'',	'网站配置',	1,	1,	1,	'fa fa-stack-overflow',	'',	1,	50,	1675052285,	1,	'_self'),
                                                                                                                                                              (4,	'system.config_group/index',	'配置分组',	1,	1,	1,	'fa fa-object-ungroup',	'',	3,	100,	0,	1,	'_self'),
                                                                                                                                                              (5,	'system.config_group/add',	'添加',	1,	1,	1,	NULL,	'',	4,	0,	0,	0,	'_self'),
                                                                                                                                                              (6,	'system.config_group/edit',	'编辑',	1,	1,	1,	NULL,	'',	4,	0,	0,	0,	'_self'),
                                                                                                                                                              (7,	'system.config_group/delete',	'删除',	1,	1,	1,	NULL,	'',	4,	0,	0,	0,	'_self'),
                                                                                                                                                              (8,	'system.config/index',	'配置管理',	1,	1,	1,	'fa fa-object-group',	'',	3,	50,	0,	1,	'_self'),
                                                                                                                                                              (9,	'system.config/add',	'添加',	1,	1,	1,	NULL,	'',	8,	0,	0,	0,	'_self'),
                                                                                                                                                              (10,	'system.config/edit',	'编辑',	1,	1,	1,	NULL,	'',	8,	0,	0,	0,	'_self'),
                                                                                                                                                              (11,	'system.config/delete',	'删除',	1,	1,	1,	NULL,	'',	8,	0,	0,	0,	'_self'),
                                                                                                                                                              (12,	'system.config/config',	'系统设置',	1,	1,	1,	'fa fa-asterisk',	'',	3,	0,	1675046065,	1,	'_self'),
                                                                                                                                                              (375,	'crm.seas/robclient',	'领取',	1,	1,	1,	'',	'',	329,	50,	1676518220,	0,	'_self'),
                                                                                                                                                              (376,	'crm.customer/to_move_gh',	'移入公海',	1,	1,	1,	'',	'',	354,	50,	1676518625,	0,	'_self'),
                                                                                                                                                              (380,	'auth.role/add',	'添加',	1,	1,	1,	NULL,	'',	379,	0,	0,	0,	'_self'),
                                                                                                                                                              (381,	'auth.role/edit',	'编辑',	1,	1,	1,	NULL,	'',	379,	0,	0,	0,	'_self'),
                                                                                                                                                              (382,	'auth.role/delete',	'删除',	1,	1,	1,	NULL,	'',	379,	0,	0,	0,	'_self'),
                                                                                                                                                              (383,	'product/modify',	'状态更改',	1,	1,	1,	'',	'',	340,	50,	1681884078,	0,	'_self'),
                                                                                                                                                              (384,	'',	' 数据分析',	1,	1,	1,	'fa fa-bar-chart-o',	'',	281,	50,	1685613293,	1,	'_self'),
                                                                                                                                                              (385,	'analysis.admin/index',	'客户量分析',	1,	1,	1,	'fa fa-address-card-o',	'',	384,	50,	1685613512,	1,	'_self'),
                                                                                                                                                              (386,	'analysis.admin/record',	'跟进分析',	1,	1,	1,	'fa fa-comment-o',	'',	384,	50,	1685613866,	1,	'_self'),
                                                                                                                                                              (387,	'customer.changes_record/index',	'变动记录',	1,	1,	1,	'fa fa-hand-o-up',	'',	284,	101,	0,	1,	'_self'),
                                                                                                                                                              (388,	'customer.changes_record/add',	'添加',	1,	1,	1,	NULL,	'',	387,	0,	0,	0,	'_self'),
                                                                                                                                                              (389,	'customer.changes_record/edit',	'编辑',	1,	1,	1,	NULL,	'',	387,	0,	0,	0,	'_self'),
                                                                                                                                                              (390,	'customer.changes_record/delete',	'删除',	1,	1,	1,	NULL,	'',	387,	0,	0,	0,	'_self'),
                                                                                                                                                              (391,	'open.apps/index',	'开发应用',	1,	1,	1,	'fa fa-cubes',	'',	281,	50,	1667739247,	1,	'_self'),
                                                                                                                                                              (392,	'open.apps/add',	'添加',	1,	1,	1,	'',	'',	391,	50,	1708136042,	0,	'_self'),
                                                                                                                                                              (393,	'open.apps/edit',	'编辑',	1,	1,	1,	'',	'',	391,	50,	1708136092,	0,	'_self'),
                                                                                                                                                              (394,	'open.apps/delete',	'删除',	1,	1,	1,	'',	'',	391,	50,	1708136265,	0,	'_self'),
                                                                                                                                                              (395,	'open.apps/doc',	'对接说明',	1,	1,	1,	'',	'',	391,	50,	1709133635,	0,	'_self'),
    (396,	'crm.customer/success_bgshow',	'数据大屏',	1,	1,	1,	'',	'',	311,	50,	1712566844,	0,	'_self');

DROP TABLE IF EXISTS `ymwl_business`;
CREATE TABLE `ymwl_business` (
                                 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                 `customer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '客户ID',
                                 `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商机名称',
                                 `money` decimal(18,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '预算金额',
                                 `total_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '销售总金额',
                                 `next_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下次联系时间 {datetime}',
                                 `is_end` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态 {radio} (0:洽淡中,1:成交,2:失败,3:无效)',
                                 `deal_time` int(11) DEFAULT NULL COMMENT '预计成交日期',
                                 `last_up_records` varchar(600) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `last_up_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后跟进时间',
                                 `create_admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
                                 `owner_admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '负责人ID',
                                 `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                 `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                 `create_username` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建人',
                                 `owner_username` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '负责人',
                                 `remark` varchar(600) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `owner_admin_id` (`owner_admin_id`) USING BTREE,
                                 KEY `create_admin_id` (`create_admin_id`) USING BTREE,
                                 KEY `is_end` (`is_end`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='商机表';

INSERT INTO `ymwl_business` (`id`, `customer_id`, `name`, `money`, `total_price`, `next_time`, `is_end`, `deal_time`, `last_up_records`, `last_up_time`, `create_admin_id`, `owner_admin_id`, `create_time`, `update_time`, `create_username`, `owner_username`, `remark`) VALUES
    (1,	1,	'第一个商机',	0.00,	6799.00,	1704211200,	0,	NULL,	'',	0,	1,	1,	1705854605,	1705854605,	'',	'',	'');

DROP TABLE IF EXISTS `ymwl_business_product`;
CREATE TABLE `ymwl_business_product` (
                                         `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                         `business_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商机id',
                                         `product_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '产品ID',
                                         `product_extend` varchar(1200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品扩展',
                                         `sale_price` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '售价',
                                         `nums` int(11) unsigned DEFAULT '0' COMMENT '数量',
                                         `discount` decimal(10,2) DEFAULT NULL COMMENT '折扣',
                                         `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
                                         `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                         `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='商机产品关联表';

INSERT INTO `ymwl_business_product` (`id`, `business_id`, `product_id`, `product_extend`, `sale_price`, `nums`, `discount`, `remark`, `create_time`, `update_time`) VALUES
                                                                                                                                                                        (1,	1,	0,	'{\"name\":\"Apple iPhone 13 Pro (A2639) 256GB 石墨色 支持移动联通电信5G 双卡双待手机\",\"specification\":\"银色256G内存*1台\",\"model\":\"Apple iPhone 13 Pro (A2639) \",\"cost_price\":\"5900.00\"}',	6799.00,	1,	0.00,	'',	NULL,	NULL),
                                                                                                                                                                        (2,	1,	1,	'{\"name\":\"Apple iPhone 13 Pro (A2639) 256GB 石墨色 支持移动联通电信5G 双卡双待手机\",\"specification\":\"银色256G内存*1台\",\"model\":\"Apple iPhone 13 Pro (A2639) \",\"cost_price\":\"5900.00\"}',	6799.00,	1,	0.00,	'',	NULL,	NULL);

DROP TABLE IF EXISTS `ymwl_business_record`;
CREATE TABLE `ymwl_business_record` (
                                        `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                        `create_admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '跟进者ID',
                                        `create_username` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '跟进者',
                                        `business_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '被跟进的商机ID',
                                        `business_name` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商机名称',
                                        `content` text COLLATE utf8mb4_unicode_ci COMMENT '跟进内容',
                                        `create_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '跟进时间',
                                        `next_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下次跟进时间',
                                        `record_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '跟进类型',
                                        `attachs` varchar(3000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='商机跟进记录表';


DROP TABLE IF EXISTS `ymwl_category`;
CREATE TABLE `ymwl_category` (
                                 `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
                                 `catname` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `catdir` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `parentdir` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `pid` smallint(5) unsigned NOT NULL DEFAULT '0',
                                 `moduleid` tinyint(2) unsigned NOT NULL DEFAULT '0',
                                 `module` char(24) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `arrparentid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `arrchildid` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `type` tinyint(1) unsigned NOT NULL DEFAULT '0',
                                 `title` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `keywords` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `sort` smallint(5) unsigned NOT NULL DEFAULT '0',
                                 `ishtml` tinyint(1) unsigned NOT NULL DEFAULT '0',
                                 `ismenu` tinyint(1) unsigned NOT NULL DEFAULT '0',
                                 `hits` int(10) unsigned NOT NULL DEFAULT '0',
                                 `image` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `child` tinyint(1) unsigned NOT NULL DEFAULT '0',
                                 `url` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `template_list` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `template_show` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `pagesize` tinyint(2) unsigned NOT NULL DEFAULT '0',
                                 `readgroup` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                 `listtype` tinyint(1) unsigned NOT NULL DEFAULT '0',
                                 `lang` tinyint(1) unsigned NOT NULL DEFAULT '0',
                                 `is_show` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否预览',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `listorder` (`sort`) USING BTREE,
                                 KEY `pid` (`pid`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;


DROP TABLE IF EXISTS `ymwl_config`;
CREATE TABLE `ymwl_config` (
                               `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT COMMENT '表id',
                               `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置的key键名',
                               `value` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置的val值',
                               `inc_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置分组',
                               `desc` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
                               PRIMARY KEY (`id`) USING BTREE,
                               KEY `name` (`name`) USING BTREE,
                               KEY `inc_type` (`inc_type`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

INSERT INTO `ymwl_config` (`id`, `name`, `value`, `inc_type`, `desc`) VALUES
                                                                          (16,	'is_mark',	'0',	'water',	'0'),
                                                                          (17,	'mark_txt',	'',	'water',	'0'),
                                                                          (18,	'mark_img',	'/public/upload/public/2017/01-20/10cd966bd5f3549833c09a5c9700a9b8.jpg',	'water',	'0'),
                                                                          (19,	'mark_width',	'',	'water',	'0'),
                                                                          (20,	'mark_height',	'',	'water',	'0'),
                                                                          (21,	'mark_degree',	'54',	'water',	'0'),
                                                                          (22,	'mark_quality',	'56',	'water',	'0'),
                                                                          (23,	'sel',	'9',	'water',	'0'),
                                                                          (24,	'sms_url',	'',	'sms',	'0'),
                                                                          (25,	'sms_user',	'',	'sms',	'0'),
                                                                          (26,	'sms_pwd',	'访问密码 080e',	'sms',	'0'),
                                                                          (27,	'regis_sms_enable',	'1',	'sms',	'0'),
                                                                          (28,	'sms_time_out',	'1200',	'sms',	'0'),
                                                                          (38,	'__hash__',	'8d9fea07e44955760d3407524e469255_6ac8706878aa807db7ffb09dd0b02453',	'sms',	'0'),
                                                                          (56,	'sms_appkey',	'123456789',	'sms',	'0'),
                                                                          (57,	'sms_secretKey',	'123456789',	'sms',	'0'),
                                                                          (58,	'sms_product',	'BLZX',	'sms',	'0'),
                                                                          (59,	'sms_templateCode',	'SMS_101234567890',	'sms',	'0'),
                                                                          (60,	'smtp_server',	'smtp.qq.com',	'smtp',	'0'),
                                                                          (61,	'smtp_port',	'4651',	'smtp',	'0'),
                                                                          (62,	'smtp_user',	'11',	'smtp',	'0'),
                                                                          (63,	'smtp_pwd',	'22',	'smtp',	'0'),
                                                                          (64,	'regis_smtp_enable',	'1',	'smtp',	'0'),
                                                                          (65,	'test_eamil',	'33',	'smtp',	'0'),
                                                                          (70,	'forget_pwd_sms_enable',	'1',	'sms',	'0'),
                                                                          (71,	'bind_mobile_sms_enable',	'1',	'sms',	'0'),
                                                                          (72,	'order_add_sms_enable',	'1',	'sms',	'0'),
                                                                          (73,	'order_pay_sms_enable',	'1',	'sms',	'0'),
                                                                          (74,	'order_shipping_sms_enable',	'1',	'sms',	'0'),
                                                                          (88,	'email_id',	'CRM',	'smtp',	'0'),
                                                                          (89,	'test_eamil_info',	' 您好！这是一封来自CRM系统测试邮件！',	'smtp',	'0'),
                                                                          (90,	'unit',	' ',	'product',	' ');

DROP TABLE IF EXISTS `ymwl_crm_client_order`;
CREATE TABLE `ymwl_crm_client_order` (
                                         `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `customer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单对应客户ID',
                                         `head_admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '负责人ID',
                                         `pr_user` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '负责人名称',
                                         `orderno` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
                                         `cphone` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号',
                                         `cname` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户名称',
                                         `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
                                         `freight` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
                                         `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '业绩状态 0 待审核 -1 审核不通过 1 审核通过',
                                         `audit_feedback` varchar(600) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审核反馈',
                                         `create_time` bigint(20) DEFAULT NULL COMMENT '添加时间',
                                         `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                         `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                         `ticheng` float(100,0) DEFAULT NULL COMMENT '提成金额',
                                         `tudo_id` int(10) unsigned DEFAULT '0' COMMENT '对应流程ID',
                                         `dingdanpingzheng` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '订单凭证{imgs}[show:1,edit:1,sort:100]',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `head_admin_id` (`head_admin_id`) USING BTREE,
                                         KEY `pr_user` (`pr_user`) USING BTREE,
                                         KEY `orderno` (`orderno`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;


DROP TABLE IF EXISTS `ymwl_crm_contract`;
CREATE TABLE `ymwl_crm_contract` (
                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                     `customer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '客户ID',
                                     `business_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商机ID',
                                     `contacts_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户签约人',
                                     `source` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来源',
                                     `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同名称',
                                     `numbering` varchar(120) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同编号',
                                     `order_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下单时间',
                                     `money` decimal(18,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '合同金额',
                                     `total_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '产品总金额',
                                     `return_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '已收到款项',
                                     `discount_rate` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '整单折扣',
                                     `check_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过',
                                     `flow_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '审核流程ID',
                                     `step_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '审核步骤ID',
                                     `check_admin_id` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '已经审批人IDs',
                                     `flow_admin_id` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '当前需要审批的人',
                                     `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
                                     `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
                                     `order_admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '公司签约人',
                                     `create_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
                                     `owner_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '负责人ID',
                                     `ro_user_id` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '只读权限',
                                     `rw_user_id` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '读写权限',
                                     `next_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下次联系时间',
                                     `create_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
                                     `update_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
                                     `expire_handle` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0合同过期未处理1已续签2不再合作',
                                     `invoice_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '已开票金额',
                                     `remark` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `numbering` (`numbering`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='合同表';


DROP TABLE IF EXISTS `ymwl_crm_contract_order`;
CREATE TABLE `ymwl_crm_contract_order` (
                                           `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `orderid` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单ID',
                                           `user_id` int(10) unsigned DEFAULT '0' COMMENT '会员ID',
                                           `amount` double(10,2) unsigned DEFAULT '0.00' COMMENT '订单金额',
                                           `payamount` double(10,2) unsigned DEFAULT '0.00' COMMENT '支付金额',
                                           `paytype` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付类型',
                                           `paytime` int(10) DEFAULT NULL COMMENT '支付时间',
                                           `ip` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
                                           `useragent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'UserAgent',
                                           `memo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                           `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                           `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                           `status` enum('created','paid','expired') COLLATE utf8mb4_unicode_ci DEFAULT 'created' COMMENT '状态',
                                           `receivables_id` int(11) DEFAULT NULL COMMENT '回款ID',
                                           `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
                                           `ordertype` enum('renew','pay') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单类型',
                                           `renew_day` int(11) DEFAULT '0' COMMENT '续费天数（总）',
                                           `number` int(11) DEFAULT '1' COMMENT '下单数量',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='支付表';


DROP TABLE IF EXISTS `ymwl_crm_contract_product`;
CREATE TABLE `ymwl_crm_contract_product` (
                                             `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                             `contract_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '合同id',
                                             `product_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '产品ID',
                                             `product_extend` varchar(1200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品扩展',
                                             `sale_price` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '售价',
                                             `nums` int(11) unsigned DEFAULT '0' COMMENT '数量',
                                             `discount` decimal(10,2) DEFAULT NULL COMMENT '折扣',
                                             `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
                                             `create_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
                                             `update_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='合同产品关联表';

INSERT INTO `ymwl_crm_contract_product` (`id`, `contract_id`, `product_id`, `product_extend`, `sale_price`, `nums`, `discount`, `remark`, `create_time`, `update_time`) VALUES
    (1,	4,	1,	'{\"name\":\"Apple iPhone 13 Pro (A2639) 256GB 石墨色 支持移动联通电信5G 双卡双待手机\",\"specification\":\"银色256G内存*1台\",\"model\":\"Apple iPhone 13 Pro (A2639) \",\"cost_price\":\"5900.00\"}',	6799.00,	1,	0.00,	'',	0,	0);

DROP TABLE IF EXISTS `ymwl_crm_contract_receivables`;
CREATE TABLE `ymwl_crm_contract_receivables` (
                                                 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                                 `number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '回款编号',
                                                 `customer_id` int(11) unsigned NOT NULL COMMENT '客户ID',
                                                 `contract_id` int(11) unsigned NOT NULL COMMENT '合同ID',
                                                 `return_time` int(11) unsigned DEFAULT NULL COMMENT '回款日期',
                                                 `account_id` int(10) NOT NULL COMMENT '收款账户ID',
                                                 `money` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '回款金额',
                                                 `check_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过',
                                                 `flow_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核流程ID',
                                                 `step_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核步骤排序ID',
                                                 `check_admin_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审批人IDs',
                                                 `flow_admin_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '流程审批人ID',
                                                 `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                                 `create_user_id` int(10) unsigned NOT NULL COMMENT '创建人ID',
                                                 `owner_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '负责人ID',
                                                 `order_admin_id` int(11) unsigned DEFAULT NULL COMMENT '合同签约人',
                                                 `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                                 `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                                 `pay_status` tinyint(1) DEFAULT '0' COMMENT '在线支付状态1已付款0未付款',
                                                 `pay_type` tinyint(1) DEFAULT '1' COMMENT '收款方式1默认2在线收款3续费',
                                                 `pay_token` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问支付的token',
                                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='回款表';


DROP TABLE IF EXISTS `ymwl_crm_customer`;
CREATE TABLE `ymwl_crm_customer` (
                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `name` varchar(99) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户名称{input}[show:1,edit:0,search:1,export:1,sort:2]',
                                     `contact` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户联系人{input}[show:1,edit:0,search:1,export:1,sort:5]',
                                     `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系电话{tel}[show:1,edit:0,search:1,export:1,sort:3]',
                                     `email` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮箱{input}[show:1,edit:0,search:1,export:1,sort:6]',
                                     `last_up_records` varchar(3000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '最新跟进记录{input}[show:1,sort:100]',
                                     `last_up_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '实际跟进时间{datetime}[sort:100]',
                                     `next_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下次跟进时间{datetime}[show:1,search:1,sort:100]',
                                     `remark` varchar(600) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注{textarea}[show:1,edit:0,search:1,export:1,sort:1000]',
                                     `wechat` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '微信号{input}[show:1,edit:0,search:1,export:1,sort:100]',
                                     `at_user` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建人{input}[show:1,search:1,sort:100]',
                                     `create_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间{datetime}[show:1,search:1,sort:100]',
                                     `update_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间{datetime}[show:1,search:1,sort:100]',
                                     `head_admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '负责人ID',
                                     `pr_user` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '负责人{input}[search:1,sort:100]',
                                     `pr_user_bef` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '前负责人{input}[search:1,sort:100]',
                                     `to_kh_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '转客户时间{datetime}[search:1,sort:100]',
                                     `to_gh_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '转公海时间{datetime}[show:1,search:1,sort:100]',
                                     `kh_hangye` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '行业类别{lselect}[show:1,edit:0,search:1,export:1,sort:100,join_table:crm_hangye,foreign_key:name]',
                                     `kh_rank` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户等级{lselect}[show:1,edit:0,search:1,export:1,sort:100,join_table:crm_rank,foreign_key:name]',
                                     `kh_status` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户状态{lselect}[show:1,edit:0,search:1,export:1,sort:999,join_table:crm_status,foreign_key:name]',
                                     `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0-线索，1-客户，2-公海，3-删除',
                                     `issuccess` tinyint(4) DEFAULT '0' COMMENT '是否成交{select}(0:未成交,1:已成交)[show:1,search:1,sort:100]',
                                     `success_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '成交时间',
                                     `source` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户来源{lselect}[show:1,edit:0,search:1,export:1,sort:100,join_table:crm_source,foreign_key:name]',
                                     `area` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '地区{district}[sort:88]',
                                     `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '详细地址{input}[sort:99]',
                                     `share_admin_ids` varchar(1200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分享给其他同事ID集合',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `at_user` (`at_user`) USING BTREE,
                                     KEY `head_admin_id` (`head_admin_id`) USING BTREE,
                                     KEY `pr_user` (`pr_user`) USING BTREE,
                                     KEY `kh_status` (`kh_status`) USING BTREE,
                                     KEY `status` (`status`) USING BTREE,
                                     KEY `issuccess` (`issuccess`) USING BTREE,
                                     KEY `phone` (`phone`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='客户表';


DROP TABLE IF EXISTS `ymwl_crm_grab`;
CREATE TABLE `ymwl_crm_grab` (
                                 `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作者',
                                 `customer_ids` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
                                 `createtime` bigint(16) unsigned DEFAULT NULL COMMENT '记录时间',
                                 `nums` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '一次领取的条数',
                                 KEY `admin_id` (`admin_id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=FIXED COMMENT='公海客户转我的客户记录表';

INSERT INTO `ymwl_crm_grab` (`admin_id`, `customer_ids`, `createtime`, `nums`) VALUES
                                                                                   (1,	'0',	1702812550,	2),
                                                                                   (1,	'2,1',	1702812934,	2);

DROP TABLE IF EXISTS `ymwl_crm_hangye`;
CREATE TABLE `ymwl_crm_hangye` (
                                   `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
                                   `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '行业类别',
                                   `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                   `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                   `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                   `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='行业类别';

INSERT INTO `ymwl_crm_hangye` (`id`, `name`, `status`, `sort`, `create_time`, `update_time`) VALUES
                                                                                                 (1,	'金融业',	1,	0,	NULL,	1677766103),
                                                                                                 (2,	'房地产',	1,	1,	NULL,	1654958795),
                                                                                                 (3,	'商业服务',	1,	3,	NULL,	1654958810),
                                                                                                 (4,	'贸易',	1,	5,	NULL,	1654958823),
                                                                                                 (5,	'运输物流',	1,	6,	NULL,	1654958835),
                                                                                                 (6,	'服务业',	1,	7,	NULL,	1654958846),
                                                                                                 (7,	'文化传媒',	1,	8,	NULL,	1654958865),
                                                                                                 (8,	'IT/互联网',	1,	9,	NULL,	1654958873),
                                                                                                 (9,	'其它',	1,	10,	NULL,	1654958880);

DROP TABLE IF EXISTS `ymwl_crm_rank`;
CREATE TABLE `ymwl_crm_rank` (
                                 `id` tinyint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                 `name` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '等级名称',
                                 `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                 `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                 `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                 `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='客户等级';

INSERT INTO `ymwl_crm_rank` (`id`, `name`, `status`, `sort`, `create_time`, `update_time`) VALUES
                                                                                               (1,	'普通客户',	1,	3,	1654935490,	1667131444),
                                                                                               (2,	'重点客户',	1,	2,	1654935506,	1654935786),
                                                                                               (3,	'优质客户',	1,	1,	1654935523,	1654935541);

DROP TABLE IF EXISTS `ymwl_crm_record`;
CREATE TABLE `ymwl_crm_record` (
                                   `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID{input}[show:1,sort:9999,foreign_key:name,status:1]',
                                   `khname` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户名称',
                                   `khphone` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系电话',
                                   `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '跟进者ID',
                                   `pr_user` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '跟进者',
                                   `customer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '被跟进的客户ID',
                                   `content` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '跟进内容',
                                   `create_time` bigint(16) unsigned NOT NULL DEFAULT '0' COMMENT '跟进时间',
                                   `next_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下次跟进时间',
                                   `record_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '跟进类型',
                                   `attachs` varchar(3000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '相关附件',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `admin_id` (`admin_id`) USING BTREE,
                                   KEY `pr_user` (`pr_user`) USING BTREE,
                                   KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='跟进记录表';

INSERT INTO `ymwl_crm_record` (`id`, `khname`, `khphone`, `admin_id`, `pr_user`, `customer_id`, `content`, `create_time`, `next_time`, `record_type`, `attachs`) VALUES
                                                                                                                                                                     (1,	'陶先生',	'15827375063',	1,	'admin',	5,	'jjj',	1679413195,	1679499540,	'微信',	'/upload/67/c1c6f8e773d122ba8ac4adf017b354.jpg|/upload/c6/dcc24cfac25dc4b91b87b65d05fcfe.doc'),
                                                                                                                                                                     (2,	'陶先生',	'15827375063',	1,	'admin',	5,	'2222',	1679413289,	1679499660,	'上门拜访',	''),
                                                                                                                                                                     (3,	'陶先生',	'15827375063',	1,	'admin',	5,	'1111',	1680652687,	1680652620,	'微信',	''),
                                                                                                                                                                     (4,	'未知',	'13387521368',	2,	'159159',	7,	'6666',	1680652805,	1680739140,	'微信',	'');

DROP TABLE IF EXISTS `ymwl_crm_record_type`;
CREATE TABLE `ymwl_crm_record_type` (
                                        `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                        `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型名称',
                                        `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                        `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='跟进类型';

INSERT INTO `ymwl_crm_record_type` (`id`, `name`, `sort`, `status`) VALUES
                                                                        (1,	'电话',	0,	1),
                                                                        (2,	'上门拜访',	0,	1),
                                                                        (3,	'微信',	0,	1),
                                                                        (4,	'短信',	0,	1);

DROP TABLE IF EXISTS `ymwl_crm_reply`;
CREATE TABLE `ymwl_crm_reply` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                  `comment_id` int(11) NOT NULL COMMENT '评论ID',
                                  `from_user_id` int(11) NOT NULL COMMENT '回复人',
                                  `to_user_id` int(11) NOT NULL COMMENT '回复对象',
                                  `reply_msg` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回复内容',
                                  `create_date` bigint(20) NOT NULL COMMENT '回复时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;


DROP TABLE IF EXISTS `ymwl_crm_source`;
CREATE TABLE `ymwl_crm_source` (
                                   `id` tinyint(4) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                   `name` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户来源',
                                   `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                   `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                   `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                   `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='客户来源';

INSERT INTO `ymwl_crm_source` (`id`, `name`, `status`, `sort`, `create_time`, `update_time`) VALUES
                                                                                                 (1,	'个人资源',	1,	0,	1563085932,	NULL),
                                                                                                 (2,	'广告',	1,	0,	1563156221,	NULL),
                                                                                                 (3,	'转介绍',	1,	0,	1583985330,	NULL),
                                                                                                 (4,	'搜索引擎',	1,	0,	1583985355,	NULL),
                                                                                                 (5,	'公司资源',	1,	0,	1650037988,	NULL),
                                                                                                 (6,	'电话咨询',	1,	0,	1654264847,	NULL),
                                                                                                 (7,	'官网咨询',	1,	0,	1654264859,	NULL),
                                                                                                 (8,	'公众号',	1,	0,	1654264872,	NULL),
                                                                                                 (9,	'抖音',	1,	0,	1654264886,	NULL),
                                                                                                 (10,	'企业微信',	1,	0,	1654264900,	NULL);

DROP TABLE IF EXISTS `ymwl_crm_status`;
CREATE TABLE `ymwl_crm_status` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                   `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '状态名称',
                                   `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                   `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                   `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                   `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='客户状态';

INSERT INTO `ymwl_crm_status` (`id`, `name`, `sort`, `status`, `create_time`, `update_time`) VALUES
                                                                                                 (1,	'新客户',	0,	1,	1560328224,	1671718037),
                                                                                                 (2,	'意向客户',	1,	1,	1560328232,	NULL),
                                                                                                 (3,	'签单客户',	2,	1,	1583991439,	NULL),
                                                                                                 (4,	'回款客户',	3,	1,	1584983912,	NULL),
                                                                                                 (5,	'上门客户',	5,	1,	1584983923,	NULL),
                                                                                                 (6,	'暂停客户',	6,	1,	1654264983,	NULL),
                                                                                                 (7,	'无效客户',	7,	1,	1654264996,	NULL);

DROP TABLE IF EXISTS `ymwl_customer_changes_record`;
CREATE TABLE `ymwl_customer_changes_record` (
                                                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                                `customer_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户对应id',
                                                `customer_name` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户名称',
                                                `create_admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作人ID',
                                                `create_username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作人',
                                                `on` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作对象',
                                                `on_field` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作字段',
                                                `before_value` varchar(6000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作前值',
                                                `after_value` varchar(6000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作后值',
                                                `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作时间',
                                                `ip` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户信息变动记录表';


DROP TABLE IF EXISTS `ymwl_login_log`;
CREATE TABLE `ymwl_login_log` (
                                  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '登陆者ID',
                                  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登陆者账号',
                                  `ip` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登陆者IP',
                                  `area` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '地址',
                                  `login_side` tinyint(3) unsigned DEFAULT '0' COMMENT '0未知登陆方式1电脑登录2手机登录方式',
                                  `info` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登录信息',
                                  `createtime` int(10) DEFAULT '0' COMMENT '登录时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `admin_id` (`admin_id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='登录日志表';


DROP TABLE IF EXISTS `ymwl_open_apps`;
CREATE TABLE `ymwl_open_apps` (
                                  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                  `name` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
                                  `token` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '通讯秘钥',
                                  `table` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '对应数据表',
                                  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 {radio} (0:禁用,1:启用)',
                                  `create_time` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
                                  `update_time` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='开放应用';

INSERT INTO `ymwl_open_apps` (`id`, `name`, `token`, `table`, `status`, `create_time`, `update_time`) VALUES
    (2,	'百度营销',	'ZTPibvmrMet9AtRxELC7iFlp2gLuqSbE',	'',	0,	1708099830,	1708099830);

DROP TABLE IF EXISTS `ymwl_product`;
CREATE TABLE `ymwl_product` (
                                `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                `product_type_id` int(10) NOT NULL DEFAULT '0' COMMENT '商品分类',
                                `name` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品名称',
                                `thumb` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品图片 {image}',
                                `specification` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '规格',
                                `model` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '型号',
                                `inventory` int(10) NOT NULL DEFAULT '0' COMMENT '商品库存',
                                `min_warning` int(10) NOT NULL DEFAULT '0' COMMENT '最低库存预警',
                                `max_warning` int(10) NOT NULL DEFAULT '0' COMMENT '最高库存预警',
                                `cost_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本价格',
                                `sale_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '出售价格',
                                `remark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
                                `create_time` bigint(16) DEFAULT NULL COMMENT '创建时间',
                                `update_time` bigint(16) DEFAULT NULL COMMENT '更新时间',
                                `delete_time` int(10) DEFAULT NULL COMMENT '删除时间',
                                `status` int(4) NOT NULL DEFAULT '1' COMMENT '1正常0下架',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='商品信息';

INSERT INTO `ymwl_product` (`id`, `product_type_id`, `name`, `thumb`, `specification`, `model`, `inventory`, `min_warning`, `max_warning`, `cost_price`, `sale_price`, `remark`, `create_time`, `update_time`, `delete_time`, `status`) VALUES
    (1,	2,	'Apple iPhone 13 Pro (A2639) 256GB 石墨色 支持移动联通电信5G 双卡双待手机',	'/upload/75/6843b2416fa852304d0c7d6e1db9c4.jpg',	'银色256G内存*1台',	'Apple iPhone 13 Pro (A2639) ',	2000,	99,	2000,	5900.00,	6799.00,	'全新苹果手机',	1659778455,	1672123241,	NULL,	1);

DROP TABLE IF EXISTS `ymwl_product_type`;
CREATE TABLE `ymwl_product_type` (
                                     `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
                                     `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
                                     `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
                                     `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父id',
                                     `sort` int(11) DEFAULT '0' COMMENT '排序',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='商品分类';

INSERT INTO `ymwl_product_type` (`id`, `title`, `create_time`, `update_time`, `pid`, `sort`) VALUES
                                                                                                 (1,	'耗材2',	1659711528,	1672396319,	0,	0),
                                                                                                 (2,	'文具',	1659758424,	1659775461,	1,	3),
                                                                                                 (3,	'仪器',	1659773648,	1659773648,	0,	0),
                                                                                                 (4,	'分析仪器',	1659773673,	1659775745,	3,	0);

DROP TABLE IF EXISTS `ymwl_region`;
CREATE TABLE `ymwl_region` (
                               `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
                               `pid` smallint(5) unsigned NOT NULL DEFAULT '0',
                               `name` varchar(120) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
                               `type` tinyint(1) NOT NULL DEFAULT '2',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;


DROP TABLE IF EXISTS `ymwl_role`;
CREATE TABLE `ymwl_role` (
                             `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
                             `name` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                             `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
                             `remark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                             `pid` smallint(5) unsigned NOT NULL DEFAULT '0',
                             `sort` smallint(6) unsigned NOT NULL DEFAULT '0',
                             PRIMARY KEY (`id`) USING BTREE,
                             KEY `status` (`status`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

INSERT INTO `ymwl_role` (`id`, `name`, `status`, `remark`, `pid`, `sort`) VALUES
                                                                              (1,	'超级管理员',	1,	'超级管理',	0,	0),
                                                                              (2,	'普通管理员',	1,	'普通管理员',	0,	0),
                                                                              (3,	'注册用户',	1,	'注册用户',	0,	0),
                                                                              (4,	'游客',	1,	'游客',	0,	0);

DROP TABLE IF EXISTS `ymwl_role_user`;
CREATE TABLE `ymwl_role_user` (
                                  `role_id` mediumint(9) unsigned DEFAULT '0',
                                  `user_id` char(32) COLLATE utf8mb4_unicode_ci DEFAULT '0',
                                  KEY `group_id` (`role_id`) USING BTREE,
                                  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=FIXED;


DROP TABLE IF EXISTS `ymwl_system_config`;
CREATE TABLE `ymwl_system_config` (
                                      `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                      `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '变量名称',
                                      `field` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '变量名',
                                      `identification` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分组',
                                      `value` text COLLATE utf8mb4_unicode_ci COMMENT '变量值',
                                      `describe` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字段描述说明',
                                      `remark` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注信息',
                                      `sort` int(10) NOT NULL DEFAULT '100',
                                      `create_time` bigint(16) NOT NULL DEFAULT '0' COMMENT '创建时间',
                                      `update_time` bigint(16) NOT NULL DEFAULT '0' COMMENT '更新时间',
                                      `formtype` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'input' COMMENT 'form表单类型',
                                      `status` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
                                      `option` varchar(3000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表单选项',
                                      `input` varchar(3000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表单html',
                                      `issystem` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '系统字段无法修改删除编辑类型',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      UNIQUE KEY `name` (`field`) USING BTREE,
                                      KEY `group` (`identification`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='系统配置表';

INSERT INTO `ymwl_system_config` (`id`, `name`, `field`, `identification`, `value`, `describe`, `remark`, `sort`, `create_time`, `update_time`, `formtype`, `status`, `option`, `input`, `issystem`) VALUES
                                                                                                                                                                                                         (1,	'网站名称',	'name',	'basic',	' CRM客户关系管理系统 ',	'',	'',	100,	1675060449,	1675060449,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'网站名称\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"name\" name=\"name\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'网站名称\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'网站名称\')}\" value=\"{$row.name|default=\'\'}\"></div></div>',	1),
                                                                                                                                                                                                         (2,	'网站简称',	'site_abbr',	'basic',	'CRM',	'',	'',	100,	1675060540,	1675060540,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'网站简称\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"site_abbr\" name=\"site_abbr\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'网站简称\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'网站简称\')}\" value=\"{$row.site_abbr|default=\'\'}\"></div></div>',	1),
                                                                                                                                                                                                         (3,	'资源地址',	'domain',	'basic',	'http://www.baidu.com/',	'必须填写正确，否则无法加载图片资源',	'',	100,	1675060602,	1675060623,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'资源地址\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"domain\" name=\"domain\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'资源地址\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'资源地址\')}\" value=\"{$row.domain|default=\'\'}\"><tip>{:fy(\"必须填写正确，否则无法加载图片资源\")}</tip></div></div>',	1),
                                                                                                                                                                                                         (4,	'Copyright',	'copyright',	'basic',	'禁止将此源码用于含诈骗、赌博、色情、木马、病毒等违法违规业务',	'',	'',	100,	1675061152,	1675061152,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'Copyright\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"copyright\" name=\"copyright\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'Copyright\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'Copyright\')}\" value=\"{$row.copyright|default=\'\'}\"></div></div>',	1),
                                                                                                                                                                                                         (5,	'分页条数',	'admin_pagesize',	'basic',	'10',	'后台数据分页条数',	'',	100,	1675061233,	1675061233,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'分页条数\')}</label><div class=\"layui-input-block\"><input type=\"number\" id=\"admin_pagesize\" name=\"admin_pagesize\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'分页条数\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'分页条数\')}\" value=\"{$row.admin_pagesize|default=\'\'}\"><tip>{:fy(\'数据分页条数\')}</tip></div></div>',	1),
                                                                                                                                                                                                         (6,	'验证码',	'code',	'basic',	'1',	'',	'',	100,	1675061337,	1675061337,	'radio',	1,	'0:关闭,1:开启',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'验证码\')}</label><div class=\"layui-input-block\"><input type=\"radio\" name=\"code\" value=\"0\" title=\"{:fy(\'关闭\')}\" {if \"0\"==$row.code}checked{/if}><input type=\"radio\" name=\"code\" value=\"1\" title=\"{:fy(\'开启\')}\" {if \"1\"==$row.code}checked{/if}></div></div>',	1),
                                                                                                                                                                                                         (7,	'公告',	'notice',	'basic',	'<p><span style=\"background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);\"><strong>CRM系统支持定制和二次开发，如果需要更完善的CRM客户系统请联系微信：jxdh1688</strong></span></p>',	'',	'',	100,	1675061574,	1677770534,	'editor',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'公告\')}</label><div class=\"layui-input-block\"><textarea  name=\"notice\"  id=\"notice\" class=\"layui-textarea editor\" data-toolbars=\'[[\"source\",\"undo\", \"redo\", \"|\", \"bold\", \"italic\", \"underline\", \"fontborder\", \"strikethrough\", \"superscript\", \"subscript\", \"removeformat\", \"formatmatch\", \"autotypeset\", \"blockquote\",  \"|\", \"forecolor\", \"backcolor\", \"selectall\", \"cleardoc\", \"|\", \"lineheight\", \"|\",   \"fontfamily\", \"fontsize\", \"|\", \"link\", \"unlink\", \"emotion\"]]\' placeholder=\"{:fy(\"Please enter\")}{:fy(\'公告\')}\">{$row.notice|raw}</textarea></div></div>',	1),
(8,	'未跟进回收周期',	'kfrecycleday',	'crm',	'0',	'客户回收周期天，填写0表示不执行回收机制',	'',	100,	1676388973,	1676388973,	'input',	1,	'',	'<div class=\"layui-form-item\">\r\n    <label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'未跟进回收周期\')}</label>\r\n    <div class=\"layui-input-inline\" style=\"width: auto;\">\r\n        <input type=\"number\" style=\"width:60px;display: inline-block;\" class=\"layui-input\" name=\"kfrecycleday\" value=\"{$row.kfrecycleday|default=\'\'}\"> {:fy(\'天\')}\r\n\r\n    </div>\r\n    <div class=\"layui-input-inline\" style=\"width: auto;\">\r\n        <label class=\"layui-form-label\" style=\"width: 70px;\">{:fy(\'已成交客户\')}</label>\r\n        <div class=\"layui-input-block\">\r\n            <input type=\"radio\" name=\"genjing_success\" value=\"1\" title=\"{:fy(\'执行\')}\" <?php if($row[\'genjing_success\'])echo \'checked=\"checked\"\'; ?>>\r\n            <input type=\"radio\" name=\"genjing_success\" value=\"0\" title=\"{:fy(\'不执行\')}\" <?php if(!$row[\'genjing_success\'])echo \'checked=\"checked\"\'; ?>>\r\n\r\n        </div>\r\n    </div>\r\n    <div class=\"layui-form-mid layui-word-aux\">{:fy(\'未跟进客户是否回收至公海，填写0表示不执行回收机制，如果已成交客户选择不执行，超过周期也不会被回收\')}</div>\r\n</div>',	1),
(9,	'未成交回收周期',	'wchjhuishouday',	'crm',	'0',	'客户回收周期天，填写0表示不执行回收机制',	'',	100,	1676388973,	1676388973,	'input',	1,	'',	'<div class=\"layui-form-item\">\r\n    <label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'未成交回收周期\')}</label>\r\n    <div class=\"layui-input-inline\" style=\"width: auto;\">\r\n        <input type=\"number\" style=\"width:60px;display: inline-block;\" class=\"layui-input\" name=\"wchjhuishouday\" value=\"{$row.wchjhuishouday|default=\'\'}\">{:fy(\'天\')}\r\n\r\n    </div>\r\n    <div class=\"layui-form-mid layui-word-aux\">{:fy(\'未成交客户是否回收至公海，填写0表示不执行回收机制\')}</div>\r\n</div>',	1),
(10,	'领取数据',	'customer_limit_counts',	'crm',	'0',	'',	'',	100,	1676564033,	1676564033,	'input',	1,	'',	'',	1),
(11,	'后台固定页',	'fixedpage',	'basic',	'index/main',	'后台首页打开的第一个页面',	'',	20,	1676623590,	1676623738,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'后台固定页\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"fixedpage\" name=\"fixedpage\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'后台固定页\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'后台固定页\')}\" value=\"{$row.fixedpage|default=\'\'}\"><tip>{:fy(\"后台首页打开的第一个页面\")}</tip></div></div>',	1),
(12,	'已成交未跟进客户是否执行回收',	'genjing_success',	'crm',	'0',	'',	'',	100,	1676629289,	1676629319,	'input',	1,	'',	'',	1),
(13,	'最大领客户数',	'customer_limit_condition',	'crm',	'day',	'填写0表示不限制',	'',	100,	**********,	**********,	'input',	1,	'',	'<div class=\"layui-form-item\">\r\n    <label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'最大领客户数\')}</label>\r\n    <div class=\"layui-input-block\">\r\n        <div class=\"layui-input-inline\" style=\"width: 80px;margin-right: 0.5em;\">\r\n            <select name=\"customer_limit_condition\">\r\n                <option value=\"day\"  <?php if($row[\'customer_limit_condition\']==\'day\')echo \'selected=\"selected\"\'; ?>>{:fy(\'当天\')}</option>\r\n                <option value=\"week\"  <?php if($row[\'customer_limit_condition\']==\'week\')echo \'selected=\"selected\"\'; ?> >{:fy(\'当周\')}</option>\r\n                <option value=\"month\"   <?php if($row[\'customer_limit_condition\']==\'month\')echo \'selected=\"selected\"\'; ?>>{:fy(\'当月\')}</option>\r\n            </select>\r\n        </div>\r\n        <div class=\"layui-input-inline\" style=\"width: auto;\">\r\n            {:fy(\'内限制领取\')} <input type=\"number\" style=\"width:60px;display: inline-block;\" class=\"layui-input\" name=\"customer_limit_counts\" value=\"{$row.customer_limit_counts|default=\'0\'}\"> {:fy(\'次\')}</div>\r\n        <div class=\"layui-form-mid layui-word-aux\">{:fy(\'填写0表示不限制\')}</div>\r\n    </div>\r\n</div>',	1),
(14,	'客户唯一字段',	'customer_unique',	'crm',	'name',	'',	'',	100,	**********,	**********,	'input',	1,	'',	'<div class=\"layui-form-item\">\r\n    <label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'客户唯一字段\')}</label>\r\n    <div class=\"layui-input-block\" style=\"margin-left: 160px;\">\r\n        <input type=\"text\" name=\"customer_unique\" data-toggle=\"selectPage\" class=\"layui-input\" data-source=\'<?php echo json_encode($fields_lst,256); ?>\'  data-field=\"name\"  data-format-item=\"{name}\"  data-multiple=\"true\"    data-primary-key=\"field\"  placeholder=\"{:fy(\'限制唯一字段\')}\"   value=\"{$row.customer_unique|default=\'\'}\" >\r\n        <div class=\"layui-form-mid layui-word-aux\">{:fy(\'勾选的唯一字段信息将不能重复\')}</div>\r\n\r\n    </div>\r\n</div>',	1),
(15,	'授权秘钥',	'license_key',	'basic',	'',	'填写程序授权秘钥',	'',	1,	**********,	**********,	'input',	1,	'',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'授权秘钥\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"license_key\" name=\"license_key\" lay-filter=\"license_key\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'授权秘钥\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'授权秘钥\')}\" value=\"{$row.license_key|default=\'\'}\"><tip>填写程序授权秘钥</tip></div></div>',	1),
(16,	'客户记录字段',	'customer_record_fields',	'crm',	'name,phone,contact',	'',	'',	100,	**********,	**********,	'input',	1,	'',	'<div class=\"layui-form-item\">\r\n    <label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'客户记录字段\')}</label>\r\n    <div class=\"layui-input-block\" style=\"margin-left: 160px;\">\r\n        <input type=\"text\" name=\"customer_record_fields\" data-toggle=\"selectPage\" class=\"layui-input\" data-source=\'<?php echo json_encode($record_fields_lst,256); ?>\'  data-field=\"name\"  data-format-item=\"{name}\"  data-multiple=\"true\"    data-primary-key=\"field\"  placeholder=\"{:fy(\'需要记录的操作字段\')}\"   value=\"{$row.customer_record_fields|default=\'\'}\" >\r\n        <div class=\"layui-form-mid layui-word-aux\">{:fy(\'勾选对客户信息操作时需要记录的字段\')}</div>\r\n\r\n    </div>\r\n</div>',	1),
(17,	'大屏展示字段',	'customer_big_fields',	'crm',	'name,contact,at_user,pr_user,source',	'',	'',	100,	**********,	**********,	'input',	1,	'',	'<div class=\"layui-form-item\">\n    <label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'大屏展示字段\')}</label>\n    <div class=\"layui-input-block\" style=\"margin-left: 160px;\">\n        <input type=\"text\" name=\"customer_big_fields\" data-toggle=\"selectPage\" class=\"layui-input\" data-source=\'<?php echo json_encode($big_fields_lst,256); ?>\'  data-field=\"name\"  data-format-item=\"{name}\"  data-multiple=\"true\"    data-primary-key=\"field\"  placeholder=\"{:fy(\'需要记录的操作字段\')}\"   value=\"{$row.customer_big_fields|default=\'\'}\" >\n        <div class=\"layui-form-mid layui-word-aux\">{:fy(\'勾选大屏展示字段\')}</div>\n\n    </div>\n</div>',	1),
(18,	'成交客户独立展示',	'chjkhdlzhsh',	'crm',	'1',	'成交客户是否独立展示，独立展示的将不再客户列表展示了',	'',	100,	1721889109,	1721889499,	'select',	1,	'0:否,1:是',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\" style=\"width: 130px;\">{:fy(\'成交客户独立展示\')}</label><div class=\"layui-input-block\" style=\"margin-left: 160px;\"><select  name=\"chjkhdlzhsh\" lay-filter=\"chjkhdlzhsh\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option><option value=\"0\" {if isset($row[\"chjkhdlzhsh\"])&&$row[\"chjkhdlzhsh\"]==\"0\"}selected{/if}>否</option><option value=\"1\" {if isset($row[\"chjkhdlzhsh\"])&&$row[\"chjkhdlzhsh\"]==\"1\"}selected{/if}>是</option></select><tip>成交客户是否独立展示，独立展示的将不再客户列表展示了</tip></div></div>',	1);


DROP TABLE IF EXISTS `ymwl_system_config_group`;
CREATE TABLE `ymwl_system_config_group` (
                                            `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                            `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分组名称',
                                            `sort` int(10) NOT NULL DEFAULT '100' COMMENT '排序',
                                            `create_time` bigint(16) NOT NULL DEFAULT '0' COMMENT '创建时间',
                                            `update_time` bigint(16) NOT NULL DEFAULT '0' COMMENT '更新时间',
                                            `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
                                            `identification` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标识',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            UNIQUE KEY `identification` (`identification`) USING BTREE,
                                            UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='配置分组';

INSERT INTO `ymwl_system_config_group` (`id`, `name`, `sort`, `create_time`, `update_time`, `status`, `identification`) VALUES
                                                                                                                            (1,	'基础配置',	100,	1674572401,	1674820400,	1,	'basic'),
                                                                                                                            (2,	'CRM',	100,	1676388882,	1677765867,	1,	'crm');

DROP TABLE IF EXISTS `ymwl_system_field`;
CREATE TABLE `ymwl_system_field` (
                                     `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                     `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字段名称[show:1,edit:1,search:1,total:0]',
                                     `xsname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '显示名称',
                                     `field` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字段名{input}[show:1,edit:1,search:1,total:0]',
                                     `type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '字段类型{select}(tinyint,smallint,mediumint,int,integer,bigint,double,float,decimal,numeric,char,varchar,datetime,tinytext,text,mediumtext,longtext)[show:1,edit:1,search:1,total:0]',
                                     `rule` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '验证规则',
                                     `msg` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '错误提示',
                                     `lang` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '长度[show:1,edit:1,search:0,total:0]',
                                     `is_null` int(1) NOT NULL DEFAULT '0' COMMENT '是空{switch}(1:开启,0:关闭)[show:1,edit:1,search:0,total:0,sort:100,export:0,default:0]',
                                     `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间{datetime}[show:1,edit:0,search:0,total:0,sort:100,export:0]',
                                     `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间{datetime}[show:0,edit:0,search:0,total:0,sort:100,export:0]',
                                     `formtype` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '表单类型{select}(input:单行文本,textarea:多行文本,file:单文件,files:多文件,img:单图片,imgs:多图片,video:视频,radio:单选框,checkbox:多选框,select:下拉框,color:颜色,editor:编辑框,none:隐藏框,password:密码框,optgroup:下拉组,switch:开关,json:json数组,lradio:联动单选框,lcheckbox:联动多选框,lselect:联动下拉框,datetime:时间,aselect:ajax联动下拉框,aselectgroup:ajax联动下拉组)[show:1,edit:0,sort:100,status:1]',
                                     `table` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '表名称[show:1,edit:1,search:0,total:0,sort:100,export:0]',
                                     `show` int(1) DEFAULT '0' COMMENT '到表格{switch}(0:不显示,1:显示)[show:1,edit:1,search:0,total:0,sort:100,export:0]',
                                     `edit` int(1) DEFAULT '0' COMMENT '表单{switch}(0:关闭,1:开启)[show:1,edit:1,search:0,total:0,sort:100,export:0]',
                                     `search` int(1) DEFAULT '0' COMMENT '搜索{switch}(0:关闭,1:开启)[show:1,edit:1,search:0,total:0,sort:100,export:0]',
                                     `total` int(1) DEFAULT '0' COMMENT '合计{switch}(0:关闭,1:开启)[show:1,edit:1,search:0,total:0,sort:100,export:0]',
                                     `export` int(1) DEFAULT '0' COMMENT '导出{switch}(0:关闭,1:开启)[show:1,edit:1,search:0,total:0,sort:100,export:0]',
                                     `sort` float DEFAULT '100' COMMENT '排序',
                                     `option` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '选项',
                                     `join_table` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '链接表{lselect}[show:1,edit:1,search:0,total:0,sort:100,export:0,join_table:system_model]',
                                     `default` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '默认值',
                                     `foreign_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'name' COMMENT '外键[show:1,edit:1]',
                                     `describe` varchar(3000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '描述{textarea}',
                                     `href` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '/admin/ajax/ajaxselect' COMMENT 'ajax下拉请求地址',
                                     `relationship_primary_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'id' COMMENT '关系主键',
                                     `grid` VARCHAR(300) NULL DEFAULT '' COMMENT '栅格布局的类列表' COLLATE 'utf8mb4_unicode_ci',
                                     `is_key` int(1) DEFAULT '0' COMMENT '是否主键',
                                     `width` int(11) DEFAULT '150' COMMENT '表格宽度{input}[sort:100,default:150,foreign_key:name,status:1]',
                                     `issystem` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '系统字段无法修改删除',
                                     `edit_readonly` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '数据编辑状态是否只读0不是只读1只读',
                                     `jscol` text COLLATE utf8mb4_unicode_ci,
                                     `addinput` text COLLATE utf8mb4_unicode_ci,
                                     `editinput` text COLLATE utf8mb4_unicode_ci,
                                     `status` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '状态记录',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `table` (`table`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='字段表';

INSERT INTO `ymwl_system_field` (`id`, `name`, `xsname`, `field`, `type`, `rule`, `msg`, `lang`, `is_null`, `create_time`, `update_time`, `formtype`, `table`, `show`, `edit`, `search`, `total`, `export`, `sort`, `option`, `join_table`, `default`, `foreign_key`, `describe`, `href`, `relationship_primary_key`, `is_key`, `width`, `issystem`, `edit_readonly`, `jscol`, `addinput`, `editinput`, `status`) VALUES
                                                                                                                                                                                                                                                                                                                                                                                                                      (8,	'ID',	'ID',	'id',	'int',	'',	'',	'11',	0,	1654336894,	1681907016,	'input',	'crm_customer',	1,	1,	0,	0,	0,	1,	NULL,	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	1,	80,	1,	0,	'{\"field\":\"id\",\"title\":\"{:fy(\'ID\')}\",\"search\":false,\"width\":80,\"totalRowText\":\"合计\"}',	'',	'<input type=\"hidden\" name=\"id\" value=\"{$row.id|default=\'\'}\">',	'noedit,noimport'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (31,	'客户来源',	'客户来源',	'source',	'varchar',	'',	'',	'255',	0,	1654408583,	1672217681,	'lselect',	'crm_customer',	1,	1,	1,	0,	1,	100,	'',	'crm_source',	'',	'name',	'',	'/admin/crm.source/index',	'name',	0,	100,	1,	0,	'{\"field\":\"source\",\"title\":\"{:fy(\'客户来源\')}\",\"search\":\"select\",\"width\":\"100\",\"selectList\":\"{:build_select_list(\'crm_source\',\'name\',\'name\')}\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户来源\')}</label><div class=\"layui-input-block\"><select  name=\"source\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_source\',\'name\',\'name\',\'\')}</select></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户来源\')}</label><div class=\"layui-input-block\"><select  name=\"source\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_source\',\'name\',\'name\',$row[\"source\"])}</select></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (33,	'地区',	'',	'area',	'varchar',	'',	'',	'60',	0,	1655314637,	1682921671,	'district',	'crm_customer',	1,	1,	0,	0,	0,	88,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"area\",\"title\":\"{:fy(\'地区\')}\",\"search\":false,\"width\":100}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'中国地区\')}</label><div class=\"layui-input-block\"><input type=\"text\" data-toggle=\"city-picker\" data-level=\"district\" data-placeholder=\"请选择省/市/区\" id=\"area\" name=\"area\" class=\"layui-input\" ></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'中国地区\')}</label><div class=\"layui-input-block\"><input type=\"text\" data-toggle=\"city-picker\" data-level=\"district\" data-placeholder=\"请选择省/市/区\" id=\"area\" name=\"area\" class=\"layui-input\"  value=\"{$row.area|default=\'\'}\"></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (9,	'客户名称',	'',	'name',	'varchar',	'require,unique',	'客户名称',	'99',	0,	1654336894,	1687881319,	'input',	'crm_customer',	1,	1,	1,	0,	1,	2,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	190,	1,	1,	'{\"field\":\"name\",\"title\":\"{:fy(\'客户名称\')}\",\"search\":true,\"width\":\"190\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户名称\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"name\" name=\"name\" class=\"layui-input\"  lay-verify=\"required\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'客户名称\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'客户名称\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户名称\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"name\" name=\"name\" lay-filter=\"name\" class=\"layui-input\"  lay-verify=\"required\"  disabled=\"disabled\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'客户名称\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'客户名称\')}\" value=\"{$row.name|default=\'\'}\"></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (10,	'客户联系人',	'',	'contact',	'varchar',	'',	'',	'100',	0,	1654336894,	1672210644,	'input',	'crm_customer',	1,	1,	1,	0,	1,	5,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	150,	1,	0,	'{\"field\":\"contact\",\"title\":\"{:fy(\'客户联系人\')}\",\"search\":true,\"width\":\"150\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户联系人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"contact\" name=\"contact\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'客户联系人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'客户联系人\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户联系人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"contact\" name=\"contact\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'客户联系人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'客户联系人\')}\" value=\"{$row.contact|default=\'\'}\"></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (11,	'联系电话',	'',	'phone',	'varchar',	'require',	'联系电话',	'20',	0,	1654336894,	1672210636,	'tel',	'crm_customer',	1,	1,	1,	0,	1,	3,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	116,	1,	0,	'{\"field\":\"phone\",\"title\":\"{:fy(\'联系电话\')}\",\"search\":true,\"width\":\"116\",\"templet\":\"ea.table.tel\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'联系电话\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"phone\" name=\"phone\" class=\"layui-input\"  lay-verify=\"required\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'联系电话\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'联系电话\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'联系电话\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"phone\" name=\"phone\" class=\"layui-input\"  lay-verify=\"required\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'联系电话\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'联系电话\')}\" value=\"{$row.phone|default=\'\'}\"></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (12,	'最新跟进记录',	'',	'last_up_records',	'varchar',	'',	'',	'200',	0,	1654336894,	1678409682,	'textarea',	'crm_customer',	1,	0,	1,	0,	1,	100,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	100,	1,	0,	'{\"field\":\"last_up_records\",\"title\":\"{:fy(\'最新跟进记录\')}\",\"search\":true,\"width\":\"100\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'最新跟进记录\')}</label><div class=\"layui-input-block\"><textarea  name=\"last_up_records\" class=\"layui-textarea\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'最新跟进记录\')}\"></textarea></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'最新跟进记录\')}</label><div class=\"layui-input-block\"><textarea  name=\"last_up_records\" class=\"layui-textarea textarea\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'最新跟进记录\')}\">{$row.last_up_records|default=\'\'}</textarea></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (13,	'实际跟进时间',	'',	'last_up_time',	'int',	'',	'',	'',	0,	1654336894,	1685942783,	'datetime',	'crm_customer',	1,	0,	0,	0,	0,	100,	'',	'',	'',	'',	'',	'/admin/ajax/ajaxselect',	'',	0,	160,	1,	0,	'{\"field\":\"last_up_time\",\"title\":\"{:fy(\'实际跟进时间\')}\",\"search\":false,\"width\":160,\"templet\":\"ea.table.datetime\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'实际跟进时间\')}</label><div class=\"layui-input-block\"><input  type=\"text\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"last_up_time\" class=\"layui-input datetime\" id=\"last_up_time\" value=\"\" autocomplete=\"off\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'实际跟进时间\')}</label><div class=\"layui-input-block\"><input type=\"text\" autocomplete=\"off\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"last_up_time\" class=\"layui-input datetime\" id=\"last_up_time\" {if is_numeric($row.last_up_time)}value=\"{:mydate(\'Y-m-d H:i:s\',$row.last_up_time)}\" {else}value=\"{$row.last_up_time}\"{/if}></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (14,	'下次跟进时间',	'',	'next_time',	'int',	'',	'',	'',	0,	1654336894,	1678409988,	'datetime',	'crm_customer',	0,	0,	0,	0,	0,	100,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	160,	1,	0,	'{\"field\":\"next_time\",\"title\":\"{:fy(\'下次跟进时间\')}\",\"search\":false,\"width\":160,\"templet\":\"ea.table.datetime\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'下次跟进时间\')}</label><div class=\"layui-input-block\"><input  type=\"text\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"next_time\" class=\"layui-input datetime\" id=\"next_time\" value=\"\" autocomplete=\"off\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'下次跟进时间\')}</label><div class=\"layui-input-block\"><input type=\"text\" autocomplete=\"off\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"next_time\" class=\"layui-input datetime\" id=\"next_time\" {if is_numeric($row.next_time)}value=\"{:mydate(\'Y-m-d H:i:s\',$row.next_time)}\" {else}value=\"{$row.next_time}\"{/if}></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (15,	'备注',	'',	'remark',	'varchar',	'',	'',	'600',	0,	1654336894,	1672211005,	'textarea',	'crm_customer',	1,	1,	1,	0,	1,	1000,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	150,	1,	0,	'{\"field\":\"remark\",\"title\":\"{:fy(\'备注\')}\",\"search\":true,\"width\":\"150\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'备注\')}</label><div class=\"layui-input-block\"><textarea  name=\"remark\" class=\"layui-textarea\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'备注\')}\"></textarea></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'备注\')}</label><div class=\"layui-input-block\"><textarea  name=\"remark\" class=\"layui-textarea textarea\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'备注\')}\">{$row.remark|default=\'\'}</textarea></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (16,	'微信号',	'',	'wechat',	'varchar',	'',	'',	'30',	0,	1654336894,	1678409893,	'input',	'crm_customer',	0,	0,	0,	0,	0,	100,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	100,	1,	0,	'{\"field\":\"wechat\",\"title\":\"{:fy(\'微信号\')}\",\"search\":false,\"width\":100}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'微信号\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"wechat\" name=\"wechat\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'微信号\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'微信号\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'微信号\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"wechat\" name=\"wechat\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'微信号\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'微信号\')}\" value=\"{$row.wechat|default=\'\'}\"></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (17,	'创建人',	'',	'at_user',	'varchar',	'',	'',	'100',	0,	1654336894,	1678409889,	'input',	'crm_customer',	0,	0,	0,	0,	0,	100,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	100,	1,	0,	'{\"field\":\"at_user\",\"title\":\"{:fy(\'创建人\')}\",\"search\":false,\"width\":100}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'创建人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"at_user\" name=\"at_user\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'创建人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'创建人\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'创建人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"at_user\" name=\"at_user\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'创建人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'创建人\')}\" value=\"{$row.at_user|default=\'\'}\"></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (18,	'创建时间',	'',	'create_time',	'int',	'',	'',	'',	0,	1654336894,	1678409885,	'datetime',	'crm_customer',	0,	0,	0,	0,	0,	100,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"create_time\",\"title\":\"{:fy(\'创建时间\')}\",\"search\":false,\"width\":100,\"templet\":\"ea.table.datetime\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'创建时间\')}</label><div class=\"layui-input-block\"><input  type=\"text\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"create_time\" class=\"layui-input datetime\" id=\"create_time\" value=\"\" autocomplete=\"off\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'创建时间\')}</label><div class=\"layui-input-block\"><input type=\"text\" autocomplete=\"off\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"create_time\" class=\"layui-input datetime\" id=\"create_time\" {if is_numeric($row.create_time)}value=\"{:mydate(\'Y-m-d H:i:s\',$row.create_time)}\" {else}value=\"{$row.create_time}\"{/if}></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (19,	'更新时间',	'',	'update_time',	'int',	'',	'',	'',	0,	1654336894,	1678409879,	'datetime',	'crm_customer',	1,	0,	0,	0,	0,	100,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"update_time\",\"title\":\"{:fy(\'更新时间\')}\",\"search\":false,\"width\":100,\"templet\":\"ea.table.datetime\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'更新时间\')}</label><div class=\"layui-input-block\"><input  type=\"text\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"update_time\" class=\"layui-input datetime\" id=\"update_time\" value=\"\" autocomplete=\"off\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'更新时间\')}</label><div class=\"layui-input-block\"><input type=\"text\" autocomplete=\"off\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"update_time\" class=\"layui-input datetime\" id=\"update_time\" {if is_numeric($row.update_time)}value=\"{:mydate(\'Y-m-d H:i:s\',$row.update_time)}\" {else}value=\"{$row.update_time}\"{/if}></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (21,	'负责人',	'',	'pr_user',	'varchar',	'',	'',	'30',	0,	1654336894,	1678409874,	'input',	'crm_customer',	0,	0,	0,	0,	0,	100,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	100,	1,	0,	'{\"field\":\"pr_user\",\"title\":\"{:fy(\'负责人\')}\",\"search\":false,\"width\":100}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'负责人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"pr_user\" name=\"pr_user\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'负责人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'负责人\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'负责人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"pr_user\" name=\"pr_user\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'负责人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'负责人\')}\" value=\"{$row.pr_user|default=\'\'}\"></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (22,	'前负责人',	'',	'pr_user_bef',	'varchar',	'',	'',	'30',	0,	1654336894,	1678191482,	'input',	'crm_customer',	0,	0,	0,	0,	0,	100,	'',	'',	'',	'name',	'',	'/admin/ajax/ajaxselect',	'id',	0,	100,	1,	0,	'{\"field\":\"pr_user_bef\",\"title\":\"{:fy(\'前负责人\')}\",\"search\":false,\"width\":100}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'前负责人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"pr_user_bef\" name=\"pr_user_bef\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'前负责人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'前负责人\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'前负责人\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"pr_user_bef\" name=\"pr_user_bef\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'前负责人\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'前负责人\')}\" value=\"{$row.pr_user_bef|default=\'\'}\"></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (23,	'转客户时间',	'',	'to_kh_time',	'int',	'',	'',	'',	0,	1654336894,	1672210816,	'datetime',	'crm_customer',	0,	0,	1,	0,	0,	100,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"to_kh_time\",\"title\":\"{:fy(\'转客户时间\')}\",\"search\":\"range\",\"width\":\"100\",\"templet\":\"ea.table.datetime\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'转客户时间\')}</label><div class=\"layui-input-block\"><input  type=\"text\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"to_kh_time\" class=\"layui-input datetime\" id=\"to_kh_time\" value=\"\" autocomplete=\"off\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'转客户时间\')}</label><div class=\"layui-input-block\"><input type=\"text\" autocomplete=\"off\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"to_kh_time\" class=\"layui-input datetime\" id=\"to_kh_time\" {if is_numeric($row.to_kh_time)}value=\"{:mydate(\'Y-m-d H:i:s\',$row.to_kh_time)}\" {else}value=\"{$row.to_kh_time}\"{/if}></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (24,	'转公海时间',	'',	'to_gh_time',	'int',	'',	'',	'',	0,	1654336894,	1672210808,	'datetime',	'crm_customer',	1,	0,	1,	0,	0,	100,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"to_gh_time\",\"title\":\"{:fy(\'转公海时间\')}\",\"search\":\"range\",\"width\":\"100\",\"templet\":\"ea.table.datetime\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'转公海时间\')}</label><div class=\"layui-input-block\"><input  type=\"text\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"to_gh_time\" class=\"layui-input datetime\" id=\"to_gh_time\" value=\"\" autocomplete=\"off\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'转公海时间\')}</label><div class=\"layui-input-block\"><input type=\"text\" autocomplete=\"off\" data-date=\"yyyy-MM-dd HH:mm:ss\" name=\"to_gh_time\" class=\"layui-input datetime\" id=\"to_gh_time\" {if is_numeric($row.to_gh_time)}value=\"{:mydate(\'Y-m-d H:i:s\',$row.to_gh_time)}\" {else}value=\"{$row.to_gh_time}\"{/if}></div></div>',	'noedit'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (25,	'行业类别',	'行业类别',	'kh_hangye',	'varchar',	'require',	'',	'255',	0,	1654336894,	1685770459,	'lselect',	'crm_customer',	1,	1,	1,	0,	1,	100,	'',	'crm_hangye',	'',	'name',	'',	'',	'name',	0,	100,	1,	0,	'{\"field\":\"kh_hangye\",\"title\":\"{:fy(\'行业类别\')}\",\"search\":\"select\",\"width\":100,\"selectList\":\"{:build_select_list(\'crm_hangye\',\'name\',\'name\')}\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'行业类别\')}</label><div class=\"layui-input-block\"><select  name=\"kh_hangye\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_hangye\',\'name\',\'name\',\'\')}</select></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'行业类别\')}</label><div class=\"layui-input-block\"><select  name=\"kh_hangye\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_hangye\',\'name\',\'name\',$row[\"kh_hangye\"])}</select></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (26,	'客户等级',	'客户等级',	'kh_rank',	'varchar',	'',	'',	'100',	0,	1654336894,	1672217926,	'lselect',	'crm_customer',	1,	1,	1,	0,	1,	100,	'',	'crm_rank',	'',	'name',	'',	'/admin/crm.rank/index',	'name',	0,	100,	1,	0,	'{\"field\":\"kh_rank\",\"title\":\"{:fy(\'客户等级\')}\",\"search\":\"select\",\"width\":\"100\",\"selectList\":\"{:build_select_list(\'crm_rank\',\'name\',\'name\')}\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户等级\')}</label><div class=\"layui-input-block\"><select  name=\"kh_rank\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_rank\',\'name\',\'name\',\'\')}</select></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户等级\')}</label><div class=\"layui-input-block\"><select  name=\"kh_rank\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_rank\',\'name\',\'name\',$row[\"kh_rank\"])}</select></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (27,	'客户状态',	'',	'kh_status',	'varchar',	'',	'',	'50',	0,	1654336894,	1672214384,	'lselect',	'crm_customer',	1,	1,	1,	0,	1,	999,	'',	'crm_status',	'',	'name',	'',	'',	'name',	0,	100,	1,	0,	'{\"field\":\"kh_status\",\"title\":\"{:fy(\'客户状态\')}\",\"search\":\"select\",\"width\":\"100\",\"selectList\":\"{:build_select_list(\'crm_status\',\'name\',\'name\')}\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户状态\')}</label><div class=\"layui-input-block\"><select  name=\"kh_status\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_status\',\'name\',\'name\',\'\')}</select></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'客户状态\')}</label><div class=\"layui-input-block\"><select  name=\"kh_status\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option>{:build_option_input(\'crm_status\',\'name\',\'name\',$row[\"kh_status\"])}</select></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (34,	'详细地址',	'',	'address',	'varchar',	'',	'',	'255',	0,	1655348157,	1682921673,	'input',	'crm_customer',	1,	1,	0,	0,	0,	99,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"address\",\"title\":\"{:fy(\'详细地址\')}\",\"search\":false,\"width\":100}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'详细地址\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"address\" name=\"address\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'详细地址\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'详细地址\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'详细地址\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"address\" name=\"address\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'详细地址\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'详细地址\')}\" value=\"{$row.address|default=\'\'}\"></div></div>',	'0'),
                                                                                                                                                                                                                                                                                                                                                                                                                      (29,	'是否成交',	'',	'issuccess',	'tinyint',	'',	'',	'4',	0,	1654336894,	1678241937,	'select',	'crm_customer',	1,	1,	1,	0,	1,	100,	'0:未成交,1:已成交',	'',	'0',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"issuccess\",\"title\":\"{:fy(\'是否成交\')}\",\"search\":\"select\",\"width\":\"100\",\"selectList\":[\"未成交\",\"已成交\"],\"templet\":\"ea.table.select\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'是否成交\')}</label><div class=\"layui-input-block\"><select  name=\"issuccess\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option><option value=\"0\">未成交</option><option value=\"1\">已成交</option></select></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'是否成交\')}</label><div class=\"layui-input-block\"><select  name=\"issuccess\" class=\"layui-select\" lay-search><option value=\"\">{:fy(\"Please select\")}</option><option value=\"0\" {if isset($row[\"issuccess\"])&&$row[\"issuccess\"]==\"0\"}selected{/if}>未成交</option><option value=\"1\" {if isset($row[\"issuccess\"])&&$row[\"issuccess\"]==\"1\"}selected{/if}>已成交</option></select></div></div>',	''),
                                                                                                                                                                                                                                                                                                                                                                                                                      (30,	'邮箱',	'',	'email',	'varchar',	'email',	'',	'30',	0,	1654336894,	1672210659,	'input',	'crm_customer',	1,	1,	1,	0,	1,	6,	'',	'',	'',	'',	'',	'',	'',	0,	100,	1,	0,	'{\"field\":\"email\",\"title\":\"{:fy(\'邮箱\')}\",\"search\":true,\"width\":\"100\"}',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'邮箱\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"email\" name=\"email\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'邮箱\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'邮箱\')}\"></div></div>',	'<div class=\"layui-form-item\"><label class=\"layui-form-label\">{:fy(\'邮箱\')}</label><div class=\"layui-input-block\"><input type=\"text\" id=\"email\" name=\"email\" class=\"layui-input\" lay-reqtext=\"{:fy(\"Please enter\")}{:fy(\'邮箱\')}\" placeholder=\"{:fy(\"Please enter\")}{:fy(\'邮箱\')}\" value=\"{$row.email|default=\'\'}\"></div></div>',	'0');

DROP TABLE IF EXISTS `ymwl_system_model`;
CREATE TABLE `ymwl_system_model` (
                                     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                     `name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '表名称[show:1,edit:1,search:1,total:0]',
                                     `table` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '表名{input}[show:1,edit:1,search:1,total:0]',
                                     `engine` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT 'MyISAM' COMMENT '引擎{select}(InnoDB,MyISAM)[show:1,edit:1,search:0,total:0]',
                                     `tabletype` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'ordinary' COMMENT '表格类型{select}(ordinary:普通表格,tree:树状表格)[show:1,edit:1,search:0,total:0]',
                                     `delete_time` int(11) DEFAULT '0',
                                     `create_time` bigint(16) DEFAULT '0' COMMENT '创建时间{datetime}[show:1,edit:0,search:0,total:0]',
                                     `update_time` bigint(16) DEFAULT '0' COMMENT '更新时间{datetime}[show:0,edit:0,search:0,total:0]',
                                     `prefix` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '表前缀[show:1,edit:1,search:0,total:0]',
                                     `is_page` int(1) DEFAULT '1' COMMENT '开启分页{switch}(0:关闭,1:开启)[show:1,edit:1,search:0,total:0,sort:100,export:0,default:1]',
                                     `sort` int(100) DEFAULT '100' COMMENT '排序',
                                     `status` int(1) DEFAULT '1' COMMENT '状态',
                                     `app` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '所属app',
                                     `databases` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'mysql' COMMENT '所属数据库{radio}[show:1,edit:0,sort:100,default:mysql,foreign_key:name,status:1]',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='模型表';

INSERT INTO `ymwl_system_model` (`id`, `name`, `table`, `engine`, `tabletype`, `delete_time`, `create_time`, `update_time`, `prefix`, `is_page`, `sort`, `status`, `app`, `databases`) VALUES
                                                                                                                                                                                           (1,	'评论表',	'crm_comment',	'MyISAM',	'ordinary',	0,	1654194033,	1654326744,	'',	1,	100,	1,	'',	'crmtp6_myfield'),
                                                                                                                                                                                           (2,	'客户表',	'crm_customer',	'InnoDB',	'ordinary',	0,	1654336894,	1654426608,	'',	1,	100,	1,	'',	'crmtp6_myfield');

DROP TABLE IF EXISTS `ymwl_system_uploadfile`;
CREATE TABLE `ymwl_system_uploadfile` (
                                          `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                          `upload_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'local' COMMENT '存储位置',
                                          `original_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件原名',
                                          `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '网络资源路径',
                                          `file_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '物理路径',
                                          `image_width` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '宽度',
                                          `image_height` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '高度',
                                          `image_type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片类型',
                                          `image_frames` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '图片帧数',
                                          `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'mime类型',
                                          `file_size` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小',
                                          `file_ext` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `md5` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件 sha1编',
                                          `create_time` bigint(20) unsigned DEFAULT NULL COMMENT '创建日期',
                                          `update_time` bigint(20) unsigned DEFAULT NULL COMMENT '更新时间',
                                          `upload_time` bigint(20) unsigned DEFAULT NULL COMMENT '上传时间',
                                          `admin_id` int(10) unsigned DEFAULT '0' COMMENT '上传者id',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `upload_type` (`upload_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='上传文件表';

INSERT INTO `ymwl_system_uploadfile` (`id`, `upload_type`, `original_name`, `url`, `file_path`, `image_width`, `image_height`, `image_type`, `image_frames`, `mime_type`, `file_size`, `file_ext`, `md5`, `create_time`, `update_time`, `upload_time`, `admin_id`) VALUES (1,	'local',	'Twothink.png',	'/upload/images/20230417/d93379a0023cc427eaeba7ed173eb170.png',	'/upload/images/20230417/d93379a0023cc427eaeba7ed173eb170.png',	'',	'',	'',	0,	'image/png',	0,	'png',	'0e7cb72b475e2dfe6726447f9f881a4e',	1667104676,	NULL,	NULL,	0),(2,	'local',	'head.jpg',	'/upload/images/20221228/bfaadea6d553f33fa215a22c6d20c7e3.jpg',	'/upload/images/20221228/bfaadea6d553f33fa215a22c6d20c7e3.jpg',	'',	'',	'',	0,	'image/jpeg',	0,	'jpg',	'c39af2f1bf72892b1db62f229d07710f',	1672234333,	NULL,	NULL,	0);


DROP TABLE IF EXISTS `ymwl_system_zone_uploadfile`;
CREATE TABLE `ymwl_system_zone_uploadfile` (
                                               `id` int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                               `upload_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'local' COMMENT '存储位置',
                                               `original_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件原名',
                                               `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '网络资源路径',
                                               `file_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '物理路径',
                                               `image_width` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '宽度',
                                               `image_height` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '高度',
                                               `image_type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片类型',
                                               `image_frames` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '图片帧数',
                                               `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'mime类型',
                                               `file_size` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小',
                                               `file_ext` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                               `md5` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件md5',
                                               `total_md5` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '总文件md5',
                                               `chunk_index` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '块索引',
                                               `chunk_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总块数',
                                               `create_time` bigint(16) DEFAULT NULL COMMENT '创建日期',
                                               `update_time` bigint(16) DEFAULT NULL COMMENT '更新时间',
                                               `upload_time` int(10) DEFAULT NULL COMMENT '上传时间',
                                               PRIMARY KEY (`id`) USING BTREE,
                                               KEY `upload_type` (`upload_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='分片上传文件表';


DROP TABLE IF EXISTS `ymwl_team`;
CREATE TABLE `ymwl_team` (
                             `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                             `title` varchar(120) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                             `title_style` varchar(225) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                             `thumb` varchar(225) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                             `hits` int(11) unsigned NOT NULL DEFAULT '0',
                             `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
                             `userid` int(8) unsigned NOT NULL DEFAULT '0',
                             `username` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                             `sort` int(10) unsigned NOT NULL DEFAULT '0',
                             `createtime` int(11) unsigned NOT NULL DEFAULT '0',
                             `updatetime` int(11) unsigned NOT NULL DEFAULT '0',
                             `lang` tinyint(1) unsigned NOT NULL DEFAULT '0',
                             `catid` smallint(5) unsigned NOT NULL DEFAULT '0',
                             `info` text COLLATE utf8mb4_unicode_ci NOT NULL,
                             `template` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;


DROP TABLE IF EXISTS `ymwl_todo`;
CREATE TABLE `ymwl_todo` (
                             `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                             `title` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型',
                             `event` varchar(120) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '事件',
                             `todo_no` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工作序号,订单号',
                             `mess` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容',
                             `url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '等待处理的url',
                             `editurl` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '编辑URL',
                             `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
                             `result` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处理情况',
                             `result_mess` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处理说明',
                             `admin_id` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '发起者',
                             `show_auth_group_id` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '可以查看的员工组',
                             `show_auth_admin_id` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '可以查看的管理员',
                             `is_finish` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否完成0未完成1完成',
                             `audittime` int(11) NOT NULL DEFAULT '0' COMMENT '审核时间',
                             `reviewer` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审核者',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='提醒消息表';


DROP TABLE IF EXISTS `ymwl_weixin`;
CREATE TABLE `ymwl_weixin` (
                               `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'Id',
                               `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '表名称',
                               `value` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段配置项目',
                               PRIMARY KEY (`id`) USING BTREE,
                               UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='微信配置';

INSERT INTO `ymwl_weixin` (`id`, `name`, `value`) VALUES
                                                      (2,	'xcx',	''),
                                                      (3,	'config',	''),
                                                      (5,	'account',	'{\"wxname\":\"\\u7f18\\u660e\\u7f51\\u7edc1\",\"appid\":\"11122\",\"appsecret\":\"22233\"}');

DROP TABLE IF EXISTS `ymwl_weixin_menu`;
CREATE TABLE `ymwl_weixin_menu` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `merchant_id` int(11) DEFAULT '0' COMMENT '店铺id',
                                    `menu_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
                                    `menu_data` text COLLATE utf8mb4_unicode_ci COMMENT '菜单数据',
                                    `hits` int(11) NOT NULL DEFAULT '0' COMMENT '触发数',
                                    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                                    `status` tinyint(1) DEFAULT '1' COMMENT '是否启用',
                                    `create_time` bigint(16) unsigned DEFAULT NULL COMMENT '创建日期',
                                    `update_time` bigint(16) unsigned DEFAULT NULL COMMENT '修改日期',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='微信设置->微信菜单';

INSERT INTO `ymwl_weixin_menu` (`id`, `merchant_id`, `menu_name`, `menu_data`, `hits`, `sort`, `status`, `create_time`, `update_time`) VALUES
                                                                                                                                           (1,	0,	'默认菜单',	'[{\"key\": \"adsfasdfadfqweqweq\", \"url\": \"asdfasdfasdf\", \"name\": \"一级菜单1212\", \"type\": \"click\", \"sub_button\": [{\"key\": \"asdfasdfadf\", \"url\": \"http://static.kancloud.cn/manual/thinkphp6_0/1037632\", \"name\": \"二级菜单\", \"type\": \"click\", \"appid\": \"asdfa\", \"pagepath\": \"asdfasdf\"}]}]',	0,	0,	0,	1627434010,	1666269142),
                                                                                                                                           (2,	0,	'默认菜单',	'[{\"key\":\"guanjianzi\",\"name\":\"系统\",\"type\":\"click\",\"sub_button\":[{\"key\":\"关键字\",\"url\":\"http://www.baidu.com\",\"name\":\"百度\",\"type\":\"view\"},{\"key\":\"rselfmenu_0_0\",\"name\":\"扫码\",\"type\":\"scancode_waitmsg\"},{\"key\":\"rselfmenu_0_1\",\"name\":\"扫码2\",\"type\":\"scancode_push\"},{\"key\":\"rselfmenu_2_0\",\"name\":\"地理位置\",\"type\":\"location_select\"},{\"key\":\"rselfmenu_1_0\",\"name\":\"拍照发图\",\"type\":\"pic_sysphoto\"}]},{\"key\":\"rselfmenu_1_2\",\"name\":\"管理\",\"type\":\"pic_weixin\",\"sub_button\":[{\"key\":\"你好\",\"name\":\"点击事件\",\"type\":\"click\"},{\"key\":\"rselfmenu_1_1\",\"name\":\"拍照相册\",\"type\":\"pic_photo_or_album\"},{\"key\":\"rselfmenu_1_2\",\"name\":\"相册发图\",\"type\":\"pic_weixin\"}]},{\"key\":\"rselfmenu_0_0\",\"url\":\"http://www.sina.com\",\"name\":\"一级菜单1\",\"type\":\"view\",\"sub_button\":[{\"name\":\"baidu网络\",\"type\":\"view\",\"key\":\"\",\"url\":\"http://baidu.com/\"}]}]',	0,	0,	0,	1627458569,	1666269991),
                                                                                                                                           (3,	0,	'默认菜单',	'[{\"key\":\"消息\",\"name\":\"一级菜单\",\"type\":\"click\",\"sub_button\":[{\"key\":\"\",\"url\":\"http://www.funadmin.cn/backend/index/index.html\",\"name\":\"二级菜单3\",\"type\":\"view\"},{\"key\":\"6666\",\"name\":\"二级菜单\",\"type\":\"click\",\"appid\":\"1111\",\"pagepath\":\"page/page/page\"}]}]',	0,	0,	0,	1627539359,	1676385059);




SET FOREIGN_KEY_CHECKS = 1;
