<?php /*a:1:{s:52:"C:\wwwroot\127.0.0.1\app\admin\view\login\index.html";i:1753572713;}*/ ?>
<!DOCTYPE html>
<html lang="zh_cn">
<head>
    <script type="text/javascript">
        if (window.top.location.href!==location.href) {
            window.onload=function (){
                document.body.style.display="none";
            };
            window.top.location.href=location.href;
        }else{
            window.onload=function (){
                document.body.style.display="block"
            };
        }
    </script>
    <title><?php echo htmlentities($system['name']); ?>后台登录</title>
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/static/plugs/layui-v2.7.6/css/layui.css">
    <style type="text/css">
        html,body{width: 100%;min-height: 100%;}
        body{background: url(/static/img/loginbg.jpg) 0% 0% / cover no-repeat;}
        @keyframes rotate{0%{transform:perspective(400px) rotateZ(20deg) rotateX(-40deg) rotateY(0)}100%{transform:perspective(400px) rotateZ(20deg) rotateX(-40deg) rotateY(-360deg)}}
        #particles-js {position: absolute;width: 100%;height: 100%;}
        .login-box{padding:25px 40px;border:1px solid #aaa;border-radius:8px;background-color:rgba(255,255,255, 0.15);width:380px;position:fixed;left:50%;top:50%;z-index:999;margin:-220px 0 0 -220px;box-shadow:0 8px 32px rgba(0,0,0,0.3);backdrop-filter:blur(10px);}
        .layui-form-pane .layui-form-label{width:60px;background-color:rgba(255,255,255, 0.8);color:#333;height:50px;line-height:50px;border-radius:6px 0 0 6px;display:flex;align-items:center;justify-content:center;}
        .layui-form-pane .layui-form-label i{font-size:20px;color:#666;}
        .layui-form-pane .layui-input-block{margin-left:60px;}
        .login-box .layui-input{font-size:16px;font-weight:400;background-color:rgba(255,255,255, 0.6);color:#333;display:inline-block;height:50px;line-height:50px;padding:0 15px;border-radius:0 6px 6px 0;border:none;}
        .login-box .layui-input::placeholder{color:#666;}
        /*.login-box input[name="password"]{letter-spacing:5px;font-weight:800}*/
        .login-box input[type="submit"]{letter-spacing:5px;height:50px;font-size:16px;border-radius:6px;}
        .login-box input[name="vercode"]{width:120px;}
        .captcha{float:right;border-radius:6px;width:160px;height:50px;overflow:hidden;cursor:pointer;transition:all 0.3s ease;}
        .captcha:hover{transform:scale(1.05);}
        .login-box .layui-btn{width:100%;margin-top:10px;}
        .login-box .copyright{text-align:center;height:50px;line-height:50px;font-size:12px;color:#fff}
        .login-box .copyright a{color:#fff;}
        .layui-form-item{margin-bottom:20px;}
        fieldset legend{font-size:18px;font-weight:bold;}
        @media only screen and (min-width:750px){
            .login-box{width:450px;margin:-220px 0 0 -245px!important}
            .login-box input[name="vercode"]{width:200px;}
            .captcha{width:180px;}
        }
    </style>
</head>
<body>
<div id="particles-js">
    <div class="login-box">
        <form method="post" class="layui-form layui-form-pane">
            <?php echo token_field(); ?>
            <fieldset class="layui-elem-field layui-field-title">
                <legend style="color:#fff;">    <?php echo fy('Manage background logins'); ?></legend>
            </fieldset>
            <div class="layui-form-item">
                <label class="layui-form-label"><i class="layui-icon layui-icon-username"></i></label>
                <div class="layui-input-block">
                    <input type="text" name="username" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Username'); ?>" autofocus="autofocus" />
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i class="layui-icon layui-icon-password"></i></label>
                <div class="layui-input-block">
                    <input type="password" name="password" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Password'); ?>" />
                </div>
            </div>
            <?php if($system['code']): ?>
                <div class="layui-form-item">
                    <label class="layui-form-label"><i class="layui-icon layui-icon-vercode"></i></label>
                    <div class="layui-input-block">
                        <input type="text" name="vercode" class="layui-input" placeholder="<?php echo fy('Captcha'); ?>" autocomplete="off" lay-verify="required" /><img  height="50" width="160" style="position: absolute;top:0;right: 0;cursor: pointer;" id="captchaImg"  src="<?php echo url('verify'); ?>" alt="captcha" onclick="this.src=this.src+'?id='+Math.random()" class="captcha" />
                    </div>
                </div>
            <?php endif; ?>
            <input type="submit" value="<?php echo fy('Log In'); ?>" lay-submit lay-filter="login" class="layui-btn layui-btn-normal">
        </form>
        <div class="copyright">
            <?php echo htmlentities($system['name']); ?>
        </div>
    </div>
</div>
<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.min.js" charset="utf-8"></script>
<script src="/static/js/particles.min.js"></script>
<script type="text/javascript">
    layui.use('form',function(){
        var form = layui.form,$ = layui.jquery;
        //监听提交
        form.on('submit(login)', function(data){
            loading =layer.load(1, {shade: [0.1,'#fff'] });//0.1透明度的白色背景
            $.ajax({
                url: '<?php echo url("login/index"); ?>', // 发送请求的地址
                type: "POST", // 请求类型（GET、POST等）
                dataType: "json", // 预期返回数据的类型
                data: data.field, // 发送到服务器的数据
                success: function(res){
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1, time: 1000}, function(){
                            location.href = "<?php echo url('index/index'); ?>";
                        });
                    }else{
                        $('input[name="vercode"]').val('');
                        layer.msg(res.msg, {icon: 2, anim: 6, time: 1000});
                        $('#captchaImg').click();
                        $("input[name='__token__']").val(res.data.token);
                    }
                }, // 请求成功时的回调函数
                error: function(xhr, status, error){
                    layer.alert(error, {
                        icon: 2,
                        skin: 'layer-ext-demo'
                    });
                }, // 请求失败时的回调函数
                complete: function(xhr, status){
                    layer.close(loading);

                } // 请求完成时的回调函数
            });

            return false;
        });
    });

    particlesJS("particles-js",{"particles":{"number":{"value":50,"density":{"enable":true,"value_area":800}},"color":{"value":"#ffffff"},"opacity":{"value":0.2,"random":false,"anim":{"enable":false,"speed":1,"opacity_min":0.1,"sync":false}},"size":{"value":3,"random":true,"anim":{"enable":false,"speed":40,"size_min":0.1,"sync":false}},"line_linked":{"enable":true,"distance":150,"color":"#ffffff","opacity":0.4,"width":1},"move":{"enable":true,"speed":6,"direction":"none","random":false,"straight":false,"out_mode":"out","bounce":false,"attract":{"enable":false,"rotateX":600,"rotateY":1200}}},"interactivity":{"detect_on":"canvas","events":{"onhover":{"enable":true,"mode":"grab"},"onclick":{"enable":true,"mode":"push"},"resize":true},"modes":{"grab":{"distance":140,"line_linked":{"opacity":1}},"bubble":{"distance":400,"size":40,"duration":2,"opacity":8,"speed":3},"repulse":{"distance":200,"duration":0.4},"push":{"particles_nb":4},"remove":{"particles_nb":2}}},"retina_detect":true});
</script>
</body>
</html>
