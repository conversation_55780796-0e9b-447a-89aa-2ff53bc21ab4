<?php

namespace app\admin\controller\analysis;

use app\common\controller\AdminController;
use think\facade\Db;
use think\facade\View;

/**
 * 通话统计管理
 */
class CallStatistics extends AdminController
{
    protected $model;
    
    public function initialize()
    {
        parent::initialize();
        $this->model = new \app\admin\model\CrmCallStatistics();
    }
    
    /**
     * 通话统计列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            list($this->page, $this->pageSize, $sort, $where) = $this->buildParames();
            
            // 添加查询条件
            $username = $this->request->param('username', '');
            $startDate = $this->request->param('start_date', '');
            $endDate = $this->request->param('end_date', '');
            
            if ($username) {
                $where[] = ['username', 'like', '%' . $username . '%'];
            }
            
            if ($startDate) {
                $where[] = ['stat_date', '>=', $startDate];
            }
            
            if ($endDate) {
                $where[] = ['stat_date', '<=', $endDate];
            }
            
            // 非超级管理员只能查看自己的数据
            if ($this->admin['group_id'] != 1) {
                $where[] = ['admin_id', '=', $this->admin['admin_id']];
            }
            
            $count = $this->model->where($where)->count();
            $list = $this->model
                ->where($where)
                ->order($sort)
                ->page($this->page, $this->pageSize)
                ->select();
                
            // 处理数据
            foreach ($list as &$item) {
                $item['avg_duration_text'] = $this->formatDuration($item['avg_duration']);
                $item['total_duration_text'] = $this->formatDuration($item['total_duration']);
                $item['connect_rate_text'] = $item['connect_rate'] . '%';
            }
            
            $result = ['code' => 0, 'msg' => '获取成功', 'data' => $list, 'count' => $count];
            return json($result);
        }
        
        // 获取用户列表（用于筛选）
        $adminList = Db::name('admin')
            ->field('admin_id,username,realname')
            ->where('is_open', 1)
            ->select();
            
        View::assign('adminList', $adminList);
        return $this->fetch();
    }
    
    /**
     * 统计图表数据
     */
    public function chart()
    {
        $type = $this->request->param('type', 'daily'); // daily, weekly, monthly
        $username = $this->request->param('username', '');
        $days = (int)$this->request->param('days', 30);
        
        $where = [];
        
        // 权限控制
        if ($this->admin['group_id'] != 1) {
            $where[] = ['admin_id', '=', $this->admin['admin_id']];
        } elseif ($username) {
            $where[] = ['username', '=', $username];
        }
        
        // 时间范围
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $where[] = ['stat_date', 'between', [$startDate, $endDate]];
        
        $data = $this->model
            ->where($where)
            ->order('stat_date', 'asc')
            ->select();
            
        $chartData = [
            'dates' => [],
            'call_counts' => [],
            'avg_durations' => [],
            'connect_rates' => []
        ];
        
        foreach ($data as $item) {
            $chartData['dates'][] = $item['stat_date'];
            $chartData['call_counts'][] = $item['call_count'];
            $chartData['avg_durations'][] = round($item['avg_duration'] / 60, 2); // 转换为分钟
            $chartData['connect_rates'][] = $item['connect_rate'];
        }
        
        $this->jsonSuccess('获取成功', $chartData);
    }
    
    /**
     * 统计汇总
     */
    public function summary()
    {
        $username = $this->request->param('username', '');
        $startDate = $this->request->param('start_date', date('Y-m-01')); // 默认本月
        $endDate = $this->request->param('end_date', date('Y-m-d'));
        
        $where = [];
        
        // 权限控制
        if ($this->admin['group_id'] != 1) {
            $where[] = ['admin_id', '=', $this->admin['admin_id']];
        } elseif ($username) {
            $where[] = ['username', '=', $username];
        }
        
        if ($startDate) {
            $where[] = ['stat_date', '>=', $startDate];
        }
        
        if ($endDate) {
            $where[] = ['stat_date', '<=', $endDate];
        }
        
        $stats = $this->model->where($where)->select();
        
        if ($stats->isEmpty()) {
            $summary = [
                'total_days' => 0,
                'total_calls' => 0,
                'total_duration' => 0,
                'avg_calls_per_day' => 0,
                'avg_duration_per_call' => 0,
                'avg_connect_rate' => 0
            ];
        } else {
            $totalDays = $stats->count();
            $totalCalls = $stats->sum('call_count');
            $totalDuration = $stats->sum('total_duration');
            $avgConnectRate = $stats->avg('connect_rate');
            
            $summary = [
                'total_days' => $totalDays,
                'total_calls' => $totalCalls,
                'total_duration' => $totalDuration,
                'total_duration_text' => $this->formatDuration($totalDuration),
                'avg_calls_per_day' => $totalDays > 0 ? round($totalCalls / $totalDays, 2) : 0,
                'avg_duration_per_call' => $totalCalls > 0 ? round($totalDuration / $totalCalls, 2) : 0,
                'avg_duration_per_call_text' => $totalCalls > 0 ? $this->formatDuration(round($totalDuration / $totalCalls, 2)) : '0秒',
                'avg_connect_rate' => round($avgConnectRate, 2)
            ];
        }
        
        $this->jsonSuccess('获取成功', $summary);
    }
    
    /**
     * 导出数据
     */
    public function export()
    {
        $username = $this->request->param('username', '');
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        
        $where = [];
        
        // 权限控制
        if ($this->admin['group_id'] != 1) {
            $where[] = ['admin_id', '=', $this->admin['admin_id']];
        } elseif ($username) {
            $where[] = ['username', '=', $username];
        }
        
        if ($startDate) {
            $where[] = ['stat_date', '>=', $startDate];
        }
        
        if ($endDate) {
            $where[] = ['stat_date', '<=', $endDate];
        }
        
        $list = $this->model
            ->where($where)
            ->order('stat_date', 'desc')
            ->select();
            
        // 准备导出数据
        $exportData = [];
        $exportData[] = ['用户名', '统计日期', '通话量', '平均时长', '总时长', '接通率', '创建时间'];
        
        foreach ($list as $item) {
            $exportData[] = [
                $item['username'],
                $item['stat_date'],
                $item['call_count'],
                $this->formatDuration($item['avg_duration']),
                $this->formatDuration($item['total_duration']),
                $item['connect_rate'] . '%',
                date('Y-m-d H:i:s', $item['create_time'])
            ];
        }
        
        // 使用PhpSpreadsheet导出Excel
        $this->exportToExcel($exportData, '通话统计数据_' . date('YmdHis'));
    }
    
    /**
     * 删除统计记录
     */
    public function delete($id)
    {
        $row = $this->model->find($id);
        if (!$row) {
            $this->error('数据不存在');
        }
        
        // 权限检查：非超级管理员只能删除自己的数据
        if ($this->admin['group_id'] != 1 && $row['admin_id'] != $this->admin['admin_id']) {
            $this->error('无权限删除此数据');
        }
        
        if ($row->delete()) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 格式化时长显示
     */
    private function formatDuration($seconds)
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainSeconds = $seconds % 60;
            return $minutes . '分' . ($remainSeconds > 0 ? $remainSeconds . '秒' : '');
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $remainSeconds = $seconds % 60;
            $result = $hours . '小时';
            if ($minutes > 0) $result .= $minutes . '分';
            if ($remainSeconds > 0) $result .= $remainSeconds . '秒';
            return $result;
        }
    }
    
    /**
     * 导出Excel
     */
    private function exportToExcel($data, $filename)
    {
        // 这里可以使用PhpSpreadsheet或其他Excel导出库
        // 简化处理，直接输出CSV格式
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.csv"');
        header('Cache-Control: max-age=0');
        
        $output = fopen('php://output', 'w');
        
        // 添加BOM头，解决中文乱码
        fwrite($output, "\xEF\xBB\xBF");
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit();
    }
}
