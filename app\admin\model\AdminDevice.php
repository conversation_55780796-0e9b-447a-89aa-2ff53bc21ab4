<?php

namespace app\admin\model;

use app\common\model\TimeModel;
use think\facade\Db;

/**
 * 管理员设备模型
 */
class AdminDevice extends TimeModel
{
    protected $name = "admin_device";
    
    protected $deleteTime = false;
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            0 => '离线',
            1 => '在线'
        ];
    }
    
    /**
     * 检查用户是否有在线设备
     */
    public static function hasOnlineDevice($adminId)
    {
        $expiredTime = date('Y-m-d H:i:s', time() - 300); // 5分钟前
        $count = self::where('admin_id', $adminId)
            ->where('status', 1)
            ->where('last_active', '>=', $expiredTime)
            ->count();
            
        // 如果没有在线设备，清理过期的设备连接
        if ($count == 0) {
            self::where('admin_id', $adminId)
                ->where('last_active', '<', $expiredTime)
                ->update(['status' => 0]);
        }
        
        return $count > 0;
    }
    
    /**
     * 获取用户的在线设备
     */
    public static function getOnlineDevice($adminId)
    {
        $expiredTime = date('Y-m-d H:i:s', time() - 300); // 5分钟前
        $device = self::where('admin_id', $adminId)
            ->where('status', 1)
            ->where('last_active', '>=', $expiredTime)
            ->find();
            
        // 如果没有找到在线设备，清理过期的设备连接
        if (!$device) {
            self::where('admin_id', $adminId)
                ->where('last_active', '<', $expiredTime)
                ->update(['status' => 0]);
        }
        
        return $device;
    }
    
    /**
     * 断开用户的所有设备连接
     */
    public static function disconnectAllDevices($adminId)
    {
        return self::where('admin_id', $adminId)
            ->update(['status' => 0]);
    }
    
    /**
     * 设置设备在线状态
     */
    public static function setDeviceOnline($adminId, $deviceId, $socketId)
    {
        try {
            // 先断开该用户的所有设备（单设备登录限制）
            self::disconnectAllDevices($adminId);

            // 检查是否已存在该设备记录
            $device = self::where('admin_id', $adminId)
                ->where('device_id', $deviceId)
                ->find();

            if ($device) {
                // 更新现有记录
                $device->socket_id = $socketId;
                $device->status = 1;
                $device->last_active = date('Y-m-d H:i:s');
                return $device->save();
            } else {
                // 创建新记录
                return self::create([
                    'admin_id' => $adminId,
                    'device_id' => $deviceId,
                    'socket_id' => $socketId,
                    'status' => 1,
                    'last_active' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (\Exception $e) {
            // 如果是唯一约束冲突，尝试更新现有记录
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $device = self::where('admin_id', $adminId)
                    ->where('device_id', $deviceId)
                    ->find();

                if ($device) {
                    $device->socket_id = $socketId;
                    $device->status = 1;
                    $device->last_active = date('Y-m-d H:i:s');
                    return $device->save();
                }
            }
            throw $e;
        }
    }
    
    /**
     * 设置设备离线状态
     */
    public static function setDeviceOffline($socketId)
    {
        return self::where('socket_id', $socketId)
            ->update(['status' => 0]);
    }
    
    /**
     * 清理过期的设备连接（超过5分钟未活跃）
     */
    public static function cleanExpiredDevices()
    {
        $expiredTime = date('Y-m-d H:i:s', time() - 300); // 5分钟前
        
        return self::where('last_active', '<', $expiredTime)
            ->where('status', 1)
            ->update(['status' => 0]);
    }
    
    /**
     * 获取设备统计信息
     */
    public static function getDeviceStats()
    {
        $total = self::count();
        $online = self::where('status', 1)->count();
        $offline = $total - $online;
        
        return [
            'total' => $total,
            'online' => $online,
            'offline' => $offline
        ];
    }
    
    /**
     * 关联管理员表
     */
    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'admin_id');
    }
}
