define(["jquery", "easy-admin", "miniAdmin", "miniTab"], function ($, ea, miniAdmin, miniTab) {
    var Controller = {
        index: function () {
            var options={};
            options = {
                iniUrl: ea.url('ajax/initAdmin'),    // 初始化接口
                clearUrl: ea.url("index/clear"), // 缓存清理接口
                urlHashLocation: true,      // 是否打开hash定位
                bgColorDefault: 3,      // 主题默认配置
                multiModule: true,          // 是否开启多模块
                menuChildOpen: false,       // 是否默认展开菜单
                loadingTime: 0,             // 初始化加载时间
                pageAnim: true,             // iframe窗口动画
                maxTabNum: 28,              // 最大的tab打开数量
            };
            miniAdmin.render(options);

            $('.login-out').on("click", function () {
                window.location =ea.url('index/logout');
            });
            //发送ajax请求，获取用户信息     license
            $.ajax({
                //几个参数需要注意一下
                type: "POST",//方法类型
                dataType: "json",//预期服务器返回的数据类型
                url:ea.url("crm.license/index") ,
                success: function (result) {
                    console.log(result);
                    if (result['code'] ) {
                        $('.license').html(result['msg']);
                    }

                }
            });

            ea.listen();
        },
        main: function () {
            miniTab.listen();
            ea.listen();
        },
    };
    return Controller;
});
