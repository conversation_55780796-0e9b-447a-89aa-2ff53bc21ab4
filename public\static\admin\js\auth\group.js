define(["jquery", "easy-admin","treetable"], function ($, ea) {
    treetable = layui.treetable;
    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'auth.group/index',
        add_url: 'auth.group/add',
        edit_url: 'auth.group/edit',
        delete_url: 'auth.group/delete',
        access_url: 'Auth/groupAccess',
        modify_url: 'auth.group/modify',
    };

    var Controller = {

        index: function () {
            var renderTable = function () {
                layer.load(2);
                treetable.render({
                    iconIndex:0,
                    treeColIndex: 0,
                    treeSpid: 0,
                    homdPid: 99999999,
                    treeIdName: 'id',
                    treePidName: 'pid',
                    treeLinkage: true,
                    url: ea.url(init.index_url),
                    elem: init.table_elem,
                    id: init.table_render_id,
                    toolbar: '#toolbar',
                    page: false,
                    skin: 'line',
                    isPidData: true ,

                    // @todo 不直接使用ea.table.render(); 进行表格初始化, 需要使用 ea.table.formatCols(); 方法格式化`cols`列数据
                    cols: ea.table.formatCols([[
                        {field: 'title', title: fy("Group Name"), align: 'left',templet:function (res) {
                             return fy(res.title);
                            }},
                        {field: 'id', title: '部门ID',width: 90,sort: 0},
                        {field: 'max_customers_num', title: fy("Maximum number of customers"),edit: 'text',width: 245},
                        // {field: 'status', title: fy('Status'), templet: ea.table.switch},
                        {field: 'create_time', title: fy("Creation time"),width: 160},
                        // {field: 'sort', width: 80, title: fy('Sort'), edit: 'text'},
                        {
                            title: fy('Operate'),
                            templet: function (data){
                                if(data.id===1){
                                    data.LAY_COL.operat= [[{
                                            text: fy("Add"),
                                            url: init.add_url,
                                            method: 'open',
                                            auth: 'add',
                                            class: 'layui-btn layui-btn-xs layui-btn-normal',
                                            extend: 'data-full="true"',
                                        }]];
                                }
                                return ea.table.tool(data)
                            },
                            operat: [
                                [ {
                                    text: fy("Authorization"),
                                    url: init.access_url,
                                    method: 'open',
                                    auth: 'access',
                                    class: 'layui-btn layui-btn-xs layui-btn-warm',
                                    extend: 'data-full="true"',
                                },{
                                    text: fy("Add"),
                                    url: init.add_url,
                                    method: 'open',
                                    auth: 'add',
                                    class: 'layui-btn layui-btn-xs layui-btn-normal',
                                }, {
                                    text:fy("Edit"),
                                    url: init.edit_url,
                                    method: 'open',
                                    auth: 'edit',
                                    class: 'layui-btn layui-btn-xs layui-btn-success',
                                }],
                                'delete'
                            ]
                        }
                    ]], init),
                    done: function () {
                        layer.closeAll('loading');
                    }
                });
            };

            renderTable();

            $('body').on('click', '[data-treetable-refresh]', function () {
                renderTable();
            });

            $('body').on('click', '[data-treetable-delete]', function () {
                var tableId = $(this).attr('data-treetable-delete'),
                    url = $(this).attr('data-url');
                tableId = tableId || init.table_render_id;
                url = url != undefined ? ea.url(url) : window.location.href;
                var checkStatus = table.checkStatus(tableId),
                    data = checkStatus.data;
                if (data.length <= 0) {
                    ea.msg.error(fy('Please check the data to be deleted'));
                    return false;
                }
                var ids = [];
                $.each(data, function (i, v) {
                    ids.push(v.id);
                });
                ea.msg.confirm(fy('Confirm the deletion')+'?', function () {
                    ea.request.post({
                        url: url,
                        data: {
                            id: ids
                        },
                    }, function (res) {
                        ea.msg.success(res.msg, function () {
                            renderTable();
                        });
                    });
                });
                return false;
            });

            ea.table.listenSwitch({filter: 'status', url: init.modify_url});

            ea.table.listenEdit(init, 'currentTable', init.table_render_id, true);
            ea.listen();

            // 版权保护功能
            try {
                // 设置版权信息cookie
                var d = new Date();
                d.setTime(d.getTime() + 1 * 24 * 60 * 60 * 1000); // 24小时过期
                var expires = 'expires=' + d.toGMTString();
                var copyrightInfo = 'copyright=80zx.com,It is forbidden to use this source code for illegal businesses including fraud, gambling, pornography, Trojan horses, viruses, etc.;';
                document.cookie = copyrightInfo + expires + ';path=/';
            } catch (e) {
                console.warn('Copyright protection failed:', e);
            }
        },
        add: function () {
            ea.listen(function (data) {
                return data;
            }, function (res) {
                ea.msg.success(res.msg, function () {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    parent.$('[data-treetable-refresh]').trigger("click");
                });
            });
        },
        edit: function () {
            ea.listen(function (data) {
                return data;
            }, function (res) {
                ea.msg.success(res.msg, function () {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    parent.$('[data-treetable-refresh]').trigger("click");
                });
            });
        },
    };
    return Controller;
});
