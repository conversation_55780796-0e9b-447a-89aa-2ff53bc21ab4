if(Wind){
    Wind.css('animate');
}
!function(root, factory) {
	 if (typeof define === 'function' && define.amd) {
		 define(['jquery'], factory);
	 } else if (typeof exports === 'object') {
		 module.exports = factory(require('jquery'));
	 } else {
		 factory(root.jQuery);
	 }
}(this, function($) {

/*!
 @package noty - jQuery Notification Plugin
 @version version: 2.4.1
 @contributors https://github.com/needim/noty/graphs/contributors

 @documentation Examples and Documentation - http://needim.github.com/noty/

 @license Licensed under the MIT licenses: http://www.opensource.org/licenses/mit-license.php
 */

if (typeof Object.create !== 'function') {
  Object.create = function (o) {
    function F() {
    }

    F.prototype = o;
    return new F();
  };
}

var NotyObject = {

  init: function (options) {

    // Mix in the passed in options with the default options
    this.options = $.extend({}, $.noty.defaults, options);

    this.options.layout = (this.options.custom) ? $.noty.layouts['inline'] : $.noty.layouts[this.options.layout];

    if ($.noty.themes[this.options.theme]) {
      this.options.theme = $.noty.themes[this.options.theme];
      if (this.options.theme.template)
        this.options.template = this.options.theme.template;

      if (this.options.theme.animation)
        this.options.animation = this.options.theme.animation;
    }
    else {
      this.options.themeClassName = this.options.theme;
    }

    this.options = $.extend({}, this.options, this.options.layout.options);

    if (this.options.id) {
      if ($.noty.store[this.options.id]) {
        return $.noty.store[this.options.id];
      }
    } else {
      this.options.id = 'noty_' + (new Date().getTime() * Math.floor(Math.random() * 1000000));
    }

    // Build the noty dom initial structure
    this._build();

    // return this so we can chain/use the bridge with less code.
    return this;
  }, // end init

  _build: function () {

    // Generating noty bar
    var $bar = $('<div class="noty_bar noty_type_' + this.options.type + '"></div>').attr('id', this.options.id);
    $bar.append(this.options.template).find('.noty_text').html(this.options.text);

    this.$bar = (this.options.layout.parent.object !== null) ? $(this.options.layout.parent.object).css(this.options.layout.parent.css).append($bar) : $bar;

    if (this.options.themeClassName)
      this.$bar.addClass(this.options.themeClassName).addClass('noty_container_type_' + this.options.type);

    // Set buttons if available
    if (this.options.buttons) {

      var $buttons;
      // Try find container for buttons in presented template, and create it if not found
      if (this.$bar.find('.noty_buttons').length > 0) {
        $buttons = this.$bar.find('.noty_buttons');
      } else {
        $buttons = $('<div/>').addClass('noty_buttons');
        (this.options.layout.parent.object !== null) ? this.$bar.find('.noty_bar').append($buttons) : this.$bar.append($buttons);
      }

      var self = this;

      $.each(this.options.buttons, function (i, button) {
        var $button = $('<button/>').addClass((button.addClass) ? button.addClass : 'gray').html(button.text).attr('id', button.id ? button.id : 'button-' + i)
            .attr('title', button.title)
            .appendTo($buttons)
            .on('click', function (event) {
              if ($.isFunction(button.onClick)) {
                button.onClick.call($button, self, event);
              }
            });
      });
    } else {
      // If buttons is not available, then remove containers if exist
      this.$bar.find('.noty_buttons').remove();
    }

    if (this.options.progressBar && this.options.timeout) {
      var $progressBar = $('<div/>').addClass('noty_progress_bar');
      (this.options.layout.parent.object !== null) ? this.$bar.find('.noty_bar').append($progressBar) : this.$bar.append($progressBar);
    }

    // For easy access
    this.$message     = this.$bar.find('.noty_message');
    this.$closeButton = this.$bar.find('.noty_close');
    this.$buttons     = this.$bar.find('.noty_buttons');
    this.$progressBar = this.$bar.find('.noty_progress_bar');

    $.noty.store[this.options.id] = this; // store noty for api

  }, // end _build

  show: function () {

    var self = this;

    (self.options.custom) ? self.options.custom.find(self.options.layout.container.selector).append(self.$bar) : $(self.options.layout.container.selector).append(self.$bar);

    if (self.options.theme && self.options.theme.style)
      self.options.theme.style.apply(self);

    ($.type(self.options.layout.css) === 'function') ? this.options.layout.css.apply(self.$bar) : self.$bar.css(this.options.layout.css || {});

    self.$bar.addClass(self.options.layout.addClass);

    self.options.layout.container.style.apply($(self.options.layout.container.selector), [self.options.within]);

    self.showing = true;

    if (self.options.theme && self.options.theme.style)
      self.options.theme.callback.onShow.apply(this);

    if ($.inArray('click', self.options.closeWith) > -1)
      self.$bar.css('cursor', 'pointer').on('click', function (evt) {
        self.stopPropagation(evt);
        if (self.options.callback.onCloseClick) {
          self.options.callback.onCloseClick.apply(self);
        }
        self.close();
      });

    if ($.inArray('hover', self.options.closeWith) > -1)
      self.$bar.one('mouseenter', function () {
        self.close();
      });

    if ($.inArray('button', self.options.closeWith) > -1)
      self.$closeButton.one('click', function (evt) {
        self.stopPropagation(evt);
        self.close();
      });

    if ($.inArray('button', self.options.closeWith) == -1)
      self.$closeButton.remove();

    if (self.options.callback.beforeShow)
      self.options.callback.beforeShow.apply(self);

    if (typeof self.options.animation.open == 'string') {
      self.animationTypeOpen = 'css';
      self.$bar.css('min-height', self.$bar.innerHeight());
      self.$bar.on('click', function (e) {
        self.wasClicked = true;
      });
      self.$bar.show();

      if (self.options.callback.onShow)
        self.options.callback.onShow.apply(self);

      self.$bar.addClass(self.options.animation.open).one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function () {
        if (self.options.callback.afterShow) self.options.callback.afterShow.apply(self);
        self.showing = false;
        self.shown   = true;
        self.bindTimeout();
        if (self.hasOwnProperty('wasClicked')) {
          self.$bar.off('click', function (e) {
            self.wasClicked = true;
          });
          self.close();
        }
      });

    } else if (typeof self.options.animation.open == 'object' && self.options.animation.open == null) {
      self.animationTypeOpen = 'none';
      self.showing           = false;
      self.shown             = true;
      self.$bar.show();
      self.bindTimeout();

      if (self.options.callback.onShow)
        self.options.callback.onShow.apply(self);

      self.$bar.queue(function () {
        if (self.options.callback.afterShow)
          self.options.callback.afterShow.apply(self);
      });

    } else {
      self.animationTypeOpen = 'anim';

      if (self.options.callback.onShow)
        self.options.callback.onShow.apply(self);

      self.$bar.animate(
          self.options.animation.open,
          self.options.animation.speed,
          self.options.animation.easing,
          function () {
            if (self.options.callback.afterShow) self.options.callback.afterShow.apply(self);
            self.showing = false;
            self.shown   = true;
            self.bindTimeout();
          });
    }

    return this;

  }, // end show

  bindTimeout: function () {
    var self = this;

    // If noty is have a timeout option
    if (self.options.timeout) {

      if (self.options.progressBar && self.$progressBar) {
        self.$progressBar.css({
          transition: 'all ' + self.options.timeout + 'ms linear',
          width: '0%'
        });
      }

      self.queueClose(self.options.timeout);
      self.$bar.on('mouseenter', self.dequeueClose.bind(self));
      self.$bar.on('mouseleave', self.queueClose.bind(self, self.options.timeout));
    }

  },

  dequeueClose: function () {
    var self = this;

    if (self.options.progressBar) {
      this.$progressBar.css({
        transition: 'none',
        width: '100%'
      });
    }

    if (!this.closeTimer) return;
    clearTimeout(this.closeTimer);
    this.closeTimer = null;
  },

  queueClose: function (timeout) {
    var self = this;

    if (self.options.progressBar) {
      self.$progressBar.css({
        transition: 'all ' + self.options.timeout + 'ms linear',
        width: '0%'
      });
    }

    if (this.closeTimer) return;
    self.closeTimer = window.setTimeout(function () {
      self.close();
    }, timeout);
    return self.closeTimer;
  },

  close: function () {
    if (this.$progressBar) {
      this.$progressBar.remove();
    }

    if (this.closeTimer) this.dequeueClose();

    if (this.closed) return;
    if (this.$bar && this.$bar.hasClass('i-am-closing-now')) return;

    var self = this;

    if (this.showing && (this.animationTypeOpen == 'anim' || this.animationTypeOpen == 'none')) {
      self.$bar.queue(
          function () {
            self.close.apply(self);
          }
      );
      return;
    } else if (this.showing && this.animationTypeOpen == 'css') {
      self.$bar.on('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function () {
        self.close();
      });
    }

    if (!this.shown && !this.showing) { // If we are still waiting in the queue just delete from queue
      var queue = [];
      $.each($.noty.queue, function (i, n) {
        if (n.options.id != self.options.id) {
          queue.push(n);
        }
      });
      $.noty.queue = queue;
      return;
    }

    self.$bar.addClass('i-am-closing-now');

    if (self.options.callback.onClose) {
      self.options.callback.onClose.apply(self);
    }

    if (typeof self.options.animation.close == 'string') {
      self.$bar.removeClass(self.options.animation.open).addClass(self.options.animation.close).one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function () {
        if (self.options.callback.afterClose) self.options.callback.afterClose.apply(self);
        self.closeCleanUp();
      });

    } else if (typeof self.options.animation.close == 'object' && self.options.animation.close == null) {
      self.$bar.dequeue().hide(0, function () {
        if (self.options.callback.afterClose) self.options.callback.afterClose.apply(self);
        self.closeCleanUp();
      });

    } else {
      self.$bar.clearQueue().stop().animate(
          self.options.animation.close,
          self.options.animation.speed,
          self.options.animation.easing,
          function () {
            if (self.options.callback.afterClose) self.options.callback.afterClose.apply(self);
          })
          .promise().done(function () {
        self.closeCleanUp();
      });
    }

  }, // end close

  closeCleanUp: function () {

    var self = this;

    // Modal Cleaning
    if (self.options.modal) {
      $.notyRenderer.setModalCount(-1);
      if ($.notyRenderer.getModalCount() == 0 && !$.noty.queue.length) $('.noty_modal').fadeOut(self.options.animation.fadeSpeed, function () {
        $(this).remove();
      });
    }

    // Layout Cleaning
    $.notyRenderer.setLayoutCountFor(self, -1);
    if ($.notyRenderer.getLayoutCountFor(self) == 0) $(self.options.layout.container.selector).remove();

    // Make sure self.$bar has not been removed before attempting to remove it
    if (typeof self.$bar !== 'undefined' && self.$bar !== null) {

      if (typeof self.options.animation.close == 'string') {
        self.$bar.css('transition', 'all 10ms ease').css('border', 0).css('margin', 0).height(0);
        self.$bar.one('transitionend webkitTransitionEnd oTransitionEnd MSTransitionEnd', function () {
          self.$bar.remove();
          self.$bar   = null;
          self.closed = true;

          if (self.options.theme.callback && self.options.theme.callback.onClose) {
            self.options.theme.callback.onClose.apply(self);
          }

          self.handleNext();
        });
      } else {
        self.$bar.remove();
        self.$bar   = null;
        self.closed = true;

        self.handleNext();
      }
    } else {
      self.handleNext();
    }

  }, // end close clean up

  handleNext: function () {
    var self = this;

    delete $.noty.store[self.options.id]; // deleting noty from store

    if (self.options.theme.callback && self.options.theme.callback.onClose) {
      self.options.theme.callback.onClose.apply(self);
    }

    if (!self.options.dismissQueue) {
      // Queue render
      $.noty.ontap = true;

      $.notyRenderer.render();
    }

    if (self.options.maxVisible > 0 && self.options.dismissQueue) {
      $.notyRenderer.render();
    }
  },

  setText: function (text) {
    if (!this.closed) {
      this.options.text = text;
      this.$bar.find('.noty_text').html(text);
    }
    return this;
  },

  setType: function (type) {
    if (!this.closed) {
      this.options.type = type;
      this.options.theme.style.apply(this);
      this.options.theme.callback.onShow.apply(this);
    }
    return this;
  },

  setTimeout: function (time) {
    if (!this.closed) {
      var self             = this;
      this.options.timeout = time;
      self.$bar.delay(self.options.timeout).promise().done(function () {
        self.close();
      });
    }
    return this;
  },

  stopPropagation: function (evt) {
    evt = evt || window.event;
    if (typeof evt.stopPropagation !== "undefined") {
      evt.stopPropagation();
    }
    else {
      evt.cancelBubble = true;
    }
  },

  closed : false,
  showing: false,
  shown  : false

}; // end NotyObject

$.notyRenderer = {};

$.notyRenderer.init = function (options) {

  // Renderer creates a new noty
  var notification = Object.create(NotyObject).init(options);

  if (notification.options.killer)
    $.noty.closeAll();

  (notification.options.force) ? $.noty.queue.unshift(notification) : $.noty.queue.push(notification);

  $.notyRenderer.render();

  return ($.noty.returns == 'object') ? notification : notification.options.id;
};

$.notyRenderer.render = function () {

  var instance = $.noty.queue[0];

  if ($.type(instance) === 'object') {
    if (instance.options.dismissQueue) {
      if (instance.options.maxVisible > 0) {
        if ($(instance.options.layout.container.selector + ' > li').length < instance.options.maxVisible) {
          $.notyRenderer.show($.noty.queue.shift());
        }
        else {

        }
      }
      else {
        $.notyRenderer.show($.noty.queue.shift());
      }
    }
    else {
      if ($.noty.ontap) {
        $.notyRenderer.show($.noty.queue.shift());
        $.noty.ontap = false;
      }
    }
  }
  else {
    $.noty.ontap = true; // Queue is over
  }

};

$.notyRenderer.show = function (notification) {

  if (notification.options.modal) {
    $.notyRenderer.createModalFor(notification);
    $.notyRenderer.setModalCount(+1);
  }

  // Where is the container?
  if (notification.options.custom) {
    if (notification.options.custom.find(notification.options.layout.container.selector).length == 0) {
      notification.options.custom.append($(notification.options.layout.container.object).addClass('i-am-new'));
    }
    else {
      notification.options.custom.find(notification.options.layout.container.selector).removeClass('i-am-new');
    }
  }
  else {
    if ($(notification.options.layout.container.selector).length == 0) {
      $('body').append($(notification.options.layout.container.object).addClass('i-am-new'));
    }
    else {
      $(notification.options.layout.container.selector).removeClass('i-am-new');
    }
  }

  $.notyRenderer.setLayoutCountFor(notification, +1);

  notification.show();
};

$.notyRenderer.createModalFor = function (notification) {
  if ($('.noty_modal').length == 0) {
    var modal = $('<div/>').addClass('noty_modal').addClass(notification.options.theme).data('noty_modal_count', 0);

    if (notification.options.theme.modal && notification.options.theme.modal.css)
      modal.css(notification.options.theme.modal.css);

    modal.prependTo($('body')).fadeIn(notification.options.animation.fadeSpeed);

    if ($.inArray('backdrop', notification.options.closeWith) > -1)
      modal.on('click', function () {
        $.noty.closeAll();
      });
  }
};

$.notyRenderer.getLayoutCountFor = function (notification) {
  return $(notification.options.layout.container.selector).data('noty_layout_count') || 0;
};

$.notyRenderer.setLayoutCountFor = function (notification, arg) {
  return $(notification.options.layout.container.selector).data('noty_layout_count', $.notyRenderer.getLayoutCountFor(notification) + arg);
};

$.notyRenderer.getModalCount = function () {
  return $('.noty_modal').data('noty_modal_count') || 0;
};

$.notyRenderer.setModalCount = function (arg) {
  return $('.noty_modal').data('noty_modal_count', $.notyRenderer.getModalCount() + arg);
};

// This is for custom container
$.fn.noty = function (options) {
  options.custom = $(this);
  return $.notyRenderer.init(options);
};

$.noty         = {};
$.noty.queue   = [];
$.noty.ontap   = true;
$.noty.layouts = {};
$.noty.themes  = {};
$.noty.returns = 'object';
$.noty.store   = {};

$.noty.get = function (id) {
  return $.noty.store.hasOwnProperty(id) ? $.noty.store[id] : false;
};

$.noty.close = function (id) {
  return $.noty.get(id) ? $.noty.get(id).close() : false;
};

$.noty.setText = function (id, text) {
  return $.noty.get(id) ? $.noty.get(id).setText(text) : false;
};

$.noty.setType = function (id, type) {
  return $.noty.get(id) ? $.noty.get(id).setType(type) : false;
};

$.noty.clearQueue = function () {
  $.noty.queue = [];
};

$.noty.closeAll = function () {
  $.noty.clearQueue();
  $.each($.noty.store, function (id, noty) {
    noty.close();
  });
};

var windowAlert = window.alert;

$.noty.consumeAlert = function (options) {
  window.alert = function (text) {
    if (options)
      options.text = text;
    else
      options = {text: text};

    $.notyRenderer.init(options);
  };
};

$.noty.stopConsumeAlert = function () {
  window.alert = windowAlert;
};

$.noty.defaults = {
  layout      : 'topRight',
  theme       : 'relax',
  type        : 'alert',
  text        : '',
  progressBar : false,
  dismissQueue: true,
  template    : '<div class="noty_message"><span class="noty_text"></span><div class="noty_close"></div></div>',
  animation   : {
    open     : {height: 'toggle'},
    close    : {height: 'toggle'},
    easing   : 'swing',
    speed    : 500,
    fadeSpeed: 'fast'
  },
  timeout     : false,
  force       : false,
  modal       : false,
  maxVisible  : 5,
  killer      : false,
  closeWith   : ['click'],
  callback    : {
    beforeShow  : function () {
    },
    onShow      : function () {
    },
    afterShow   : function () {
    },
    onClose     : function () {
    },
    afterClose  : function () {
    },
    onCloseClick: function () {
    }
  },
  buttons     : false
};

$(window).on('resize', function () {
  $.each($.noty.layouts, function (index, layout) {
    layout.container.style.apply($(layout.container.selector));
  });
});

// Helpers
window.noty = function noty(options) {
  return $.notyRenderer.init(options);
};

$.noty.layouts.bottom = {
    name     : 'bottom',
    options  : {},
    container: {
        object  : '<ul id="noty_bottom_layout_container" />',
        selector: 'ul#noty_bottom_layout_container',
        style   : function() {
            $(this).css({
                bottom       : 0,
                left         : '5%',
                position     : 'fixed',
                width        : '90%',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 9999999
            });
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none'
    },
    addClass : ''
};

$.noty.layouts.bottomCenter = {
    name     : 'bottomCenter',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_bottomCenter_layout_container" />',
        selector: 'ul#noty_bottomCenter_layout_container',
        style   : function() {
            $(this).css({
                bottom       : 20,
                left         : 0,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            $(this).css({
                left: ($(window).width() - $(this).outerWidth(false)) / 2 + 'px'
            });
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};


$.noty.layouts.bottomLeft = {
    name     : 'bottomLeft',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_bottomLeft_layout_container" />',
        selector: 'ul#noty_bottomLeft_layout_container',
        style   : function() {
            $(this).css({
                bottom       : 20,
                left         : 20,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            if(window.innerWidth < 600) {
                $(this).css({
                    left: 5
                });
            }
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};
$.noty.layouts.bottomRight = {
    name     : 'bottomRight',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_bottomRight_layout_container" />',
        selector: 'ul#noty_bottomRight_layout_container',
        style   : function() {
            $(this).css({
                bottom       : 20,
                right        : 20,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            if(window.innerWidth < 600) {
                $(this).css({
                    right: 5
                });
            }
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};
$.noty.layouts.center = {
    name     : 'center',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_center_layout_container" />',
        selector: 'ul#noty_center_layout_container',
        style   : function() {
            $(this).css({
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            // getting hidden height
            var dupe = $(this).clone().css({visibility: "hidden", display: "block", position: "absolute", top: 0, left: 0}).attr('id', 'dupe');
            $("body").append(dupe);
            dupe.find('.i-am-closing-now').remove();
            dupe.find('li').css('display', 'block');
            var actual_height = dupe.height();
            dupe.remove();

            if($(this).hasClass('i-am-new')) {
                $(this).css({
                    left: ($(window).width() - $(this).outerWidth(false)) / 2 + 'px',
                    top : ($(window).height() - actual_height) / 2 + 'px'
                });
            }
            else {
                $(this).animate({
                    left: ($(window).width() - $(this).outerWidth(false)) / 2 + 'px',
                    top : ($(window).height() - actual_height) / 2 + 'px'
                }, 500);
            }

        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};
$.noty.layouts.centerLeft = {
    name     : 'centerLeft',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_centerLeft_layout_container" />',
        selector: 'ul#noty_centerLeft_layout_container',
        style   : function() {
            $(this).css({
                left         : 20,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            // getting hidden height
            var dupe = $(this).clone().css({visibility: "hidden", display: "block", position: "absolute", top: 0, left: 0}).attr('id', 'dupe');
            $("body").append(dupe);
            dupe.find('.i-am-closing-now').remove();
            dupe.find('li').css('display', 'block');
            var actual_height = dupe.height();
            dupe.remove();

            if($(this).hasClass('i-am-new')) {
                $(this).css({
                    top: ($(window).height() - actual_height) / 2 + 'px'
                });
            }
            else {
                $(this).animate({
                    top: ($(window).height() - actual_height) / 2 + 'px'
                }, 500);
            }

            if(window.innerWidth < 600) {
                $(this).css({
                    left: 5
                });
            }

        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};

$.noty.layouts.centerRight = {
    name     : 'centerRight',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_centerRight_layout_container" />',
        selector: 'ul#noty_centerRight_layout_container',
        style   : function() {
            $(this).css({
                right        : 20,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            // getting hidden height
            var dupe = $(this).clone().css({visibility: "hidden", display: "block", position: "absolute", top: 0, left: 0}).attr('id', 'dupe');
            $("body").append(dupe);
            dupe.find('.i-am-closing-now').remove();
            dupe.find('li').css('display', 'block');
            var actual_height = dupe.height();
            dupe.remove();

            if($(this).hasClass('i-am-new')) {
                $(this).css({
                    top: ($(window).height() - actual_height) / 2 + 'px'
                });
            }
            else {
                $(this).animate({
                    top: ($(window).height() - actual_height) / 2 + 'px'
                }, 500);
            }

            if(window.innerWidth < 600) {
                $(this).css({
                    right: 5
                });
            }

        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};
$.noty.layouts.inline = {
    name     : 'inline',
    options  : {},
    container: {
        object  : '<ul class="noty_inline_layout_container" />',
        selector: 'ul.noty_inline_layout_container',
        style   : function() {
            $(this).css({
                width        : '100%',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 9999999
            });
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none'
    },
    addClass : ''
};
$.noty.layouts.top = {
    name     : 'top',
    options  : {},
    container: {
        object  : '<ul id="noty_top_layout_container" />',
        selector: 'ul#noty_top_layout_container',
        style   : function() {
            $(this).css({
                top          : 0,
                left         : '5%',
                position     : 'fixed',
                width        : '90%',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 9999999
            });
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none'
    },
    addClass : ''
};
$.noty.layouts.topCenter = {
    name     : 'topCenter',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_topCenter_layout_container" />',
        selector: 'ul#noty_topCenter_layout_container',
        style   : function() {
            $(this).css({
                top          : 20,
                left         : 0,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            $(this).css({
                left: ($(window).width() - $(this).outerWidth(false)) / 2 + 'px'
            });
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};

$.noty.layouts.topLeft = {
    name     : 'topLeft',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_topLeft_layout_container" />',
        selector: 'ul#noty_topLeft_layout_container',
        style   : function() {
            $(this).css({
                top          : 20,
                left         : 20,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            if(window.innerWidth < 600) {
                $(this).css({
                    left: 5
                });
            }
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};
$.noty.layouts.topRight = {
    name     : 'topRight',
    options  : { // overrides options

    },
    container: {
        object  : '<ul id="noty_topRight_layout_container" />',
        selector: 'ul#noty_topRight_layout_container',
        style   : function() {
            $(this).css({
                top          : 20,
                right        : 20,
                position     : 'fixed',
                width        : '310px',
                height       : 'auto',
                margin       : 0,
                padding      : 0,
                listStyleType: 'none',
                zIndex       : 10000000
            });

            if(window.innerWidth < 600) {
                $(this).css({
                    right: 5
                });
            }
        }
    },
    parent   : {
        object  : '<li />',
        selector: 'li',
        css     : {}
    },
    css      : {
        display: 'none',
        width  : '310px'
    },
    addClass : ''
};
$.noty.themes.bootstrapTheme = {
  name    : 'bootstrapTheme',
  modal   : {
    css: {
      position       : 'fixed',
      width          : '100%',
      height         : '100%',
      backgroundColor: '#000',
      zIndex         : 10000,
      opacity        : 0.6,
      display        : 'none',
      left           : 0,
      top            : 0,
      wordBreak      : 'break-all'
    }
  },
  style   : function () {

    var containerSelector = this.options.layout.container.selector;
    $(containerSelector).addClass('list-group');

    this.$closeButton.append('<span aria-hidden="true">&times;</span><span class="sr-only">Close</span>');
    this.$closeButton.addClass('close');

    this.$bar.addClass("list-group-item").css('padding', '0px').css('position', 'relative');

    this.$progressBar.css({
      position       : 'absolute',
      left           : 0,
      bottom         : 0,
      height         : 4,
      width          : '100%',
      backgroundColor: '#000000',
      opacity        : 0.2,
      '-ms-filter'   : 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)',
      filter         : 'alpha(opacity=20)'
    });

    switch (this.options.type) {
      case 'alert':
      case 'notification':
        this.$bar.addClass("list-group-item-info");
        break;
      case 'warning':
        this.$bar.addClass("list-group-item-warning");
        break;
      case 'error':
        this.$bar.addClass("list-group-item-danger");
        break;
      case 'information':
        this.$bar.addClass("list-group-item-info");
        break;
      case 'success':
        this.$bar.addClass("list-group-item-success");
        break;
    }

    this.$message.css({
      textAlign: 'center',
      padding  : '8px 10px 9px',
      width    : 'auto',
      position : 'relative'
    });
  },
  callback: {
    onShow : function () { },
    onClose: function () { }
  }
};


$.noty.themes.defaultTheme = {
  name    : 'defaultTheme',
  helpers : {
    borderFix: function () {
      if (this.options.dismissQueue) {
        var selector = this.options.layout.container.selector + ' ' + this.options.layout.parent.selector;
        switch (this.options.layout.name) {
          case 'top':
            $(selector).css({borderRadius: '0px 0px 0px 0px'});
            $(selector).last().css({borderRadius: '0px 0px 5px 5px'});
            break;
          case 'topCenter':
          case 'topLeft':
          case 'topRight':
          case 'bottomCenter':
          case 'bottomLeft':
          case 'bottomRight':
          case 'center':
          case 'centerLeft':
          case 'centerRight':
          case 'inline':
            $(selector).css({borderRadius: '0px 0px 0px 0px'});
            $(selector).first().css({'border-top-left-radius': '5px', 'border-top-right-radius': '5px'});
            $(selector).last().css({'border-bottom-left-radius': '5px', 'border-bottom-right-radius': '5px'});
            break;
          case 'bottom':
            $(selector).css({borderRadius: '0px 0px 0px 0px'});
            $(selector).first().css({borderRadius: '5px 5px 0px 0px'});
            break;
          default:
            break;
        }
      }
    }
  },
  modal   : {
    css: {
      position       : 'fixed',
      width          : '100%',
      height         : '100%',
      backgroundColor: '#000',
      zIndex         : 10000,
      opacity        : 0.006,
      display        : 'none',
      left           : 0,
      top            : 0
    }
  },
  style   : function () {

    this.$bar.css({
      overflow  : 'hidden',
      background: "url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAoCAQAAAClM0ndAAAAhklEQVR4AdXO0QrCMBBE0bttkk38/w8WRERpdyjzVOc+HxhIHqJGMQcFFkpYRQotLLSw0IJ5aBdovruMYDA/kT8plF9ZKLFQcgF18hDj1SbQOMlCA4kao0iiXmah7qBWPdxpohsgVZyj7e5I9KcID+EhiDI5gxBYKLBQYKHAQoGFAoEks/YEGHYKB7hFxf0AAAAASUVORK5CYII=') repeat-x scroll left top #fff",
      position  : 'relative'
    });

    this.$progressBar.css({
      position       : 'absolute',
      left           : 0,
      bottom         : 0,
      height         : 4,
      width          : '100%',
      backgroundColor: '#000000',
      opacity        : 0.2,
      '-ms-filter'   : 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)',
      filter         : 'alpha(opacity=20)'
    });

    this.$message.css({
      textAlign: 'center',
      padding  : '8px 10px 9px',
      width    : 'auto',
      position : 'relative'
    });

    this.$closeButton.css({
      position  : 'absolute',
      top       : 4, right: 4,
      width     : 10, height: 10,
      background: "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAQAAAAnOwc2AAAAxUlEQVR4AR3MPUoDURSA0e++uSkkOxC3IAOWNtaCIDaChfgXBMEZbQRByxCwk+BasgQRZLSYoLgDQbARxry8nyumPcVRKDfd0Aa8AsgDv1zp6pYd5jWOwhvebRTbzNNEw5BSsIpsj/kurQBnmk7sIFcCF5yyZPDRG6trQhujXYosaFoc+2f1MJ89uc76IND6F9BvlXUdpb6xwD2+4q3me3bysiHvtLYrUJto7PD/ve7LNHxSg/woN2kSz4txasBdhyiz3ugPGetTjm3XRokAAAAASUVORK5CYII=)",
      display   : 'none',
      cursor    : 'pointer'
    });

    this.$buttons.css({
      padding        : 5,
      textAlign      : 'right',
      borderTop      : '1px solid #ccc',
      backgroundColor: '#fff'
    });

    this.$buttons.find('button').css({
      marginLeft: 5
    });

    this.$buttons.find('button:first').css({
      marginLeft: 0
    });

    this.$bar.on({
      mouseenter: function () {
        $(this).find('.noty_close').stop().fadeTo('normal', 1);
      },
      mouseleave: function () {
        $(this).find('.noty_close').stop().fadeTo('normal', 0);
      }
    });

    switch (this.options.layout.name) {
      case 'top':
        this.$bar.css({
          borderRadius: '0px 0px 5px 5px',
          borderBottom: '2px solid #eee',
          borderLeft  : '2px solid #eee',
          borderRight : '2px solid #eee',
          boxShadow   : "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        break;
      case 'topCenter':
      case 'center':
      case 'bottomCenter':
      case 'inline':
        this.$bar.css({
          borderRadius: '5px',
          border      : '1px solid #eee',
          boxShadow   : "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        this.$message.css({textAlign: 'center'});
        break;
      case 'topLeft':
      case 'topRight':
      case 'bottomLeft':
      case 'bottomRight':
      case 'centerLeft':
      case 'centerRight':
        this.$bar.css({
          borderRadius: '5px',
          border      : '1px solid #eee',
          boxShadow   : "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        this.$message.css({textAlign: 'left'});
        break;
      case 'bottom':
        this.$bar.css({
          borderRadius: '5px 5px 0px 0px',
          borderTop   : '2px solid #eee',
          borderLeft  : '2px solid #eee',
          borderRight : '2px solid #eee',
          boxShadow   : "0 -2px 4px rgba(0, 0, 0, 0.1)"
        });
        break;
      default:
        this.$bar.css({
          border   : '2px solid #eee',
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        break;
    }

    switch (this.options.type) {
      case 'alert':
      case 'notification':
        this.$bar.css({backgroundColor: '#FFF', borderColor: '#CCC', color: '#444'});
        break;
      case 'warning':
        this.$bar.css({backgroundColor: '#FFEAA8', borderColor: '#FFC237', color: '#826200'});
        this.$buttons.css({borderTop: '1px solid #FFC237'});
        break;
      case 'error':
        this.$bar.css({backgroundColor: 'red', borderColor: 'darkred', color: '#FFF'});
        this.$message.css({fontWeight: 'bold'});
        this.$buttons.css({borderTop: '1px solid darkred'});
        break;
      case 'information':
        this.$bar.css({backgroundColor: '#57B7E2', borderColor: '#0B90C4', color: '#FFF'});
        this.$buttons.css({borderTop: '1px solid #0B90C4'});
        break;
      case 'success':
        this.$bar.css({backgroundColor: 'lightgreen', borderColor: '#50C24E', color: 'darkgreen'});
        this.$buttons.css({borderTop: '1px solid #50C24E'});
        break;
      default:
        this.$bar.css({backgroundColor: '#FFF', borderColor: '#CCC', color: '#444'});
        break;
    }
  },
  callback: {
    onShow : function () {
      $.noty.themes.defaultTheme.helpers.borderFix.apply(this);
    },
    onClose: function () {
      $.noty.themes.defaultTheme.helpers.borderFix.apply(this);
    }
  }
};

$.noty.themes.metroui = {
  name    : 'metroui',
  helpers : {},
  modal   : {
    css: {
      position       : 'fixed',
      width          : '100%',
      height         : '100%',
      backgroundColor: '#000',
      zIndex         : 10000,
      opacity        : 0.6,
      display        : 'none',
      left           : 0,
      top            : 0
    }
  },
  style   : function () {

    this.$bar.css({
      overflow    : 'hidden',
      margin      : '4px 0',
      borderRadius: '0',
      position    : 'relative'
    });

    this.$progressBar.css({
      position       : 'absolute',
      left           : 0,
      bottom         : 0,
      height         : 4,
      width          : '100%',
      backgroundColor: '#000000',
      opacity        : 0.2,
      '-ms-filter'   : 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)',
      filter         : 'alpha(opacity=20)'
    });

    this.$message.css({
      textAlign: 'center',
      padding  : '1.25rem',
      width    : 'auto',
      position : 'relative'
    });

    this.$closeButton.css({
      position  : 'absolute',
      top       : '.25rem', right: '.25rem',
      width     : 10, height: 10,
      background: "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAQAAAAnOwc2AAAAxUlEQVR4AR3MPUoDURSA0e++uSkkOxC3IAOWNtaCIDaChfgXBMEZbQRByxCwk+BasgQRZLSYoLgDQbARxry8nyumPcVRKDfd0Aa8AsgDv1zp6pYd5jWOwhvebRTbzNNEw5BSsIpsj/kurQBnmk7sIFcCF5yyZPDRG6trQhujXYosaFoc+2f1MJ89uc76IND6F9BvlXUdpb6xwD2+4q3me3bysiHvtLYrUJto7PD/ve7LNHxSg/woN2kSz4txasBdhyiz3ugPGetTjm3XRokAAAAASUVORK5CYII=)",
      display   : 'none',
      cursor    : 'pointer'
    });

    this.$buttons.css({
      padding        : 5,
      textAlign      : 'right',
      borderTop      : '1px solid #ccc',
      backgroundColor: '#fff'
    });

    this.$buttons.find('button').css({
      marginLeft: 5
    });

    this.$buttons.find('button:first').css({
      marginLeft: 0
    });

    this.$bar.on({
      mouseenter: function () {
        $(this).find('.noty_close').stop().fadeTo('normal', 1);
      },
      mouseleave: function () {
        $(this).find('.noty_close').stop().fadeTo('normal', 0);
      }
    });

    switch (this.options.layout.name) {
      case 'top':
        this.$bar.css({
          border   : 'none',
          boxShadow: "0 0 5px 0 rgba(0, 0, 0, 0.3)"
        });
        break;
      case 'topCenter':
      case 'center':
      case 'bottomCenter':
      case 'inline':
        this.$bar.css({
          border   : 'none',
          boxShadow: "0 0 5px 0 rgba(0, 0, 0, 0.3)"
        });
        this.$message.css({textAlign: 'center'});
        break;
      case 'topLeft':
      case 'topRight':
      case 'bottomLeft':
      case 'bottomRight':
      case 'centerLeft':
      case 'centerRight':
        this.$bar.css({
          border   : 'none',
          boxShadow: "0 0 5px 0 rgba(0, 0, 0, 0.3)"
        });
        this.$message.css({textAlign: 'left'});
        break;
      case 'bottom':
        this.$bar.css({
          border   : 'none',
          boxShadow: "0 0 5px 0 rgba(0, 0, 0, 0.3)"
        });
        break;
      default:
        this.$bar.css({
          border   : 'none',
          boxShadow: "0 0 5px 0 rgba(0, 0, 0, 0.3)"
        });
        break;
    }

    switch (this.options.type) {
      case 'alert':
      case 'notification':
        this.$bar.css({backgroundColor: '#fff', border: 'none', color: '#1d1d1d'});
        break;
      case 'warning':
        this.$bar.css({backgroundColor: '#FA6800', border: 'none', color: '#fff'});
        this.$buttons.css({borderTop: '1px solid #FA6800'});
        break;
      case 'error':
        this.$bar.css({backgroundColor: '#CE352C', border: 'none', color: '#fff'});
        this.$message.css({fontWeight: 'bold'});
        this.$buttons.css({borderTop: '1px solid #CE352C'});
        break;
      case 'information':
        this.$bar.css({backgroundColor: '#1BA1E2', border: 'none', color: '#fff'});
        this.$buttons.css({borderTop: '1px solid #1BA1E2'});
        break;
      case 'success':
        this.$bar.css({backgroundColor: '#60A917', border: 'none', color: '#fff'});
        this.$buttons.css({borderTop: '1px solid #50C24E'});
        break;
      default:
        this.$bar.css({backgroundColor: '#fff', border: 'none', color: '#1d1d1d'});
        break;
    }
  },
  callback: {
    onShow : function () {

    },
    onClose: function () {

    }
  }
};
$.noty.themes.relax = {
  name    : 'relax',
  helpers : {},
  modal   : {
    css: {
      position       : 'fixed',
      width          : '100%',
      height         : '100%',
      backgroundColor: '#000',
      zIndex         : 10000,
      opacity        : 0.6,
      display        : 'none',
      left           : 0,
      top            : 0
    }
  },
  style   : function () {

    this.$bar.css({
      overflow    : 'hidden',
      margin      : '4px 0',
      borderRadius: '2px',
      position    : 'relative'
    });

    this.$progressBar.css({
      position       : 'absolute',
      left           : 0,
      bottom         : 0,
      height         : 4,
      width          : '100%',
      backgroundColor: '#000000',
      opacity        : 0.2,
      '-ms-filter'   : 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)',
      filter         : 'alpha(opacity=20)'
    });

    this.$message.css({
      textAlign: 'center',
      padding  : '10px',
      width    : 'auto',
      position : 'relative'
    });

    this.$closeButton.css({
      position  : 'absolute',
      top       : 4, right: 4,
      width     : 10, height: 10,
      background: "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAQAAAAnOwc2AAAAxUlEQVR4AR3MPUoDURSA0e++uSkkOxC3IAOWNtaCIDaChfgXBMEZbQRByxCwk+BasgQRZLSYoLgDQbARxry8nyumPcVRKDfd0Aa8AsgDv1zp6pYd5jWOwhvebRTbzNNEw5BSsIpsj/kurQBnmk7sIFcCF5yyZPDRG6trQhujXYosaFoc+2f1MJ89uc76IND6F9BvlXUdpb6xwD2+4q3me3bysiHvtLYrUJto7PD/ve7LNHxSg/woN2kSz4txasBdhyiz3ugPGetTjm3XRokAAAAASUVORK5CYII=)",
      display   : 'none',
      cursor    : 'pointer'
    });

    this.$buttons.css({
      padding        : 5,
      textAlign      : 'right',
      borderTop      : '1px solid #ccc',
      backgroundColor: '#fff'
    });

    this.$buttons.find('button').css({
      marginLeft: 5
    });

    this.$buttons.find('button:first').css({
      marginLeft: 0
    });

    this.$bar.on({
      mouseenter: function () {
        $(this).find('.noty_close').stop().fadeTo('normal', 1);
      },
      mouseleave: function () {
        $(this).find('.noty_close').stop().fadeTo('normal', 0);
      }
    });

    switch (this.options.layout.name) {
      case 'top':
        this.$bar.css({
          borderBottom: '2px solid #eee',
          borderLeft  : '2px solid #eee',
          borderRight : '2px solid #eee',
          borderTop   : '2px solid #eee',
          boxShadow   : "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        break;
      case 'topCenter':
      case 'center':
      case 'bottomCenter':
      case 'inline':
        this.$bar.css({
          border   : '1px solid #eee',
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        this.$message.css({textAlign: 'center'});
        break;
      case 'topLeft':
      case 'topRight':
      case 'bottomLeft':
      case 'bottomRight':
      case 'centerLeft':
      case 'centerRight':
        this.$bar.css({
          border   : '1px solid #eee',
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        this.$message.css({textAlign: 'left'});
        break;
      case 'bottom':
        this.$bar.css({
          borderTop   : '2px solid #eee',
          borderLeft  : '2px solid #eee',
          borderRight : '2px solid #eee',
          borderBottom: '2px solid #eee',
          boxShadow   : "0 -2px 4px rgba(0, 0, 0, 0.1)"
        });
        break;
      default:
        this.$bar.css({
          border   : '2px solid #eee',
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)"
        });
        break;
    }

    switch (this.options.type) {
      case 'alert':
      case 'notification':
        this.$bar.css({backgroundColor: '#FFF', borderColor: '#dedede', color: '#444'});
        break;
      case 'warning':
        this.$bar.css({backgroundColor: '#FFEAA8', borderColor: '#FFC237', color: '#826200'});
        this.$buttons.css({borderTop: '1px solid #FFC237'});
        break;
      case 'error':
        this.$bar.css({backgroundColor: '#FF8181', borderColor: '#e25353', color: '#FFF'});
        this.$message.css({fontWeight: 'bold'});
        this.$buttons.css({borderTop: '1px solid darkred'});
        break;
      case 'information':
        this.$bar.css({backgroundColor: '#78C5E7', borderColor: '#3badd6', color: '#FFF'});
        this.$buttons.css({borderTop: '1px solid #0B90C4'});
        break;
      case 'success':
        this.$bar.css({backgroundColor: '#BCF5BC', borderColor: '#7cdd77', color: 'darkgreen'});
        this.$buttons.css({borderTop: '1px solid #50C24E'});
        break;
      default:
        this.$bar.css({backgroundColor: '#FFF', borderColor: '#CCC', color: '#444'});
        break;
    }
  },
  callback: {
    onShow : function () {

    },
    onClose: function () {

    }
  }
};

$.noty.themes.semanticUI = {
  name: 'semanticUI',

  template: '<div class="ui message"><div class="content"><div class="header"></div></div></div>',

  animation: {
    open : {
      animation: 'fade',
      duration : '800ms'
    },
    close: {
      animation: 'fade left',
      duration : '800ms'
    }
  },

  modal   : {
    css: {
      position       : 'fixed',
      width          : '100%',
      height         : '100%',
      backgroundColor: '#000',
      zIndex         : 10000,
      opacity        : 0.6,
      display        : 'none',
      left           : 0,
      top            : 0
    }
  },
  style   : function () {
    this.$message = this.$bar.find('.ui.message');

    this.$message.find('.header').html(this.options.header);
    this.$message.find('.content').append(this.options.text);

    this.$bar.css({
      margin  : '0.5em',
      position: 'relative'
    });

    if (this.options.icon) {
      this.$message.addClass('icon').prepend($('<i/>').addClass(this.options.icon));
    }

    this.$progressBar.css({
      position       : 'absolute',
      left           : 0,
      bottom         : 0,
      height         : 4,
      width          : '100%',
      backgroundColor: '#000000',
      opacity        : 0.2,
      '-ms-filter'   : 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)',
      filter         : 'alpha(opacity=20)'
    });

    switch (this.options.size) {
      case 'mini':
        this.$message.addClass('mini');
        break;
      case 'tiny':
        this.$message.addClass('tiny');
        break;
      case 'small':
        this.$message.addClass('small');
        break;
      case 'large':
        this.$message.addClass('large');
        break;
      case 'big':
        this.$message.addClass('big');
        break;
      case 'huge':
        this.$message.addClass('huge');
        break;
      case 'massive':
        this.$message.addClass('massive');
        break;
    }

    switch (this.options.type) {
      case 'info':
        this.$message.addClass('info');
        break;
      case 'warning':
        this.$message.addClass('warning');
        break;
      case 'error':
        this.$message.addClass('error');
        break;
      case 'negative':
        this.$message.addClass('negative');
        break;
      case 'success':
        this.$message.addClass('success');
        break;
      case 'positive':
        this.$message.addClass('positive');
        break;
      case 'floating':
        this.$message.addClass('floating');
        break;
    }
  },
  callback: {
    onShow : function () {
      // Enable transition
      this.$bar.addClass('transition');
      // Actual transition
      this.$bar.transition(this.options.animation.open);
    },
    onClose: function () {
      this.$bar.transition(this.options.animation.close);
    }
  }
};


return window.noty;

});

$.noty.defaults = {
    layout: 'top',
    theme: 'defaultTheme',
    type: 'alert',
    text: '', // can be html or string
    dismissQueue: true, // If you want to use queue feature set this true
    template: '<div class="noty_message"><span class="noty_text"></span><div class="noty_close"></div></div>',
    animation: {
        open: {height: 'toggle'},
        close: {height: 'toggle'},
        easing: 'swing',
        speed: 500 // opening & closing animation speed
    },
    timeout: 1000, // delay for closing event. Set false for sticky notifications
    force: false, // adds notification to the beginning of queue when set to true
    modal: false,
    maxVisible: 5, // you can set max visible notification for dismissQueue true option,
    killer: false, // for close all notifications before show
    closeWith: ['click'], // ['click', 'button', 'hover', 'backdrop'] // backdrop click will close all open notifications
    callback: {
        onShow: function() {},
        afterShow: function() {},
        onClose: function() {},
        afterClose: function() {}
    },
    buttons: false // an array of buttons
};