{include file="common/head"/}
<div class="admin-main layui-anim layui-anim-upbit" ng-app="hd" ng-controller="ctrl">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{$title}</legend>
    </fieldset>
    <form class="layui-form layui-form-pane">
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Category Name')}</label>
            <div class="layui-input-block">
                <input type="text" name="title" ng-model="field.title" lay-verify="required" placeholder="{:lang('Please enter')}碎片{:fy('Category Name')}" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">建议格式: 【首页】中部碎片</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('order')}</label>
            <div class="layui-input-block">
                <input type="text" name="sort" ng-model="field.sort" value="" placeholder="从小到大{:fy('Sort')}" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit">{:lang('submit')}</button>
                <a href="{:url('type')}" class="layui-btn layui-btn-primary">{:lang('back')}</a>
            </div>
        </div>
    </form>
</div>
{include file="common/foot"/}
<script src="__MY_PUBLIC__/static/common/js/angular.min.js"></script>
<script>
    var m = angular.module('hd',[]);
    m.controller('ctrl',['$scope',function($scope) {
        $scope.field = '{$info|raw}'!='null'?{$info|raw}:{id:'',name:'',sort:50};
        layui.use(['form', 'layer'], function () {
            var form = layui.form, $ = layui.jquery;
            form.on('submit(submit)', function (data) {
                // 提交到方法 默认为本身
                data.field.id = $scope.field.id;
                var loading = layer.load(1, {shade: [0.1, '#fff']});
                $.post("", data.field, function (res) {
                    layer.close(loading);
                    if (res.code > 0) {
                        layer.msg(res.msg, {time: 1000, icon: 1}, function () {
                            location.href = res.url;
                        });
                    } else {
                        layer.msg(res.msg, {time: 1000, icon: 2});
                    }
                });
            })
        });
    }]);
</script>