<!doctype html>
<html>
<head>
    {include file="public/head"/}
    <script src="__MY_PUBLIC__/static/common/js/jquery.2.1.1.min.js"></script>
    <style>
        .rewrite-correct, .rewrite-error {
            display: none;
        }
    </style>
</head>
<body>
<div class="wrap">
    {include file="public/header"/}
    <section class="section">
        <div class="step">
            <ul class="unstyled">
                <li class="current"><em>1</em>检测环境</li>
                <li><em>2</em>创建数据</li>
                <li><em>3</em>完成安装</li>
            </ul>
        </div>
        <div class="server">
            <table width="100%">
                <tr>
                    <td class="td1">环境检测</td>
                    <td class="td1" width="25%">推荐配置</td>
                    <td class="td1" width="25%">当前状态</td>
                    <td class="td1" width="25%">最低要求</td>
                </tr>
                <tr>
                    <td>操作系统</td>
                    <td>类UNIX</td>
                    <td><i class="fa fa-check correct"></i> {$os}</td>
                    <td>不限制</td>
                </tr>
                <tr>
                    <td>PHP版本</td>
                    <td>>7.3.x</td>
                    <td><i class="fa fa-check correct"></i> {$phpversion}</td>
                    <td>7.3.0</td>
                </tr>
                <!-- 模块检测 -->
                <tr>
                    <td class="td1" colspan="4">
                        模块检测
                    </td>
                </tr>
                <tr>
                    <td>session</td>
                    <td>开启</td>
                    <td>
                        {$session|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        fsockopen
                        <a href="https://www.baidu.com/s?wd=PHP开启fsockopen扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$fsockopen|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        PDO
                        <a href="https://www.baidu.com/s?wd=开启PDO,PDO_MYSQL扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$pdo|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        PDO_MySQL
                        <a href="https://www.baidu.com/s?wd=开启PDO,PDO_MYSQL扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$pdo_mysql|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        CURL
                        <a href="https://www.baidu.com/s?wd=开启PHP CURL扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$curl|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        GD
                        <a href="https://www.baidu.com/s?wd=开启PHP GD扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$gd|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        MBstring
                        <a href="https://www.baidu.com/s?wd=开启PHP MBstring扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$mbstring|raw}
                    </td>
                    <td>开启</td>
                </tr>
                <tr>
                    <td>
                        fileinfo
                        <a href="https://www.baidu.com/s?wd=开启PHP fileinfo扩展" target="_blank">
                            <i class="fa fa-question-circle question"></i>
                        </a>
                    </td>
                    <td>开启</td>
                    <td>
                        {$fileinfo|raw}
                    </td>
                    <td>开启</td>
                </tr>
                {notempty name="show_always_populate_raw_post_data_tip"}
                    <tr>
                        <td>
                            $HTTP_RAW_POST_DATA关闭检测
                            <a href="https://www.baidu.com/s?wd=开启PHP fileinfo扩展" target="_blank">
                                <i class="fa fa-question-circle question"></i>
                            </a>
                        </td>
                        <td>关闭</td>
                        <td>
                            {$always_populate_raw_post_data|raw}

                        </td>
                        <td>关闭</td>
                    </tr>
                    <tr>
                        <td>$HTTP_RAW_POST_DATA未关闭解决</td>
                        <td colspan="3">
							<pre>
								;php.ini 找到 always_populate_raw_post_data设置如下:
								always_populate_raw_post_data = -1
							</pre>
                        </td>
                    </tr>
                {/notempty}
                <!-- 大小限制检测 -->
                <tr>
                    <td class="td1" colspan="4">
                        大小限制检测
                    </td>
                </tr>
                <tr>
                    <td>附件上传</td>
                    <td>>2M</td>
                    <td>
                        <?php
 if (ini_get('file_uploads')) {
            $data['upload_size'] = '<i class="fa fa-check correct"></i> ' . ini_get('upload_max_filesize');
                        } else {
                        '<i class="fa fa-remove error"></i> 禁止上传';
                        }
?>
                        {$upload_size|raw}
                    </td>
                    <td>不限制</td>
                </tr>
            </table>
            <table width="100%">
                <tr>
                    <td class="td1">目录、文件权限检查</td>
                    <td class="td1" width="25%">写入</td>
                    <td class="td1" width="25%">读取</td>
                </tr>
                {foreach name="folders" item="vo" key="dir"}
                    <tr>
                        <td>
                            {$dir}
                        </td>
                        <td>
                            {if condition="$vo['w']"}
                                <i class="fa fa-check correct"></i> 可写
                            {else/}
                                <i class="fa fa-remove error"></i> 不可写
                            {/if}
                        </td>
                        <td>
                            {if condition="$vo['r']"}
                                <i class="fa fa-check correct"></i> 可读
                                {else/}
                                <i class="fa fa-remove error"></i> 不可读
                            {/if}
                        </td>
                    </tr>
                {/foreach}
            </table>
        </div>
        <div class="bottom text-center">
            <a href="/install.php?s=index/step2" class="btn btn-primary">重新检测</a>

            <!--<a <?php if($err){ echo 'disabled="disabled"'; }else{ echo 'href="/install.php?s=index/step3"'; } ?> class="btn btn-primary">下一步</a>-->
            <a  href="/install.php?s=index/step3" class="btn btn-primary">下一步</a>
        </div>
    </section>
</div>
{include file="public/footer"/}
</body>
</html>