<?php
namespace app\api\controller;

use app\BaseController;
use jwt\JWT;
use think\facade\Config;
use think\facade\Request;
use think\facade\Db;

/**
 * API控制器基类
 */
class Common extends BaseController
{
    /**
     * 当前登录用户信息
     * @var array
     */
    protected $adminInfo = [];
    
    /**
     * 是否需要登录验证
     * @var bool
     */
    protected $needLogin = true;
    
    /**
     * JWT密钥
     * @var string
     */
    protected $jwtKey = 'ymwl_crm_api_key';
    
    /**
     * Token过期时间（秒）
     * @var int
     */
    protected $tokenExpire = 86400; // 默认24小时
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        // 跨域请求支持
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-Requested-With');
        header('Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE');
        header('Access-Control-Max-Age: 1728000');
        
        // OPTIONS请求直接返回成功
        if (request()->isOptions()) {
            $this->success('success');
        }
        
        // 验证登录
        if ($this->needLogin) {
            $this->checkLogin();
        }
    }
    
    /**
     * 检查登录状态
     */
    protected function checkLogin()
    {
        // 获取token
        $token = Request::header('Authorization');
        if (empty($token)) {
            $this->error('请先登录', [], 401);
        }
        
        // 验证token
        $payload = JWT::verifyToken($token, $this->jwtKey);
        if ($payload === false) {
            $this->error('登录已过期，请重新登录', [], 401);
        }
        
        // 获取用户信息
        $adminInfo = Db::name('admin')->where('admin_id', $payload['admin_id'])->find();
        if (empty($adminInfo)) {
            $this->error('用户不存在', [], 401);
        }
        
        // 检查用户状态
        if ($adminInfo['is_open'] != 1) {
            $this->error('账号已被禁用', [], 401);
        }
        
        // 检查设备登录状态
        if (!empty($payload['device_id'])) {
            $device = Db::name('admin_device')
                ->where('admin_id', $adminInfo['admin_id'])
                ->where('device_id', $payload['device_id'])
                ->where('status', 1)
                ->find();
            
            if (empty($device)) {
                $this->error('您的账号已在其他设备登录', [], 401);
            }
        }
        
        // 保存用户信息
        unset($adminInfo['pwd'], $adminInfo['salt']);
        $this->adminInfo = $adminInfo;
    }
    
    /**
     * 生成JWT Token
     * @param array $adminInfo 用户信息
     * @param string $deviceId 设备ID
     * @return string
     */
    protected function createToken(array $adminInfo, string $deviceId = '')
    {
        $payload = [
            'admin_id' => $adminInfo['admin_id'],
            'username' => $adminInfo['username'],
            'device_id' => $deviceId
        ];
        
        return JWT::getToken($payload, $this->jwtKey, $this->tokenExpire);
    }
    
    /**
     * 成功响应
     * @param string $msg 提示信息
     * @param array $data 返回数据
     * @param int $code 状态码
     * @param array $header 响应头
     * @param array $options 其他选项
     * @return void
     */
    protected function success($msg = '', $data = [], $code = 200, array $header = [], $options = [])
    {
        $this->result($msg, $data, $code, $header, $options);
    }
    
    /**
     * 错误响应
     * @param string $msg 提示信息
     * @param array $data 返回数据
     * @param int $code 状态码
     * @param array $header 响应头
     * @param array $options 其他选项
     * @return void
     */
    protected function error($msg = '', $data = [], $code = 400, array $header = [], $options = [])
    {
        $this->result($msg, $data, $code, $header, $options);
    }
    
    /**
     * 返回JSON结果
     * @param string $msg 提示信息
     * @param array $data 返回数据
     * @param int $code 状态码
     * @param array $header 响应头
     * @param array $options 其他选项
     * @return void
     */
    protected function result($msg, $data = [], $code = 200, array $header = [], $options = [])
    {
        $result = [
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
        ];
        
        // 发送JSON响应
        $response = json($result, $code, $header, $options);
        $response->send();
        exit;
    }
}