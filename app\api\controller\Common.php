<?php

namespace app\api\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Cache;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * API通用控制器基类
 */
class Common extends BaseController
{
    /**
     * 当前登录用户信息
     */
    protected $admin = [];
    
    /**
     * JWT密钥
     */
    protected $jwtKey = 'crm_api_jwt_secret_key_2024';
    
    /**
     * Token过期时间（秒）
     */
    protected $tokenExpire = 86400 * 7; // 7天
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // 处理OPTIONS预检请求
        if ($this->request->method() == 'OPTIONS') {
            exit();
        }
    }
    
    /**
     * 验证Token并获取用户信息
     */
    protected function checkAuth()
    {
        $token = $this->getToken();
        if (!$token) {
            $this->jsonError('未提供认证令牌', [], 401);
        }
        
        try {
            $decoded = JWT::decode($token, new Key($this->jwtKey, 'HS256'));
            $adminId = $decoded->admin_id;
            
            // 检查token是否在有效列表中（单设备登录）
            $validToken = Cache::get('admin_token_' . $adminId);
            if (!$validToken || $validToken !== $token) {
                $this->jsonError('令牌已失效，请重新登录', [], 401);
            }
            
            // 获取用户信息
            $admin = Db::name('admin')
                ->where('admin_id', $adminId)
                ->where('is_open', 1)
                ->find();
                
            if (!$admin) {
                $this->jsonError('用户不存在或已被禁用', [], 401);
            }
            
            $this->admin = $admin;
            
        } catch (\Exception $e) {
            $this->jsonError('令牌验证失败：' . $e->getMessage(), [], 401);
        }
    }
    
    /**
     * 获取请求头中的Token
     */
    protected function getToken()
    {
        $authorization = $this->request->header('Authorization');
        if ($authorization && strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }
        return null;
    }
    
    /**
     * 生成JWT Token
     */
    protected function generateToken($adminId, $deviceId = '')
    {
        $payload = [
            'admin_id' => $adminId,
            'device_id' => $deviceId,
            'iat' => time(),
            'exp' => time() + $this->tokenExpire
        ];
        
        $token = JWT::encode($payload, $this->jwtKey, 'HS256');
        
        // 存储token到缓存（实现单设备登录）
        Cache::set('admin_token_' . $adminId, $token, $this->tokenExpire);
        
        return $token;
    }
    
    /**
     * 成功响应
     */
    protected function jsonSuccess($msg = '操作成功', $data = [], $code = 200)
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
            'time' => time()
        ];
        
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    /**
     * 错误响应
     */
    protected function jsonError($msg = '操作失败', $data = [], $code = 400)
    {
        $result = [
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
            'time' => time()
        ];
        
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    /**
     * 验证POST请求
     */
    protected function checkPostRequest()
    {
        if (!$this->request->isPost()) {
            $this->jsonError('请求方法不正确', [], 405);
        }
    }
    
    /**
     * 验证GET请求
     */
    protected function checkGetRequest()
    {
        if (!$this->request->isGet()) {
            $this->jsonError('请求方法不正确', [], 405);
        }
    }
    
    /**
     * 获取分页参数
     */
    protected function getPageParams()
    {
        $page = (int)$this->request->param('page', 1);
        $limit = (int)$this->request->param('limit', 15);

        $page = max(1, $page);
        $limit = min(100, max(1, $limit)); // 限制每页最多100条

        return [$page, $limit];
    }

    /**
     * 验证必需参数
     */
    protected function validateRequired($params, $required)
    {
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '') {
                $this->jsonError("参数 {$field} 不能为空");
            }
        }
    }

    /**
     * 记录API访问日志
     */
    protected function logApiAccess($action = '', $params = [])
    {
        $logData = [
            'admin_id' => $this->admin['admin_id'] ?? 0,
            'username' => $this->admin['username'] ?? '',
            'action' => $action,
            'params' => json_encode($params, JSON_UNESCAPED_UNICODE),
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent'),
            'create_time' => time()
        ];

        // 这里可以记录到日志表或文件
        // Db::name('api_log')->insert($logData);
    }

    /**
     * 检查用户权限
     */
    protected function checkPermission($permission)
    {
        // 超级管理员跳过权限检查
        if ($this->admin['group_id'] == 1) {
            return true;
        }

        // 这里可以实现具体的权限检查逻辑
        // 暂时返回true，后续可以根据需要完善
        return true;
    }
}
