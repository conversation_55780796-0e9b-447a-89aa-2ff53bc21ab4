define(["jquery", "easy-admin","vue"], function ($, ea,Vue) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'business.record/index',
        add_url: 'business.record/add',
        edit_url: 'business.record/edit',
        delete_url: 'business.record/delete',
        export_url: 'business.record/export',
        modify_url: 'business.record/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                toolbar: ['refresh','delete'],
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'ID'},
                    {field: 'create_username', title: fy('Follower'),search:true},
                    {field: 'create_time', title: fy('Follow up time'),search:'range',templet: ea.table.date},
                    {field: 'content', title: fy('Follow up content'),search:true},
                    {field: 'next_time', title: fy('Next follow-up time'),search:'range',templet: ea.table.date},
                    {field: 'record_type', title: fy('Follow up type')},
                    {field: 'attachs', title: fy("Attachment"),templet:function (res,option){
                            if(res.attachs){
                                return '<a class="layui-btn layui-btn-success layui-btn-xs" data-open="attachs/index?attachs='+res.attachs+'" data-title="'+fy('Follow up')+' '+fy('Attachment')+'">'+fy('Attachment')+'</a>';
                            }else{
                                return '';
                            }
                        }},
                    {width: 250, title: fy('Operate'), templet: ea.table.tool,operat:['delete']},
                ]],
                done: function(res, curr, count){
                       if(count===undefined && res.msg && res.url){
                           ea.msg.tips(res.msg,1,function (){
                               window.top.location.href=res.url;
                           })
                       }
                },where: {scope: 1}
            });

            ea.listen();
        },
        add: function () {
            this.renderPro();
            ea.listen('',
                function (res) {
                    ea.msg.success(res.msg, function () {
                        layui.table.reload(init.table_render_id, {page: {curr: 1}});
                        // location.reload();
                    });
                }, function (res) {
                    ea.msg.error(res.msg, function () {
                    });
                });
        },
        edit: function () {
            ea.listen();
        },renderPro:function (){
            business_id=$('#table-pro').data('business_id');
            ea.table.render({
                toolbar: ['refresh'],
                init: init,limit:CONFIG.ADMINPAGESIZE,
                cols: [[
                    {field: 'id', title: 'ID'},
                    {field: 'create_username', title: fy('Follower'),search:true},
                    {field: 'create_time', title:  fy('Follow up time'),search:'range',templet: ea.table.date},
                    {field: 'content', title: fy('Follow up content'),search:true},
                    {field: 'next_time', title: fy('Next follow-up time'),search:'range',templet: ea.table.date},
                    {field: 'record_type', title: fy('Follow up type')},
                    {field: 'attachs', title:fy('Attachment'),templet:function (res,option){
                            if(res.attachs){
                                return '<a class="layui-btn layui-btn-success layui-btn-xs" data-open="attachs/index?attachs='+res.attachs+'" data-title="'+fy('Follow up')+' '+fy('Attachment')+'">'+fy('Attachment')+'</a>';
                            }else{
                                return '';
                            }
                        }}
                ]],
                done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                },where: {scope: 1,business_id: business_id}
            });

            var app = new Vue({
                el: '#app',
                data: {
                    pro_list: [],
                    cost_sum:0.00,sale_sum:0.00
                },
                methods:{
                    entryTime(index){
                        if(!this.pro_list[index]['create_time']){
                            this.pro_list[index]['create_time']=(Date.parse(new Date()))/1000;
                        }
                        return layui.util.toDateString(this.pro_list[index]['create_time']*1000, 'yyyy-MM-dd HH:mm');
                    },removePro(index,business_product_id){
                        //删除数据库对应的商机产品
                        that =this;
                        if(business_product_id){
                            ea.request.ajax('get',{url:ea.url('business/delproduct_by_business'),data:{'business_product_id':business_product_id}},function (res){
                                if(res.code){
                                    that.pro_list.splice(index, 1)
                                }else{
                                    ea.msg.error(fy('Delete failed'));
                                }
                            });
                        }else{
                            that.pro_list.splice(index, 1)
                        }
                    }
                },computed: {
                    getTotal() {
                        // 获取productList中select为true的数据
                        var proList = this.pro_list
                        // 设置一个值用来存储总价
                        var cost_sum=0,discount_sum=0,sale_sum=0,nums_sum=0;
                        for (let i = 0; i < proList.length; i++) {

                            cost_sum += proList[i].cost_price * proList[i].nums;
                            sale_sum += proList[i].sale_price * proList[i].nums;
                            discount_sum+=parseFloat(proList[i].discount);
                            nums_sum += parseInt(proList[i].nums);
                        }
                        if(sale_sum){
                            real_sale_sum=sale_sum-discount_sum;
                        }else{
                            real_sale_sum=0;
                        }

                        return {

                            cost_sum: cost_sum.toFixed(2),
                            nums_sum: nums_sum,
                            discount_sum: discount_sum.toFixed(2),
                            sale_sum: sale_sum.toFixed(2),
                            real_sale_sum: real_sale_sum.toFixed(2),
                        }
                    },
                }
            });

            if(business_id){
                //获取商机对应产品
                ea.request.ajax('get',{url:ea.url('business/product_by_business'),data:{'business_id':business_id}},function (res){
                    app.pro_list=res.data;

                });
            }

        }
    };
    return Controller;
});
