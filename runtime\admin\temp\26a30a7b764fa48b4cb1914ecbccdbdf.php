<?php /*a:3:{s:58:"/www/wwwroot/ceshi71.cn/app/admin/view/auth/rule_edit.html";i:1690988074;s:55:"/www/wwwroot/ceshi71.cn/app/admin/view/common/head.html";i:1671106604;s:55:"/www/wwwroot/ceshi71.cn/app/admin/view/common/foot.html";i:1672069288;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/public/static/plugs/layui-v2.7.6/css/layui.css" media="all">

    <link rel="stylesheet" href="/public/static/plugs/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/global.css" media="all">
    <script src="/public/static/common/js/jquery.min.js"></script>

    <script src="/public/static/admin/js/common.js"></script>
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
</head>
<body >
<div class="admin-main">
    <fieldset class="layui-elem-field layui-field-title">
        <legend><?php echo fy('Edit'); ?> <?php echo fy('Permissions'); ?></legend>
    </fieldset>
    <form class="layui-form layui-form-pane">
        <input type="hidden" name="id" value="<?php echo htmlentities($rule['id']); ?>">
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Parent'); ?></label>
            <div class="layui-input-block">
                <select name="pid" lay-verify="required" lay-filter="pid" >
                    <option value="0"><?php echo fy('Default Top Level'); ?></option>
                    <?php if(is_array($admin_rule) || $admin_rule instanceof \think\Collection || $admin_rule instanceof \think\Paginator): $i = 0; $__LIST__ = $admin_rule;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($vo['id']); ?>" <?php if($rule['pid']==$vo['id'])echo 'selected'; ?>><?php echo htmlentities($vo['lefthtml']); ?><?php echo fy($vo['title']); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Menu'); ?><?php echo fy('Name'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="title" value="<?php echo htmlentities($rule['title']); ?>" lay-verify="required" placeholder="<?php echo lang('Please enter'); ?><?php echo fy('Menu'); ?><?php echo fy('Name'); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Controller"); ?>/<?php echo fy("Method"); ?></label>
            <div class="layui-input-block">
                <input type="text" name="href" value="<?php echo htmlentities($rule['href']); ?>" placeholder="<?php echo lang('Please enter'); ?><?php echo fy("Controller"); ?>/<?php echo fy("Method"); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Icon'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="icon" value="<?php echo htmlentities($rule['icon']); ?>" placeholder="<?php echo lang('Please enter'); ?><?php echo fy('Icon'); ?>" class="layui-input hide" lay-filter="icon" id="icon">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Link Open Method'); ?></label>
            <div class="layui-input-block">
                <?php foreach(['_self'=>'当前窗口打开','_blank'=>'新窗口打开','_parent'=>'父窗口打开','_top'=>'顶端打开窗口'] as $key=>$vo): ?>
                <input type="radio" name="target" value="<?php echo htmlentities($key); ?>" title="<?php echo fy($vo); ?>" <?php if($rule['target']==$key): ?>checked=""<?php endif; ?>>
                <?php endforeach; ?>

            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否菜单</label>
            <div class="layui-input-block">
                <input type="radio" name="menustatus" <?php if($rule['menustatus'] == 1): ?>checked<?php endif; ?> value="1" title="是">
                <input type="radio" name="menustatus" <?php if($rule['menustatus'] == 0): ?>checked<?php endif; ?> value="0" title="否">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Sort'); ?></label>
            <div class="layui-input-2">
                <input type="text" name="sort" value="<?php echo htmlentities($rule['sort']); ?>" placeholder="<?php echo lang('Please enter'); ?><?php echo fy('Sort number'); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="auth"><?php echo fy('Save'); ?></button>
            </div>
        </div>
    </form>
</div>

<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/public/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script>
    layui.use(['form', 'layer','iconPickerFa'], function () {
        var form = layui.form,layer = layui.layer,$ = layui.jquery,iconPickerFa = layui.iconPickerFa;

        console.log(iconPickerFa);
        iconPickerFa.render({
            // 选择器，推荐使用input
            elem: '#icon',
            // fa 图标接口
            url: "/public/static/plugs/font-awesome-4.7.0/less/variables.less",
            // 是否开启搜索：true/false，默认true
            search: true,
            // 是否开启分页：true/false，默认true
            page: true,
            // 每页显示数量，默认12
            limit: 300,
            // 点击回调
            click: function (data) {
                $('#icon').val('fa ' + data.icon);
            }

        });

        form.on('submit(auth)', function (data) {
            // 提交到方法 默认为本身
            $.post("<?php echo url('ruleEdit'); ?>",data.field,function(res){
                if(res.code > 0){
                    layer.msg(res.msg,{time:1800,icon:1},function(){
                        window.parent.location.reload();
                    });
                }else{
                    layer.msg(res.msg,{time:1800,icon:2});
                }
            });
        })
    })
</script>
</body>
</html>
