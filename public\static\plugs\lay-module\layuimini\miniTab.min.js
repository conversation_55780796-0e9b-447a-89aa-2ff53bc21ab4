define(["jquery"],function(s){var f=layui.element,s=layui.$;var m={render:function(a){a.filter=a.filter||null;a.multiModule=a.multiModule||false;a.urlHashLocation=a.urlHashLocation||false;a.maxTabNum=a.maxTabNum||20;a.menuList=a.menuList||[];a.homeInfo=a.homeInfo||{};a.listenSwichCallback=a.listenSwichCallback||function(){};m.listen(a);m.listenRoll();m.listenSwitch(a);m.listenHash(a)},create:function(a){a.tabId=a.tabId||null;a.href=a.href||null;a.title=a.title||null;a.isIframe=a.isIframe||false;a.maxTabNum=a.maxTabNum||20;if(s(".layuimini-tab .layui-tab-title li").length>=a.maxTabNum){layer.msg("Tab窗口已达到限定数量，请先关闭部分Tab");return false}var i=f;if(a.isIframe)i=parent.layui.element;i.tabAdd("layuiminiTab",{title:'<span class="layuimini-tab-active"></span><span>'+a.title+'</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>',content:'<iframe width="100%" height="100%" frameborder="no" border="0" marginwidth="0" marginheight="0"   src="'+a.href+'"></iframe>',id:a.tabId});s(".layuimini-menu-left").attr("layuimini-tab-tag","add");sessionStorage.setItem("layuiminimenu_"+a.tabId,a.title)},change:function(a){f.tabChange("layuiminiTab",a)},delete:function(a,i){s(".layuimini-tab .layui-tab-title .layui-unselect.layui-tab-bar").remove();if(i===true){parent.layui.element.tabDelete("layuiminiTab",a)}else{f.tabDelete("layuiminiTab",a)}},openNewTabByIframe:function(a){a.href=a.href||null;a.title=a.title||null;var i=parent.layer.load(0,{shade:false,time:2*1e3});if(a.href===null||a.href===undefined)a.href=(new Date).getTime();var e=m.check(a.href,true);if(!e){m.create({tabId:a.href,href:a.href,title:a.title,isIframe:true})}parent.layui.element.tabChange("layuiminiTab",a.href);parent.layer.close(i)},deleteCurrentByIframe:function(){var a=s(".layuimini-tab .layui-tab-title li.layui-this",parent.document);if(a.length>0){var i=s(a[0]).attr("lay-id");m.delete(i,true)}},check:function(i,a){var e=false;if(a===undefined||a===false){s(".layui-tab-title li").each(function(){var a=s(this).attr("lay-id");if(a!=null&&a===i){e=true}})}else{parent.layui.$(".layui-tab-title li").each(function(){var a=s(this).attr("lay-id");if(a!=null&&a===i){e=true}})}return e},openTabRignMenu:function(a,i){m.closeTabRignMenu();var e='<div class="layui-unselect layui-form-select layui-form-selected layuimini-tab-mousedown layui-show" data-tab-id="'+a+'" style="left: '+i+'px!important">\n'+"<dl>\n"+'<dd><a href="javascript:;" layuimini-tab-menu-close="current">关 闭 当 前</a></dd>\n'+'<dd><a href="javascript:;" layuimini-tab-menu-close="other">关 闭 其 他</a></dd>\n'+'<dd><a href="javascript:;" layuimini-tab-menu-close="all">关 闭 全 部</a></dd>\n'+"</dl>\n"+"</div>";var t='<div class="layuimini-tab-make"></div>';s(".layuimini-tab .layui-tab-title").after(e);s(".layuimini-tab .layui-tab-content").after(t)},closeTabRignMenu:function(){s(".layuimini-tab-mousedown").remove();s(".layuimini-tab-make").remove()},searchMenu:function(a,i){var e;for(key in i){var t=i[key];if(t.href===a){e=t;break}if(t.child){newMenu=m.searchMenu(a,t.child);if(newMenu){e=newMenu;break}}}return e},listen:function(r){r=r||{};r.maxTabNum=r.maxTabNum||20;s("body").on("click","[layuimini-href]",function(){var a=layer.load(0,{shade:false,time:2*1e3});var i=s(this).attr("layuimini-href"),e=s(this).attr("layuimini-href"),t=s(this).text(),l=s(this).attr("target");var n=s("[layuimini-href='"+e+"']",".layuimini-menu-left");layer.close(window.openTips);if(n.length){s(n).closest(".layui-nav-tree").find(".layui-this").removeClass("layui-this");s(n).parent().addClass("layui-this")}if(l==="_blank"){layer.close(a);window.open(e,"_blank");return false}if(i===null||i===undefined)i=(new Date).getTime();var u=m.check(i);if(!u){m.create({tabId:i,href:e,title:t,isIframe:false,maxTabNum:r.maxTabNum})}f.tabChange("layuiminiTab",i);layer.close(a)});s("body").on("click","[layuimini-content-href]",function(){var a=parent.layer.load(0,{shade:false,time:2*1e3});var i=s(this).attr("layuimini-content-href"),e=s(this).attr("layuimini-content-href"),t=s(this).attr("data-title"),l=s(this).attr("target");if(l==="_blank"){parent.layer.close(a);window.open(e,"_blank");return false}if(i===null||i===undefined)i=(new Date).getTime();var n=m.check(i,true);if(!n){m.create({tabId:i,href:e,title:t,isIframe:true,maxTabNum:r.maxTabNum})}parent.layui.element.tabChange("layuiminiTab",i);parent.layer.close(a)});s("body").on("click",".layuimini-tab .layui-tab-title .layui-tab-close",function(){var a=layer.load(0,{shade:false,time:2*1e3});var i=s(this).parent();var e=i.attr("lay-id");if(e!==undefined||e!==null){m.delete(e)}layer.close(a)});s("body").on("click","[layuimini-tab-close]",function(){var a=layer.load(0,{shade:false,time:2*1e3});var t=s(this).attr("layuimini-tab-close");s(".layuimini-tab .layui-tab-title li").each(function(){var a=s(this).attr("lay-id");var i=s(this).attr("id");var e=s(this).hasClass("layui-this");if(i!=="layuiminiHomeTabId"){if(t==="all"){m.delete(a)}else{if(t==="current"&&e){m.delete(a)}else if(t==="other"&&!e){m.delete(a)}}}});layer.close(a)});s(".layuimini-tab .layui-tab-title").unbind("mousedown").bind("contextmenu",function(a){a.preventDefault();return false});s("body").on("mousedown",".layuimini-tab .layui-tab-title li",function(a){var i=s(this).offset().left-s(".layuimini-tab ").offset().left+s(this).width()/2,e=s(this).attr("lay-id");if(a.which===3){m.openTabRignMenu(e,i)}});s("body").on("click",".layui-body,.layui-header,.layuimini-menu-left,.layuimini-tab-make",function(){m.closeTabRignMenu()});s("body").on("click","[layuimini-tab-menu-close]",function(){var a=layer.load(0,{shade:false,time:2*1e3});var e=s(this).attr("layuimini-tab-menu-close"),t=s(".layuimini-tab-mousedown").attr("data-tab-id");s(".layuimini-tab .layui-tab-title li").each(function(){var a=s(this).attr("lay-id");var i=s(this).attr("id");if(i!=="layuiminiHomeTabId"){if(e==="all"){m.delete(a)}else{if(e==="current"&&t===a){m.delete(a)}else if(e==="other"&&t!==a){m.delete(a)}}}});m.closeTabRignMenu();layer.close(a)})},listenSwitch:function(e){e.filter=e.filter||null;e.multiModule=e.multiModule||false;e.urlHashLocation=e.urlHashLocation||false;e.listenSwichCallback=e.listenSwichCallback||function(){};f.on("tab("+e.filter+")",function(a){var i=s(this).attr("lay-id");if(e.urlHashLocation){location.hash=i}if(typeof e.listenSwichCallback==="function"){e.listenSwichCallback()}if(s(".layuimini-menu-left").attr("layuimini-tab-tag")==="add"){s(".layuimini-menu-left").attr("layuimini-tab-tag","no")}else{s("[layuimini-href]").parent().removeClass("layui-this");if(e.multiModule){m.listenSwitchMultiModule(i)}else{m.listenSwitchSingleModule(i)}}m.rollPosition()})},listenHash:function(i){i.urlHashLocation=i.urlHashLocation||false;i.maxTabNum=i.maxTabNum||20;i.homeInfo=i.homeInfo||{};i.menuList=i.menuList||[];if(!i.urlHashLocation)return false;var e=location.hash.replace(/^#/,"");if(e===null||e===undefined||e==="")return false;if(e===i.homeInfo.href)return false;var a=m.searchMenu(e,i.menuList);if(a!==undefined){m.create({tabId:e,href:e,title:a.title,isIframe:false,maxTabNum:i.maxTabNum});s(".layuimini-menu-left").attr("layuimini-tab-tag","no");f.tabChange("layuiminiTab",e);return false}var t=false;s("[layuimini-content-href]").each(function(){if(s(this).attr("layuimini-content-href")===e){var a=s(this).attr("data-title");m.create({tabId:e,href:e,title:a,isIframe:false,maxTabNum:i.maxTabNum});s(".layuimini-menu-left").attr("layuimini-tab-tag","no");f.tabChange("layuiminiTab",e);t=true;return false}});if(t)return false;var l=sessionStorage.getItem("layuiminimenu_"+e)===null?e:sessionStorage.getItem("layuiminimenu_"+e);m.create({tabId:e,href:e,title:l,isIframe:false,maxTabNum:i.maxTabNum});f.tabChange("layuiminiTab",e);return false},listenRoll:function(){s(".layuimini-tab-roll-left").click(function(){m.rollClick("left")});s(".layuimini-tab-roll-right").click(function(){m.rollClick("right")})},listenSwitchSingleModule:function(a){s("[layuimini-href]").each(function(){if(s(this).attr("layuimini-href")===a){var e=function(a,i){if(i===1){a.addClass("layui-this");if(a.hasClass("layui-nav-item")&&a.hasClass("layui-this")){s(".layuimini-header-menu li").attr("class","layui-nav-item")}else{e(a.parent().parent(),2)}}else{a.addClass("layui-nav-itemed");if(a.hasClass("layui-nav-item")&&a.hasClass("layui-nav-itemed")){s(".layuimini-header-menu li").attr("class","layui-nav-item")}else{e(a.parent().parent(),2)}}};e(s(this).parent(),1);return false}})},listenSwitchMultiModule:function(a){s("[layuimini-href]").each(function(){if(s(this).attr("layuimini-href")===a){var t=function(a,i){if(i===1){a.addClass("layui-this");if(a.hasClass("layui-nav-item")&&a.hasClass("layui-this")){var e=a.parent().attr("id");s(".layuimini-header-menu li").attr("class","layui-nav-item");s("#"+e+"HeaderId").addClass("layui-this");s(".layuimini-menu-left .layui-nav.layui-nav-tree").attr("class","layui-nav layui-nav-tree layui-hide");s("#"+e).attr("class","layui-nav layui-nav-tree layui-this")}else{t(a.parent().parent(),2)}}else{a.addClass("layui-nav-itemed");if(a.hasClass("layui-nav-item")&&a.hasClass("layui-nav-itemed")){var e=a.parent().attr("id");s(".layuimini-header-menu li").attr("class","layui-nav-item");s("#"+e+"HeaderId").addClass("layui-this");s(".layuimini-menu-left .layui-nav.layui-nav-tree").attr("class","layui-nav layui-nav-tree layui-hide");s("#"+e).attr("class","layui-nav layui-nav-tree layui-this")}else{t(a.parent().parent(),2)}}};t(s(this).parent(),1);return false}})},rollPosition:function(){var a=s(".layuimini-tab  .layui-tab-title");var i=0;a.children("li").each(function(){if(s(this).hasClass("layui-this")){return false}else{i+=s(this).outerWidth()}});a.animate({scrollLeft:i-a.width()/3},200)},rollClick:function(a){var i=s(".layuimini-tab  .layui-tab-title");var e=i.scrollLeft();if("left"===a){i.animate({scrollLeft:e-450},200)}else{i.animate({scrollLeft:e+450},200)}}};return m});