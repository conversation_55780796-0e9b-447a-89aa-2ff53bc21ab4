<?php /*a:3:{s:55:"C:\wwwroot\127.0.0.1\app\admin\view\auth\adminForm.html";i:1678200308;s:52:"C:\wwwroot\127.0.0.1\app\admin\view\common\head.html";i:1671106604;s:52:"C:\wwwroot\127.0.0.1\app\admin\view\common\foot.html";i:1672069288;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/static/plugs/layui-v2.7.6/css/layui.css" media="all">

    <link rel="stylesheet" href="/static/plugs/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/global.css" media="all">
    <script src="/static/common/js/jquery.min.js"></script>

    <script src="/static/admin/js/common.js"></script>
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
</head>
<body >
<div class="admin-main layui-anim layui-anim-upbit">
    <form class="layui-form layui-form-pane" lay-filter="form">
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo lang('username'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="username" lay-verify="required"  placeholder="<?php echo lang('Please enter'); ?><?php echo lang('username'); ?>" class="layui-input" <?php if(!empty($info_raw['username']))echo 'readonly'; ?>>
                <tip><?php echo fy("The username is between 2 and 25 characters. (Please confirm the user name at one time, and changing the user name at will may cause the data associated with the user name to be unable to be associated)"); ?></tip>
            </div>

        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Real name"); ?></label>
            <div class="layui-input-block">
                <input type="text" name="realname"   placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Real name'); ?>" class="layui-input" >
            </div>

        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">部门岗位</label>
            <div class="layui-input-block">
                <?php
                $authGroup = \think\facade\Db::name('auth_group')->field('id,pid,title')->order('pid asc,id asc')->select();
                $nav = new \clt\Leftnav();
                if($admin['group_id']==1){
                    $group_id=0;
                }else{
                    $group_id=\think\facade\Db::name('auth_group')->where('id','=',$admin['group_id'])->value('pid');

                }
                $authGroupTree = $nav->menu($authGroup,'|—',$group_id);
                ?>

                <select name="group_id" lay-verify="required">
                    <option value=""><?php echo fy('Please select'); ?><?php echo fy("Group"); ?></option>
                    <?php if(is_array($authGroupTree) || $authGroupTree instanceof \think\Collection || $authGroupTree instanceof \think\Paginator): $i = 0; $__LIST__ = $authGroupTree;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($vo['id']); ?>" <?php if(!empty($info_raw['group_id']) && $info_raw['group_id']==$vo['id'])echo 'selected'; ?>><?php echo htmlentities($vo['lefthtml']); ?><?php echo fy($vo['title']); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">角色</label>
            <div class="layui-input-block">
                <?php
                $authRole = \think\facade\Db::name('auth_role')->field('id,name')->order('sort asc,id asc')->select();

?>
                <select name="role_id" lay-verify="required">
                    <option value="">请选择角色</option>
                    <?php if(is_array($authRole) || $authRole instanceof \think\Collection || $authRole instanceof \think\Paginator): $i = 0; $__LIST__ = $authRole;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($vo['id']); ?>" <?php if(!empty($info_raw['role_id']) && $info_raw['role_id']==$vo['id'])echo 'selected'; ?>><?php echo htmlentities($vo['name']); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">直属上级</label>
            <div class="layui-input-block">
                <?php
                $where=[];
                if(!empty($info_raw['admin_id'])){
                    $where[]=['admin_id','<>',$info_raw['admin_id']];
                }
                $where[]=['is_open','=',1];
                $adminLst = \think\facade\Db::name('admin')->field('admin_id,username')->where($where)->order('admin_id asc')->select();

                ?>
                <select name="parent_id" >
                    <option value="0">无</option>
                    <?php if(is_array($adminLst) || $adminLst instanceof \think\Collection || $adminLst instanceof \think\Paginator): $i = 0; $__LIST__ = $adminLst;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($vo['admin_id']); ?>" <?php if(!empty($info_raw['parent_id']) && $info_raw['parent_id']==$vo['admin_id'])echo 'selected'; ?>><?php echo htmlentities($vo['username']); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Month target'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="mubiao"  placeholder="<?php echo fy('Performance monthly target amount'); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Commission point'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="ticheng"  class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo lang('pwd'); ?></label>
            <div class="layui-input-block">
                <input type="password" name="pwd" placeholder="<?php echo lang('Please enter'); ?> <?php echo fy('Login password'); ?>" <?php if(ACTION == 'adminadd'): ?>lay-verify="required"<?php endif; ?> class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Avatar"); ?></label>
            <input type="hidden" name="avatar" id="avatar">
            <input type="hidden" name="admin_id" id="admin_id">
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-primary" id="adBtn"><i class="icon icon-upload3"></i><?php echo fy('Click Upload'); ?></button>
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" id="adPic">
                        <p id="demoText"></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo lang('email'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="email" placeholder="<?php echo lang('Please enter'); ?> <?php echo fy('Username'); ?> <?php echo fy('email'); ?>" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo lang('tel'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="tel" lay-verify="required" value="" placeholder="<?php echo lang('Please enter'); ?> <?php echo fy('phone number'); ?>" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit="" lay-filter="submit"><?php echo lang('submit'); ?></button>
            </div>
        </div>
    </form>
</div>

<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script>
    layui.use(['form', 'layer','upload'], function () {
        var form = layui.form, layer = layui.layer,$= layui.jquery,upload = layui.upload;
        var info = <?php echo $info; ?>;
        form.val("form", info);
        if(info){
            $('#adPic').attr('src',"<?php echo attrUrl($info_raw?$info_raw['avatar']:''); ?>");
        }
        form.render();
        form.on('submit(submit)', function (data) {
            loading =layer.load(1, {shade: [0.1,'#fff']});
            $.post("", data.field, function (res) {
                layer.close(loading);
                if (res.code > 0) {
                    layer.msg(res.msg, {time: 2800, icon: 1}, function () {
                            window.parent.location.reload();
                    });
                } else {
                    layer.msg(res.msg, {time: 2800, icon: 2});
                }
            });
        });
        //普通图片上传
        var uploadInst = upload.render({
            elem: '#adBtn'
            ,url: '<?php echo url("ajax/upload"); ?>'
            ,before: function(obj){
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#adPic').attr('src', result); //图片链接（base64）
                });
            },
            done: function(res){
                if(res.code>0){
                    $('#avatar').val(res.data.url);
                    $('#demoText').hide();
                }else{
                    return layer.msg(res.msg);
                }
            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;"><?php echo fy("Upload failed"); ?></span> <a class="layui-btn layui-btn-mini demo-reload"><?php echo fy("Retry"); ?></a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });
    });
</script>