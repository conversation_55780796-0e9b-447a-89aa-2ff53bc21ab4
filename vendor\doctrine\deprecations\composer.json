{"name": "doctrine/deprecations", "type": "library", "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "license": "MIT", "require": {"php": "^7.1|^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3", "doctrine/coding-standard": "^9"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "autoload-dev": {"psr-4": {"DeprecationTests\\": "test_fixtures/src", "Doctrine\\Foo\\": "test_fixtures/vendor/doctrine/foo"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}