<?php

namespace app\admin\model;

use app\common\model\TimeModel;

class CrmContract extends TimeModel
{

    protected $name = "crm_contract";

    protected $deleteTime = false;


    /**
     * 根据规则自动生成编码
     * @param $prefix
     * @return int|mixed|string
     */
    public function autoNo($prefix){
        $replace_data=['[Y]'=>date('Y'),'[m]'=>date('m'),'[d]'=>date('d'),'[h]'=>date("H"),'[i]'=>date("i"),'[s]'=>date("s"),'[rand]'=>rand(100000,999999)];


        $numbering=str_replace(array_keys($replace_data),$replace_data,$prefix);
        $id=$this->where(['numbering'=>$numbering])->lock(true)->value('id');
//        保证合同编号唯一
        if($id){
          return  $this->autoNo($prefix);
        }
        return $numbering;
    }

}