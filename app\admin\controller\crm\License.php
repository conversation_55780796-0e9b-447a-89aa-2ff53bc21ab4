<?php

namespace app\admin\controller\crm;

use app\common\controller\AdminController;

use think\App;


class license extends AdminController
{


    public function __construct(App $app)
    {
        parent::__construct($app);

    }
    public function index(){
        try {
            // 直接返回授权成功信息，不再请求远程验证服务器
            $license_name = "系统已授权";
            
            // 可选: 将授权信息缓存下来，提高性能
            cache('license_name', $license_name);
            
            return json(['code'=>1, 'msg'=> $license_name]);
            
        } catch (\Throwable $t) {
            $this->error($t->getMessage());
        }
    }

    
}