define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'wechat/admin/menu/index',
        add_url: 'wechat/admin/menu/add',
        edit_url: 'wechat/admin/menu/edit',
        delete_url: 'wechat/admin/menu/delete',
        export_url: 'wechat/admin/menu/export',
        modify_url: 'wechat/admin/menu/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                toolbar:['refresh',[{
                    class: 'layui-btn layui-btn-normal layui-btn-sm',
                    method: 'open',
                    text: fy('Add'),
                    auth: 'add',
                    url: init.add_url,
                    extend: 'data-full="true"', icon: 'fa fa-plus',
                }], 'delete'],
                init: init,limit:CONFIG.ADMINPAGESIZE,modifyReload: true,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'id', title: 'ID'},
                    {field:'menu_name', title: fy("Menu name"),align: 'center',sort:'sort'},
                    {field: 'status',filter:'status',search: 'select',selectList:{0:"disabled",1:"enabled"},title: fy("Status"),sort:true,templet: ea.table.switch},
                    {field: 'create_time', title: fy('Creation time'),align: 'center',sort:'sort'},
                    {field:'update_time', title: fy("Update time"),align: 'center',sort:'sort'},
                    {width: 250, title: fy("Operate"), templet: ea.table.tool,operat: [
                [{
                    class: 'layui-btn layui-btn-success layui-btn-xs',
                    method: 'open',
                    text: fy("Edit"),
                    auth: 'edit',
                                url: 'wechat/admin/menu/edit',
                    extend: 'data-full="true"', icon: '',
                }], 'delete']},
                ]],done: function(res, curr, count){
                    if(count===undefined && res.msg && res.url){
                        ea.msg.tips(res.msg,1,function (){
                            window.top.location.href=res.url;
                        })
                    }
                    if(ea.checkMobile()){
                        ea.booksTemplet();//填充手机端视图模板
                    }
                }
            });

            ea.listen();
        },
        add: function () {
            Controller.api.bindevent();
            ea.listen();
        },
        edit: function () {
            Controller.api.bindevent();
            ea.listen();
        },
        api: {
            bindevent: function () {
                var menu_name = fy('Default menu'), obj;
                //一级菜单对象
                function parents(param) {
                    this.name = param;
                    this.type = 'click';
                    this.sub_button = [];
                }
                //二级菜单对象
                function subs(param) {
                    this.name = param;
                    this.type = 'click';
                }
                $(document).on('click', '.con-body .left .wx-menu-add', function (e) {
                    $(this).removeClass('wx-menu-add');
                    $(this).addClass('active');
                    $(this).siblings().removeClass('active');
                    var id = $(this).attr("data-id");
                    var ids = id.split("_");
                    var menuLength = $(this).siblings().length;
                    //一级菜单
                    if (ids.length === 1) {
                        //首级菜单只有一个时
                        if (menuLength < 2) {
                            var data_id = menuLength + 2
                            $(this).parent().append('<li class="menu-list-view wx-menu-add" data-id="' + data_id + '"><span class="layui-icon layui-icon-close"></span></li>');
                            $(this).children().remove();
                            $(this).append("<span>"+fy("Level %s menu",1)+"</span>")
                        }
                        //最后一个菜单时
                        if (menuLength === 2) {
                            $(this).children().remove();
                            $(this).append("<span>"+fy("Level %s menu",1)+"</span>")
                        }
                        data_id = ids[0] + '_' + 0;
                        if($(this).find('ul').length ===0){
                            $(this).append('<ul style="display: block"><li data-id="' + data_id + '" class="menu-list-sub wx-menu-add"><span class="layui-icon layui-icon-close"></span></li></ul>');
                        }
                        menuList.push(new parents(fy("Level %s menu",1)))
                    } else {   //二级菜单
                        if (menuLength < 4) {
                            data_id = ids[0] + '_' + (1+parseInt(ids[1]))
                            $(this).parent().append('<li class="menu-list-sub wx-menu-add" data-id="' + data_id + '"><span class="layui-icon layui-icon-close"></span></li>');
                            $(this).children().remove();
                            $(this).append("<span>"+fy("Level %s menu",2)+"</span>")
                        }
                        //最后一个菜单时
                        if (menuLength === 4) {
                            $(this).children().remove();
                            $(this).append("<span>"+fy("Level %s menu",2)+"</span>")
                        }
                        if (!menuList[ids[0]].hasOwnProperty('sub_button')) {
                            var sub_button = {"sub_button": {'name':fy("Level %s menu",2),"type":'click'}};
                            menuList[ids[0]].append(sub_button);
                        }
                        menuList[ids[0]].sub_button.push(new subs(fy("Level %s menu",2)));
                    }
                    e.stopPropagation();//阻止冒泡
                    e.preventDefault();
                });
                $(document).on('click', '.menu-list-view,.menu-list-sub', function (e) {
                    $('.menu-list').find('.active').removeClass('active');
                    $(this).addClass('active');
                    var id = $(this).attr('data-id'), type;
                    ids = id.split('_');

                    // type = menuList[ids[0]]['sub_button'][ids[1]]['type'];
                    // $('input[name="type"][value="'+type+'"]').prop("checked", 'checked');
                    // $('.media').show();
                    // $('.delmenu').html(fy("Delete the submenu"));

                   if (ids.length === 1) {
                        // if($(this).find('ul>li').length > 1){
                        //     $('.media').hide();
                        // }else{
                        //     $('.media').show();
                        // }
                       $('.media').show();
                        type = menuList[ids[0]]['type']
                        $('input[value="'+type+'"]').prop("checked", 'checked');
                        $('.delmenu').html(fy("Delete")+fy('Menu'))
                    } else {
                        type = menuList[ids[0]]['sub_button'][ids[1]]['type'];
                        $('input[name="type"][value="'+type+'"]').prop("checked", 'checked');
                        $('.media').show();
                        $('.delmenu').html(fy("Delete the submenu"));
                    }
                    var radioindex = $('.layui-tab-title').find('input[name="type"]').index($('input[value="'+type+'"]'));
                    if(type==='click' || type==='view' || type==="miniprogram"){
                        $('.layui-tab-content').find('.layui-tab-item').eq(radioindex).addClass('layui-show').siblings().removeClass('layui-show');
                        var inputVal  = ids.length>1?menuList[ids[0]]['sub_button'][ids[1]]['key']:menuList[ids[0]]['key'];
                        var input1 = $('.layui-tab-content').find('.layui-tab-item').eq(radioindex).find('input').eq(0);
                        switch(type){
                            case 'click':
                                input1.val(inputVal);
                                break;
                            case 'view':
                                inputVal = ids.length>1?menuList[ids[0]]['sub_button'][ids[1]]['url']:menuList[ids[0]]['url'];
                                input1.val(inputVal);
                                break;
                            case 'miniprogram':
                                var appid = ids.length>1?menuList[ids[0]]['sub_button'][ids[1]]['appid']:menuList[ids[0]]['appid'];
                                var pagepath = ids.length>1?menuList[ids[0]]['sub_button'][ids[1]]['pagepath']:menuList[ids[0]]['pagepath'];
                                input1.val(appid);
                                $('.layui-tab-content').find('.layui-tab-item').eq(radioindex).find('input').eq(1).val(pagepath)
                                break;
                        }
                    }else{
                        $('.layui-tab-content').find('.layui-tab-item').removeClass('layui-show');
                    }
                    layui.form.render();
                    $('input[name="title"]').val($(this).children('span').text())
                    e.stopPropagation();//阻止冒泡
                    e.preventDefault();
                })
                layui.form.on('radio(type)',function(){
                    var type = $(this).val();
                    var key = $(this).data('key');
                    upmenulist(3,type,key);
                })
                $('input').keyup(function () {
                    //更新菜单那
                    var val = $(this).val();
                    var name = $(this).attr('name');
                    if (name === 'title') {
                        $('.menu-list').find('.active').children('span').text(val);
                        upmenulist();
                    } else if (name === 'menu_name') {
                        menu_name = val;
                    } else if (name === 'url') {
                        upmenulist(2, 'view', name);
                    } else if (name === 'keyword') {
                        upmenulist(2, 'click', name);
                    } else if(name === 'miniprogram') {
                        upmenulist(2, 'miniprogram', name);
                    }else {
                        upmenulist(2, 'click', name);
                    }
                })
                //更新菜单
                function upmenulist(i=1,type='view',name='') {
                    obj = $('.menu-list').find('.active');
                    if(obj.length === 0) return false;
                    var id = obj.attr("data-id");
                    var ids = id.split("_");
                    var val = obj.children('span').text();
                    if(i ===1){ //菜单
                        if (ids.length === 1) {
                            menuList[ids[0]].name = val;
                        } else {
                            menuList[ids[0]].sub_button[ids[1]].name = val;
                        }
                    }else if(i===2){ //事件
                        val = $('input[name="'+name+'"]').val();
                        if (ids.length === 1) {
                            menuList[ids[0]].type = type;
                            menuList[ids[0]][name] = val
                        } else {
                            menuList[ids[0]].sub_button[ids[1]].type = type;
                            menuList[ids[0]].sub_button[ids[1]][name] = val
                        }
                    }else if(i===3){//radio
                        if (ids.length === 1) {
                            menuList[ids[0]].name = val;
                            menuList[ids[0]].type = type;
                            if(!menuList[ids[0]].hasOwnProperty('key')){
                                menuList[ids[0]].key = name;
                            }
                        } else {
                            menuList[ids[0]].sub_button[ids[1]].name = val;
                            menuList[ids[0]].sub_button[ids[1]].type = type;
                            if(!menuList[ids[0]].sub_button[ids[1]].hasOwnProperty('key')){
                                menuList[ids[0]].sub_button[ids[1]].key = name;
                            }
                        }
                    }
                }
                //删除菜单
                $(document).on('click', '.delmenu', function (e) {
                    console.log(menuList);
                    layer.confirm(fy("OK to delete the menu")+'？', {btn: [fy("Ok"), fy("Cancel")], title: fy("Tips")}, function () {
                        $menu = $('.menu-list').find('.active');
                        if ($menu.length === 0) {
                            ea.msg.error(fy("Please select the menu you want to delete"),function (){
                                layer.closeAll();
                            });

                            return false;
                        }
                        if ($menu.find('ul>li').length > 1) {
                            ea.msg.error(fy("If there is a submenu, please delete the submenu first"),function (){
                                layer.closeAll();
                            });
                            return false;
                        }
                        var id = $menu.attr('data-id');
                        ids = id.split('_');
                        if (ids.length > 1) {
                            // delete menuList[ids[0]]['sub_button'][ids[1]];
                            menuList[ids[0]]['sub_button'].splice(ids[1],1);
                            if($menu.parent('ul').children('.wx-menu-add').length ===0){
                                data_id = ids[0]+'_'+ ((parseInt(ids[1])-1));
                                $menu.parent('ul').append('<li class="menu-list-sub wx-menu-add" data-id="' + data_id + '"><span class="layui-icon layui-icon-close"></span></li>');
                            }
                        } else {
                            // delete menuList[ids[0]];
                            menuList.splice(ids[0],1);
                            if($menu.parent('ul').children('.wx-menu-add').length ===0) {
                                var data_id = ids[0];
                                $menu.parent('ul').append('<li class="menu-list-view wx-menu-add" data-id="' + data_id + '"><span class="layui-icon layui-icon-close"></span></li>');
                            }
                        }
                        $menu.remove();
                        layer.closeAll();
                    });
                })
                //保存自定义菜单
                $(document).on('click', '#save',function () {
                    let url = null;
                    let strRegex = '(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]';
                    let re = new RegExp(strRegex);
                    let flag ;
                    if(menuList.length===0)  ea.msg.error(fy("Please enter")+fy("Menu name")); flag = false;
                    for (let i = 0; i < menuList.length; i++) {
                        console.log(menuList[i].sub_button.length);
                        if (menuList[i].sub_button.length) {
                            //判断是否有子元素
                            for (let j = 0; j < menuList[i].sub_button.length; j++) {
                                //二级菜单
                                console.log(menuList[i].sub_button[j]);
                                if (menuList[i].sub_button[j]==undefined || !menuList[i].sub_button[j]['name'] || !menuList[i].sub_button[j]['type']) {
                                    ea.msg.error(fy("The menu name cannot be empty"));
                                    return false;
                                }else if (menuList[i].sub_button[j]['type']==='view' && menuList[i].sub_button[j].hasOwnProperty('url')){
                                    flag = true;
                                    url = menuList[i].sub_button[j].url;
                                    if (!re.test(url)) {
                                        ea.msg.error(fy("Please enter the correct URL address"));
                                        return false;
                                    }
                                } else if (menuList[i].sub_button[j].hasOwnProperty('appid') &&  menuList[i].sub_button[j].hasOwnProperty('pagepath')) {
                                    flag = true;
                                } else  {
                                    flag = true;
                                }
                            }
                        } else {
                            //一级菜单 url
                            if (!menuList[i]['name'] || !menuList[i]['type']  ) {
                                ea.msg.error(fy("The menu name or menu type cannot be empty"));
                                return false;
                            }else if (menuList[i]['type']==='view' && menuList[i].hasOwnProperty('url')) {
                                flag = true;
                                url = menuList[i].url;
                                if (!re.test(url)) {
                                    ea.msg.error(fy("Please fill in the correct URL address"));
                                    return false;
                                }
                            } else if (menuList[i].hasOwnProperty('appid') && menuList[i].hasOwnProperty('pagepath') ) {
                                flag = true;
                            } else {
                                flag = true;
                            }
                        }
                    }
                    // 添加素材
                    if (flag) {
                        ea.request.post({ url: window.location.href,
                            data:{
                                "menu_data": JSON.stringify(menuList),//先将对象转换为字符串再传给后台
                                'menu_name':menu_name,
                                '__token__':$('input[name="__token__"]').val()
                            }}, function (res) {
                            ea.msg.success(res.msg,function (){
                                parent.layui.table.reload(init.table_render_id);
                            });

                        }, function (res) {
                            ea.msg.error(res.msg);
                        });
                    }
                    return false;
                });
            },
        }
    };
    return Controller;
});
