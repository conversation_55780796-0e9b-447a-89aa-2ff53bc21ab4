<?php /*a:2:{s:58:"C:\wwwroot\127.0.0.1\app\admin\view\system\fields\add.html";i:1709911190;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<style>
    .layui-iconpicker-body.layui-iconpicker-body-page .hide {
        display: none;
    }
</style>
<link rel="stylesheet" href="/static/plugs/lay-module/autocomplete/autocomplete.css" media="all">
<script>
    var parameter = '';
</script>
<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        <input type="hidden" name="table" value="<?php echo htmlentities((isset($row['table']) && ($row['table'] !== '')?$row['table']:'')); ?>">
        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('Field'); ?><?php echo fy('Name'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="name" name="name" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Field'); ?><?php echo fy('Name'); ?>"  lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Field'); ?><?php echo fy('Name'); ?>" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Presentation name'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="xsname" name="xsname" class="layui-input"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Presentation name'); ?>">
                <tip><?php echo fy('Fill out the form'); ?><?php echo fy('Presentation name'); ?></tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('Field'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="field" name="field" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Field'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Field'); ?>" value=""  lay-verify="required">
                <tip>数据库表字段名，<?php echo fy('Must start with an English letter, followed by supported numbers and _'); ?>，必须保证同一张表，字段名唯一。</tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Sort'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="sort" name="sort" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Sort'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Sort'); ?>" value="100">
            </div>
        </div>
        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required"><?php echo fy('Field type'); ?></label>
            <div class="layui-input-block">
                <select name="type" class="layui-select" lay-search>
                    <?php if(isset($alldata['field']['type']['option'])): foreach($alldata['field']['type']['option'] as $key=>$vo): if($key=='varchar'): ?>
                    <option value="<?php echo htmlentities($key); ?>" selected><?php echo $vo; ?></option>
                    <?php else: ?>
                    <option value="<?php echo htmlentities($key); ?>"><?php echo $vo; ?></option>
                    <?php endif; ?>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Field length'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="lang" name="lang" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Field length'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Field length'); ?>" value="255">
                <tip><?php echo fy('Enter the maximum number of characters that need to be saved in this field'); ?>。</tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Default value'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="default" name="default" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Default value'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Default value'); ?>" value="">
                <tip><?php echo fy('Please enter'); ?><?php echo fy('Default value'); ?>。</tip>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Validation rules'); ?></label>
            <div class="layui-input-block">
                <div id="multiple-select"></div>
                <tip><?php echo fy('Added edit data will be validated'); ?></tip>
            </div>
        </div>
        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required"><?php echo fy('form types'); ?></label>
            <div class="layui-input-block">
                <select name="formtype" class="layui-select" lay-search>
                    <?php if(isset($alldata['field']['formtype']['option'])): foreach($alldata['field']['formtype']['option'] as $key=>$vo): if(!IS_DEV && in_array($key,['video','color','none','password','optgroup','json','lradio',"lcheckbox","lselect",'lselects',"treecheckbox",  "aselect","selectgroup"])){continue; }?>
                    <option value="<?php echo htmlentities($key); ?>"><?php echo fy($vo); ?></option>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Options'); ?></label>
            <div class="layui-input-block">
                <textarea type="text" id="option" name="option" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Options'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Options'); ?>" ></textarea>
                <tip><?php echo fy('The form type is a single choice box, a multiple choice box, or a drop-down box. Example: 0: close, 1: open'); ?></tip>
            </div>
        </div>
        <?php  if(IS_DEV){ ?>
        <div class="layui-form-item">
            <label class="layui-form-label">ajax<?php echo fy('Drop down the request address'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="href" name="href" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?>ajax<?php echo fy('Drop down the request address'); ?>" placeholder="<?php echo fy('Please enter'); ?>ajax<?php echo fy('Drop down the request address'); ?>" value="<?php echo myurl('ajax/ajaxselect'); ?>">
                <tip><?php echo fy('Please enter'); ?>ajax<?php echo fy('Drop down the request address'); ?>。</tip>
            </div>
        </div>

        <div class="layui-form-item  layui-row layui-col-xs12">
            <label class="layui-form-label required"><?php echo fy('Association Table'); ?></label>
            <div class="layui-input-block">
                <select name="join_table" class="layui-select" lay-search>
                    <option value=""><?php echo fy('Please select'); ?></option>
                    <?php foreach($getTableList as $key=>$vo): ?>
                    <option value="<?php echo htmlentities($vo['value']); ?>"><?php echo $vo['name']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Presentation field'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="foreign_key" name="foreign_key" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Presentation field'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Presentation field'); ?>" value="name">

            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Storage field'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="relationship_primary_key" name="relationship_primary_key" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Storage field'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Storage field'); ?>" value="id">

            </div>
        </div>
        <?php } ?>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Description'); ?></label>
            <div class="layui-input-block">
                <textarea type="text" id="describe" name="describe" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Description'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Description'); ?>" ></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('List display'); ?></label>
            <div class="layui-input-block">
                <input type="checkbox" name="show" checked lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('Form'); ?></label>
            <div class="layui-input-block">
                <input type="checkbox" name="edit" checked lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('Search'); ?></label>
            <div class="layui-input-block">
                <input type="checkbox" name="search" lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>">

            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('Import'); ?></label>
            <div class="layui-input-block">
                <input type="checkbox" name="export" lay-skin="switch" lay-text="<?php echo fy('Open'); ?>|<?php echo fy('Close'); ?>">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('width'); ?></label>
            <div class="layui-input-block">
                <input type="text" id="width" name="width" class="layui-input" lay-reqtext="<?php echo fy('Please enter'); ?><?php echo fy('Field'); ?> <?php echo fy('width'); ?>" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Field'); ?> <?php echo fy('width'); ?>" value="100">
            </div>
        </div>

    <div class="layui-form-item">
    <label class="layui-form-label">布局类</label>
    <div class="layui-input-block">
            <input type="text" id="grid" name="grid" class="layui-input" placeholder="请填写栅格布局的类名" value="">
    <tip>栅格布局类名(实现1行占多少个表单)，类名参考https://layui.dev/docs/2/layout/grid.html，通常实现中等屏幕(桌面≥992px)layui-col-md4（1行3列）  layui-col-md6（1行2列）</tip>
    </div>
    </div>

        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit><?php echo fy('Confirm'); ?></button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm"><?php echo fy('Reset'); ?></button>
        </div>

    </form>
</div>

</body>
</html>