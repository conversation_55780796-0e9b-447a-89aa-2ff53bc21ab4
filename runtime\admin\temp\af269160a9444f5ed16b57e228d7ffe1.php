<?php /*a:1:{s:52:"C:\wwwroot\127.0.0.1\app\admin\view\index\index.html";i:1741851894;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.min.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.min.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.min.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.min.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.min.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body class="layui-layout-body layuimini-all">
<div class="layui-layout layui-layout-admin">

    <div class="layui-header header">
        <div class="layui-logo layuimini-logo">
            <?php echo htmlentities($system['site_abbr']); ?>
        </div>

        <div class="layuimini-header-content">
            <a>
                <div class="layuimini-tool"><i title="展开" class="fa fa-outdent" data-side-fold="1"></i></div>
            </a>

            <!--电脑端头部菜单-->
            <ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-menu-header-pc layuimini-pc-show">
            </ul>

            <!--手机端头部菜单-->
            <ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-mobile-show">
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="fa fa-list-ul"></i> 选择模块</a>
                    <dl class="layui-nav-child layuimini-menu-header-mobile">
                    </dl>
                </li>
            </ul>

            <ul class="layui-nav layui-layout-right">

                <li class="layui-nav-item" lay-unselect>
                    <a class="refresh" href="javascript:;" data-refresh="刷新"><i class="fa fa-refresh"></i></a>
                </li>
                <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:;" data-clear="清理" class="layuimini-clear"><i class="fa fa-trash-o"></i></a>
                </li>
              <!--  <li class="layui-nav-item lay-unselect">
                    <a href="javascript:;"><i class="fa fa-language"></i></a>
                    <dl class="layui-nav-child">

                        <dd>
                            <a class="js-ajax-btn <?php if(cookie(config('lang.cookie_var'))=='zh-cn'){echo 'active';} ?>" href="javascript:;" data-url="index/language?lang=zh-cn"  data-refresh="1">简体中文</a>
                        </dd>
                        <dd>
                            <a class="js-ajax-btn <?php if(cookie(config('lang.cookie_var'))=='en-us'){echo 'active';} ?>" href="javascript:;" data-url="index/language?lang=en-us" data-refresh="1">English</a>
                        </dd>
                    </dl>
                </li>-->
                <li class="layui-nav-item mobile layui-hide-xs" lay-unselect>
                    <a href="javascript:;" data-check-screen="full"><i class="fa fa-arrows-alt"></i></a>
                </li>

                <li class="layui-nav-item layuimini-setting">
                    <a href="javascript:;">
                        <img src="<?php echo attrUrl($admin['avatar']); ?>" class="layui-nav-img" width="50" height="50">
                        <cite class="adminName"><?php echo htmlentities($admin['username']); ?></cite>
                        <span class="layui-nav-more"></span>
                    </a>
                    <dl class="layui-nav-child">

                        <dd>
                            <a href="javascript:;" layuimini-content-href="<?php echo url('general/profile'); ?>" data-title="<?php echo lang('Profile'); ?>" data-icon="fa fa-gears"><?php echo lang('Profile'); ?></a>
                        </dd>
                        <dd>
                            <hr>
                        </dd>
                        <dd>
                            <a href="javascript:;" class="login-out"><?php echo lang('Logout'); ?></a>
                        </dd>
                    </dl>
                </li>
                <li class="layui-nav-item layuimini-select-bgcolor" lay-unselect>
                    <a href="javascript:;" data-bgcolor="<?php echo fy('Color scheme'); ?>"><i class="fa fa-ellipsis-v"></i></a>
                </li>
            </ul>
        </div>
    </div>

    <!--无限极左侧菜单-->
    <div class="layui-side layui-bg-black layuimini-menu-left">
    </div>

    <!--初始化加载层-->
    <div class="layuimini-loader">
        <div class="layuimini-loader-inner"></div>
    </div>

    <!--手机端遮罩层-->
    <div class="layuimini-make"></div>

    <!-- 移动导航 -->
    <div class="layuimini-site-mobile"><i class="layui-icon"></i></div>

    <div class="layui-body">
        <div class="layuimini-tab layui-tab-rollTool layui-tab" lay-filter="layuiminiTab" lay-allowclose="true">
            <ul class="layui-tab-title">
                <li class="layui-this" id="layuiminiHomeTabId" lay-id=""></li>
            </ul>
            <div class="layui-tab-control">
                <li class="layuimini-tab-roll-left layui-icon layui-icon-left"></li>
                <li class="layuimini-tab-roll-right layui-icon layui-icon-right"></li>
                <li class="layui-tab-tool layui-icon layui-icon-down">
                    <ul class="layui-nav close-box">
                        <li class="layui-nav-item">
                            <a href="javascript:;"><span class="layui-nav-more"></span></a>
                            <dl class="layui-nav-child">
                                <dd><a href="javascript:;" layuimini-tab-close="current">关 闭 当 前</a></dd>
                                <dd><a href="javascript:;" layuimini-tab-close="other">关 闭 其 他</a></dd>
                                <dd><a href="javascript:;" layuimini-tab-close="all">关 闭 全 部</a></dd>
                            </dl>
                        </li>
                    </ul>
                </li>
            </div>
            <div class="layui-tab-content">
                <div id="layuiminiHomeTabIframe" class="layui-tab-item layui-show">

                </div>

            </div>
            <div class="layui-footer footer footer-demo" id="admin-footer">
                <div class="layui-main">
                    <p style="text-align: center;">
                        <script>
                           document.write((new Date()).getFullYear()+'&copy; CRM')
                        </script>
                        <span class="license"></span>
                    <?php echo htmlentities($system['copyright']); ?>

                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
