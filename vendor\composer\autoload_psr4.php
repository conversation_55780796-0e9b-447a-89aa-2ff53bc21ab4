<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'think\\view\\driver\\' => array($vendorDir . '/topthink/think-view/src'),
    'think\\trace\\' => array($vendorDir . '/topthink/think-trace/src'),
    'think\\captcha\\' => array($vendorDir . '/topthink/think-captcha/src'),
    'think\\app\\' => array($vendorDir . '/topthink/think-multi-app/src'),
    'think\\' => array($vendorDir . '/topthink/framework/src/think', $vendorDir . '/topthink/think-filesystem/src', $vendorDir . '/topthink/think-helper/src', $vendorDir . '/topthink/think-orm/src', $vendorDir . '/topthink/think-template/src'),
    'liliuwei\\think\\' => array($vendorDir . '/liliuwei/thinkphp-jump/src'),
    'app\\' => array($baseDir . '/app'),
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'WePay\\' => array($vendorDir . '/zoujingli/wechat-developer/WePay'),
    'WePayV3\\' => array($vendorDir . '/zoujingli/wechat-developer/WePayV3'),
    'WeMini\\' => array($vendorDir . '/zoujingli/wechat-developer/WeMini'),
    'WeChat\\' => array($vendorDir . '/zoujingli/wechat-developer/WeChat'),
    'Test\\' => array($vendorDir . '/zhongshaofa/easy-admin/tests'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Php72\\' => array($vendorDir . '/symfony/polyfill-php72'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'MyCLabs\\Enum\\' => array($vendorDir . '/myclabs/php-enum/src'),
    'MockApp\\' => array($vendorDir . '/zhongshaofa/easy-admin/mock_app'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'EasyAdmin\\' => array($vendorDir . '/zhongshaofa/easy-admin/src'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/lib/Doctrine/Deprecations'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Doctrine\\Common\\Annotations\\' => array($vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Box\\Spout\\' => array($vendorDir . '/box/spout/src/Spout'),
    'AliPay\\' => array($vendorDir . '/zoujingli/wechat-developer/AliPay'),
);
