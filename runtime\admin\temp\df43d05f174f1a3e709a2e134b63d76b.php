<?php /*a:2:{s:55:"/www/wwwroot/ceshi71.cn/app/admin/view/product/add.html";i:1672123190;s:58:"/www/wwwroot/ceshi71.cn/app/admin/view/layout/default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/public/static/js/html5.min.js"></script>
    <script src="/public/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/public/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/public/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">
        
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Product Classification'); ?></label>
            <div class="layui-input-block">
                <select name="product_type_id" lay-verify="required" lay-filter="product_type_id" >
                    <option value=""><?php echo fy('Please select'); ?><?php echo fy('Product Classification'); ?></option>
                    <?php
 $typeLst = \think\facade\Db::name('product_type')->order('pid asc,sort asc,id asc')->select();
                    $nav = new \clt\Leftnav();
                    $typeLst = $nav->menu($typeLst);
                    if(is_array($typeLst) || $typeLst instanceof \think\Collection || $typeLst instanceof \think\Paginator): $i = 0; $__LIST__ = $typeLst;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <option value="<?php echo htmlentities($vo['id']); ?>" ><?php echo htmlentities($vo['lefthtml']); ?><?php echo htmlentities($vo['title']); ?></option>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Product name'); ?></label>
            <div class="layui-input-block">

                <input type="text" name="name" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Product name'); ?>" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required"><?php echo fy('Product images'); ?></label>
            <div class="layui-input-block layuimini-upload">
                <input name="thumb" class="layui-input layui-col-xs6"   placeholder="<?php echo fy('Please upload'); ?> <?php echo fy('Product images'); ?>" value="">
                <div class="layuimini-upload-btn">
                    <span><a class="layui-btn" data-upload="thumb" data-upload-number="one" data-upload-exts="png,jpg,ico,jpeg" data-upload-icon="image"><i class="fa fa-upload"></i><?php echo fy('Upload'); ?></a></span>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Specifications'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="specification" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Specifications'); ?>，<?php echo fy('For example'); ?>：1000g*1 <?php echo fy('bag'); ?>" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Model'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="model" class="layui-input"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Model'); ?>" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy("Inventory"); ?></label>
            <div class="layui-input-block">
                <input type="text" name="inventory" class="layui-input" lay-verify="required" placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Inventory'); ?>" value="0">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Minimum stock warning'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="min_warning" class="layui-input"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Minimum stock warning'); ?>" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Max Stock Alert'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="max_warning" class="layui-input"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Max Stock Alert'); ?>" value="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Cost price'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="cost_price" class="layui-input"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Cost price'); ?>" value="0.00">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Sale price'); ?></label>
            <div class="layui-input-block">
                <input type="text" name="sale_price" class="layui-input"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Sale price'); ?>" value="0.00">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label"><?php echo fy('Remark'); ?></label>
            <div class="layui-input-block">
                <textarea name="remark" class="layui-textarea"  placeholder="<?php echo fy('Please enter'); ?>备注"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo fy('Status'); ?></label>
            <div class="layui-input-inline">
                <select name="status"  lay-filter="status">
                    <option value="1"><?php echo fy('Shelves'); ?></option>
                    <option value="0"><?php echo fy('Removal'); ?></option>
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux"><?php echo fy('Removed products will not be associated with actions'); ?></div>
        </div>
        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit><?php echo fy('Confirm'); ?></button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm"><?php echo fy('Reset'); ?></button>
        </div>

    </form>
</div>
</body>
</html>