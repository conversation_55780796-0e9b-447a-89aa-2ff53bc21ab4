<?php
namespace app\admin\model;

use think\Model;

class CrmClientOrder extends Model
{
    protected $deleteTime = false;
    public function getStatusList()
    {
        return ['-1'=>fy('The audit failed'),'0'=>fy('To be reviewed'),'1'=>fy('Approved'),];
    }

    public function getOrderNo(){
        $max_orderno=self::whereRaw('length(`orderno`)=13')->lock(true)->max('orderno');
        $last_four = substr($max_orderno, -5);  // 获取后面7位
        $remaining = substr($max_orderno, 0, -5);  // 获取除了后面7位以外的所有字符
        $jt=date('Ymd');
        if($remaining==$jt){
//            说明是今天
            if(is_numeric($last_four)){
                $last_four=$last_four+1;
            }else{
                $last_four=1;
            }

        }else{
//            今天的从1开始
            $last_four=1;
        }
        $key =  $jt . str_pad($last_four, 5, "0", STR_PAD_LEFT);
        $res=$this->where(['orderno'=>$key])->lock(true)->count();
        if($res){
            return  $this->getOrderNo();
        }
        return $key;
    }
}
