{"name": "zhongshaofa/easy-admin", "description": "EasyAdmin工具，https://github.com/zhongshaofa/easyadmin-sdk", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "minimum-stability": "stable", "require": {"php": ">=7.1.0", "doctrine/annotations": "^1.13.1", "ext-json": "*"}, "require-dev": {"mockery/mockery": "^1.3.0", "phpunit/phpunit": "^8.5.0"}, "autoload": {"psr-4": {"EasyAdmin\\": "src", "MockApp\\": "mock_app", "Test\\": "tests"}}, "scripts": {"test": "phpunit --testdox"}}