<?php /*a:2:{s:57:"/www/wwwroot/ceshi71.cn/app/admin/view/open/apps/doc.html";i:1709446614;s:58:"/www/wwwroot/ceshi71.cn/app/admin/view/layout/default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/public/static/js/html5.min.js"></script>
    <script src="/public/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/public/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/public/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/public/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/public/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/public/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<style>
    pre {
        border: 1px solid #e4e4e4;
        background: #f9fafa;
        border-radius: 1px;
        font-family: monaco, Consolas, "Liberation Mono", Menlo, Courier, monospace;
        padding: 16px;
        overflow: auto;
        line-height: 1.45;
    }
    h2 {
         font-size: 28px;
         font-weight: 500;
         clean: both;

     }
    h2,h3,h4{
        font-family: "Segoe UI Light", "Segoe UI","Microsoft Jhenghei", "Microsoft Yahei", arial, sans-serif;
    }
    h3 {
        font-size: 22px;
        font-weight: 450;
    }
    h4 {
        font-size: 20px;
        font-weight: 450;
    }
    th,td {
        font-family: "Segoe UI Light", "Segoe UI","Microsoft Jhenghei", "Microsoft Yahei", arial, sans-serif;
        font-size: 16px;
        border: 1px solid #e0e0e0;
        padding: 3px 12px; text-align: left;
    }

</style>
<div class="layuimini-container">
    <div class="layuimini-main">

        <h2>接口1：获取数据</h2>
        通过api get查询哪个账号哪一天有多少数据量和数据内容列表
        <pre>GET <?php echo request()->domain(); ?>/open/index/id/<?php echo htmlentities($row['id']); ?>/token/<?php echo htmlentities($row['token']); ?> </pre>
        <h3>请求参数</h3>

        <table>
            <thead>
            <tr>

                <th>参数名称</th>
                <th>类型</th>
                <th>必须</th>
                <th>描述</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>account</td>
                <td>string</td>
                <td>是</td>
                <td>查询哪个账号负责人下的数据</td>
            </tr>
            <tr>
                <td>create_time</td>
                <td>string</td>
                <td>是</td>
                <td>查询哪一天的数据,格式类似：2024-02-18</td>
            </tr>
            <tr>
                <td>page</td>
                <td>string</td>
                <td>否</td>
                <td>查询哪一页的数据,默认返回第一页，每一页返回10条，根据客户信息创建时间降序排列</td>
            </tr>
            </tbody>
        </table>
        <h3>返回参数</h3>
        <table>
            <thead>
            <tr>
                <th>名称</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>code</td>
                <td>int</td>
                <td>状态 0 获取失败 1 获取成功 </td>
            </tr>
            <tr>
                <td>msg</td>
                <td>string</td>
                <td>返回提示信息</td>
            </tr>
            <tr>
                <td>count</td>
                <td>int</td>
                <td>查询结果总数据量</td>
            </tr>
            <tr>
                <td>data</td>
                <td>json</td>
                <td>查询结果数据集合(详见如下data数据集说明，返回的是列表展示字段数据集合)</td>
            </tr>
            </tbody>
        </table>
        <h4>data数据集说明</h4>
        <table>
            <thead>
            <tr>
                <th>名称</th>
                <th>类型</th>
                <th>说明</th>
            </tr>
            </thead>
            <tbody>
            <?php
$prefix=getDataBaseConfig('prefix');
            $fields=\think\facade\Db::query("SELECT  `name`,`xsname`,`field`,`type`,`show` FROM `".$prefix."system_field` WHERE `table`='crm_customer' AND `show`=1 order BY `sort` ASC,id ASC");

            foreach($fields as $k=>$v){
            $name=!empty($v['xsname'])?$v['xsname']:$v['name'];
?>
            <tr>
                <td><?php echo htmlentities($v['field']); ?></td>
                <td>
                    <?php echo strpos($v['type'], 'int') !== false?'int':'string' ?>
                </td>
                <td><?php echo htmlentities($name); ?></td>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
    <div class="layuimini-main">

        <h2>接口2：客户数据插入</h2>
        通过api POST提交客户数据
        <pre>POST <?php echo request()->domain(); ?>/open/index/id/<?php echo htmlentities($row['id']); ?>/token/<?php echo htmlentities($row['token']); ?> </pre>
        <h3>POST参数</h3>
        <table>
            <thead>
            <tr>
                <th>参数名称</th>
                <th>类型</th>
                <th>必须</th>
                <th>描述</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>account</td>
                <td>string</td>
                <td>否</td>
                <td>客户信息负责人（必须是后台存在的账号，不存在或不填写则进入公海）</td>
            </tr>


            <?php
$prefix=getDataBaseConfig('prefix');
            $fields=\think\facade\Db::query("SELECT  `name`,`xsname`,`field`,`type`,`rule` FROM `".$prefix."system_field` WHERE `edit`=1 AND `table`='crm_customer' AND `addinput` is not null AND field<>'id' order BY `sort` ASC,id ASC");

            foreach($fields as $k=>$v){
            $name=!empty($v['xsname'])?$v['xsname']:$v['name'];
            ?>
            <tr>
                <td><?php echo htmlentities($v['field']); ?></td>
                <td>
                    <?php echo strpos($v['type'], 'int') !== false?'int':'string' ?>
                </td>
                <td><?php echo strpos($v['rule'], 'require') !== false?'是':'否' ?></td>
                <td><?php echo htmlentities($name); ?></td>
            </tr>
            <?php } ?>

            </tbody>
        </table>
        <h3>返回参数</h3>
        <table>
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
            <tr>
                <td>code</td>
                <td>int</td>
                <td>状态 0 录入失败 1 录入成功 </td>
            </tr>
            <tr>
                <td>msg</td>
                <td>string</td>
                <td>返回提示信息</td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

</body>
</html>