<script>
    var menuList = {:json_encode($formData['menu_data']??[])};
</script>
<link rel="stylesheet" type="text/css" href="__MY_PUBLIC__/static/plugins/wechat/admin/css/menu/index.css">
<link rel="stylesheet" type="text/css" href="__MY_PUBLIC__/static/plugins/wechat/admin/css/iconfont/iconfont.css">
<!-- 主体 S -->
<div class="content">
    <div class="con">
        <div class="layui-wx-msg">
            <div class="layui-elem-quote">
                <div>
                    <span class="layui-icon layui-icon-tips"></span>
                </div>
                <div>
                    <p>{:fy('Menu editing')}</p>
                </div>
            </div>
        </div>
        <form class="layui-form">
            <div class="con-body">
                <div class="left">
                    <div>
                        <div class="mobile-header">
                            <p class="menu-title">{$account.wxname}</p>
                        </div>
                        <div class="menu-keybords">
                            <ul class="menu-list">
                                {if $formData && $formData['menu_data']}
                                {volist name="formData['menu_data']" id="vo"}
                                <li class="menu-list-view" data-id="{$i-1}" >
                                    <span class="layui-icon">{$vo.name}</span>
                                    <ul style="display:block;">
                                        {if count($vo.sub_button)>0}
                                        {volist name="vo.sub_button" id="v" key='k'}
                                        <li class="menu-list-sub" data-id="{$i-1}_{$k-1}">
                                            <span class="layui-icon">{$v.name}</span>
                                        </li>
                                        {/volist}
                                        {if count($vo.sub_button)<5}
                                        <li class="menu-list-sub wx-menu-add" data-id="{$i-1}_{:count($vo.sub_button)}">
                                            <span class="layui-icon layui-icon-close"></span>
                                        </li>
                                        {/if}
                                        {else /}
                                        <li class="menu-list-sub  wx-menu-add" data-id="{$i-1}_0">
                                            <span class="layui-icon layui-icon-close"></span>
                                        </li>
                                        {/if}
                                    </ul>
                                </li>
                                {/volist}
                                {if count($formData['menu_data'])<3}
                                <li class="menu-list-view wx-menu-add" data-id="0">
                                    <span class="layui-icon layui-icon-close"></span>
                                </li>
                                {/if}

                                {else /}
                                <li class="menu-list-view wx-menu-add" data-id="0">
                                    <span class="layui-icon layui-icon-close"></span>
                                </li>
                                {/if}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="right">
                    <p class="menu-title">
<!--                        <span>系统帮助</span>-->
                        <a href="javascript:;" class="delmenu">{:fy("Delete the submenu")}</a>
                    </p>
                    <div class="layui-form-item">
                        <p class="menu-title">{:fy('Menu title')}</p>
                        <div class="layui-input-block" style="margin-left: auto;">
                            <input type="text" name="menu_name" value="<?php echo isset($formData['menu_name'])?$formData['menu_name']:fy('Default menu') ?>"  lay-verify="required" placeholder="{:fy('Menu title')}" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <p class="menu-title">{:fy('Menu')} {:fy("Name")}</p>
                        <div class="layui-input-block" style="margin-left: auto;">
                            <input type="text" name="title"  lay-verify="required" placeholder="{:fy('Please enter')} {:fy('Menu')} {:fy('Name')}" autocomplete="off" class="layui-input">
                            <span class="layui-input-tips">{:fy('The number of words should not exceed 4 Chinese characters or 8 letters')}</span>
                        </div>
                    </div>
                    <form action="" class="layui-form">

                        <div class="layui-form-item media">
                            <p class="menu-title">{:fy('Menu events')}</p>
                            <div class="layui-input-block" style="margin-left: auto;">
                                <div class="layui-tab">
                                    <ul class="layui-tab-title">
                                        <li class="layui-this">
                                            <input type="radio" name="type" lay-filter="type" data-key="" value="click" title="{:fy('send message')}" checked>
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="" value="view" title="{:fy('Jump to a web page')}">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="" value="miniprogram" title="{:fy('Jump applet')}">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="rselfmenu_0_0" value="scancode_waitmsg" title="{:fy('Scanning code')}">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="rselfmenu_0_1" value="scancode_push" title="{:fy('Scanning code')}({:fy('Wait for information')}) ">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="rselfmenu_2_0" value="location_select" title="{:fy('geographical position')}">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="rselfmenu_1_0" value="pic_sysphoto" title="{:fy('Take pictures and send pictures')}">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="rselfmenu_1_1" value="pic_photo_or_album" title="{:fy('Photo album')}">
                                        </li>
                                        <li>
                                            <input type="radio" name="type"  lay-filter="type" data-key="rselfmenu_1_2" value="pic_weixin" title="{:fy('Album posting pictures')}">
                                        </li>
                                    </ul>
                                    <div class="layui-tab-content">
                                        <div class="layui-tab-item layui-show">
                                            <div style="padding:20px;">
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">{:fy('Message keywords')}</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="key"  lay-verify="" placeholder="{:fy('Please enter')} {:fy('Message keywords')}" autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-tab-item">
                                            <div style="padding:20px;">
                                                <p style="padding-left:15px;margin-bottom:10px;color:#8d8d8d;">{:fy('Subscribers clicking on this submenu will jump to the following link')}</p>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">{:fy('Link address')}</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="url"  lay-verify="" placeholder="{:fy('Please enter')} {:fy('Link address')}" autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-tab-item">
                                            <div style="padding:20px;">
                                                <p style="padding-left:15px;margin-bottom:10px;color:#8d8d8d;">{:fy('Subscribers clicking this submenu will jump to the Mini Program address')}</p>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">{:fy('Mini program')} APPID</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="appid"  placeholder="{:fy('Please enter')} APPID" autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                                <div class="layui-form-item">
                                                    <label class="layui-form-label">{:fy('Mini program')} {:fy('Address')}</label>
                                                    <div class="layui-input-block">
                                                        <input type="text" name="pagepath"  placeholder="{:fy('Please enter')} {:fy('Page')} {:fy('Address')}" autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-tab-item"></div>
                                        <div class="layui-tab-item"></div>
                                        <div class="layui-tab-item"></div>
                                        <div class="layui-tab-item"></div>
                                        <div class="layui-tab-item"></div>
                                        <div class="layui-tab-item"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="wx-menu-left"></span>
                        <span class="wx-menu-left"></span>
                    </form>
                </div>
            </div>
            <div style="text-align: center;">
                {:token_field()}
                <button type="button" class="layui-btn layui-form-btn" id="save">{:fy('Save')}</button>
                <button type="reset" class="layui-btn layui-btn-primary">{:fy('Reset')}</button>
            </div>
        </form>
    </div>
</div>
