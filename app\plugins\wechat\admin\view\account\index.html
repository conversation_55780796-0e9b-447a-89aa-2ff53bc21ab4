<div class="layuimini-container">
    <form id="app-form" class="layui-form layuimini-form">

     <!--   <div class="layui-form-item">
            <label class="layui-form-label">请求设置</label>
            <div class="layui-input-block">

                <input type="radio" name="log" value="1" title="开启" <?php if(isset($row['log']) && $row['log']==1)echo 'checked'; ?>>
                <input type="radio" name="log" value="0" title="关闭" <?php if(isset($row['log']) && $row['log']==0)echo 'checked'; ?>>
<tip>记录微信端请求本站的日志，建议在开发阶段开启，正式上线后关闭</tip>
            </div>
        </div>-->
        <blockquote class="layui-elem-quote" style="color:#FFB800; ">
            {:fy('Reference')} <a href="https://www.kancloud.cn/sh495812627/twothink_crm/3092933" target="_blank">{:fy('Configuration tutorials')}</a>
        </blockquote>
        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Official account')}{:fy('Name')}</label>
            <div class="layui-input-block">

                <input type="text" id="wxname" name="wxname" class="layui-input"
                       lay-verify="required" lay-reqtext="{:fy('Please enter')}{:fy('Official account')}{:fy('Name')}"
                       placeholder="{:fy('Please enter')}{:fy('Official account')}{:fy('Name')}" value="<?php if(isset($row['wxname']))echo $row['wxname']; ?>">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">AppId</label>
            <div class="layui-input-block">

                <input type="text" id="appid" name="appid" class="layui-input"
                       lay-verify="required" lay-reqtext="{:fy('Please enter')}{:fy('Official account')} AppId"
                       placeholder="{:fy('Please enter')}{:fy('Official account')} AppId" value="<?php if(isset($row['appid']))echo $row['appid']; ?>">
            </div>
        </div>



        <div class="layui-form-item">
            <label class="layui-form-label">AppSecret</label>
            <div class="layui-input-block">

                <input type="text" id="appsecret" name="appsecret" class="layui-input"
                       lay-verify="required" lay-reqtext="{:fy('Please enter')}{:fy('Official account')} AppSecret"
                       placeholder="{:fy('Please enter')}{:fy('Official account')} AppSecret" value="<?php if(isset($row['appsecret']))echo $row['appsecret']; ?>">
            </div>
        </div>
      <!--  <div class="layui-form-item">
            <label class="layui-form-label">Token</label>
            <div class="layui-input-block">

                <div class="layuimini-upload">
                    <input type="text" id="token" name="token" class="layui-input layui-col-xs6"
                           lay-verify="required" lay-reqtext="请输入微信公众号Token"
                           placeholder="请输入微信公众号Token" value="<?php if(isset($row['token']))echo $row['token']; ?>">
                    <div class="layuimini-upload-btn">
                        <span><a class="layui-btn random-char" data-length="32" data-target="#token">生成</a></span>
                    </div>
                </div>

            </div>
        </div>-->
<!--
        <div class="layui-form-item">
            <label class="layui-form-label">EncodingAESKey</label>
            <div class="layui-input-block">

                <div class="layuimini-upload">
                    <input type="text" id="aeskey" name="aeskey" class="layui-input layui-col-xs6"
                           lay-verify="required" lay-reqtext="请输入微信公众号EncodingAESKeyn"
                           placeholder="请输入微信公众号EncodingAESKey" value="<?php if(isset($row['aeskey']))echo $row['aeskey']; ?>">
                    <div class="layuimini-upload-btn">
                        <span><a class="layui-btn random-char" data-length="43" data-target="#aeskey">生成</a></span>
                    </div>
                </div>

            </div>
        </div>-->
     <!--   <div class="layui-form-item">
            <label class="layui-form-label">服务器地址</label>
            <div class="layui-input-block" style="line-height: 38px;">

            </div>
        </div>-->

        <div class="layui-form-item">
            <label class="layui-form-label">公众号关注地址</label>
            <div class="layui-input-block" style="line-height: 38px;">
                <input type="text" id="gzurl" name="gzurl" class="layui-input"
                       lay-verify="required" lay-reqtext="请填写公众号关注地址"
                       placeholder="请填写公众号关注地址" value="<?php if(isset($row['gzurl']))echo $row['gzurl']; ?>">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:fy('Access prompts')}</label>
            <div class="layui-input-block red" style="line-height: 38px;">
                <?php if(isset($row['token_msg']))echo $row['token_msg']; ?>
            </div>
        </div>

        <div class="hr-line"></div>
        <div class="layui-form-item text-center">
            <button type="submit" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit>{:fy('Save')}</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">{:fy('Reset')}</button>
        </div>

    </form>
</div>
