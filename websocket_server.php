<?php
/**
 * WebSocket服务器 - 用于CRM系统拨号和挂断功能
 * 基于PHP原生Socket实现
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

class WebSocketServer
{
    private $server;
    private $apiServer; // 内部API服务器
    private $clients = [];
    private $devices = []; // 存储设备连接信息
    private $host = '0.0.0.0';
    private $port = 8080;
    private $apiPort = 8081; // 内部API端口
    
    public function __construct($host = '0.0.0.0', $port = 8080, $apiPort = 8081)
    {
        $this->host = $host;
        $this->port = $port;
        $this->apiPort = $apiPort;

        // 初始化ThinkPHP环境
        $this->initThinkPHP();
    }
    
    /**
     * 初始化ThinkPHP环境
     */
    private function initThinkPHP()
    {
        // 定义应用目录
        define('APP_PATH', __DIR__ . '/app/');
        
        // 加载框架引导文件
        require __DIR__ . '/vendor/topthink/framework/src/helper.php';
        
        // 初始化应用
        $app = new \think\App();
        $app->initialize();
        
        // 设置数据库配置
        Config::set([
            'default' => 'mysql',
            'connections' => [
                'mysql' => [
                    'type' => 'mysql',
                    'hostname' => '127.0.0.1',
                    'database' => '127',
                    'username' => '127',
                    'password' => 'RWkCrGifLKDEfSbF',
                    'hostport' => '3306',
                    'charset' => 'utf8mb4',
                    'prefix' => 'ymwl_',
                ]
            ]
        ], 'database');
    }
    
    /**
     * 启动WebSocket服务器
     */
    public function start()
    {
        // 启动WebSocket服务器
        $this->server = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        socket_set_option($this->server, SOL_SOCKET, SO_REUSEADDR, 1);
        socket_bind($this->server, $this->host, $this->port);
        socket_listen($this->server, 5);

        // 启动内部API服务器
        $this->apiServer = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        socket_set_option($this->apiServer, SOL_SOCKET, SO_REUSEADDR, 1);
        socket_bind($this->apiServer, $this->host, $this->apiPort);
        socket_listen($this->apiServer, 5);

        echo "WebSocket服务器启动成功，监听 {$this->host}:{$this->port}\n";
        echo "内部API服务器启动成功，监听 {$this->host}:{$this->apiPort}\n";

        while (true) {
            $read = array_merge([$this->server, $this->apiServer], $this->clients);
            $write = null;
            $except = null;

            if (socket_select($read, $write, $except, 0, 10000) < 1) {
                continue;
            }

            // 处理WebSocket新连接
            if (in_array($this->server, $read)) {
                $client = socket_accept($this->server);
                $this->clients[] = $client;
                $this->handshake($client);
                echo "新WebSocket客户端连接\n";

                $key = array_search($this->server, $read);
                unset($read[$key]);
            }

            // 处理API新连接
            if (in_array($this->apiServer, $read)) {
                $apiClient = socket_accept($this->apiServer);
                $this->handleApiRequest($apiClient);
                echo "处理API请求\n";

                $key = array_search($this->apiServer, $read);
                unset($read[$key]);
            }

            // 处理客户端消息
            foreach ($read as $client) {
                $data = socket_read($client, 1024);
                if ($data === false || $data === '') {
                    $this->disconnect($client);
                    continue;
                }

                $message = $this->decode($data);
                if ($message) {
                    $this->handleMessage($client, $message);
                }
            }
        }
    }
    
    /**
     * WebSocket握手
     */
    private function handshake($client)
    {
        $request = socket_read($client, 1024);
        preg_match('#Sec-WebSocket-Key: (.*)\r\n#', $request, $matches);
        
        if (empty($matches[1])) {
            return false;
        }
        
        $key = $matches[1];
        $acceptKey = base64_encode(pack('H*', sha1($key . '258EAFA5-E914-47DA-95CA-C5AB0DC85B11')));
        
        $response = "HTTP/1.1 101 Switching Protocols\r\n";
        $response .= "Upgrade: websocket\r\n";
        $response .= "Connection: Upgrade\r\n";
        $response .= "Sec-WebSocket-Accept: $acceptKey\r\n\r\n";
        
        socket_write($client, $response, strlen($response));
        return true;
    }
    
    /**
     * 解码WebSocket消息
     */
    private function decode($data)
    {
        $length = ord($data[1]) & 127;
        
        if ($length == 126) {
            $masks = substr($data, 4, 4);
            $payload = substr($data, 8);
        } elseif ($length == 127) {
            $masks = substr($data, 10, 4);
            $payload = substr($data, 14);
        } else {
            $masks = substr($data, 2, 4);
            $payload = substr($data, 6);
        }
        
        $text = '';
        for ($i = 0; $i < strlen($payload); ++$i) {
            $text .= $payload[$i] ^ $masks[$i % 4];
        }
        
        return json_decode($text, true);
    }
    
    /**
     * 编码WebSocket消息
     */
    private function encode($message)
    {
        $data = json_encode($message, JSON_UNESCAPED_UNICODE);
        $length = strlen($data);
        
        if ($length < 126) {
            return pack('CC', 0x81, $length) . $data;
        } elseif ($length < 65536) {
            return pack('CCn', 0x81, 126, $length) . $data;
        } else {
            return pack('CCNN', 0x81, 127, 0, $length) . $data;
        }
    }
    
    /**
     * 处理API请求
     */
    private function handleApiRequest($apiClient)
    {
        $data = socket_read($apiClient, 1024);
        if (!$data) {
            if (is_resource($apiClient) && get_resource_type($apiClient) === 'Socket') {
                socket_close($apiClient);
            }
            return;
        }

        $message = json_decode($data, true);
        if (!$message || !isset($message['type'])) {
            socket_write($apiClient, json_encode(['success' => false, 'message' => '无效请求']));
            if (is_resource($apiClient) && get_resource_type($apiClient) === 'Socket') {
                socket_close($apiClient);
            }
            return;
        }

        $result = false;
        switch ($message['type']) {
            case 'dial':
                $result = $this->sendDialCommand(
                    $message['admin_id'],
                    $message['data']['phone'],
                    $message['data']['client_name'],
                    $message['data']['client_id'],
                    $message['data']['record_id']
                );
                break;
            case 'hangup':
                $result = $this->sendHangupCommand($message['admin_id']);
                break;
        }

        $response = ['success' => $result];
        socket_write($apiClient, json_encode($response));
        if (is_resource($apiClient) && get_resource_type($apiClient) === 'Socket') {
            socket_close($apiClient);
        }
    }

    /**
     * 处理客户端消息
     */
    private function handleMessage($client, $message)
    {
        if (!is_array($message) || !isset($message['type'])) {
            return;
        }

        switch ($message['type']) {
            case 'auth':
                $this->handleAuth($client, $message);
                break;
            case 'heartbeat':
                $this->handleHeartbeat($client, $message);
                break;
            default:
                echo "未知消息类型: " . $message['type'] . "\n";
        }
    }
    
    /**
     * 处理设备认证
     */
    private function handleAuth($client, $message)
    {
        $username = $message['username'] ?? '';
        $password = $message['password'] ?? '';
        $deviceId = $message['device_id'] ?? '';
        $deviceType = $message['device_type'] ?? 'android';
        
        // 验证用户名和密码
        $admin = Db::name('admin')
            ->where('username', $username)
            ->where('is_open', 1)
            ->find();

        if (!$admin) {
            $response = [
                'type' => 'auth_result',
                'success' => false,
                'message' => '用户名不存在或已被禁用'
            ];
            socket_write($client, $this->encode($response));
            return;
        }

        // 验证密码（支持MD5+盐值和原始密码两种方式）
        $isValidPassword = false;
        if (!empty($admin['salt'])) {
            // 使用MD5+盐值验证
            $hashedPassword = md5($password . $admin['salt']);
            $isValidPassword = ($hashedPassword === $admin['pwd']);
        } else {
            // 直接比较或使用password_verify
            $isValidPassword = ($password === $admin['pwd']) || password_verify($password, $admin['pwd']);
        }

        if (!$isValidPassword) {
            $response = [
                'type' => 'auth_result',
                'success' => false,
                'message' => '密码错误'
            ];
            socket_write($client, $this->encode($response));
            return;
        }
        
        // 检查是否已有设备在线（单设备登录限制）
        $existingDevice = Db::name('admin_device')
            ->where('admin_id', $admin['admin_id'])
            ->where('status', 1)
            ->find();

        if ($existingDevice) {
            // 断开现有设备连接
            $this->disconnectDevice($existingDevice['socket_id']);
        }

        // 生成socket_id
        $socketId = uniqid('socket_', true);

        // 检查是否已存在该设备记录
        $deviceRecord = Db::name('admin_device')
            ->where('admin_id', $admin['admin_id'])
            ->where('device_id', $deviceId)
            ->find();

        if ($deviceRecord) {
            // 更新现有设备记录
            Db::name('admin_device')
                ->where('id', $deviceRecord['id'])
                ->update([
                    'socket_id' => $socketId,
                    'status' => 1,
                    'last_active' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } else {
            // 创建新设备记录
            Db::name('admin_device')->insert([
                'admin_id' => $admin['admin_id'],
                'device_id' => $deviceId,
                'socket_id' => $socketId,
                'status' => 1,
                'last_active' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        // 存储客户端信息
        $this->devices[$socketId] = [
            'client' => $client,
            'admin_id' => $admin['admin_id'],
            'username' => $username,
            'device_id' => $deviceId,
            'last_heartbeat' => time()
        ];
        
        $response = [
            'type' => 'auth_result',
            'success' => true,
            'message' => '认证成功',
            'admin_id' => $admin['admin_id']
        ];
        
        socket_write($client, $this->encode($response));
        echo "用户 {$username} 认证成功，设备ID: {$deviceId}\n";
    }
    
    /**
     * 处理心跳包
     */
    private function handleHeartbeat($client, $message)
    {
        $socketId = $this->getSocketIdByClient($client);
        if ($socketId && isset($this->devices[$socketId])) {
            $this->devices[$socketId]['last_heartbeat'] = time();
            
            // 更新数据库中的最后活跃时间
            Db::name('admin_device')
                ->where('socket_id', $socketId)
                ->update(['last_active' => date('Y-m-d H:i:s')]);
            
            $response = [
                'type' => 'heartbeat_response',
                'timestamp' => $message['timestamp'] ?? time()
            ];
            
            socket_write($client, $this->encode($response));
        }
    }
    
    /**
     * 发送拨号指令
     */
    public function sendDialCommand($adminId, $phone, $clientName, $clientId, $recordId)
    {
        $device = $this->getDeviceByAdminId($adminId);
        if (!$device) {
            return false;
        }
        
        $message = [
            'type' => 'dial',
            'action' => 'make_call',
            'data' => [
                'phone' => $phone,
                'client_name' => $clientName,
                'client_id' => $clientId,
                'record_id' => $recordId
            ],
            'timestamp' => time()
        ];
        
        socket_write($device['client'], $this->encode($message));
        echo "发送拨号指令到用户 {$device['username']}: {$phone}\n";
        return true;
    }
    
    /**
     * 发送挂断指令
     */
    public function sendHangupCommand($adminId, $recordId = null)
    {
        $device = $this->getDeviceByAdminId($adminId);
        if (!$device) {
            return false;
        }
        
        $message = [
            'type' => 'hangup',
            'action' => 'hangup_call',
            'data' => [
                'record_id' => $recordId
            ],
            'timestamp' => time()
        ];
        
        socket_write($device['client'], $this->encode($message));
        echo "发送挂断指令到用户 {$device['username']}\n";
        return true;
    }
    
    /**
     * 根据管理员ID获取设备信息
     */
    private function getDeviceByAdminId($adminId)
    {
        foreach ($this->devices as $socketId => $device) {
            if ($device['admin_id'] == $adminId) {
                return $device;
            }
        }
        return null;
    }
    
    /**
     * 根据客户端获取socket_id
     */
    private function getSocketIdByClient($client)
    {
        foreach ($this->devices as $socketId => $device) {
            if ($device['client'] === $client) {
                return $socketId;
            }
        }
        return null;
    }
    
    /**
     * 断开设备连接
     */
    private function disconnectDevice($socketId)
    {
        if (isset($this->devices[$socketId])) {
            $client = $this->devices[$socketId]['client'];
            // 检查socket是否仍然有效
            if (is_resource($client) && get_resource_type($client) === 'Socket') {
                socket_close($client);
            }
            unset($this->devices[$socketId]);
        }
        
        // 更新数据库状态
        Db::name('admin_device')
            ->where('socket_id', $socketId)
            ->update(['status' => 0]);
    }
    
    /**
     * 断开客户端连接
     */
    private function disconnect($client)
    {
        $socketId = $this->getSocketIdByClient($client);
        if ($socketId) {
            $this->disconnectDevice($socketId);
        }
        
        $key = array_search($client, $this->clients);
        if ($key !== false) {
            unset($this->clients[$key]);
        }
        
        // 检查socket是否仍然有效再关闭
        if (is_resource($client) && get_resource_type($client) === 'Socket') {
            socket_close($client);
        }
        echo "客户端断开连接\n";
    }
}

// 启动WebSocket服务器
if (php_sapi_name() === 'cli') {
    $server = new WebSocketServer('0.0.0.0', 8080);
    $server->start();
}
