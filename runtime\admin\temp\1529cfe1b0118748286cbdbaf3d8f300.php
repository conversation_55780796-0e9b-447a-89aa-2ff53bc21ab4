<?php /*a:0:{}*/ ?>
<div class="layui-form-item"><label class="layui-form-label"><?php echo fy('授权秘钥'); ?></label><div class="layui-input-block"><input type="text" id="license_key" name="license_key" lay-filter="license_key" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('授权秘钥'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('授权秘钥'); ?>" value="<?php echo htmlentities((isset($row['license_key']) && ($row['license_key'] !== '')?$row['license_key']:'')); ?>"><tip>填写程序授权秘钥</tip></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('后台固定页'); ?></label><div class="layui-input-block"><input type="text" id="fixedpage" name="fixedpage" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('后台固定页'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('后台固定页'); ?>" value="<?php echo htmlentities((isset($row['fixedpage']) && ($row['fixedpage'] !== '')?$row['fixedpage']:'')); ?>"><tip><?php echo fy("后台首页打开的第一个页面"); ?></tip></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('网站名称'); ?></label><div class="layui-input-block"><input type="text" id="name" name="name" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('网站名称'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('网站名称'); ?>" value="<?php echo htmlentities((isset($row['name']) && ($row['name'] !== '')?$row['name']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('网站简称'); ?></label><div class="layui-input-block"><input type="text" id="site_abbr" name="site_abbr" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('网站简称'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('网站简称'); ?>" value="<?php echo htmlentities((isset($row['site_abbr']) && ($row['site_abbr'] !== '')?$row['site_abbr']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('资源地址'); ?></label><div class="layui-input-block"><input type="text" id="domain" name="domain" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('资源地址'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('资源地址'); ?>" value="<?php echo htmlentities((isset($row['domain']) && ($row['domain'] !== '')?$row['domain']:'')); ?>"><tip><?php echo fy("必须填写正确，否则无法加载图片资源"); ?></tip></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('Copyright'); ?></label><div class="layui-input-block"><input type="text" id="copyright" name="copyright" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('Copyright'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('Copyright'); ?>" value="<?php echo htmlentities((isset($row['copyright']) && ($row['copyright'] !== '')?$row['copyright']:'')); ?>"></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('分页条数'); ?></label><div class="layui-input-block"><input type="number" id="admin_pagesize" name="admin_pagesize" class="layui-input" lay-reqtext="<?php echo fy("Please enter"); ?><?php echo fy('分页条数'); ?>" placeholder="<?php echo fy("Please enter"); ?><?php echo fy('分页条数'); ?>" value="<?php echo htmlentities((isset($row['admin_pagesize']) && ($row['admin_pagesize'] !== '')?$row['admin_pagesize']:'')); ?>"><tip><?php echo fy('数据分页条数'); ?></tip></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('验证码'); ?></label><div class="layui-input-block"><input type="radio" name="code" value="0" title="<?php echo fy('关闭'); ?>" <?php if("0"==$row['code']): ?>checked<?php endif; ?>><input type="radio" name="code" value="1" title="<?php echo fy('开启'); ?>" <?php if("1"==$row['code']): ?>checked<?php endif; ?>></div></div><div class="layui-form-item"><label class="layui-form-label"><?php echo fy('公告'); ?></label><div class="layui-input-block"><textarea  name="notice"  id="notice" class="layui-textarea editor" data-toolbars='[["source","undo", "redo", "|", "bold", "italic", "underline", "fontborder", "strikethrough", "superscript", "subscript", "removeformat", "formatmatch", "autotypeset", "blockquote",  "|", "forecolor", "backcolor", "selectall", "cleardoc", "|", "lineheight", "|",   "fontfamily", "fontsize", "|", "link", "unlink", "emotion"]]' placeholder="<?php echo fy("Please enter"); ?><?php echo fy('公告'); ?>"><?php echo $row['notice']; ?></textarea></div></div>