<?php

namespace app\plugins\wechat\library;
use think\exception;
/**
 * 微信配置类
 */
class Config
{

    public static function load()
    {
        $value=\think\facade\Db::name('weixin')->where('name','=','account')->value('value');
        if($value){
            $data=json_decode($value,true);
            return [
                'appid'=>$data['appid'],
                'appsecret'=>$data['appsecret'],
//            'token'          => $data['token'],
//            'encodingaeskey' => $data['aeskey'],
                // 配置商户支付参数（可选，在使用支付功能时需要）
//            'mch_id'         => "**********",
//            'mch_key'        => 'IKI4kpHjU94ji3oqre5zYaQMwLHuZPmj',
                // 配置商户支付双向证书目录（可选，在使用退款|打款|红包时需要）
                'ssl_key'        => '',
                'ssl_cer'        => '',
                // 缓存目录配置（可选，需拥有读写权限）
                'cache_path'     => '',

            ];
        }else{
            throw new  exception('公众号未配置');
        }
    }

//    读取企业微信配置
    public static function qyload()
    {
        $value=\think\facade\Db::name('weixin')->where('name','=','qywx_account')->value('value');
        if($value){
            $data=json_decode($value,true);
            return $data;
        }else{
            throw new  exception('企业微信未配置');
        }

    }

}
