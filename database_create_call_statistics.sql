-- 创建通话统计数据表
CREATE TABLE `ymwl_crm_call_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `call_count` int(11) NOT NULL DEFAULT '0' COMMENT '通话量',
  `avg_duration` int(11) NOT NULL DEFAULT '0' COMMENT '平均时长(秒)',
  `total_duration` int(11) NOT NULL DEFAULT '0' COMMENT '总通话时长(秒)',
  `connect_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '接通率(%)',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_admin_date` (`admin_id`,`stat_date`),
  KEY `idx_username` (`username`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CRM通话统计表';

-- 插入测试数据
INSERT INTO `ymwl_crm_call_statistics` (`admin_id`, `username`, `stat_date`, `call_count`, `avg_duration`, `total_duration`, `connect_rate`, `create_time`, `update_time`) VALUES
(1, 'admin', '2024-01-15', 25, 180, 4500, 85.50, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'admin', '2024-01-16', 30, 165, 4950, 88.20, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'admin', '2024-01-17', 22, 195, 4290, 82.30, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
