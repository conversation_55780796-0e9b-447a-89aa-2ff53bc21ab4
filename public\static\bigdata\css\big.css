html, head, meta, title, link, body, div, span, a, ul, li, ol, li, p, i, img, input, select, option, table, thead, tbody, tr, th, td, dl, dd, h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
}
ul li, ol li {
    list-style: none;
}
body {
    font-family: "Microsoft YaHei";
}
input {
    outline: none;
}
a {
    text-decoration: none;
}
img {
    border: none;
}
.pabsolute {
    position: absolute;
}
.prelative {
    position: relative;
}
.pstatic {
    position: static;
}
.pfixed {
    position: fixed;
}

img {
    border-style:none;
}
.clear {
    clear: both;
}
.logos .mesI2 {
    margin: 0px 0 0 15px;
    position:  absolute;
    top: 23px;
    width: 76px;
}

body, html {
    height: 100%;
    overflow: hidden;
}
.todayTimeBox {
    width: 100%;
    height: 60px;
    line-height: 60px;
    color: #fff;
    font-size: 20px;
    position: absolute;top:  70px;left: 0;
}
.todayTimeBox .todayTime {
    float: right;
    margin-right: 60px;
}
.mainbody {
    width: 98%;
    margin: 0 auto;
    display: flex;
    padding-top: 0;
    padding-bottom: 37px;
}
.mainbody .leftContent, .mainbody .rightContent {width: 100%;}
.mainbody .centerContent {
    width: 652px;
}
.leftContent .serviceItem {
    width: 100%;
    height: 340px;
    background-image: url(../images/k_2all.png);
    margin-top: 20px;
    background-size: 100%;
    background-repeat:  no-repeat;
}
.div_any_child{
    width: 100%;
    height: 330px;
    box-shadow: -10px 0px 15px #034c6a inset,
    0px -10px 15px #034c6a inset,
    10px 0px 15px #034c6a inset,
    0px 10px 15px #034c6a inset;
    border: 1px solid #034c6a;
    box-sizing: border-box;
    position: relative;
    margin-top: 25px;
}
.serviceForm {
    margin-top: 30px;
    width: 100%;
    box-shadow: -10px 0px 15px #034c6a inset,
    0px -10px 15px #034c6a inset,
    10px 0px 15px #034c6a inset,
    0px 10px 15px #034c6a inset;
    border: 1px solid #034c6a;
    box-sizing: border-box;

}
.seHeader {
    padding-top: 18px;
    overflow: hidden;
}
.seHeader span:first-child {
    float: left;
    margin-left: 29px;
    margin-top: 8px;
}
.seHeader span:first-child .p-1 {
    font-size: 20px;
    color: #fff;
}
.seHeader span:first-child .p-2 {
    font-size: 14px;
    color: #66dffb;
}
.seHeader span:last-child {
    float: right;
    font-size: 46px;
    color: #7bb9ff;
    margin-right: 28px;
}

.statusList,.topss {
    width: 96%;
    margin: 0 auto;
    margin-top: 5px;
}
.statusList{
    margin-bottom:12px;
}

.statusList .seTable {
    width: 100%;
    padding-bottom: 0px;
    overflow:  hidden;
    display:  flex;
}
.statusList .seTable li {
    float: left;

    font-size: 18px;
    color: #66dffb;
    background: #093e79;
    padding: 10px 0;
    text-align:  center;
    border-left: 1px solid #034c6a;
    border-right: 1px solid #034c6a;
}
.statusList .seTable .outlineBorder {
    font-size: 14px;
}
.statusList .outlineBorder ul {
    height: 40px;
}

.statusList .outlineBorder ul:nth-child(even) {
    color: #8ec0ff;
}
.statusList .outlineBorder ul:nth-child(odd) {
    background-color: #072951;
    box-shadow: -10px 0px 15px #034c6a inset,
    10px 0px 15px #034c6a inset;
}
.statusList .outlineBorder ul li {
    float: left;
    width: 20%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #fff;
    border-left: 1px solid #034c6a;
    border-right: 1px solid #034c6a;
    box-sizing: border-box;
}


/* 无缝滚动  */
@-webkit-keyframes scrollText1 {
    0%{
        -webkit-transform: translateY(0px);
    }
    20%{
        -webkit-transform: translateY(-40px);
    }
    40%{
        -webkit-transform: translateY(-80px);
    }
    60%{
        -webkit-transform: translateY(-120px);
    }
    80%{
        -webkit-transform: translateY(-160px);
    }
    100%{
        -webkit-transform: translateY(-200px);
    }
}

@keyframes scrollText1 {
    0%{
        transform: translateY(0px);
    }
    20%{
        transform: translateY(-40px);
    }
    40%{
        transform: translateY(-80px);
    }
    60%{
        transform: translateY(-120px);
    }
    80%{
        transform: translateY(-160px);
    }
    100%{
        transform: translateY(-200px);
    }
}
.outlineBorder {
    position: relative;
    width: 100%;
    overflow: hidden;
}
.rolling {
    top: 0px;
    -webkit-animation:scrollText1 10s infinite  cubic-bezier(1, 0, 0.5, 0);
    animation:scrollText1 10s infinite  cubic-bezier(1, 0, 0.5, 0);
}
.outlineBorder:hover  .rolling {
    animation-play-state:paused;
    -webkit-animation-play-state:paused;
}

/* 地图 */
.implantation {
    width: 100%;
    height: 100%;
    position:  relative;
    z-index: 500;
}

/* 呼吸服务 */
.breathe {
    width: 100%;
    height: 242px;
    background: url(../img/k_2.png);
    margin-top: 20px;
}
.serData {
    margin-top: 7px;
    overflow: hidden;
    color: #66dffb;
}
.serData > div {
    float: left;
    font-size: 18px;
    text-align:  center;
}
.serData > .serDataLeft {
    margin-left: 15%;
    padding: 0 12px;
}
.serDataLeft .serNum {
    font-size: 60px;
    color: #f0bd54;
}
.phoneCall {
    width: 100%;
    height: 650px;
    background-image: url(../img/k_4.png);
    background-size:  100%;
    background-repeat: no-repeat;
    margin-top: 34px;
}
.phoneNum {
    width: 90%;
    height: 250px;
    margin: 0 auto;
}
/* 折线图  */
.brokenLine {
    width: 100%;
    height: 300px;
}
#mainbody {
    transform-origin: 0 0;
}
.centerContent {
    position: relative;
}
.centerContent  .sumDot {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.centerContent .sumDot > span {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    z-index: 600;
}
.redClass {
    background: red;
}
.greenClass {
    /*background: green;*/
}
/* 水波扩散效果 */
@keyframes warn {
    0% {
        transform: scale(0);
        opacity: 0.0;
    }
    25% {
        transform: scale(0);
        opacity: 0.1;
    }
    50% {
        transform: scale(0.1);
        opacity: 0.3;
    }
    75% {
        transform: scale(0.5);
        opacity: 0.5;
    }
    100% {
        transform: scale(1);
        opacity: 0.0;
    }
}
@-webkit-keyframes "warn" {
    0% {
        -webkit-transform: scale(0);
        opacity: 0.0;
    }
    25% {
        -webkit-transform: scale(0);
        opacity: 0.1;
    }
    50% {
        -webkit-transform: scale(0.1);
        opacity: 0.3;
    }
    75% {
        -webkit-transform: scale(0.5);
        opacity: 0.5;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 0.0;
    }
}
.sumDot span .container {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 10px;
    height: 10px;
}
.dot {
    position: absolute;
    width: 10px;
    height: 10px;
    top: -2px;
    left: -2px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    /*border: 2px solid red;*/
    border-radius: 20px;
    z-index: 2;
}
/* 产生动画（向外扩散变大）的圆圈  */
.pulse {
    position: absolute;
    width: 50px;
    height: 50px;
    left: -50px;
    top: -50px;
    /*border: 30px solid red;*/
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    border-radius: 50%;
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}
/* sos弹窗 */
.sosInfor {
    position: absolute;
    top: 20px;
    left: 30px;
    width: 230px;
    height: 188px;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    box-shadow: 0 0 10px #fff;
    border-radius: 5px;
}
.sosInfor .sosTitle {
    font-size: 16px;
    color: #ece14f;
    padding: 6px 10px;
}
.sosInfor > ul {
    font-size: 14px;
    color: #fff;
    margin-top: 9px;
    padding: 0px 11px;
}
.sosInfor > ul li {
    margin-bottom: 5px;
}
.borColor-1 {
    border: 2px solid red;
}
.borColor-2 {
    border: 2px solid green;
}
.borColor-3 {
    border: 30px solid red;
}
.borColor-4 {
    border: 30px solid green;
}
.close {
    position: absolute;
    top: -10px;
    right: -10px;
    cursor: pointer;
    width: 20px;
}
.tips {
    position: absolute;
    min-width: 138px;
    background: #000;
    color: #ece14f;
    top: -40px;
    left: -70px;
    padding: 5px 10px;
    border-radius: 5px;
    display: none;
}
.header_center{
    width: 30%;
    margin: 0px auto;
    color: #FFFFff;
    text-align: center;
    height: 80px;
    background-image: url("../images/logoBg.png");
    background-size: 100% 100%;
    font-family: "微软雅黑"!important;


}
.header_center h2{
    margin-top: 16px !important;
    font-size: 28px !important;
    padding-top: 6px;

}
