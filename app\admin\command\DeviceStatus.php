<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\admin\model\AdminDevice;

class DeviceStatus extends Command
{
    protected function configure()
    {
        $this->setName('device:status')
            ->setDescription('设备状态检查和清理命令 - 每30秒运行一次');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始设备状态检查...');
        
        // 获取当前时间
        $currentTime = time();
        $expiredTime = date('Y-m-d H:i:s', $currentTime - 300); // 5分钟前
        
        try {
            // 查找所有过期的在线设备
            $expiredDevices = Db::name('admin_device')
                ->where('status', 1)
                ->where('last_active', '<', $expiredTime)
                ->select();
            
            if (!empty($expiredDevices)) {
                // 批量更新过期设备状态为离线
                $affectedRows = Db::name('admin_device')
                    ->where('status', 1)
                    ->where('last_active', '<', $expiredTime)
                    ->update([
                        'status' => 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                
                $output->writeln("发现 {$affectedRows} 个过期设备，已设置为离线状态");
                
                // 输出详细信息
                foreach ($expiredDevices as $device) {
                    $output->writeln("设备ID: {$device['device_id']}, 用户ID: {$device['admin_id']}, 最后活跃: {$device['last_active']}");
                }
            } else {
                $output->writeln('没有发现过期的在线设备');
            }
            
            // 统计当前设备状态
            $totalDevices = Db::name('admin_device')->count();
            $onlineDevices = Db::name('admin_device')
                ->where('status', 1)
                ->where('last_active', '>=', $expiredTime)
                ->count();
            $offlineDevices = $totalDevices - $onlineDevices;
            
            $output->writeln("设备状态统计: 总计 {$totalDevices} 个设备, 在线 {$onlineDevices} 个, 离线 {$offlineDevices} 个");
            
        } catch (\Exception $e) {
            $output->writeln('设备状态检查失败: ' . $e->getMessage());
            return 1;
        }
        
        $output->writeln('设备状态检查完成');
        return 0;
    }
    
    /**
     * 启动循环检查模式
     */
    public function startLoop()
    {
        echo "启动设备状态循环检查 (每10秒检查一次)...\n";
        echo "按 Ctrl+C 停止\n\n";
        
        while (true) {
            $this->checkDeviceStatus();
            sleep(10); // 等待10秒
        }
    }
    
    /**
     * 执行设备状态检查
     */
    private function checkDeviceStatus()
    {
        $currentTime = time();
        $expiredTime = date('Y-m-d H:i:s', $currentTime - 300); // 5分钟前
        
        try {
            // 查找所有过期的在线设备
            $expiredDevices = Db::name('admin_device')
                ->where('status', 1)
                ->where('last_active', '<', $expiredTime)
                ->select();
            
            if (!empty($expiredDevices)) {
                // 批量更新过期设备状态为离线
                $affectedRows = Db::name('admin_device')
                    ->where('status', 1)
                    ->where('last_active', '<', $expiredTime)
                    ->update([
                        'status' => 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                
                echo "[" . date('Y-m-d H:i:s') . "] 发现 {$affectedRows} 个过期设备，已设置为离线状态\n";
                
                // 输出详细信息
                foreach ($expiredDevices as $device) {
                    echo "  - 设备ID: {$device['device_id']}, 用户ID: {$device['admin_id']}, 最后活跃: {$device['last_active']}\n";
                }
            } else {
                echo "[" . date('Y-m-d H:i:s') . "] 没有发现过期的在线设备\n";
            }
            
        } catch (\Exception $e) {
            echo "[" . date('Y-m-d H:i:s') . "] 设备状态检查失败: " . $e->getMessage() . "\n";
        }
    }
}