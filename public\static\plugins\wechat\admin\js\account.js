define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'crm.status/index',
        add_url: 'crm.status/add',
        edit_url: 'crm.status/edit',
        delete_url: 'crm.status/delete',
        export_url: 'crm.status/export',
        modify_url: 'crm.status/modify',
    };

    var Controller = {

        index: function () {
            $(document).on('click','.random-char',function (){
                  $($(this).attr('data-target')).val(ea.randomChar($(this).attr('data-length')));

            });

            ea.listen();
        },
        add: function () {
            ea.listen();
        },
        edit: function () {
            ea.listen();
        },
    };
    return Controller;
});