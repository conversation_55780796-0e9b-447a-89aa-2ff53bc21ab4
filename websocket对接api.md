连接数据库，读取验证账号密码
所有WebSocket消息均采用JSON格式：

### 1. 设备认证

**客户端发送：**
```json
{
    "type": "auth",
    "username": "admin",
    "password": "123456",
    "device_id": "设备唯一标识",
    "device_type": "android"
}
```

**服务器响应：**
```json
{
    "type": "auth_result",
    "success": true,
    "message": "认证成功",
    "admin_id": "用户ID"
}
错误响应
{
    "type": "auth_result",
    "success": false,
    "message": "用户名或密码错误"
}
```

### 2. 心跳包

**客户端发送：**
```json
{
    "type": "heartbeat",
    "timestamp": 1642234567
}
```

**服务器响应：**
```json
{
    "type": "heartbeat_response",
    "timestamp": 1642234567
}
```

### 3. 拨号指令
**服务器推送：**
```json
{
    "type": "dial",
    "action": "make_call",
    "data": {
        "phone": "13800138000",
        "client_name": "张三",
        "client_id": 123,
        "record_id": 456
    },
    "timestamp": 1642234567
}
```

### 3.1 挂断电话指令
**服务器推送：**
```json
{
    "type": "hangup",
    "action": "hangup_call",
    "data": {
        "record_id": "52"
    },
    "timestamp": 1749381891
}

```
