<?php /*a:2:{s:60:"C:\wwwroot\127.0.0.1\app\admin\view\business\record\add.html";i:1680830508;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<div class="layuimini-main layui-row layui-col-space15">

    <div class="layui-col-xs12 layui-col-sm6 layui-col-md6 layui-col-lg6">
        <div class="layui-card layui-panel">
            <div class="layui-card-header" >
                <?php echo fy('Basic information'); ?>
                    <a class="layui-btn layui-btn-xs layui-btn-success" data-open="business/edit?id=<?php echo htmlentities($business_id); ?>" data-title="<?php echo fy('Edit'); ?>" data-full="true" style="float: right;margin-top: 10px;"><?php echo fy('Modify'); ?></a>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
<?php
$businessInfo=think\facade\Db::name('business')->where('id','=',$business_id)->find();
?>
                <div class="layui-form-item">
                    <label class="layui-form-label"><?php echo fy('Opportunity Name'); ?></label>
                    <div class="layui-input-block">
                        <input type="text" name="business[name]" class="layui-input" readonly value="<?php echo htmlentities((isset($businessInfo['name']) && ($businessInfo['name'] !== '')?$businessInfo['name']:'')); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"> <?php echo fy('Budget amount'); ?></label>
                    <div class="layui-input-block">
                        <input type="text" name="business[money]" class="layui-input" readonly  value="<?php echo htmlentities((isset($businessInfo['money']) && ($businessInfo['money'] !== '')?$businessInfo['money']:'')); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><?php echo fy('Estimated transaction date'); ?></label>
                    <div class="layui-input-block">
                        <input type="text" name="business[deal_time]" class="layui-input" readonly  value="<?php if($businessInfo['deal_time']){echo date('Y-m-d H:i',$businessInfo['deal_time']);}?>">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label"><?php echo fy('Remark'); ?></label>
                    <div class="layui-input-block">
                        <textarea  class="layui-textarea" readonly  style="min-height:50px;"><?php echo (isset($businessInfo['remark']) && ($businessInfo['remark'] !== '')?$businessInfo['remark']:''); ?></textarea>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="layui-col-xs12 layui-col-sm6 layui-col-md6 layui-col-lg6">
        <div class="layui-card layui-panel">
            <div class="layui-card-header" >
                <?php echo fy('Follow up actions'); ?>
            </div>
            <div class="layui-card-body layuiadmin-card-list layui-form">
                <div class="layui-form-item layui-form-text">

                    <div class="layui-input-block" style="margin-left:0;">
                        <textarea name="content" id="content"  required lay-verify="required" placeholder="<?php echo fy('Follow up frequently and sign more'); ?>"  class="layui-textarea fly-editor" style="height: 150px;"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><?php echo fy('Attachment'); ?></label>
                    <div class="layui-input-block layuimini-upload">
                        <input name="attachs" class="layui-input layui-col-xs6" placeholder="<?php echo fy('Please upload'); ?> <?php echo fy('Attachment'); ?>" value="">
                        <div class="layuimini-upload-btn">
                            <span><a class="layui-btn" data-upload="attachs" data-upload-number="5" ><i class="fa fa-upload"></i> <?php echo fy('Upload'); ?></a></span>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label"> <?php echo fy('Follow up type'); ?>:</label>
                    <div class="layui-input-inline">
                        <select name="record_type" required lay-verify="required" lay-reqtext="<?php echo fy('Please select'); ?> <?php echo fy('Follow up type'); ?>">
                            <option value=""><?php echo fy('Please select'); ?> <?php echo fy('Follow up type'); ?></option>
                            <?php
$typeList=think\facade\Db::name('crm_record_type')->field('`id`,`name`')->where('status','=',1)->order('sort ASC,id DESC')->select();
                            foreach($typeList as $v){
                            ?>
                            <option value="<?php echo htmlentities($v['name']); ?>"><?php echo htmlentities($v['name']); ?></option>
                            <?php } ?>


                        </select>


                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label"> <?php echo fy('Next follow-up time'); ?>:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" autocomplete="off" name="next_time"  placeholder="<?php echo fy('Please enter'); ?><?php echo fy('Next follow-up time'); ?>" data-date="yyyy-MM-dd HH:mm" value="<?php echo date('Y-m-d H:i',strtotime('+1 day')); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <input type="hidden" name="business_id" value="<?php echo htmlentities($business_id); ?>">
                    <button class="layui-btn" lay-filter="btn_comment" lay-submit="<?php echo url('add'); ?>"><?php echo fy('Save'); ?></button>
                </div>
            </div>
        </div>
    </div>


<div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
    <div class="layui-tab" lay-filter="tabDemo">
        <ul class="layui-tab-title">
            <li class="layui-this" lay-id="1"><?php echo fy('Follow up record'); ?></li>
            <li lay-id="2"><?php echo fy('Client Information'); ?></li>
            <li lay-id="3"><?php echo fy('Intent Product'); ?></li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <div class="layuimini-container">
                    <div class="layuimini-main">
                        <table id="currentTable" class="layui-table layui-hide"
                               data-auth-add="<?php echo auth('business.record/add'); ?>"
                               data-auth-delete="<?php echo auth('business.record/delete'); ?>"
                               lay-filter="currentTable">
                        </table>
                    </div>
                </div>

            </div>
            <div class="layui-tab-item layui-row form-readonly">

                <?php echo $fields_str; ?>
            </div>
            <div class="layui-tab-item" id="app">

                <table class="layui-table" id="table-pro" data-business_id="<?php echo htmlentities($business_id); ?>">
                    <thead>
                    <tr>
                        <th>NO.</th>
                        <th><?php echo fy('Product name'); ?></th>
                        <th><?php echo fy('Specifications'); ?></th>
                        <th><?php echo fy('Model'); ?></th>
                        <th><?php echo fy('Cost price'); ?></th>
                        <th><?php echo fy('Sale price'); ?></th>
                        <th style="width: 90px;"><?php echo fy('Quantity'); ?></th>
                        <th style="width: 90px;"><?php echo fy('Discount'); ?></th>
                        <th><?php echo fy('Sales amount'); ?></th>
                        <th><?php echo fy('Remark'); ?></th>
                        <th><?php echo fy('Entry time'); ?></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(item,index) in pro_list" :key="index+1">
                        <td>
                            {{index+1}}
                            <input type="hidden" :value="item.product_id" :name="'product['+(index+1)+'][product_id]'">
                            <input type="hidden" :value="item.id" :name="'product['+(index+1)+'][id]'">

                        </td>
                        <td><input type="hidden" :value="item.name" :name="'product['+(index+1)+'][product_extend][name]'">{{item.name}}</td>
                        <td><input type="hidden" :value="item.specification" :name="'product['+(index+1)+'][product_extend][specification]'">{{item.specification}}</td>
                        <td><input type="hidden" :value="item.model" :name="'product['+(index+1)+'][product_extend][model]'">{{item.model}}</td>
                        <td><input type="hidden" :value="item.cost_price" :name="'product['+(index+1)+'][product_extend][cost_price]'">{{item.cost_price}}</td>
                        <td><input class="layui-input" :name="'product['+(index+1)+'][sale_price]'"  type="text" v-model="item.sale_price" readonly></td>
                        <td><input class="layui-input" :name="'product['+(index+1)+'][nums]'"  type="text" v-model="item.nums" readonly></td>
                        <td><input class="layui-input" :name="'product['+(index+1)+'][discount]'"  type="text" v-model="item.discount" readonly></td>
                        <td>{{item.sale_price*item.nums-item.discount}}</td>
                        <td><input  class="layui-input" :name="'product['+(index+1)+'][remark]'" type="text" v-model="item.remark" readonly></td>
                        <td>{{entryTime(index)}}</td>
                    </tr>
                    </tbody>
                </table>
                <div class="total-pro" style="text-align: center">
                    <?php echo fy('Total cost'); ?>：<span class="red">{{getTotal.cost_sum}}</span>  <?php echo fy('Total price'); ?>：<span class="red">{{getTotal.sale_sum}}</span>  <?php echo fy('Total quantity'); ?>：<span class="red">{{getTotal.nums_sum}}</span>  <?php echo fy('Total discount'); ?>：<span class="red">{{getTotal.discount_sum}}</span>  <?php echo fy('Final total amount'); ?>：<span class="red">{{getTotal.real_sale_sum}}</span>
                </div>
            </div>

        </div>
    </div>
</div>

</div>
</body>
</html>