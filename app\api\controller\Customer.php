<?php

namespace app\api\controller;

use think\facade\Db;
use app\api\model\CrmCustomer;

/**
 * 客户管理控制器
 */
class Customer extends Common
{
    protected $model;
    
    public function initialize()
    {
        parent::initialize();
        $this->model = new CrmCustomer();
    }
    
    /**
     * 获取客户列表
     * GET /api/customer/list
     */
    public function list()
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        list($page, $limit) = $this->getPageParams();
        
        // 获取筛选参数
        $followStatus = $this->request->param('follow_status', ''); // 跟进状态：never(从未跟进), today(今日已跟进), pending(待跟进)
        $startDate = $this->request->param('start_date', '');
        $endDate = $this->request->param('end_date', '');
        $keyword = $this->request->param('keyword', ''); // 搜索关键词
        $status = $this->request->param('status', ''); // 客户状态：0线索,1客户,2公海
        
        // 构建查询条件
        $where = [];
        
        // 只查询当前用户负责的客户
        $where[] = ['pr_user', '=', $this->admin['username']];
        
        // 客户状态筛选
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        
        // 关键词搜索
        if ($keyword) {
            $where[] = ['name|phone|company', 'like', '%' . $keyword . '%'];
        }
        
        // 构建跟进状态查询
        $query = $this->model->where($where);
        
        // 根据跟进状态筛选
        if ($followStatus) {
            $this->buildFollowStatusWhere($query, $followStatus, $startDate, $endDate);
        }
        
        // 获取总数
        $total = $query->count();
        
        // 获取列表数据
        $list = $query->field([
                'id', 'name', 'phone', 'company', 'status', 'pr_user',
                'at_time', 'to_kh_time', 'kh_rank', 'source_id', 'hangye_id',
                'address', 'remark', 'last_record_time', 'next_record_time'
            ])
            ->order('id', 'desc')
            ->page($page, $limit)
            ->select();
            
        // 处理列表数据
        $list = $this->processCustomerList($list);
        
        $data = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
        
        $this->jsonSuccess('获取成功', $data);
    }
    
    /**
     * 获取客户详情
     * GET /api/customer/detail/{id}
     */
    public function detail($id)
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        $customer = $this->model
            ->where('id', $id)
            ->where('pr_user', $this->admin['username'])
            ->find();
            
        if (!$customer) {
            $this->jsonError('客户不存在或无权限访问');
        }
        
        // 获取跟进记录
        $records = Db::name('crm_record')
            ->where('customer_id', $id)
            ->order('create_time', 'desc')
            ->limit(10)
            ->select();
            
        // 获取合同信息
        $contracts = Db::name('crm_contract')
            ->where('customer_id', $id)
            ->order('create_time', 'desc')
            ->select();
            
        $data = [
            'customer' => $customer,
            'records' => $records,
            'contracts' => $contracts
        ];
        
        $this->jsonSuccess('获取成功', $data);
    }
    
    /**
     * 获取客户统计信息
     * GET /api/customer/statistics
     */
    public function statistics()
    {
        $this->checkGetRequest();
        $this->checkAuth();
        
        $username = $this->admin['username'];
        
        // 统计各状态客户数量
        $stats = [
            'total' => $this->model->where('pr_user', $username)->count(),
            'leads' => $this->model->where('pr_user', $username)->where('status', 0)->count(),
            'customers' => $this->model->where('pr_user', $username)->where('status', 1)->count(),
            'seas' => $this->model->where('pr_user', $username)->where('status', 2)->count(),
        ];
        
        // 今日新增
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = $todayStart + 86400;
        $stats['today_new'] = $this->model
            ->where('pr_user', $username)
            ->where('at_time', 'between', [$todayStart, $todayEnd])
            ->count();
            
        // 待跟进客户（下次跟进时间在今天之前的）
        $stats['pending_follow'] = $this->model
            ->where('pr_user', $username)
            ->where('status', 1)
            ->where('next_record_time', '>', 0)
            ->where('next_record_time', '<', time())
            ->count();
            
        // 今日已跟进
        $stats['today_followed'] = Db::name('crm_record')
            ->alias('r')
            ->join('crm_customer c', 'r.customer_id = c.id')
            ->where('c.pr_user', $username)
            ->where('r.create_time', 'between', [$todayStart, $todayEnd])
            ->count();
        
        $this->jsonSuccess('获取成功', $stats);
    }
    
    /**
     * 构建跟进状态查询条件
     */
    private function buildFollowStatusWhere($query, $followStatus, $startDate, $endDate)
    {
        switch ($followStatus) {
            case 'never':
                // 从未跟进：没有跟进记录的客户
                $query->whereNotExists(function ($subQuery) {
                    $subQuery->name('crm_record')
                        ->whereRaw('customer_id = crm_customer.id');
                });
                break;
                
            case 'today':
                // 今日已跟进
                $todayStart = strtotime(date('Y-m-d'));
                $todayEnd = $todayStart + 86400;
                $query->whereExists(function ($subQuery) use ($todayStart, $todayEnd) {
                    $subQuery->name('crm_record')
                        ->whereRaw('customer_id = crm_customer.id')
                        ->where('create_time', 'between', [$todayStart, $todayEnd]);
                });
                break;
                
            case 'pending':
                // 待跟进：下次跟进时间在当前时间之前
                $query->where('next_record_time', '>', 0)
                      ->where('next_record_time', '<', time());
                break;
                
            case 'range':
                // 指定日期范围内已跟进
                if ($startDate && $endDate) {
                    $start = strtotime($startDate);
                    $end = strtotime($endDate . ' 23:59:59');
                    $query->whereExists(function ($subQuery) use ($start, $end) {
                        $subQuery->name('crm_record')
                            ->whereRaw('customer_id = crm_customer.id')
                            ->where('create_time', 'between', [$start, $end]);
                    });
                }
                break;
        }
    }
    
    /**
     * 处理客户列表数据
     */
    private function processCustomerList($list)
    {
        foreach ($list as &$item) {
            // 格式化时间
            $item['at_time_text'] = $item['at_time'] ? date('Y-m-d H:i', $item['at_time']) : '';
            $item['to_kh_time_text'] = $item['to_kh_time'] ? date('Y-m-d H:i', $item['to_kh_time']) : '';
            $item['last_record_time_text'] = $item['last_record_time'] ? date('Y-m-d H:i', $item['last_record_time']) : '';
            $item['next_record_time_text'] = $item['next_record_time'] ? date('Y-m-d H:i', $item['next_record_time']) : '';
            
            // 状态文本
            $statusMap = [0 => '线索', 1 => '客户', 2 => '公海'];
            $item['status_text'] = $statusMap[$item['status']] ?? '未知';
            
            // 是否需要跟进
            $item['need_follow'] = $item['next_record_time'] > 0 && $item['next_record_time'] < time();
        }
        
        return $list;
    }
}
