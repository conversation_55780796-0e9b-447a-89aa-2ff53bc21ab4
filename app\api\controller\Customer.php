<?php
namespace app\api\controller;

use app\admin\model\CrmCustomer;
use think\facade\Db;

/**
 * 客户管理控制器
 */
class Customer extends Common
{
    /**
     * 模型对象
     * @var \app\admin\model\CrmCustomer
     */
    protected $model;
    
    /**
     * 排序规则
     * @var array
     */
    protected $sort = [
        'id' => 'DESC',
    ];
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->model = new CrmCustomer();
    }
    
    /**
     * 获取客户列表
     * @return void
     */
    public function index()
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 15);
        $where = $this->buildSearchWhere();
        
        // 只能查看自己负责的客户
        $where[] = ['pr_user', '=', $this->adminInfo['username']];
        
        $count = $this->model->where($where)->count();
        $list = $this->model->where($where)
            ->page($page, $limit)
            ->order($this->sort)
            ->select();
        
        $data = [
            'count' => $count,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 获取客户详情
     * @return void
     */
    public function read()
    {
        $id = $this->request->param('id/d');
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        $customer = $this->model->find($id);
        if (empty($customer)) {
            $this->error('客户不存在');
        }
        
        // 只能查看自己负责的客户
        if ($customer['pr_user'] != $this->adminInfo['username']) {
            $this->error('无权限查看此客户');
        }
        
        // 获取最近跟进记录
        $records = Db::name('crm_record')
            ->where('customer_id', $id)
            ->order('create_time', 'desc')
            ->limit(5)
            ->select()
            ->toArray();
        
        $data = [
            'customer' => $customer,
            'records' => $records,
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 构建搜索条件
     * @return array
     */
    protected function buildSearchWhere()
    {
        $where = [];
        $params = $this->request->param();
        
        // 客户名称搜索
        if (!empty($params['name'])) {
            $where[] = ['name', 'like', '%' . $params['name'] . '%'];
        }
        
        // 联系人搜索
        if (!empty($params['contact'])) {
            $where[] = ['contact', 'like', '%' . $params['contact'] . '%'];
        }
        
        // 电话搜索
        if (!empty($params['phone'])) {
            $where[] = ['phone', 'like', '%' . $params['phone'] . '%'];
        }
        
        // 跟进状态筛选
        if (isset($params['kh_status']) && $params['kh_status'] !== '') {
            $where[] = ['kh_status', '=', $params['kh_status']];
        }
        
        // 成交状态筛选
        if (isset($params['issuccess']) && $params['issuccess'] !== '') {
            $where[] = ['issuccess', '=', intval($params['issuccess'])];
        }
        
        // 客户状态筛选（正常/回收站）
        if (isset($params['status']) && $params['status'] !== '') {
            $where[] = ['status', '=', intval($params['status'])];
        } else {
            // 默认只显示正常状态的客户
            $where[] = ['status', '=', 1];
        }
        
        // 行业筛选
        if (!empty($params['kh_hangye'])) {
            $where[] = ['kh_hangye', '=', $params['kh_hangye']];
        }
        
        // 客户级别筛选
        if (!empty($params['kh_rank'])) {
            $where[] = ['kh_rank', '=', $params['kh_rank']];
        }
        
        // 来源筛选
        if (!empty($params['source'])) {
            $where[] = ['source', '=', $params['source']];
        }
        
        return $where;
    }
    
    /**
     * 获取客户跟进状态列表
     * @return void
     */
    public function getStatusList()
    {
        $list = Db::name('crm_status')->field('id,name')->select();
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取客户行业列表
     * @return void
     */
    public function getHangyeList()
    {
        $list = Db::name('crm_hangye')->field('id,name')->select();
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取客户级别列表
     * @return void
     */
    public function getRankList()
    {
        $list = Db::name('crm_rank')->field('id,name')->select();
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取客户来源列表
     * @return void
     */
    public function getSourceList()
    {
        $list = Db::name('crm_source')->field('id,name')->select();
        $this->success('获取成功', $list);
    }
}