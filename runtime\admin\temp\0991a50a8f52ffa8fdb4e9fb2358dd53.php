<?php /*a:2:{s:60:"C:\wwwroot\127.0.0.1\app\admin\view\crm\record\dialogue.html";i:1686062138;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<style>
    .layuimini-container .layui-table-cell,.layui-table-view .layui-table{height: auto;}
</style>
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend><?php echo fy('Write follow-up'); ?></legend>
    </fieldset>
    <div class="layui-row" style="">
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md6 layui-col-lg6">
            <form class="layui-form form-readonly">
            <?php echo $fields_str; ?>
            </form>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md6 layui-col-lg6" style="padding: 0 10px 0 10px;">
            <fieldset class="layui-elem-field " style="padding: 15px 15px 0 15px">
                <legend>填写跟进</legend>

                <div class="layui-form layui-form-pane">

                    <div class="layui-form-item layui-form-text">

                        <div class="layui-input-block">
                            <textarea name="content" id="content"  required lay-verify="required" placeholder="<?php echo fy('Follow up frequently and sign more'); ?>"  class="layui-textarea fly-editor" style="height: 150px;"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><?php echo fy('Attachment'); ?></label>
                        <div class="layui-input-block layuimini-upload">
                            <input name="attachs" class="layui-input layui-col-xs6" placeholder="<?php echo fy('Please upload'); ?> <?php echo fy('Attachment'); ?>" value="">
                            <div class="layuimini-upload-btn">
                                <span><a class="layui-btn" data-upload="attachs" data-upload-number="5" ><i class="fa fa-upload"></i> <?php echo fy('Upload'); ?></a></span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"> <?php echo fy('Follow up type'); ?>:</label>
                        <div class="layui-input-inline">
                            <select name="record_type" required lay-verify="required" lay-reqtext="<?php echo fy('Please select'); ?> <?php echo fy('Follow up type'); ?>">
                                <option value=""><?php echo fy('Please select'); ?> <?php echo fy('Follow up type'); ?></option>
                                <?php
$typeList=think\facade\Db::name('crm_record_type')->field('`id`,`name`')->where('status','=',1)->order('sort ASC,id DESC')->select();
                                foreach($typeList as $v){
?>
                                <option value="<?php echo htmlentities($v['name']); ?>"><?php echo htmlentities($v['name']); ?></option>
<?php } ?>


                            </select>


                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"> <?php echo fy('Next follow-up time'); ?>:</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" autocomplete="off" name="next_time"  placeholder="<?php echo fy('Please enter'); ?>时间" data-date="yyyy-MM-dd HH:mm" value="<?php echo date('Y-m-d H:i',strtotime('+1 day')); ?>" data-date-min="<?php echo date('Y-m-d H:i'); ?>">
                        </div>
                    </div>
                    <div class="layui-form-item text-center">
                        <input type="hidden" name="customer_id" value="<?php echo htmlentities($result['id']); ?>">
                        <button class="layui-btn" lay-filter="btn_comment" lay-submit="<?php echo url('add'); ?>" data-refresh="true">提交</button>
                        <?php if($next_url){ ?>

                        <button  type="submit" lay-submit="<?php echo url('add'); ?>" data-jump="<?php echo htmlentities($next_url); ?>" class="layui-btn layui-btn-normal" data-refresh="true">提交进入下一条</button>

                        <a  href="<?php echo htmlentities($next_url); ?>" class="layui-btn layui-btn-primary" data-refresh="true">下一条</a>
                        <?php } ?>

                    </div>

                </div>

            </fieldset>

            <div class="layui-row">
                <div class="layuimini-container">
                    <div class="layuimini-main">
                        <table id="currentTable" class="layui-table layui-hide"
                               data-auth-add="<?php echo auth('business.record/add'); ?>"
                               data-auth-delete="<?php echo auth('business.record/delete'); ?>"
                               lay-filter="currentTable">
                        </table>
                    </div>
                </div>
            </div>



        </div>


    </div>




</div>
</body>
</html>