define(["jquery","easy-admin"],function(e,r){var t={table_elem:"#currentTable",table_render_id:"currentTableRenderId",index_url:"customer.changes_record/index",add_url:"customer.changes_record/add",edit_url:"customer.changes_record/edit",delete_url:"customer.changes_record/delete",export_url:"customer.changes_record/export",modify_url:"customer.changes_record/modify"};var i={index:function(){r.table.render({init:t,limit:CONFIG.ADMINPAGESIZE,cols:[[{type:"checkbox"},{field:"id",title:"id"},{field:"customer_id",title:"客户对应id"},{field:"customer_name",title:"客户名称",search:true},{field:"create_username",title:"操作人",search:true},{field:"on",title:"操作对象"},{field:"on_field",title:"操作字段",search:true},{field:"before_value",title:"操作前值"},{field:"after_value",title:"操作后值"},{field:"ip",title:"操作IP",search:true},{field:"create_time",title:"操作时间",width:160},{width:250,title:"操作",templet:r.table.tool}]],done:function(e,t,i){if(i===undefined&&e.msg&&e.url){r.msg.tips(e.msg,1,function(){window.top.location.href=e.url})}if(r.checkMobile()){r.booksTemplet()}}});r.listen()},add:function(){r.listen()},edit:function(){r.listen()}};return i});