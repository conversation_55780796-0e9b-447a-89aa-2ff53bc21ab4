!
    function() {
        window._qqWechatEmotionParser = {};
        window.qqWechatEmotionParser = function() {};
    } (); !
    function() {
        var emojiBaseUrl = 'https://res.wx.qq.com/mpres/htmledition/images/icon/emotion/';
        // emojiBaseUrl = config.emojiBaseUrl;
        window._qqWechatEmotionParser.emotion_map = {
            "/::)": emojiBaseUrl + "0.gif",
            "/::~": emojiBaseUrl + "1.gif",
            "/::B": emojiBaseUrl + "2.gif",
            "/::|": emojiBaseUrl + "3.gif",
            "/:8-)": emojiBaseUrl + "4.gif",
            "/::<": emojiBaseUrl + "5.gif",
            "/::$": emojiBaseUrl + "6.gif",
            "/::X": emojiBaseUrl + "7.gif",
            "/::Z": emojiBaseUrl + "8.gif",
            "/::'(": emojiBaseUrl + "9.gif",
            "/::-|": emojiBaseUrl + "10.gif",
            "/::@": emojiBaseUrl + "11.gif",
            "/::P": emojiBaseUrl + "12.gif",
            "/::D": emojiBaseUrl + "13.gif",
            "/::O": emojiBaseUrl + "14.gif",
            "/::(": emojiBaseUrl + "15.gif",
            "/::+": emojiBaseUrl + "16.gif",
            "/:--b": emojiBaseUrl + "17.gif",
            "/::Q": emojiBaseUrl + "18.gif",
            "/::T": emojiBaseUrl + "19.gif",
            "/:,@P": emojiBaseUrl + "20.gif",
            "/:,@-D": emojiBaseUrl + "21.gif",
            "/::d": emojiBaseUrl + "22.gif",
            "/:,@o": emojiBaseUrl + "23.gif",
            "/::g": emojiBaseUrl + "24.gif",
            "/:|-)": emojiBaseUrl + "25.gif",
            "/::!": emojiBaseUrl + "26.gif",
            "/::L": emojiBaseUrl + "27.gif",
            "/::>": emojiBaseUrl + "28.gif",
            "/::,@": emojiBaseUrl + "29.gif",
            "/:,@f": emojiBaseUrl + "30.gif",
            "/::-S": emojiBaseUrl + "31.gif",
            "/:?": emojiBaseUrl + "32.gif",
            "/:,@x": emojiBaseUrl + "33.gif",
            "/:,@@": emojiBaseUrl + "34.gif",
            "/::8": emojiBaseUrl + "35.gif",
            "/:,@!": emojiBaseUrl + "36.gif",
            "/:!!!": emojiBaseUrl + "37.gif",
            "/:xx": emojiBaseUrl + "38.gif",
            "/:bye": emojiBaseUrl + "39.gif",
            "/:wipe": emojiBaseUrl + "40.gif",
            "/:dig": emojiBaseUrl + "41.gif",
            "/:handclap": emojiBaseUrl + "42.gif",
            "/:&-(": emojiBaseUrl + "43.gif",
            "/:B-)": emojiBaseUrl + "44.gif",
            "/:<@": emojiBaseUrl + "45.gif",
            "/:@>": emojiBaseUrl + "46.gif",
            "/::-O": emojiBaseUrl + "47.gif",
            "/:>-|": emojiBaseUrl + "48.gif",
            "/:P-(": emojiBaseUrl + "49.gif",
            "/::'|": emojiBaseUrl + "50.gif",
            "/:X-)": emojiBaseUrl + "51.gif",
            "/::*": emojiBaseUrl + "52.gif",
            "/:@x": emojiBaseUrl + "53.gif",
            "/:8*": emojiBaseUrl + "54.gif",
            "/:pd": emojiBaseUrl + "55.gif",
            "/:<W>": emojiBaseUrl + "56.gif",
            "/:beer": emojiBaseUrl + "57.gif",
            "/:basketb": emojiBaseUrl + "58.gif",
            "/:oo": emojiBaseUrl + "59.gif",
            "/:coffee": emojiBaseUrl + "60.gif",
            "/:eat": emojiBaseUrl + "61.gif",
            "/:pig": emojiBaseUrl + "62.gif",
            "/:rose": emojiBaseUrl + "63.gif",
            "/:fade": emojiBaseUrl + "64.gif",
            "/:showlove": emojiBaseUrl + "65.gif",
            "/:heart": emojiBaseUrl + "66.gif",
            "/:break": emojiBaseUrl + "67.gif",
            "/:cake": emojiBaseUrl + "68.gif",
            "/:li": emojiBaseUrl + "69.gif",
            "/:bome": emojiBaseUrl + "70.gif",
            "/:kn": emojiBaseUrl + "71.gif",
            "/:footb": emojiBaseUrl + "72.gif",
            "/:ladybug": emojiBaseUrl + "73.gif",
            "/:shit": emojiBaseUrl + "74.gif",
            "/:moon": emojiBaseUrl + "75.gif",
            "/:sun": emojiBaseUrl + "76.gif",
            "/:gift": emojiBaseUrl + "77.gif",
            "/:hug": emojiBaseUrl + "78.gif",
            "/:strong": emojiBaseUrl + "79.gif",
            "/:weak": emojiBaseUrl + "80.gif",
            "/:share": emojiBaseUrl + "81.gif",
            "/:v": emojiBaseUrl + "82.gif",
            "/:@)": emojiBaseUrl + "83.gif",
            "/:jj": emojiBaseUrl + "84.gif",
            "/:@@": emojiBaseUrl + "85.gif",
            "/:bad": emojiBaseUrl + "86.gif",
            "/:lvu": emojiBaseUrl + "87.gif",
            "/:no": emojiBaseUrl + "88.gif",
            "/:ok": emojiBaseUrl + "89.gif",
            "/:love": emojiBaseUrl + "90.gif",
            "/:<L>": emojiBaseUrl + "91.gif",
            "/:jump": emojiBaseUrl + "92.gif",
            "/:shake": emojiBaseUrl + "93.gif",
            "/:<O>": emojiBaseUrl + "94.gif",
            "/:circle": emojiBaseUrl + "95.gif",
            "/:kotow": emojiBaseUrl + "96.gif",
            "/:turn": emojiBaseUrl + "97.gif",
            "/:skip": emojiBaseUrl + "98.gif",
            "/:oY": emojiBaseUrl + "99.gif",
            "/:#-0": emojiBaseUrl + "100.gif",
            "/:hiphot": emojiBaseUrl + "101.gif",
            "/:kiss": emojiBaseUrl + "102.gif",
            "/:<&": emojiBaseUrl + "103.gif",
            "/:&>": emojiBaseUrl + "104.gif",
            // 其他转码
            "[囧]": emojiBaseUrl + "17.gif",
        };
    } (); !
    function() {
        function Trie() {
            this.words = 0;
            this.empty = 1;
            this.index = 0;
            this.children = {};
        }
        Trie.prototype = {
            insert: function(str, pos, idx) {
                if (str.length === 0) {
                    return;
                }
                var T = this;
                var k;
                var child;
                if (pos === undefined) {
                    pos = 0;
                }
                if (pos === str.length) {
                    T.index = idx;
                    return;
                }
                k = str[pos];
                if (T.children[k] === undefined) {
                    T.children[k] = new Trie();
                    T.empty = 0;
                    T.children[k].words = this.words + 1;
                }
                child = T.children[k];
                child.insert(str, pos + 1, idx);
            },
            build: function(arr) {
                var len = arr.length;
                for (var i = 0; i < len; i++) {
                    this.insert(arr[i], 0, i);
                }
            },
            searchOne: function(str, pos) {
                if (pos === undefined) {
                    pos = 0;
                }
                var result = {};
                if (str.length === 0) return result;
                var T = this;
                var child;
                var k;
                result.arr = [];
                k = str[pos];
                child = T.children[k];
                if (child !== undefined && pos < str.length) {
                    return child.searchOne(str, pos + 1);
                }
                if (child === undefined && T.empty === 0) return result;
                if (T.empty == 1) {
                    result.arr[0] = pos - T.words;
                    result.arr[1] = T.index;
                    result.words = T.words;
                    return result;
                }
                return result;
            },
            search: function(str) {
                if (this.empty == 1) return [];
                var len = str.length;
                var searchResult = [];
                var tmp;
                for (var i = 0; i < len - 1; i++) {
                    tmp = this.searchOne(str, i);
                    if (typeof tmp.arr !== 'undefined' && tmp.arr.length > 0) {
                        searchResult.push(tmp.arr);
                        i = i + tmp.words - 1;
                    }
                }
                return searchResult;
            }
        };
        if (typeof module !== 'undefined') {
            module.exports = Trie;
        } else if (typeof window !== 'undefined') {
            window._qqWechatEmotionParser.Trie = Trie;
        }
    } (); !
    function() {
        var emotion_map, trie, emotion_list, Trie;
        if (typeof module !== 'undefined') {
            emotion_map = require('./emotions.json');
            Trie = require('./trie');
            build();
            module.exports = qqWechatEmotionParser;
        } else if (window !== 'undefined') {
            emotion_map = window._qqWechatEmotionParser.emotion_map;
            Trie = window._qqWechatEmotionParser.Trie;
            build();
            window.qqWechatEmotionParser = qqWechatEmotionParser;
        }
        function build() {
            emotion_list = keys(emotion_map);
            trie = new Trie();
            trie.build(emotion_list);
        }
        function qqWechatEmotionParser(str) {
            console.log(str);
            str = str.replace(/&gt;/g,'>');
            str = str.replace(/&lt;/g,'<');
            console.log(str);
            var indices = trie.search(str);
            indices.reverse().map(function(idx) {
                var pos = idx[0],
                    emotion = emotion_list[idx[1]],
                    img = '<img src="' + emotion_map[emotion] + '" alt="' + emotion + '">';
                str = splice(str, pos, emotion.length, img);
            });
            return str;
        }
        function splice(str, index, count, add) {
            return str.slice(0, index) + add + str.slice(index + count);
        }
        function keys(map) {
            var list = [];
            for (var k in map) {
                if (map.hasOwnProperty(k)) list.push(k);
            }
            return list;
        }
    } ();