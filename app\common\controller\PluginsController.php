<?php

// +----------------------------------------------------------------------
// | EasyAdmin
// +----------------------------------------------------------------------
// | PHP交流群: 763822524
// +----------------------------------------------------------------------
// | 开源协议  https://mit-license.org
// +----------------------------------------------------------------------
// | github开源项目：https://github.com/zhongshaofa/EasyAdmin
// +----------------------------------------------------------------------


namespace app\common\controller;


use app\BaseController;
use think\App;
use think\facade\Env;
use think\facade\Lang;
use think\facade\View;
use think\Model;
use app\admin\controller\Common;


class PluginsController extends AdminController
{

    public $params;
    protected $layout;
    public function __construct(App $app)
    {

        parent::__construct($app);
        $this->params = $this->request->param();
        $plugin_path=$this->app->getBasePath().'plugins'.DIRECTORY_SEPARATOR.$this->params['pluginsname'].DIRECTORY_SEPARATOR.$this->params['pluginsgroup'].DIRECTORY_SEPARATOR;
//        var_dump($plugin_path);
        $this->layout=$this->app->getBasePath().$this->params['pluginsgroup'].DIRECTORY_SEPARATOR.'view/layout/plugins.html';
        $this->app->view->engine()->layout($this->layout);

        list($thisControllerArr, $jsPath) = [explode('.', $this->params['pluginscontrol']), null];
        foreach ($thisControllerArr as $vo) {
            empty($jsPath) ? $jsPath = parse_name($vo) : $jsPath .= '/' . parse_name($vo);
        }
        $thisControllerJsPath = "plugins/".$this->params['pluginsname'].'/'.$this->params['pluginsgroup']."/js/{$jsPath}.js";

        $this->assignconfig([
            'CONTROLLER_PLUGINS_JS_PATH'=>$thisControllerJsPath,
            'PLUGINS_NAME'=>$this->params['pluginsname'],
                'PLUGINS_CONTROLLER'=>$this->params['pluginscontrol'],
                'PLUGINS_GROUP'=>$this->params['pluginsgroup'],
                'PLUGINS_ACTION'=>$this->params['pluginsaction']]
        );

        $this->app->view->config([
            'view_path' => $plugin_path . 'view' . DIRECTORY_SEPARATOR
        ]);


    }


    protected function fetch($template = '', $vars = []){
        if(empty($template)){
            $template=$this->params['pluginscontrol'].'/'.$this->params['pluginsaction'];
        }
        return $this->app->view->fetch($template, $vars);
    }
    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        $this->params = $this->request->param();
        $this->get = $this->request->get();
        $lang = Lang::getLangSet();
//        var_dump($lang);
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})\$/i", $lang) ? $lang : 'zh-cn';
//        pluginsname: wechat
//pluginscontrol: menu
//pluginsaction: index
        $pluginsname = isset($this->get['pluginsname'])?$this->get['pluginsname']: $this->params['pluginsname'];
        $pluginscontrol =  isset($this->get['pluginscontrol'])?$this->get['pluginscontrol']:$this->params['pluginscontrol'];
        $pluginsgroup =  isset($this->get['pluginsgroup'])?$this->get['pluginsgroup']:$this->params['pluginsgroup'];
        $pluginsaction =  isset($this->get['pluginsaction'])?$this->get['pluginsaction']:$this->params['pluginsaction'];
        if($pluginsname && $pluginscontrol && $pluginsaction && $pluginsgroup){
            list($thisControllerArr, $jsPath) = [explode('.', $pluginscontrol), null];
            foreach ($thisControllerArr as $vo) {
                empty($jsPath) ? $jsPath = parse_name($vo) : $jsPath .= '/' . parse_name($vo);
            }
            Lang::load($this->app->getAppPath(). $pluginsname.'/'.$pluginsgroup.'/lang/' . $lang . '/' . $jsPath . '.php');
        }

    }

}