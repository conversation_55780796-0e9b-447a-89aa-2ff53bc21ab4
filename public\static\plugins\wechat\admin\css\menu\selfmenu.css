.msg_card_inner {
    overflow: hidden;
    border: 1px solid #e7e7eb
}

.msg_card_bd {
    padding: 14px;
    background-color: #fff
}

.msg_card_ft {
    background-color: #f4f5f9;
    border-top: 1px solid #e7e7eb
}

.msg_card_title {
    overflow: hidden;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    line-height: 1.2;
    max-height: 2.4;
    word-wrap: normal;
    white-space: pre-wrap
}

.msg_card_title a {
    display: block;
    color: #222
}

.msg_card_info {
    font-size: 13px;
    line-height: 20px;
    margin-bottom: 12px;
    color: #8d8d8d
}

.msg_card_info_meta {
    font-weight: 400;
    font-style: normal
}

.msg_card_extra_info {
    margin: 14px 0 5px
}

.msg_card_desc {
    word-wrap: normal;
    white-space: pre-wrap
}

.msg_card_opr_list {
    font-size: 0
}

.msg_card_opr_item {
    text-align: center
}

.msg_card_opr_item a {
    display: block
}

.msg_card_opr_item:first-child .msg_card_opr_item_inner {
    border-left-width: 0
}

.msg_card_opr_item_inner {
    display: inline-block;
    vertical-align: top;
    width: 98.5%;
    margin: 13px 0;
    line-height: 18px;
    height: 18px;
    cursor: pointer;
    border-left: 1px solid #e7e7eb
}

.mini .msg_card_opr_list {
    line-height: 30px
}

.mini .msg_card_opr_item_inner {
    margin: 5px 0
}

.no_opr_border .msg_card_opr_item_inner {
    border-left: none
}

.no_title .msg_card_extra_info {
    margin-top: 0
}

.portable_editor {
    position: relative
}

.portable_editor .editor_inner {
    padding: 0 20px 5px;
    background-color: #f4f5f9;
    border: 1px solid #e7e7eb;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none
}

.portable_editor .editor_arrow_wrp, .portable_editor .editor_arrow {
    position: absolute
}

.portable_editor .frm_label .title {
    font-weight: 400;
    font-style: normal
}

.portable_editor .frm_label .tips {
    color: #8d8d8d
}

.portable_editor .frm_control_group {
    margin-top: 30px;
    margin-bottom: 30px;
    padding-bottom: 0
}

.portable_editor .edit_item {
    margin-top: 25px;
    margin-bottom: 25px
}

.portable_editor .edit_item .frm_label {
    *zoom: 1;
    display: block;
    float: none;
    width: auto;
    margin-bottom: 6px
}

.portable_editor .edit_item .frm_label:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both
}

.portable_editor .edit_item .frm_input_box, .portable_editor .edit_item .frm_textarea_box {
    display: block;
    width: auto;
    border-color: #e7e7eb
}

.portable_editor .edit_item .frm_input_box {
    height: auto;
    min-height: 30px
}

.portable_editor .edit_item.dropdown_item {
    *zoom: 1
}

.portable_editor .edit_item.dropdown_item:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both
}

.portable_editor .edit_item.dropdown_item .frm_label {
    float: left;
    margin-top: 0;
    line-height: 32px
}

.portable_editor.to_left {
    padding-left: 12px
}

.portable_editor.to_left .editor_arrow_wrp {
    left: 0;
    top: 20px
}

.portable_editor.to_left .editor_arrow {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 12px;
    border-style: dashed;
    border-color: transparent;
    border-left-width: 0;
    border-right-color: #e7e7eb;
    border-right-style: solid
}

.portable_editor.to_left .editor_arrow_out {
    left: 0
}

.portable_editor.to_left .editor_arrow_in {
    left: 1px;
    border-right-color: #f4f5f9
}

.portable_editor.to_right {
    padding-right: 12px
}

.portable_editor.to_right .editor_arrow_wrp {
    right: 0;
    top: 20px
}

.portable_editor.to_right .editor_arrow {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 12px;
    border-style: dashed;
    border-color: transparent;
    border-right-width: 0;
    border-left-color: #e7e7eb;
    border-left-style: solid
}

.portable_editor.to_right .editor_arrow_out {
    right: 0
}

.portable_editor.to_right .editor_arrow_in {
    right: 1px;
    border-left-color: #f4f5f9
}

.portable_editor.to_top {
    padding-top: 12px
}

.portable_editor.to_top .editor_arrow_wrp {
    top: 0;
    left: 20px
}

.portable_editor.to_top .editor_arrow {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 12px;
    border-style: dashed;
    border-color: transparent;
    border-top-width: 0;
    border-bottom-color: #e7e7eb;
    border-bottom-style: solid
}

.portable_editor.to_top .editor_arrow_out {
    top: 0
}

.portable_editor.to_top .editor_arrow_in {
    top: 1px;
    border-bottom-color: #f4f5f9
}

.portable_editor.to_bottom {
    padding-bottom: 12px
}

.portable_editor.to_bottom .editor_arrow_wrp {
    bottom: 0;
    left: 20px
}

.portable_editor.to_bottom .editor_arrow {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 12px;
    border-style: dashed;
    border-color: transparent;
    border-bottom-width: 0;
    border-top-color: #e7e7eb;
    border-top-style: solid
}

.portable_editor.to_bottom .editor_arrow_out {
    bottom: 0
}

.portable_editor.to_bottom .editor_arrow_in {
    bottom: 1px;
    border-top-color: #f4f5f9
}

.wx_video_dialog.dialog_wrp .dialog_bd {
    text-align: center;
    padding: 45px 0
}

.specialchoose-desc {
    color: #9a9a9a;
    margin-bottom: 30px
}

.align_edge .weui-desktop-dialog__bd {
    padding: 0 32px 32px
}

.info_container .avatar {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 1px solid #000;
    margin-right: 5px
}

.info_container .title {
    display: inline-block;
    vertical-align: top
}

.menu_tab_content .homepage {
    margin-left: -20px
}

.special-list {
    overflow-y: auto
}

.special-list .weui-desktop-form__check-label {
    margin-bottom: 30px;
    padding-bottom: 20px;
    margin-right: 15px
}

.special-list .weui-desktop-form__check-label:nth-child(even) {
    margin-right: 0
}

.special-list .weui-desktop-form__check-content {
    height: 50px;
    display: inline-block
}

.special-list .special-cover {
    display: inline-block;
    width: 300px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    color: #fff
}

.special-list .special-cover_title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 300px
}

.weui-desktop-media-tips {
    height: 350px
}

.weui-desktop-media-tips_loading {
    font-size: 0;
    color: transparent;
    text-align: center
}

.weui-desktop-media-tips_loading:before {
    content: " ";
    background: transparent url(data:image/gif;base64,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) no-repeat 0 0;
    width: 40px;
    height: 40px;
    vertical-align: middle;
    display: inline-block
}

.weui-desktop-media-tips_loading:after {
    content: " ";
    display: inline-block;
    vertical-align: middle;
    width: 0;
    height: 100%;
    overflow: hidden
}

.simulator {
    width: 318px;
    margin: 0 auto;
    border: 1px solid #e7e7eb
}

.simulator_hd .title {
    position: absolute;
    bottom: 10px;
    font-weight: normal;
    color: #fff;
    text-align: center;
    left: 60px;
    right: 50px;
    height: 1.6em;
    overflow: hidden
}

.simulator_bd {
    min-height: 450px
}

.plugin_content {
    position: relative
}

.plugin_mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    background-color: rgba(229, 229, 229, 0.85);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#d9e5e5e5', endcolorstr='#d9e5e5e5')
}

.plugin_mask a {
    position: absolute;
    top: 50%;
    margin-top: -11px;
    left: 50%;
    margin-left: -11px
}

.plugin_mask.transparent {
    display: none !important;
    background-color: transparent;
    cursor: default
}

.plugin_mask.transparent a {
    display: none
}

.menu_tab_content {
    position: relative
}

.question .highlight_box {
    width: 100%;
    top: 0;
    left: 0;
    box-sizing: border-box;
    position: absolute
}

.highlight_box {
    width: 100%;
    top: 0;
    left: 0;
    box-sizing: border-box;
    position: absolute
}

.question__table-wrp {
    margin-top: 10px;
    max-height: 300px;
    overflow: auto
}

.question__table-wrp .table {
    margin-top: 10px
}

.table_cell.question__table-wrp .table:last-child {
    text-align: right
}

.question__item {
    display: flex
}

.question__list {
    margin-top: 30px
}

.question__list .frm_input_box {
    width: 100%;
    box-sizing: border-box
}

.question__list .frm_control_group {
    margin: 50px 0
}

.question-list {
    padding: 0
}

.question__list-wrp {
    max-height: 350px;
    overflow: auto;
    border-bottom: 1px solid #e0e1e2
}

.question-list__item {
    color: #9a9a9a;
    font-size: 14px;
    position: relative
}

.question-list__item:last-child .question-list__item-container {
    border-bottom: 0
}

.question-list__item-container {
    display: flex;
    padding: 20px 0;
    border-bottom: 1px solid #e0e1e2
}

.question__list-item-head {
    background-color: #f4f5f9;
    color: #9a9a9a
}

.question-list__item-hd {
    width: 30px;
    padding: 0 10px;
    box-sizing: border-box
}

.question-list__item-hd .frm_radio_label {
    margin-top: 8px
}

.question-list__item-bd {
    flex: 1;
    padding: 0 10px;
    flex-shrink: 0;
    overflow: hidden;
    box-sizing: border-box;
    display: flex
}

.question-list__item-ft {
    width: 130px;
    padding: 0 10px;
    padding-right: 40px;
    text-align: right;
    box-sizing: border-box
}

.question__author-avatar {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    border-radius: 2px;
    overflow: hidden
}

.question__author-avatar img {
    width: 100%
}

.question__item-content {
    flex: 1;
    overflow: hidden
}

.question__item-main {
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e1e2
}

.question__item-answer {
    padding-top: 10px;
    display: flex
}

.question__author-info {
    margin-bottom: 3px
}

.loading-area {
    text-align: center;
    padding: 40px 0
}

.question__item-title {
    color: #353535;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.question__answer-info {
    position: relative;
    padding-left: 10px
}

.question__answer-info::before {
    content: "";
    position: absolute;
    width: 2px;
    height: 14px;
    background-color: #1aad19;
    top: 4px;
    left: 0
}

.question__answer-content {
    color: #353535;
    flex: 1;
    margin-left: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.menu_setting_tips {
    padding-bottom: 20px
}

.menu_initial_box {
    text-align: center;
    background: transparent url(../img/index_z4510e0.png) no-repeat 0 0;
    background-position: 150px 100px;
    padding: 280px 0 0 220px;
    min-height: 300px
}

.menu_initial_box .tips_global {
    padding-bottom: 5px
}

.menu_setting_box .tool_bar {
    padding-top: 0
}

.menu_setting_msg {
    margin-bottom: 40px
}

.menu_setting_area {
    *zoom: 1;
    margin: 14px 30px 0
}

.menu_setting_area:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both
}

.menu_preview_area {
    float: left;
    margin-right: 12px;
    position: relative
}

.menu_preview_area .sort_btn_wrp {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -72px;
    text-align: center
}

.menu_preview_area .icon14_menu_add {
    background: url(../img/index_z4510e0.png) 0 0 no-repeat;
    width: 14px;
    height: 14px;
    vertical-align: middle;
    display: inline-block;
    margin-top: -2px
}

.menu_preview_area .pre_menu_list {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #e7e7eb;
    background: transparent url(../img/bottommenu.png) no-repeat 0 0;
    background-position: 0 0;
    background-repeat: no-repeat;
    padding-left: 43px
}

.menu_preview_area .pre_menu_list .sort_gray {
    display: none
}

.menu_preview_area .pre_menu_list.no_menu .pre_menu_item .pre_menu_link {
    border: 1px solid #44b549;
    color: #44b549
}

.menu_preview_area .pre_menu_list.no_menu .icon14_menu_add {
    background: url(../img/index_z4510e0.png) 0 -18px no-repeat
}

.menu_preview_area .pre_menu_list.sorting .sort_gray {
    display: inline-block
}

.menu_preview_area .pre_menu_list.sorting .icon_menu_dot {
    display: none
}

.menu_preview_area .pre_menu_list.sorting .pre_menu_item.current .pre_menu_link {
    border: 0;
    border-left: 1px solid #e7e7eb;
    background-color: transparent;
    line-height: 50px;
    color: #616161
}

.menu_preview_area .pre_menu_list.sorting .sub_pre_menu_list li.current {
    background-color: transparent;
    border-color: transparent
}

.menu_preview_area .pre_menu_list.sorting .sub_pre_menu_list li.current a {
    color: #616161
}

.menu_preview_area .pre_menu_list.sorting .sub_pre_menu_list li.current .sub_pre_menu_inner {
    border-top-width: 1px
}

.menu_preview_area .pre_menu_list .sort_gray {
    margin-top: -4px
}

.menu_preview_area .pre_menu_item {
    line-height: 50px
}

.menu_preview_area .pre_menu_item:first-child .pre_menu_link {
    border-left-width: 0
}

.menu_preview_area .pre_menu_item.current .pre_menu_link {
    border: 1px solid #44b549;
    line-height: 48px;
    background-color: #fff;
    color: #44b549
}

.menu_preview_area .pre_menu_item.dragging {
    border: 1px solid #e7e7eb;
    background-color: #fafafa
}

.menu_preview_area .pre_menu_link .add_gray {
    margin-top: -3px
}

.menu_preview_area .sub_pre_menu_box {
    bottom: 60px;
    background-color: #fafafa;
    border-top-width: 0
}

.menu_preview_area .sub_pre_menu_inner {
    display: block;
    border-top: 1px solid #e7e7eb;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    cursor: pointer
}

.menu_preview_area .sub_pre_menu_list li {
    line-height: 44px;
    border: 1px solid transparent;
    margin: 0 -1px -1px
}

.menu_preview_area .sub_pre_menu_list li:first-child {
    border-top: 1px solid #d0d0d0
}

.menu_preview_area .sub_pre_menu_list li:first-child .sub_pre_menu_inner {
    border-top-width: 0
}

.menu_preview_area .sub_pre_menu_list li:hover {
    background-color: #eee;
    border: 1px solid #d0d0d0;
    line-height: 45px;
    cursor: pointer
}

.menu_preview_area .sub_pre_menu_list li:hover .sub_pre_menu_inner {
    border-top-width: 0
}

.menu_preview_area .sub_pre_menu_list li:hover:first-child {
    line-height: 44px
}

.menu_preview_area .sub_pre_menu_list li.current {
    background-color: #fff;
    border: 1px solid #44b549;
    position: relative;
    z-index: 1;
    line-height: 45px;
    *zoom: 1
}

.menu_preview_area .sub_pre_menu_list li.current .sub_pre_menu_inner {
    border-top-width: 0
}

.menu_preview_area .sub_pre_menu_list li.current a {
    color: #44b549
}

.menu_preview_area .menu_drag_placeholder {
    float: left;
    width: 80px;
    height: 42px;
    margin: 3px;
    border: 1px solid #e7e7eb;
    background-color: #fff
}

.menu_preview_area li.menu_sub_drag_placeholder {
    margin: 3px;
    height: 45px;
    border: 1px solid #e7e7eb;
    background-color: #fff
}

.mobile_menu_preview {
    position: relative;
    width: 317px;
    height: 580px;
    background: transparent url(../img/tophead.png) no-repeat 0 0;
    background-position: 0 0;
    border: 1px solid #e7e7eb
}

.mobile_menu_preview .mobile_hd {
    color: #fff;
    text-align: center;
    padding-top: 30px;
    font-size: 15px;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    margin: 0 30px
}

.menu_form_area {
    display: table-cell;
    vertical-align: top;
    float: none;
    width: auto;
    *display: block;
    *zoom: 1
}

.menu_form_area:after {
    content: " . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . ";
    visibility: hidden;
    clear: both;
    height: 0 !important;
    display: block;
    line-height: 0
}

.menu_form_area .editor_inner {
    min-height: 560px;
    padding-bottom: 20px
}

.menu_form_area .portable_editor.to_left .editor_arrow_wrp {
    top: 545px
}

.menu_form_area .msg_wrp {
    padding: 4px 10px;
    border: 1px solid #e7e7eb;
    word-wrap: break-word;
    word-break: break-all
}

.menu_form_area .msg_wrp .richvideo, .menu_form_area .msg_wrp .appmsg, .menu_form_area .msg_wrp .richvideo_msg_box {
    margin-bottom: 0;
    width: 320px
}

.menu_form_area .msg_wrp img {
    vertical-align: middle
}

.menu_form_area .msg_wrp .wxmImg {
    margin-bottom: -4px
}

.menu_form_area .frm_label {
    width: 5em;
    *width: 5.5em
}

.menu_form_area .frm_tips, .menu_form_area .frm_msg {
    width: auto
}

.menu_form_area .msg_sender_msg {
    padding-top: 10px
}

.menu_form_area .msg_sender_tips {
    padding-top: 10px
}

.menu_form_area .msg_sender .tab_navs_wrp {
    width: 420px
}

.menu_form_area .msg_sender .tab_navs_switch_wrp.switch_next {
    display: block
}

.menu_form_area .msg_sender .tab_cont_cover {
    padding: 20px
}

.menu_form_area .msg_sender .tab_cont_cover .media_cover, .menu_form_area .msg_sender .tab_cont_cover .appmsg_cover {
    width: 47%;
    margin-right: 0;
    margin-left: 5.5%
}

.menu_form_area .msg_sender .tab_cont_cover .media_cover:first-child, .menu_form_area .msg_sender .tab_cont_cover .appmsg_cover:first-child {
    margin-left: 0
}

.menu_form_area .msg_sender .tab_cont_cover .create_access {
    padding: 42px 0
}

.menu_form_area .msg_sender.error {
    border-color: #e15f63
}

.menu_form_area .msg_sender .emotion_editor .edit_area {
    height: 151px
}

.menu_form_area .msg_sender .editor_toolbar {
    border-bottom: 0;
    padding-bottom: 0
}

.menu_form_area .msg_sender .upload_file_box {
    text-align: left
}

.menu_form_area .msg_sender .progress_bar {
    display: block;
    margin-top: 10px;
    width: auto
}

.menu_form_hd {
    padding: 9px 0;
    border-bottom: 1px solid #e7e7eb
}

.menu_form_hd h4 {
    font-weight: 400
}

.menu_initial_tips {
    text-align: center;
    padding-top: 200px
}

.menu_content_edit_access {
    float: right
}

.msg_tab {
    background-color: #fff
}

.menu_content {
    padding: 16px 20px;
    border: 1px solid #e7e7eb;
    background-color: #fff
}

.menu_content .frm_control_group {
    margin-top: 0
}

.menu_content.send {
    border: 0;
    padding: 0
}

.menu_content_tips {
    padding-bottom: 10px
}

.portable_editor .frm_control_group {
    margin-bottom: 10px
}

.pre_menu_item {
    position: relative;
    float: left;
    line-height: 38px;
    text-align: center
}

.pre_menu_item .icon_menu_dot {
    background: url(../img/index_z4510e0.png) 0 -36px no-repeat;
    width: 7px;
    height: 7px;
    vertical-align: middle;
    display: inline-block;
    margin-right: 2px;
    margin-top: -2px;
    *margin-top: 0
}

.pre_menu_item a {
    display: block;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    color: #616161;
    text-decoration: none
}

.pre_menu_link {
    border-left: 1px solid #e7e7eb
}

.pre_menu_link:hover {
    color: #222
}

.sub_pre_menu_box {
    position: absolute;
    bottom: 50px;
    left: 0;
    width: 100%;
    border: 1px solid #d0d0d0;
    background-color: #fff
}

.sub_pre_menu_box .arrow {
    position: absolute;
    left: 50%;
    margin-left: -6px
}

.sub_pre_menu_box .arrow_out {
    bottom: -6px;
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: dashed;
    border-color: transparent;
    border-bottom-width: 0;
    border-top-color: #d0d0d0;
    border-top-style: solid
}

.sub_pre_menu_box .arrow_in {
    bottom: -5px;
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: dashed;
    border-color: transparent;
    border-bottom-width: 0;
    border-top-color: #fafafa;
    border-top-style: solid
}

.sub_pre_menu_list li {
    line-height: 30px
}

.sub_pre_menu_list li a {
    padding: 0 .5em
}

.mobile_preview {
    position: fixed;
    display: none;
    top: 50%;
    left: 50%;
    width: 327px;
    margin-top: -425px;
    margin-left: -161px;
    z-index: 9999;
    background: transparent url(../../images/bg_mobile.png) no-repeat 0 0
}

.mobile_preview .richvideo_content .video_wrp {
    min-height: 0
}

.mobile_preview .richvideo_content .video_player, .mobile_preview .richvideo_content .video_shot {
    height: 80px
}

.mobile_preview .vjs-default-skin .vjs-time-controls {
    width: 3em
}

.mobile_preview .vjs-default-skin .vjs-control-bar {
    overflow: hidden
}

.mobile_preview .vjs-default-skin .vjs-big-play-button {
    width: 80px;
    height: 60px;
    background-position: -305px 8px;
    margin-top: -30px;
    margin-left: -40px
}

.mobile_preview .pre_menu_list {
    *zoom: 1;
    margin-left: 65px;
    margin-right: 36px
}

.mobile_preview .pre_menu_list:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both
}

.mobile_preview_hd {
    height: 150px;
    text-align: center
}

.mobile_preview_hd .nickname {
    display: inline-block;
    font-weight: 400;
    padding-top: 120px;
    width: 122px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    font-size: 14px;
    color: #fff
}

.mobile_preview_bd {
    position: relative;
    height: 393px;
    margin-left: 40px;
    margin-right: 28px;
    overflow-y: scroll
}

.show_list {
    padding-right: 1em
}

.show_item {
    *zoom: 1;
    padding-top: 1.5em
}

.show_item:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both
}

.show_item .avatar {
    float: left;
    width: 48px;
    margin-right: 5px
}

.show_content {
    overflow: hidden;
    *zoom: 1
}

.show_content .richvideo, .show_content .richvideo_msg_box {
    width: auto
}

.mobile_preview_ft {
    height: 152px
}

.mobile_preview_closed {
    position: absolute;
    left: 112px
}

.btn_appmsg_wrap {
    margin-top: -10px;
    padding-left: 85px
}

.select_list_container {
    padding: 20px 30px
}

.select_list_container .frm_input_box {
    width: 278px
}

.select_list_container .loading_area {
    position: relative
}

.select_list_container .icon_loading_small {
    margin-left: -20px;
    top: 185px
}

.select_list_container .select_list_hd {
    margin-bottom: 15px
}

.select_list_container .select_list_bd {
    height: 343px;
    overflow-y: auto;
    border: 1px solid #e7e7eb
}

.select_list_container .select_list_ft .pagination_wrp {
    margin-top: 20px
}

.select_list_container .loading_area {
    height: 240px;
    text-align: center
}

.select_list_container .select_list {
    border-bottom: 0
}

.select_list_container .no_appmsg {
    text-align: center;
    line-height: 340px;
    color: #8d8d8d
}

.select_list_container .list_item_date {
    float: right;
    color: #8d8d8d
}

.select_list_container .select_list_item {
    display: block;
    margin-right: 0;
    padding: 10px 15px;
    border-bottom: 1px solid #e7e7eb
}

.select_list_container .select_list_item:hover {
    background-color: #f4f5f9
}

.select_list_container .select_list_item:last-child {
    border-bottom: 0
}

.menu_create_box {
    text-align: center;
    padding-top: 2em
}

.menu_create_box .tips_global {
    margin-bottom: .5em
}

.menu_mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    filter: alpha(opacity=75);
    -moz-opacity: .75;
    -khtml-opacity: .75;
    opacity: .75;
    background-color: #000
}

.menu_tab_content {
    padding: 40px;
    height: 430px
}

.menu_tab_content .link_insertion {
    margin: 100px auto;
    width: 470px
}

.menu_tab_content .link_insertion .frm_label {
    float: none;
    display: block;
    width: auto
}

.menu_tab_content .media_lib .loading {
    display: none;
    text-align: center;
    padding: 150px 0
}

.menu_tab_content .media_lib .table_wrp {
    margin-top: 20px;
    max-height: 300px;
    overflow: auto;
    border: 1px solid #e7e7eb
}

.menu_tab_content .media_lib .table {
    border: 0;
    margin-bottom: 0
}

.menu_tab_content .media_lib tr {
    cursor: pointer
}

.menu_tab_content .media_lib .table_cell.date {
    width: 120px;
    color: #8d8d8d
}

.menu_tab_content .history_msg {
    display: none;
    overflow: hidden;
    margin-top: 20px
}


.menu_tab_content .history_msg .preview_area .desc {
    position: absolute;
    top: 100%;
    margin-top: 10px;
    width: 100%;
    text-align: center
}

.menu_tab_content .history_msg .form_area {
    overflow: hidden;
    padding: 180px 0;
    text-align: center
}

.menu_tab_content .homepage {
    max-height: 440px;
    overflow: auto;
    margin-right: -20px
}

.menu_tab_content .homepage .loading {
    padding: 120px 260px;
    color: #8d8d8d
}

.menu_tab_content .homepage .no_homepage {
    padding: 120px 230px
}

.menu_tab_content .homepage .col_tpl {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    width: 295px;
    margin-right: 20px;
    margin-bottom: 15px;
    position: relative
}

.menu_tab_content .homepage .col_tpl .msg_card {
    border: 0
}

.menu_tab_content .homepage .col_tpl .homepage_mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    filter: alpha(opacity=60);
    -moz-opacity: .6;
    -khtml-opacity: .6;
    opacity: .6;
    z-index: 1
}

.menu_tab_content .homepage .col_tpl .icon_card_selected {
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    margin-top: -23px;
    margin-left: -23px;
    line-height: 999em;
    overflow: hidden;
    z-index: 1
}

.menu_tab_content .homepage .col_tpl.selected .homepage_mask {
    display: block
}

.menu_tab_content .homepage .col_tpl.selected .icon_card_selected {
    display: block
}

.menu_tab_content .homepage .col_tpl .simulator .simulator_bd .article_list {
    *height: 420px
}

.menu_tab_content .homepage .col_tpl .simulator .simulator_bd .tab .article_list {
    *height: 220px
}

.menu_tab_content .homepage .msg_card_extra_info {
    height: 440px;
    overflow: hidden
}

.menu_tab_content .homepage .msg_card_extra_info .simulator {
    width: 265px;
    border: 0
}

.menu_tab_content .homepage .msg_card_extra_info .simulator_hd {
    background: #333;
    height: 40px;
    font-size: 14px
}

.menu_tab_content .homepage .msg_card_extra_info .simulator_bd {
    height: 395px;
    overflow: hidden;
    border-top: 0
}

.menu_tab_content .homepage .msg_card_extra_info .simulator_bd .article_list {
    height: auto;
    overflow: hidden
}

.menu_tab_content .homepage .msg_card_opr_item {
    *margin-right: -5px
}

.menu_link_weapp {
    float: none
}

.menu_link_weapp .weui-desktop-media-global-bar {
    padding: 100px 300px 0;
    border: 0
}

.menu_link_weapp .weui-desktop-global__info {
    float: none
}

.menu_link_weapp .weapplinks_box {
    padding: 0;
    margin-top: -1px
}

.menu_link_weapp .weapplinks_box .weapplink_item {
    float: none
}

.menu_link_weapp .weapplinks_box .weapplink_item .weapplink_item_inner {
    margin: 0
}

.menu_link_weapp .weapplinks_box .weapplink_list {
    margin: 0
}

.menu_link_weapp .common_container {
    position: relative;
    padding-left: 4em;
    min-height: 1em;
    margin-top: 20px;
    margin-bottom: 15px
}

.menu_link_weapp .common_title {
    display: inline-block;
    position: absolute;
    left: 0;
    color: #9a9a9a
}

.menu_link_weapp .common_ele {
    display: inline-block;
    margin: 0 .5em 5px 0
}

.weapplinks_box {
    padding: 0 100px 20px
}

.weapplinks_box .weapplink_list {
    overflow: hidden;
    margin-right: -20px
}

.weapplinks_box .weapplink_item {
    float: left;
    width: 50%
}

.weapplinks_box .weapplink_item .weapplink_item_inner {
    border-radius: 2px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    margin: 0 20px 20px 0;
    padding: 15px 20px;
    border: 1px solid #e7e7eb;
    position: relative
}

.weapplinks_box .weapplink_item .weapplink_opr {
    float: right;
    margin-left: 1em;
    text-align: right;
    position: relative;
    z-index: 1
}

.weapplinks_box .weapplink_item .weapplink_info {
    padding: 10px 0;
    padding-left: 66px;
    min-height: 50px;
    position: relative
}

.weapplinks_box .weapplink_item strong, .weapplinks_box .weapplink_item em {
    font-weight: 400;
    font-style: normal;
    display: block;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal
}

.weapplinks_box .weapplink_item .weapplink_name {
    line-height: 50px
}

.weapplinks_box .weapplink_item .weapplink_avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    position: absolute;
    top: 10px;
    left: 0
}

.link_weapp_desc {
    padding: 20px 100px
}

.link_weapp_desc .link_weapp_desc_extend {
    float: right
}

.link_weapp_wrp {
    height: 320px;
    overflow-y: auto
}

.weapp_select_dialog .dialog_bd {
    padding: 0
}

.weapplink_select_mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    text-align: center;
    font-size: 0
}

.weapplink_select_mask .icon_card_selected {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -23px;
    margin-left: -23px
}

.weapplink_item_inner {
    cursor: pointer
}

.weapplink_item_inner:hover .weapplink_select_mask {
    display: block
}

.selected .weapplink_select_mask {
    display: block
}

.link_weapp_loading {
    text-align: center;
    padding: 82px
}

.link_weapp_empty_desc {
    display: block;
    text-align: center;
    padding-top: 140px
}

.weapp_empty_box {
    text-align: center;
    padding: 70px 0
}

.weapp_empty_box .desc {
    margin-bottom: 10px
}

.processor_step_content .step_content_bd {
    height: 430px;
    box-sizing: border-box
}

.processor_step_content .step_content_bd .link_weapp_wrp {
    height: 368px
}

.processor_step_content .step_content_ft {
    height: 65px;
    line-height: 65px;
    background-color: #fff;
    text-align: center;
    border-radius: 0 0 4px 4px
}

.processor_step_content .step_content_ft .btn_input {
    margin: 0 5px
}

.processor_step_content .cover_preview {
    width: 180px;
    height: 140px
}

.processor_step_content .cover_preview_wrp {
    *zoom: 1
}

.processor_step_content .cover_preview_wrp:after {
    content: "\200B";
    display: block;
    height: 0;
    clear: both
}

.processor_step_content .cover_preview {
    float: left;
    position: relative;
    text-align: center;
    color: #fff;
    margin-top: 10px;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover
}

.processor_step_content .cover_preview.first_appmsg_cover {
    width: 188px
}

.processor_step_content .cover_preview .del_media_white {
    position: absolute;
    bottom: 12px;
    left: 50%;
    margin-left: -10px
}

.processor_step_content .cover_preview .card_mask_global {
    display: none
}

.processor_step_content .cover_preview .hover_mask {
    top: auto;
    height: 140px
}

.processor_step_content .cover_preview .cover_error_msg {
    padding: 38px
}

.processor_step_content .cover_preview .error_mask {
    display: block;
    padding: 32px 8px 50px;
    overflow: auto;
    word-wrap: break-word;
    word-break: break-all
}

.processor_step_content .cover_preview:hover .hover_mask {
    display: block
}

.processor_step_content.step2 .step_content_bd {
    padding: 20px 140px
}

.processor_step_content.step2 .weapp_type_select_area {
    margin-top: 10px;
    margin-right: -40px
}

.processor_step_content.step2 .weapp_type_box {
    display: inline-block;
    width: 240px
}

.processor_step_content.step2 .type_preview {
    display: inline-block;
    width: 219px;
    height: 380px;
    background-size: cover;
    background-repeat: no-repeat
}

.processor_step_content.step3 .step_content_bd {
    padding: 45px 160px
}

.processor_step_content.step4 .step_content_bd {
    overflow-y: auto
}

.processor_step_content.step4 .step_content_bd_inner {
    padding: 45px 90px 15px
}

.processor_step_content.step4 .link_weapp_desc {
    padding: 0 0 20px
}

.processor_step_content.step4 .frm_tips, .processor_step_content.step4 .frm_msg {
    width: auto
}

.screen_small .weapp_select_dialog .dialog_bd {
    max-height: 540px
}

.flex_context {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.flex_bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    word-wrap: break-word;
    word-break: break-all
}

.weapp_card {
    border: 1px solid #e1e1e1;
    background-color: #fdfdfd;
    width: 270px;
    color: #222;
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    text-indent: 0;
    text-align: left;
    text-decoration: none
}

.weapp_card.flex_context {
    padding: 12px 15px
}

.weapp_card.flex_context .weapp_card_hd {
    padding-right: 1em
}

.weapp_card.flex_context .weapp_card_avatar {
    width: 50px;
    height: 50px
}

.weapp_card.flex_context .weapp_card_nickname {
    font-size: 17px;
    font-weight: 400;
    display: block;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal
}

.weapp_card.app_context {
    padding-top: 10px;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px
}

.weapp_card.app_context .weapp_card_bd {
    padding: 0 15px 15px
}

.weapp_card.app_context .weapp_card_profile {
    display: block;
    position: relative;
    font-size: 12px;
    color: #8d8d8d;
    padding-left: 25px
}

.weapp_card.app_context .weapp_card_avatar {
    width: 20px;
    height: 20px;
    margin: -0.2em 5px 0 0;
    position: absolute;
    top: 2px;
    left: 0
}

.weapp_card.app_context .weapp_card_nickname {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    font-weight: 400
}

.weapp_card.app_context .weapp_card_title {
    padding: .3em 0 .75em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    font-weight: 400
}

.weapp_card.app_context .weapp_card_thumb_wrp {
    position: relative;
    display: block;
    padding-bottom: 75%;
    background-repeat: no-repeat;
    background-position: center center;
    -webkit-background-size: cover;
    background-size: cover
}

.weapp_card.app_context .weapp_card_thumb {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100% !important
}

.weapp_card.app_context .weapp_card_ft {
    padding: 0 15px;
    border-top: 1px solid #e1e1e1;
    line-height: 24px
}

.weapp_card.app_context, .weapp_card .weapp_card_bd, .weapp_card .weapp_card_ft, .weapp_card .weapp_card_nickname, .weapp_card .weapp_card_info, .weapp_card .weapp_card_title {
    display: block
}

.weapp_card_avatar {
    padding: 0
}

.weapp_card_avatar img {
    width: 100%
}

.weapp_card_logo {
    color: #8d8d8d;
    font-size: 13px
}

.icon_weapp_logo_mini {
    width: 14px;
    height: 14px;
    vertical-align: middle;
    margin-right: .2em;
    margin-top: -0.2em
}

.card-preview__area {
    display: inline-block;
    width: 304px;
    vertical-align: top
}

.edit-image__area {
    display: inline-block;
    border-left: 1px solid #e7e7eb;
    vertical-align: top;
    padding-left: 34px
}

.edit-image__area .edit-image__preview {
    max-width: 330px;
    max-height: 260px
}

.edit-image__tips {
    margin-bottom: 10px
}

.upload-image_before__wrp {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed #e7e7eb;
    text-align: center;
    padding-top: 58px;
    background-color: #fff
}

.upload-image_before__wrp .upload_tips {
    font-size: 13px;
    margin-top: 10px
}

.upload-image_after__wrp {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    text-align: center;
    transition: all .3s;
    opacity: 0
}

.weapp_card_thumb_after_wrp:hover .upload-image_after__wrp {
    opacity: 1
}

.weapp_card_thumb_after_wrp {
    position: relative;
    font-size: 0;
    display: block
}

.weapp_card_thumb_preview {
    width: 100%
}

.popover_morepage {
    left: 0;
    margin-left: 500px;
    top: 50%;
    margin-top: -165px;
    width: 445px
}

.popover_morepage .popover_arrow_out {
    top: 50%;
    margin-top: -22px;
    left: -8px;
    border: 0;
    border-style: solid;
    border-width: 8px;
    border-color: transparent;
    border-right-color: #d9dadc
}

.popover_morepage .popover_arrow_in {
    top: 50%;
    margin-top: -22px;
    left: -8px;
    border: 0;
    border-style: solid;
    border-width: 8px;
    border-color: transparent;
    border-right-color: #fff
}

.popover_content_morepage .popover_content_morepage_desc {
    color: #8d8d8d;
    margin-bottom: 15px
}

.popover_content_morepage .popover_content_morepage_desc .link {
    margin-left: .5em
}

.popover_content_morepage .desc {
    color: #8d8d8d
}

.popover_content_morepage .step_vertical_container {
    counter-reset: sectioncounter;
    padding-left: 30px
}

.popover_content_morepage .step_vertical_ele {
    position: relative
}

.popover_content_morepage .step_vertical_ele:after {
    content: ' ';
    width: 0;
    height: 70%;
    display: block;
    border-right: 1px solid #e4e8eb;
    position: absolute;
    top: 30px;
    left: -19px
}

.popover_content_morepage .step_vertical_ele:last-child:after {
    display: none
}

.popover_content_morepage .step_vertical_ele:before {
    content: counter(sectioncounter);
    counter-increment: sectioncounter;
    width: 20px;
    border-radius: 50%;
    border: 1px solid #44b549;
    display: block;
    text-align: center;
    line-height: 20px;
    position: absolute;
    top: 0;
    left: -30px;
    color: #44b549
}

.popover_content_morepage .step_vertical_title {
    color: #44b549;
    margin-bottom: 5px
}

.popover_content_morepage .tag_container {
    position: relative;
    display: inline-block;
    line-height: 24px;
    background-color: #e5e7ec;
    border: 1px solid #e7e7eb;
    color: #353535;
    padding: 0 .5em;
    margin-bottom: 5px;
    margin-right: 5px
}

.popover_content_morepage .tag_container:last-child {
    margin-right: 0
}

.popover_content_morepage .tag_container .close_flat {
    margin-left: 3px;
    background: url(../img/index_z4510e0.png) 0 -47px no-repeat;
    width: 14px;
    height: 14px;
    vertical-align: middle;
    display: inline-block
}

.popover_content_morepage .tag_container .close_flat:hover {
    background: url(../img/index_z4510e0.png) 0 -65px no-repeat;
    cursor: pointer
}

.frm_tips {
    position: relative
}

.frm_tips .popover {
    position: absolute;
    top: 0;
    margin-top: -200px;
    left: 200px
}

.moblie_preview_weapp {
    position: absolute;
    top: 95px;
    left: 28px;
    width: 271px;
    height: 486px;
    background-color: #efeff4;
    text-align: center
}

.moblie_preview_weapp .weapp_hd {
    width: 100%;
    height: 50px;
    background: transparent url(../img/tophead.png) no-repeat 0 0;
    background-size: contain
}

.moblie_preview_weapp .weapp_hd .icon_back_weapp {
    display: inline-block;
    width: 40px;
    height: 40px;
    cursor: pointer;
    position: absolute;
    top: 10px;
    left: 0;
    font-size: 0
}


.moblie_preview_weapp .weapp_logo {
    margin-top: 60px
}

.moblie_preview_weapp .weapp_logo img {
    width: 80px;
    height: 80px;
    border-radius: 50%
}

.moblie_preview_weapp .weapp_title {
    padding-top: 20px;
    font-size: 14px;
    width: 12em;
    margin: 0 auto;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.moblie_preview_weapp .preview_extra {
    padding-top: 190px;
    width: 100%;
    color: #8d8d8d
}

.audio_dialog .weui-desktop-dialog__bd {
    padding: 0
}
