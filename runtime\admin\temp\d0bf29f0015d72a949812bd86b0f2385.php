<?php /*a:3:{s:56:"C:\wwwroot\127.0.0.1\app\admin\view\auth\admin_rule.html";i:1690988216;s:55:"C:\wwwroot\127.0.0.1\app\admin\view\layout\default.html";i:1683620908;s:52:"C:\wwwroot\127.0.0.1\app\admin\view\common\foot.html";i:1672069288;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo htmlentities($system['name']); ?>后台管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!--[if lt IE 9]>
    <script src="/static/js/html5.min.js"></script>
    <script src="/static/js/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo htmlentities($config['VERSION']); ?>" media="all">
    <script>
        window.CONFIG =<?php echo json_encode($config,320); ?>;
    </script>
    <?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
    <script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js" charset="utf-8"></script>
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/layuimini.css" media="all">
    <link rel="stylesheet" href="/static/plugs/lay-module/layuimini/themes/default.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>
<body>
<link rel="stylesheet" href="/static/admin/css/global.css" media="all">
<div class="admin-main layui-anim layui-anim-upbit">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>菜单管理</legend>
    </fieldset>

    <blockquote class="layui-elem-quote" style="color:#FFB800; ">
        <?php echo fy('Non professionals are not allowed to operate this page, otherwise the background page menu may not be displayed'); ?>. <span class="red"><?php echo fy('Note: Actions such as deletion, editing, modification, etc. are operation buttons, no right navigation display, no need to open the menu status'); ?></span>
    </blockquote>
    <blockquote class="layui-elem-quote">
        <a  class="layui-btn layui-btn-sm" data-open="<?php echo url('ruleAdd'); ?>" data-title="<?php echo lang('add'); ?><?php echo lang('node'); ?>" ><?php echo lang('add'); ?><?php echo lang('node'); ?></a>
        <a class="layui-btn layui-btn-sm layui-btn-danger" data-request="<?php echo url('clear'); ?>" data-title="<?php echo fy('Are you sure you want to clear the node'); ?>？"><?php echo fy('Clear the node'); ?></a>
        <a class="layui-btn layui-btn-normal layui-btn-sm"  onclick="openAll();"><?php echo fy('Expand or collapse all'); ?></a>

    </blockquote>
    <table class="layui-table" id="treeTable" lay-filter="treeTable"></table>
</div>
<script type="text/html" id="auth">
    <input type="checkbox" name="authopen" value="{{d.id}}" lay-skin="switch" lay-text="<?php echo fy('yes'); ?>|<?php echo fy('no'); ?>" lay-filter="authopen" {{ d.authopen == 1 ? 'checked' : '' }}>
</script>
<script type="text/html" id="status">
    <input type="checkbox" name="menustatus" value="{{d.id}}" lay-skin="switch" lay-text="是|否" lay-filter="menustatus" {{ d.menustatus == 1 ? 'checked' : '' }}>
</script>
<script type="text/html" id="order">
    <input name="{{d.id}}" data-id="{{d.id}}" class="list_order layui-input" value=" {{d.sort}}" size="10"/>
</script>
<script type="text/html" id="icon">
    <span class="icon {{d.icon}}"></span>
</script>
<script type="text/html" id="action">
    <a class="layui-btn layui-btn-xs layui-btn-normal" data-open="<?php echo url('ruleAdd'); ?>?id={{d.id}}" data-title="<?php echo lang('Add child nodes'); ?>"><?php echo lang('Add child nodes'); ?></a>
    <a class="layui-btn layui-btn-xs" data-open="<?php echo url('ruleEdit'); ?>?id={{d.id}}" data-title=<?php echo lang('Edit'); ?>><?php echo lang('Edit'); ?></a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" data-request="<?php echo url('ruleDel'); ?>?id={{d.id}}" data-title="<?php echo fy('Are you sure you want to delete the node'); ?>？"><?php echo lang('del'); ?></a>
</script>
<script type="text/html" id="topBtn">
   <a href="<?php echo url('ruleAdd'); ?>" class="layui-btn layui-btn-sm"><?php echo lang('add'); ?> <?php echo fy('Permissions'); ?></a>
</script>

<?php
$lang=\think\facade\Lang::getLangSet();
if($lang=='en-us'){
$l='_en';
 }else{
$l='';
} ?>
<script src="/static/plugs/layui-v2.7.6/layui<?php echo htmlentities($l); ?>.js" charset="utf-8"></script>
<script src="/static/plugs/layui-v2.7.6/lay-config.js" charset="utf-8"></script>




<script>
var editObj=null,ptable=null,treeGrid=null,tableId='treeTable',layer=null;
    layui.config({
        base: '/static/plugs/layui-v2.7.6/extend/'
    }).extend({
        treeGrid:'treeGrid'
    }).use(['jquery','treeGrid','layer','form'], function(){
        var $=layui.jquery;
        treeGrid = layui.treeGrid;
        layer=layui.layer;
		form = layui.form;
        ptable=treeGrid.render({
            id:tableId
            ,elem: '#'+tableId
            ,idField:'id'
            ,url:'<?php echo url("adminRule"); ?>'
            ,cellMinWidth: 100
            ,treeId:'id'//树形id字段名称
            ,treeUpId:'pid'//树形父id字段名称
            ,treeShowName:'title'//以树形式显示的字段
            ,height:'full-140'
            ,isFilter:false
            ,iconOpen:true//是否显示图标【默认显示】
            ,isOpenDefault:true//节点默认是展开还是折叠【默认展开】
            ,cols: [[
                {field: 'id', title: 'ID', width: 70, fixed: true},
                {field: 'icon', align: 'center',title: '<?php echo lang("icon"); ?>', width: 60,templet: '#icon'},
                {field: 'title', title: "<?php echo fy('Menu name'); ?>", width: 200},
                {field: 'href', title: '<?php echo fy("Controller"); ?>/<?php echo fy("Method"); ?>', width: 200},
                {field: 'authopen',align: 'center', title: '<?php echo fy("Whether to verify permissions"); ?>', width: 150,toolbar: '#auth'},
                {field: 'menustatus',align: 'center',title: '菜单', width: 150,toolbar: '#status'},
                {field: 'sort',align: 'center', title: '<?php echo lang("order"); ?>', width: 80, templet: '#order'},
                {width: 200,align: 'center', toolbar: '#action'}
            ]]
            ,page:false
        });
        treeGrid.on('tool('+tableId+')',function (obj) {
			var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('<?php echo fy("Are you sure you want to delete the record"); ?>？', function(index){
                    var loading = layer.load(1, {shade: [0.1, '#fff']});
                    $.post("<?php echo url('ruleDel'); ?>",{id:data.id},function(res){
                        layer.close(loading);
                        if(res.code==1){
                            layer.msg(res.msg,{time:2000,icon:1});
                            obj.del();
                        }else{
                            layer.msg(res.msg,{time:2000,icon:2});
                        }
                    });
                    layer.close(index);
                });
            }
        });
		form.on('switch(authopen)', function(obj){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var id = this.value;
            var authopen = obj.elem.checked===true?1:0;
            $.post('<?php echo url("ruleTz"); ?>',{'id':id,'authopen':authopen},function (res) {
                layer.close(loading);
                if (res.status==1) {
                    treeGrid.render;
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    treeGrid.render;
                    return false;
                }
            })
        });
		form.on('switch(menustatus)', function(obj){
            loading =layer.load(1, {shade: [0.1,'#fff']});
            var id = this.value;
            var menustatus = obj.elem.checked===true?1:0;
            $.post('<?php echo url("ruleState"); ?>',{'id':id,'menustatus':menustatus},function (res) {
                layer.close(loading);
                if (res.status==1) {
                    treeGrid.render;
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    treeGrid.render;
                    return false;
                }
            })
        });
		$('body').on('blur','.list_order',function() {
           var id = $(this).attr('data-id');
           var sort = $(this).val();
           $.post('<?php echo url("ruleOrder"); ?>',{id:id,sort:sort},function(res){
                if(res.code==1){
                    layer.msg(res.msg,{time:2000,icon:1},function(){
                        location.href = res.url;
                    });
                }else{
                    layer.msg(res.msg,{time:2000,icon:2});
                    treeGrid.render;
                }
           })
        })
    });
function openAll() {
    var treedata=treeGrid.getDataTreeList(tableId);
    treeGrid.treeOpenAll(tableId,!treedata[0][treeGrid.config.cols.isOpen]);
}
</script>

</body>
</html>